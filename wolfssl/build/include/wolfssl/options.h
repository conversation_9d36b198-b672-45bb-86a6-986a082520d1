/* wolfssl options.h
 * generated from configure options
 *
 * Copyright (C) 2006-2025 wolfSSL Inc.
 *
 * This file is part of wolfSSL. (formerly known as CyaSSL)
 *
 */

#ifdef WOLFSSL_NO_OPTIONS_H
/* options.h inhibited by configuration */
#elif !defined(WOLFSSL_OPTIONS_H)
#define WOLFSSL_OPTIONS_H


#ifdef __cplusplus
extern "C" {
#endif

#undef  WOLFSSL_HAVE_ATOMIC_H
#define WOLFSSL_HAVE_ATOMIC_H

#undef  WOLFSSL_HAVE_ASSERT_H
#define WOLFSSL_HAVE_ASSERT_H

#undef  WOLFSSL_WOLFSSH
#define WOLFSSL_WOLFSSH

#undef  HAVE_C___ATOMIC
#define HAVE_C___ATOMIC 1

#undef  HAVE_THREAD_LS
#define HAVE_THREAD_LS

#undef  NO_DO178
#define NO_DO178

#undef  WOLFSSL_X86_64_BUILD
#define WOLFSSL_X86_64_BUILD

#undef  WOLFSSL_ASN_TEMPLATE
#define WOLFSSL_ASN_TEMPLATE

#undef  HAVE_CRL_IO
#define HAVE_CRL_IO

#undef  HAVE_IO_TIMEOUT
#define HAVE_IO_TIMEOUT

#undef  WOLFSSL_DER_LOAD
#define WOLFSSL_DER_LOAD

#undef  KEEP_OUR_CERT
#define KEEP_OUR_CERT

#undef  KEEP_PEER_CERT
#define KEEP_PEER_CERT

#undef  WOLFSSL_SUBJ_DIR_ATTR
#define WOLFSSL_SUBJ_DIR_ATTR

#undef  WOLFSSL_FPKI
#define WOLFSSL_FPKI

#undef  WOLFSSL_SUBJ_INFO_ACC
#define WOLFSSL_SUBJ_INFO_ACC

#undef  WOLFSSL_CERT_NAME_ALL
#define WOLFSSL_CERT_NAME_ALL

#undef  WOLFSSL_VERBOSE_ERRORS
#define WOLFSSL_VERBOSE_ERRORS

#undef  HAVE_ECC_CDH
#define HAVE_ECC_CDH

#undef  HAVE_ECC_KOBLITZ
#define HAVE_ECC_KOBLITZ

#undef  HAVE_ECC_SECPR2
#define HAVE_ECC_SECPR2

#undef  HAVE_ECC_SECPR3
#define HAVE_ECC_SECPR3

#undef  WOLFSSL_DES_ECB
#define WOLFSSL_DES_ECB

#undef  HAVE_AES_DECRYPT
#define HAVE_AES_DECRYPT

#undef  HAVE_AES_ECB
#define HAVE_AES_ECB

#undef  WOLFSSL_ALT_NAMES
#define WOLFSSL_ALT_NAMES

#undef  HAVE_FFDHE_2048
#define HAVE_FFDHE_2048

#undef  HAVE_FFDHE_3072
#define HAVE_FFDHE_3072

#undef  WOLFSSL_ASN_ALL
#define WOLFSSL_ASN_ALL

#undef  WOLFSSL_DH_EXTRA
#define WOLFSSL_DH_EXTRA

#undef  WOLFSSL_ECDSA_DETERMINISTIC_K_VARIANT
#define WOLFSSL_ECDSA_DETERMINISTIC_K_VARIANT

#undef  WOLFSSL_HAVE_ISSUER_NAMES
#define WOLFSSL_HAVE_ISSUER_NAMES

#undef  WOLFSSL_DTLS
#define WOLFSSL_DTLS

#undef  WOLFSSL_DTLS_MTU
#define WOLFSSL_DTLS_MTU

#undef  SHOW_SECRETS
#define SHOW_SECRETS

#undef  HAVE_SECRET_CALLBACK
#define HAVE_SECRET_CALLBACK

#undef  WOLFSSL_SSLKEYLOGFILE
#define WOLFSSL_SSLKEYLOGFILE

#undef  WOLFSSL_KEYLOG_EXPORT_WARNED
#define WOLFSSL_KEYLOG_EXPORT_WARNED

#undef  WOLFSSL_QUIC
#define WOLFSSL_QUIC

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  WOLFSSL_POST_HANDSHAKE_AUTH
#define WOLFSSL_POST_HANDSHAKE_AUTH

#undef  WOLFSSL_SEND_HRR_COOKIE
#define WOLFSSL_SEND_HRR_COOKIE

#undef  WOLFSSL_LIBWEBSOCKETS
#define WOLFSSL_LIBWEBSOCKETS

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  OPENSSL_NO_EC
#define OPENSSL_NO_EC

#undef  WOLFSSL_OPENSSH
#define WOLFSSL_OPENSSH

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  WOLFSSL_BASE16
#define WOLFSSL_BASE16

#undef  WOLFSSL_ERROR_CODE_OPENSSL
#define WOLFSSL_ERROR_CODE_OPENSSL

#undef  ERROR_QUEUE_PER_THREAD
#define ERROR_QUEUE_PER_THREAD

#undef  TFM_TIMING_RESISTANT
#define TFM_TIMING_RESISTANT

#undef  ECC_TIMING_RESISTANT
#define ECC_TIMING_RESISTANT

#undef  WC_RSA_BLINDING
#define WC_RSA_BLINDING

#undef  FORTRESS
#define FORTRESS

#undef  WOLFSSL_ALWAYS_VERIFY_CB
#define WOLFSSL_ALWAYS_VERIFY_CB

#undef  WOLFSSL_AES_COUNTER
#define WOLFSSL_AES_COUNTER

#undef  WOLFSSL_AES_DIRECT
#define WOLFSSL_AES_DIRECT

#undef  WOLFSSL_DER_LOAD
#define WOLFSSL_DER_LOAD

#undef  WOLFSSL_KEY_GEN
#define WOLFSSL_KEY_GEN

#undef  PERSIST_SESSION_CACHE
#define PERSIST_SESSION_CACHE

#undef  PERSIST_CERT_CACHE
#define PERSIST_CERT_CACHE

#undef  ATOMIC_USER
#define ATOMIC_USER

#undef  HAVE_PK_CALLBACKS
#define HAVE_PK_CALLBACKS

#undef  WOLFSSL_AES_CBC_LENGTH_CHECKS
#define WOLFSSL_AES_CBC_LENGTH_CHECKS

#undef  HAVE_AESCCM
#define HAVE_AESCCM

#undef  WOLFSSL_AES_EAX
#define WOLFSSL_AES_EAX

#undef  WOLFSSL_AES_OFB
#define WOLFSSL_AES_OFB

#undef  WOLFSSL_AES_DIRECT
#define WOLFSSL_AES_DIRECT

#undef  WOLFSSL_AES_CFB
#define WOLFSSL_AES_CFB

#undef  WOLFSSL_ARMASM_NO_HW_CRYPTO
#define WOLFSSL_ARMASM_NO_HW_CRYPTO

#undef  USE_INTEL_SPEEDUP
#define USE_INTEL_SPEEDUP

#undef  WOLFSSL_AESNI
#define WOLFSSL_AESNI

#undef  WOLFSSL_USE_ALIGN
#define WOLFSSL_USE_ALIGN

#undef  HAVE_CAMELLIA
#define HAVE_CAMELLIA

#undef  WOLFSSL_MD2
#define WOLFSSL_MD2

#undef  HAVE_NULL_CIPHER
#define HAVE_NULL_CIPHER

#undef  WOLFSSL_RIPEMD
#define WOLFSSL_RIPEMD

#undef  HAVE_BLAKE2
#define HAVE_BLAKE2

#undef  HAVE_BLAKE2B
#define HAVE_BLAKE2B

#undef  HAVE_BLAKE2S
#define HAVE_BLAKE2S

#undef  WOLFSSL_SHA224
#define WOLFSSL_SHA224

#undef  WOLFSSL_SHA512
#define WOLFSSL_SHA512

#undef  WOLFSSL_SHA384
#define WOLFSSL_SHA384

#undef  SESSION_CERTS
#define SESSION_CERTS

#undef  WOLFSSL_SEP
#define WOLFSSL_SEP

#undef  KEEP_PEER_CERT
#define KEEP_PEER_CERT

#undef  HAVE_HKDF
#define HAVE_HKDF

#undef  HAVE_X963_KDF
#define HAVE_X963_KDF

#undef  HAVE_ECC
#define HAVE_ECC

#undef  ECC_SHAMIR
#define ECC_SHAMIR

#undef  ECC_MIN_KEY_SZ
#define ECC_MIN_KEY_SZ 224

#undef  HAVE_ECC_BRAINPOOL
#define HAVE_ECC_BRAINPOOL

#undef  HAVE_CURVE25519
#define HAVE_CURVE25519

#undef  FP_ECC
#define FP_ECC

#undef  HAVE_ECC_ENCRYPT
#define HAVE_ECC_ENCRYPT

#undef  WOLFCRYPT_HAVE_ECCSI
#define WOLFCRYPT_HAVE_ECCSI

#undef  WOLFSSL_PUBLIC_MP
#define WOLFSSL_PUBLIC_MP

#undef  WOLFCRYPT_HAVE_SAKKE
#define WOLFCRYPT_HAVE_SAKKE

#undef  NO_OLD_TLS
#define NO_OLD_TLS

#undef  WOLFSSL_QT
#define WOLFSSL_QT

#undef  SESSION_CERTS
#define SESSION_CERTS

#undef  OPENSSL_NO_SSL2
#define OPENSSL_NO_SSL2

#undef  WOLFSSL_KEY_GEN
#define WOLFSSL_KEY_GEN

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  WOLFSSL_CUSTOM_CURVES
#define WOLFSSL_CUSTOM_CURVES

#undef  HAVE_ECC_SECPR2
#define HAVE_ECC_SECPR2

#undef  HAVE_ECC_SECPR3
#define HAVE_ECC_SECPR3

#undef  HAVE_ECC_BRAINPOOL
#define HAVE_ECC_BRAINPOOL

#undef  HAVE_ECC_KOBLITZ
#define HAVE_ECC_KOBLITZ

#undef  WC_RSA_PSS
#define WC_RSA_PSS

#undef  WOLFSSL_PSS_LONG_SALT
#define WOLFSSL_PSS_LONG_SALT

#undef  HAVE_ANON
#define HAVE_ANON

#undef  WOLFSSL_ASN_PRINT
#define WOLFSSL_ASN_PRINT

#undef  WOLFSSL_DTLS13
#define WOLFSSL_DTLS13

#undef  WOLFSSL_W64_WRAPPER
#define WOLFSSL_W64_WRAPPER

#undef  WOLFSSL_AES_DIRECT
#define WOLFSSL_AES_DIRECT

#undef  WOLFSSL_DTLS_CID
#define WOLFSSL_DTLS_CID

#undef  WOLFSSL_DTLS_CH_FRAG
#define WOLFSSL_DTLS_CH_FRAG

#undef  WOLFSSL_BASE64_ENCODE
#define WOLFSSL_BASE64_ENCODE

#undef  WOLFSSL_BASE16
#define WOLFSSL_BASE16

#undef  WOLFSSL_SIPHASH
#define WOLFSSL_SIPHASH

#undef  WOLFSSL_CMAC
#define WOLFSSL_CMAC

#undef  WOLFSSL_AES_DIRECT
#define WOLFSSL_AES_DIRECT

#undef  HAVE_WEBSERVER
#define HAVE_WEBSERVER

#undef  WOLFSSL_AES_XTS
#define WOLFSSL_AES_XTS

#undef  WOLFSSL_AES_DIRECT
#define WOLFSSL_AES_DIRECT

#undef  WOLFSSL_CUSTOM_CURVES
#define WOLFSSL_CUSTOM_CURVES

#undef  HAVE_ECC_SECPR2
#define HAVE_ECC_SECPR2

#undef  HAVE_ECC_SECPR3
#define HAVE_ECC_SECPR3

#undef  HAVE_ECC_BRAINPOOL
#define HAVE_ECC_BRAINPOOL

#undef  HAVE_ECC_KOBLITZ
#define HAVE_ECC_KOBLITZ

#undef  HAVE_ECC_CDH
#define HAVE_ECC_CDH

#undef  HAVE_CURVE448
#define HAVE_CURVE448

#undef  HAVE_ED448
#define HAVE_ED448

#undef  WOLFSSL_ED448_STREAMING_VERIFY
#define WOLFSSL_ED448_STREAMING_VERIFY

#undef  WC_SRTP_KDF
#define WC_SRTP_KDF

#undef  HAVE_AES_ECB
#define HAVE_AES_ECB

#undef  WOLFSSL_AES_DIRECT
#define WOLFSSL_AES_DIRECT

#undef  WOLFSSL_SHA3
#define WOLFSSL_SHA3

#undef  WOLFSSL_SHAKE128
#define WOLFSSL_SHAKE128

#undef  WOLFSSL_SHAKE256
#define WOLFSSL_SHAKE256

#undef  HAVE_POLY1305
#define HAVE_POLY1305

#undef  HAVE_CHACHA
#define HAVE_CHACHA

#undef  HAVE_XCHACHA
#define HAVE_XCHACHA

#undef  HAVE_HASHDRBG
#define HAVE_HASHDRBG

#undef  HAVE_OPENSSL_CMD
#define HAVE_OPENSSL_CMD

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_CERTIFICATE_STATUS_REQUEST
#define HAVE_CERTIFICATE_STATUS_REQUEST

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_CERTIFICATE_STATUS_REQUEST_V2
#define HAVE_CERTIFICATE_STATUS_REQUEST_V2

#undef  HAVE_CRL
#define HAVE_CRL

#undef  HAVE_CRL_MONITOR
#define HAVE_CRL_MONITOR

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_SNI
#define HAVE_SNI

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_ALPN
#define HAVE_ALPN

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_MAX_FRAGMENT
#define HAVE_MAX_FRAGMENT

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_TRUSTED_CA
#define HAVE_TRUSTED_CA

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_TRUNCATED_HMAC
#define HAVE_TRUNCATED_HMAC

#undef  HAVE_FALLBACK_SCSV
#define HAVE_FALLBACK_SCSV

#undef  HAVE_KEYING_MATERIAL
#define HAVE_KEYING_MATERIAL

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_SUPPORTED_CURVES
#define HAVE_SUPPORTED_CURVES

#undef  HAVE_FFDHE_2048
#define HAVE_FFDHE_2048

#undef  HAVE_SUPPORTED_CURVES
#define HAVE_SUPPORTED_CURVES

#undef  WOLFSSL_TLS13
#define WOLFSSL_TLS13

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_SESSION_TICKET
#define HAVE_SESSION_TICKET

#undef  HAVE_EXTENDED_MASTER
#define HAVE_EXTENDED_MASTER

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_SNI
#define HAVE_SNI

#undef  HAVE_MAX_FRAGMENT
#define HAVE_MAX_FRAGMENT

#undef  HAVE_TRUNCATED_HMAC
#define HAVE_TRUNCATED_HMAC

#undef  HAVE_ALPN
#define HAVE_ALPN

#undef  HAVE_TRUSTED_CA
#define HAVE_TRUSTED_CA

#undef  HAVE_SUPPORTED_CURVES
#define HAVE_SUPPORTED_CURVES

#undef  WOLFSSL_EARLY_DATA
#define WOLFSSL_EARLY_DATA

#undef  HAVE_SMIME
#define HAVE_SMIME

#undef  WOLFCRYPT_HAVE_SRP
#define WOLFCRYPT_HAVE_SRP

#undef  ASN_BER_TO_DER
#define ASN_BER_TO_DER

#undef  WOLFSSL_HAVE_CERT_SERVICE
#define WOLFSSL_HAVE_CERT_SERVICE

#undef  HAVE_LIGHTY
#define HAVE_LIGHTY

#undef  HAVE_WOLFSSL_SSL_H
#define HAVE_WOLFSSL_SSL_H 1

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  OPENSSL_ALL
#define OPENSSL_ALL

#undef  WOLFSSL_KEY_GEN
#define WOLFSSL_KEY_GEN

#undef  WOLFSSL_NGINX
#define WOLFSSL_NGINX

#undef  WOLFSSL_SIGNER_DER_CERT
#define WOLFSSL_SIGNER_DER_CERT

#undef  OPENSSL_COMPATIBLE_DEFAULTS
#define OPENSSL_COMPATIBLE_DEFAULTS

#undef  WOLFSSL_ERROR_CODE_OPENSSL
#define WOLFSSL_ERROR_CODE_OPENSSL

#undef  WOLFSSL_OPENVPN
#define WOLFSSL_OPENVPN

#undef  HAVE_KEYING_MATERIAL
#define HAVE_KEYING_MATERIAL

#undef  WOLFSSL_DES_ECB
#define WOLFSSL_DES_ECB

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  WOLFSSL_KEY_GEN
#define WOLFSSL_KEY_GEN

#undef  WOLFSSL_ALWAYS_VERIFY_CB
#define WOLFSSL_ALWAYS_VERIFY_CB

#undef  WOLFSSL_ALWAYS_KEEP_SNI
#define WOLFSSL_ALWAYS_KEEP_SNI

#undef  KEEP_OUR_CERT
#define KEEP_OUR_CERT

#undef  KEEP_PEER_CERT
#define KEEP_PEER_CERT

#undef  HAVE_EXT_CACHE
#define HAVE_EXT_CACHE

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  WOLFSSL_CERT_GEN
#define WOLFSSL_CERT_GEN

#undef  WOLFSSL_ASIO
#define WOLFSSL_ASIO

#undef  ASIO_USE_WOLFSSL
#define ASIO_USE_WOLFSSL

#undef  WOLFSSL_KEY_GEN
#define WOLFSSL_KEY_GEN

#undef  BOOST_ASIO_USE_WOLFSSL
#define BOOST_ASIO_USE_WOLFSSL

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  SSL_TXT_TLSV1_2
#define SSL_TXT_TLSV1_2

#undef  OPENSSL_NO_SSL2
#define OPENSSL_NO_SSL2

#undef  OPENSSL_NO_SSL3
#define OPENSSL_NO_SSL3

#undef  HAVE_ENCRYPT_THEN_MAC
#define HAVE_ENCRYPT_THEN_MAC

#undef  HAVE_STUNNEL
#define HAVE_STUNNEL

#undef  WOLFSSL_ALWAYS_VERIFY_CB
#define WOLFSSL_ALWAYS_VERIFY_CB

#undef  WOLFSSL_ALWAYS_KEEP_SNI
#define WOLFSSL_ALWAYS_KEEP_SNI

#undef  HAVE_EX_DATA
#define HAVE_EX_DATA

#undef  WOLFSSL_DES_ECB
#define WOLFSSL_DES_ECB

#undef  WOLFSSL_SIGNER_DER_CERT
#define WOLFSSL_SIGNER_DER_CERT

#undef  OPENSSL_COMPATIBLE_DEFAULTS
#define OPENSSL_COMPATIBLE_DEFAULTS

#undef  WOLFSSL_TICKET_HAVE_ID
#define WOLFSSL_TICKET_HAVE_ID

#undef  WOLFSSL_ALT_CERT_CHAINS
#define WOLFSSL_ALT_CERT_CHAINS

#undef  WOLFSSL_IP_ALT_NAME
#define WOLFSSL_IP_ALT_NAME

#undef  NO_SESSION_CACHE_REF
#define NO_SESSION_CACHE_REF

#undef  WOLFSSL_DES_ECB
#define WOLFSSL_DES_ECB

#undef  WOLFSSL_TICKET_NONCE_MALLOC
#define WOLFSSL_TICKET_NONCE_MALLOC

#undef  WOLFSSL_ENCRYPTED_KEYS
#define WOLFSSL_ENCRYPTED_KEYS

#undef  HAVE_SCRYPT
#define HAVE_SCRYPT

#undef  WOLFSSL_HAVE_SP_RSA
#define WOLFSSL_HAVE_SP_RSA

#undef  WOLFSSL_HAVE_SP_DH
#define WOLFSSL_HAVE_SP_DH

#undef  WOLFSSL_SP_4096
#define WOLFSSL_SP_4096

#undef  WOLFSSL_SP_LARGE_CODE
#define WOLFSSL_SP_LARGE_CODE

#undef  WOLFSSL_HAVE_SP_ECC
#define WOLFSSL_HAVE_SP_ECC

#undef  HAVE_ECC384
#define HAVE_ECC384

#undef  WOLFSSL_SP_384
#define WOLFSSL_SP_384

#undef  HAVE_ECC521
#define HAVE_ECC521

#undef  WOLFSSL_SP_521
#define WOLFSSL_SP_521

#undef  WOLFSSL_SP_1024
#define WOLFSSL_SP_1024

#undef  WOLFSSL_SP_MATH_ALL
#define WOLFSSL_SP_MATH_ALL

#undef  WOLFSSL_SP_X86_64
#define WOLFSSL_SP_X86_64

#undef  WOLFSSL_SP_ASM
#define WOLFSSL_SP_ASM

#undef  WOLFSSL_SP_X86_64_ASM
#define WOLFSSL_SP_X86_64_ASM

#undef  WOLF_CRYPTO_CB
#define WOLF_CRYPTO_CB

#undef  WC_NO_ASYNC_THREADING
#define WC_NO_ASYNC_THREADING

#undef  HAVE_AES_KEYWRAP
#define HAVE_AES_KEYWRAP

#undef  WOLFSSL_AES_DIRECT
#define WOLFSSL_AES_DIRECT

#undef  NO_OLD_RNGNAME
#define NO_OLD_RNGNAME

#undef  NO_OLD_WC_NAMES
#define NO_OLD_WC_NAMES

#undef  NO_OLD_SSL_NAMES
#define NO_OLD_SSL_NAMES

#undef  NO_OLD_SHA_NAMES
#define NO_OLD_SHA_NAMES

#undef  NO_OLD_MD5_NAME
#define NO_OLD_MD5_NAME

#undef  WOLFSSL_HASH_FLAGS
#define WOLFSSL_HASH_FLAGS

#undef  RSA_MAX_SIZE
#define RSA_MAX_SIZE 4096

#undef  FP_MAX_BITS
#define FP_MAX_BITS 8192

#undef  SP_INT_BITS
#define SP_INT_BITS 4096

#undef  WOLFSSL_TRUST_PEER_CERT
#define WOLFSSL_TRUST_PEER_CERT

#undef  NO_SESSION_CACHE_REF
#define NO_SESSION_CACHE_REF

#undef  WOLFSSL_TLS13_NO_PEEK_HANDSHAKE_DONE
#define WOLFSSL_TLS13_NO_PEEK_HANDSHAKE_DONE

#undef  WOLFSSL_ALT_CERT_CHAINS
#define WOLFSSL_ALT_CERT_CHAINS

#undef  WOLFSSL_PRIORITIZE_PSK
#define WOLFSSL_PRIORITIZE_PSK

#undef  WOLFSSL_CHECK_ALERT_ON_ERR
#define WOLFSSL_CHECK_ALERT_ON_ERR

#undef  WOLFSSL_TICKET_HAVE_ID
#define WOLFSSL_TICKET_HAVE_ID

#undef  WOLFSSL_SYS_CA_CERTS
#define WOLFSSL_SYS_CA_CERTS

#undef  HAVE_RPK
#define HAVE_RPK

#undef  WOLFSSL_KEY_GEN
#define WOLFSSL_KEY_GEN

#undef  WOLFSSL_CERT_REQ
#define WOLFSSL_CERT_REQ

#undef  WOLFSSL_CERT_GEN
#define WOLFSSL_CERT_GEN

#undef  WOLFSSL_CERT_EXT
#define WOLFSSL_CERT_EXT

#undef  HAVE_ED25519
#define HAVE_ED25519

#undef  HAVE_OCSP
#define HAVE_OCSP

#undef  WOLFSSL_ED25519_STREAMING_VERIFY
#define WOLFSSL_ED25519_STREAMING_VERIFY

#undef  OPENSSL_ALL
#define OPENSSL_ALL

#undef  WOLFSSL_EITHER_SIDE
#define WOLFSSL_EITHER_SIDE

#undef  WC_RSA_NO_PADDING
#define WC_RSA_NO_PADDING

#undef  WC_RSA_PSS
#define WC_RSA_PSS

#undef  WOLFSSL_PSS_LONG_SALT
#define WOLFSSL_PSS_LONG_SALT

#undef  WOLFSSL_TICKET_HAVE_ID
#define WOLFSSL_TICKET_HAVE_ID

#undef  WOLFSSL_ERROR_CODE_OPENSSL
#define WOLFSSL_ERROR_CODE_OPENSSL

#undef  WOLFSSL_CERT_NAME_ALL
#define WOLFSSL_CERT_NAME_ALL

#undef  WOLFSSL_AES_SIV
#define WOLFSSL_AES_SIV

#undef  OPENSSL_EXTRA
#define OPENSSL_EXTRA

#undef  WOLFSSL_HAVE_WOLFSCEP
#define WOLFSSL_HAVE_WOLFSCEP

#undef  HAVE_PKCS7
#define HAVE_PKCS7

#undef  NO_DES3_TLS_SUITES
#define NO_DES3_TLS_SUITES

#undef  GCM_TABLE_4BIT
#define GCM_TABLE_4BIT

#undef  HAVE_AESGCM
#define HAVE_AESGCM

#undef  WOLFSSL_AESGCM_STREAM
#define WOLFSSL_AESGCM_STREAM

#undef  WOLFSSL_AESXTS_STREAM
#define WOLFSSL_AESXTS_STREAM

#undef  WOLFSSL_SRTP
#define WOLFSSL_SRTP

#undef  WOLFSSL_MULTICAST
#define WOLFSSL_MULTICAST

#undef  WOLFSSL_PUBLIC_MP
#define WOLFSSL_PUBLIC_MP

#undef  HAVE_TLS_EXTENSIONS
#define HAVE_TLS_EXTENSIONS

#undef  HAVE_SERVER_RENEGOTIATION_INFO
#define HAVE_SERVER_RENEGOTIATION_INFO

#undef  HAVE_COMP_KEY
#define HAVE_COMP_KEY

#undef  WOLFSSL_ALLOW_RC4
#define WOLFSSL_ALLOW_RC4

#undef  WOLFSSL_TLS_OCSP_MULTI
#define WOLFSSL_TLS_OCSP_MULTI

#undef  HAVE___UINT128_T
#define HAVE___UINT128_T 1

#undef  HAVE_WC_INTROSPECTION
#define HAVE_WC_INTROSPECTION


#ifdef __cplusplus
}
#endif


#endif /* WOLFSSL_OPTIONS_H */

