/* certs_test.h */
/* This file was generated using: ./gencertbuf.pl */

#ifndef WOLFSSL_CERTS_TEST_H
#define WOLFSSL_CERTS_TEST_H

#ifdef USE_CERT_BUFFERS_1024

/* ./certs/1024/client-key.der, 1024-bit */
static const unsigned char client_key_der_1024[] =
{
        0x30, 0x82, 0x02, 0x5C, 0x02, 0x01, 0x00, 0x02, 0x81, 0x81,
        0x00, 0xBC, 0x73, 0x0E, 0xA8, 0x49, 0xF3, 0x74, 0xA2, 0xA9,
        0xEF, 0x18, 0xA5, 0xDA, 0x55, 0x99, 0x21, 0xF9, 0xC8, 0xEC,
        0xB3, 0x6D, 0x48, 0xE5, 0x35, 0x35, 0x75, 0x77, 0x37, 0xEC,
        0xD1, 0x61, 0x90, 0x5F, 0x3E, 0xD9, 0xE4, 0xD5, 0xDF, 0x94,
        0xCA, 0xC1, 0xA9, 0xD7, 0x19, 0xDA, 0x86, 0xC9, 0xE8, 0x4D,
        0xC4, 0x61, 0x36, 0x82, 0xFE, 0xAB, 0xAD, 0x7E, 0x77, 0x25,
        0xBB, 0x8D, 0x11, 0xA5, 0xBC, 0x62, 0x3A, 0xA8, 0x38, 0xCC,
        0x39, 0xA2, 0x04, 0x66, 0xB4, 0xF7, 0xF7, 0xF3, 0xAA, 0xDA,
        0x4D, 0x02, 0x0E, 0xBB, 0x5E, 0x8D, 0x69, 0x48, 0xDC, 0x77,
        0xC9, 0x28, 0x0E, 0x22, 0xE9, 0x6B, 0xA4, 0x26, 0xBA, 0x4C,
        0xE8, 0xC1, 0xFD, 0x4A, 0x6F, 0x2B, 0x1F, 0xEF, 0x8A, 0xAE,
        0xF6, 0x90, 0x62, 0xE5, 0x64, 0x1E, 0xEB, 0x2B, 0x3C, 0x67,
        0xC8, 0xDC, 0x27, 0x00, 0xF6, 0x91, 0x68, 0x65, 0xA9, 0x02,
        0x03, 0x01, 0x00, 0x01, 0x02, 0x81, 0x80, 0x13, 0x97, 0xEA,
        0xE8, 0x38, 0x78, 0x25, 0xA2, 0x5C, 0x04, 0xCE, 0x0D, 0x40,
        0x7C, 0x31, 0xE5, 0xC4, 0x70, 0xCD, 0x9B, 0x82, 0x3B, 0x58,
        0x09, 0x86, 0x3B, 0x66, 0x5F, 0xDC, 0x31, 0x90, 0xF1, 0x4F,
        0xD5, 0xDB, 0x15, 0xDD, 0xDE, 0xD7, 0x3B, 0x95, 0x93, 0x31,
        0x18, 0x31, 0x0E, 0x5E, 0xA3, 0xD6, 0xA2, 0x1A, 0x71, 0x6E,
        0x81, 0x48, 0x1C, 0x4B, 0xCF, 0xDB, 0x8E, 0x7A, 0x86, 0x61,
        0x32, 0xDC, 0xFB, 0x55, 0xC1, 0x16, 0x6D, 0x27, 0x92, 0x24,
        0x45, 0x8B, 0xF1, 0xB8, 0x48, 0xB1, 0x4B, 0x1D, 0xAC, 0xDE,
        0xDA, 0xDD, 0x8E, 0x2F, 0xC2, 0x91, 0xFB, 0xA5, 0xA9, 0x6E,
        0xF8, 0x3A, 0x6A, 0xF1, 0xFD, 0x50, 0x18, 0xEF, 0x9F, 0xE7,
        0xC3, 0xCA, 0x78, 0xEA, 0x56, 0xD3, 0xD3, 0x72, 0x5B, 0x96,
        0xDD, 0x4E, 0x06, 0x4E, 0x3A, 0xC3, 0xD9, 0xBE, 0x72, 0xB6,
        0x65, 0x07, 0x07, 0x4C, 0x01, 0x02, 0x41, 0x00, 0xFA, 0x47,
        0xD4, 0x7A, 0x7C, 0x92, 0x3C, 0x55, 0xEF, 0x81, 0xF0, 0x41,
        0x30, 0x2D, 0xA3, 0xCF, 0x8F, 0x1C, 0xE6, 0x87, 0x27, 0x05,
        0x70, 0x0D, 0xDF, 0x98, 0x35, 0xD6, 0xF1, 0x8B, 0x38, 0x2F,
        0x24, 0xB5, 0xD0, 0x84, 0xB6, 0x79, 0x4F, 0x71, 0x29, 0x94,
        0x5A, 0xF0, 0x64, 0x6A, 0xAC, 0xE7, 0x72, 0xC6, 0xED, 0x4D,
        0x59, 0x98, 0x3E, 0x67, 0x3A, 0xF3, 0x74, 0x2C, 0xF9, 0x61,
        0x17, 0x69, 0x02, 0x41, 0x00, 0xC0, 0xC1, 0x82, 0x0D, 0x0C,
        0xEB, 0xC6, 0x2F, 0xDC, 0x92, 0xF9, 0x9D, 0x82, 0x1A, 0x31,
        0xE9, 0xE9, 0xF7, 0x4B, 0xF2, 0x82, 0x87, 0x1C, 0xEE, 0x16,
        0x6A, 0xD1, 0x1D, 0x18, 0x82, 0x70, 0xF3, 0xC0, 0xB6, 0x2F,
        0xF6, 0xF3, 0xF7, 0x1D, 0xF1, 0x86, 0x23, 0xC8, 0x4E, 0xEB,
        0x8F, 0x56, 0x8E, 0x8F, 0xF5, 0xBF, 0xF1, 0xF7, 0x2B, 0xB5,
        0xCC, 0x3D, 0xC6, 0x57, 0x39, 0x0C, 0x1B, 0x54, 0x41, 0x02,
        0x41, 0x00, 0x9D, 0x7E, 0x05, 0xDE, 0xED, 0xF4, 0xB7, 0xB2,
        0xFB, 0xFC, 0x30, 0x4B, 0x55, 0x1D, 0xE3, 0x2F, 0x01, 0x47,
        0x96, 0x69, 0x05, 0xCD, 0x0E, 0x2E, 0x2C, 0xBD, 0x83, 0x63,
        0xB6, 0xAB, 0x7C, 0xB7, 0x6D, 0xCA, 0x5B, 0x64, 0xA7, 0xCE,
        0xBE, 0x86, 0xDF, 0x3B, 0x53, 0xDE, 0x61, 0xD2, 0x1E, 0xEB,
        0xA5, 0xF6, 0x37, 0xED, 0xAC, 0xAB, 0x78, 0xD9, 0x4C, 0xE7,
        0x55, 0xFB, 0xD7, 0x11, 0x99, 0xC1, 0x02, 0x40, 0x18, 0x98,
        0x18, 0x29, 0xE6, 0x1E, 0x27, 0x39, 0x70, 0x21, 0x68, 0xAC,
        0x0A, 0x2F, 0xA1, 0x72, 0xC1, 0x21, 0x86, 0x95, 0x38, 0xC6,
        0x58, 0x90, 0xA0, 0x57, 0x9C, 0xBA, 0xE3, 0xA7, 0xB1, 0x15,
        0xC8, 0xDE, 0xF6, 0x1B, 0xC2, 0x61, 0x23, 0x76, 0xEF, 0xB0,
        0x9D, 0x1C, 0x44, 0xBE, 0x13, 0x43, 0x39, 0x67, 0x17, 0xC8,
        0x9D, 0xCA, 0xFB, 0xF5, 0x45, 0x64, 0x8B, 0x38, 0x82, 0x2C,
        0xF2, 0x81, 0x02, 0x40, 0x39, 0x89, 0xE5, 0x9C, 0x19, 0x55,
        0x30, 0xBA, 0xB7, 0x48, 0x8C, 0x48, 0x14, 0x0E, 0xF4, 0x9F,
        0x7E, 0x77, 0x97, 0x43, 0xE1, 0xB4, 0x19, 0x35, 0x31, 0x23,
        0x75, 0x9C, 0x3B, 0x44, 0xAD, 0x69, 0x12, 0x56, 0xEE, 0x00,
        0x61, 0x64, 0x16, 0x66, 0xD3, 0x7C, 0x74, 0x2B, 0x15, 0xB4,
        0xA2, 0xFE, 0xBF, 0x08, 0x6B, 0x1A, 0x5D, 0x3F, 0x90, 0x12,
        0xB1, 0x05, 0x86, 0x31, 0x29, 0xDB, 0xD9, 0xE2
};
static const int sizeof_client_key_der_1024 = sizeof(client_key_der_1024);

/* ./certs/1024/client-keyPub.der, 1024-bit */
static const unsigned char client_keypub_der_1024[] =
{
        0x30, 0x81, 0x9F, 0x30, 0x0D, 0x06, 0x09, 0x2A, 0x86, 0x48,
        0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01, 0x05, 0x00, 0x03, 0x81,
        0x8D, 0x00, 0x30, 0x81, 0x89, 0x02, 0x81, 0x81, 0x00, 0xBC,
        0x73, 0x0E, 0xA8, 0x49, 0xF3, 0x74, 0xA2, 0xA9, 0xEF, 0x18,
        0xA5, 0xDA, 0x55, 0x99, 0x21, 0xF9, 0xC8, 0xEC, 0xB3, 0x6D,
        0x48, 0xE5, 0x35, 0x35, 0x75, 0x77, 0x37, 0xEC, 0xD1, 0x61,
        0x90, 0x5F, 0x3E, 0xD9, 0xE4, 0xD5, 0xDF, 0x94, 0xCA, 0xC1,
        0xA9, 0xD7, 0x19, 0xDA, 0x86, 0xC9, 0xE8, 0x4D, 0xC4, 0x61,
        0x36, 0x82, 0xFE, 0xAB, 0xAD, 0x7E, 0x77, 0x25, 0xBB, 0x8D,
        0x11, 0xA5, 0xBC, 0x62, 0x3A, 0xA8, 0x38, 0xCC, 0x39, 0xA2,
        0x04, 0x66, 0xB4, 0xF7, 0xF7, 0xF3, 0xAA, 0xDA, 0x4D, 0x02,
        0x0E, 0xBB, 0x5E, 0x8D, 0x69, 0x48, 0xDC, 0x77, 0xC9, 0x28,
        0x0E, 0x22, 0xE9, 0x6B, 0xA4, 0x26, 0xBA, 0x4C, 0xE8, 0xC1,
        0xFD, 0x4A, 0x6F, 0x2B, 0x1F, 0xEF, 0x8A, 0xAE, 0xF6, 0x90,
        0x62, 0xE5, 0x64, 0x1E, 0xEB, 0x2B, 0x3C, 0x67, 0xC8, 0xDC,
        0x27, 0x00, 0xF6, 0x91, 0x68, 0x65, 0xA9, 0x02, 0x03, 0x01,
        0x00, 0x01
};
static const int sizeof_client_keypub_der_1024 = sizeof(client_keypub_der_1024);

/* ./certs/1024/client-cert.der, 1024-bit */
static const unsigned char client_cert_der_1024[] =
{
        0x30, 0x82, 0x04, 0x18, 0x30, 0x82, 0x03, 0x81, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x14, 0x09, 0x1D, 0x03, 0x41, 0x8B,
        0x92, 0xBD, 0x2A, 0x2A, 0x1C, 0x77, 0xE0, 0x13, 0xA8, 0x3D,
        0xF0, 0x33, 0xDA, 0x7F, 0x72, 0x30, 0x0D, 0x06, 0x09, 0x2A,
        0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B, 0x05, 0x00,
        0x30, 0x81, 0x9E, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55,
        0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E,
        0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E,
        0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03,
        0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D,
        0x61, 0x6E, 0x31, 0x15, 0x30, 0x13, 0x06, 0x03, 0x55, 0x04,
        0x0A, 0x0C, 0x0C, 0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C,
        0x5F, 0x31, 0x30, 0x32, 0x34, 0x31, 0x19, 0x30, 0x17, 0x06,
        0x03, 0x55, 0x04, 0x0B, 0x0C, 0x10, 0x50, 0x72, 0x6F, 0x67,
        0x72, 0x61, 0x6D, 0x6D, 0x69, 0x6E, 0x67, 0x2D, 0x31, 0x30,
        0x32, 0x34, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04,
        0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F,
        0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D,
        0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x30, 0x1E, 0x17, 0x0D, 0x32, 0x34, 0x31, 0x32, 0x31,
        0x38, 0x32, 0x31, 0x32, 0x35, 0x32, 0x39, 0x5A, 0x17, 0x0D,
        0x32, 0x37, 0x30, 0x39, 0x31, 0x34, 0x32, 0x31, 0x32, 0x35,
        0x32, 0x39, 0x5A, 0x30, 0x81, 0x9E, 0x31, 0x0B, 0x30, 0x09,
        0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31,
        0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07,
        0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30,
        0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F,
        0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x15, 0x30, 0x13, 0x06,
        0x03, 0x55, 0x04, 0x0A, 0x0C, 0x0C, 0x77, 0x6F, 0x6C, 0x66,
        0x53, 0x53, 0x4C, 0x5F, 0x31, 0x30, 0x32, 0x34, 0x31, 0x19,
        0x30, 0x17, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x10, 0x50,
        0x72, 0x6F, 0x67, 0x72, 0x61, 0x6D, 0x6D, 0x69, 0x6E, 0x67,
        0x2D, 0x31, 0x30, 0x32, 0x34, 0x31, 0x18, 0x30, 0x16, 0x06,
        0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48,
        0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E,
        0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C,
        0x2E, 0x63, 0x6F, 0x6D, 0x30, 0x81, 0x9F, 0x30, 0x0D, 0x06,
        0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01,
        0x05, 0x00, 0x03, 0x81, 0x8D, 0x00, 0x30, 0x81, 0x89, 0x02,
        0x81, 0x81, 0x00, 0xBC, 0x73, 0x0E, 0xA8, 0x49, 0xF3, 0x74,
        0xA2, 0xA9, 0xEF, 0x18, 0xA5, 0xDA, 0x55, 0x99, 0x21, 0xF9,
        0xC8, 0xEC, 0xB3, 0x6D, 0x48, 0xE5, 0x35, 0x35, 0x75, 0x77,
        0x37, 0xEC, 0xD1, 0x61, 0x90, 0x5F, 0x3E, 0xD9, 0xE4, 0xD5,
        0xDF, 0x94, 0xCA, 0xC1, 0xA9, 0xD7, 0x19, 0xDA, 0x86, 0xC9,
        0xE8, 0x4D, 0xC4, 0x61, 0x36, 0x82, 0xFE, 0xAB, 0xAD, 0x7E,
        0x77, 0x25, 0xBB, 0x8D, 0x11, 0xA5, 0xBC, 0x62, 0x3A, 0xA8,
        0x38, 0xCC, 0x39, 0xA2, 0x04, 0x66, 0xB4, 0xF7, 0xF7, 0xF3,
        0xAA, 0xDA, 0x4D, 0x02, 0x0E, 0xBB, 0x5E, 0x8D, 0x69, 0x48,
        0xDC, 0x77, 0xC9, 0x28, 0x0E, 0x22, 0xE9, 0x6B, 0xA4, 0x26,
        0xBA, 0x4C, 0xE8, 0xC1, 0xFD, 0x4A, 0x6F, 0x2B, 0x1F, 0xEF,
        0x8A, 0xAE, 0xF6, 0x90, 0x62, 0xE5, 0x64, 0x1E, 0xEB, 0x2B,
        0x3C, 0x67, 0xC8, 0xDC, 0x27, 0x00, 0xF6, 0x91, 0x68, 0x65,
        0xA9, 0x02, 0x03, 0x01, 0x00, 0x01, 0xA3, 0x82, 0x01, 0x4F,
        0x30, 0x82, 0x01, 0x4B, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D,
        0x0E, 0x04, 0x16, 0x04, 0x14, 0x81, 0x69, 0x0F, 0xF8, 0xDF,
        0xDD, 0xCF, 0x34, 0x29, 0xD5, 0x67, 0x75, 0x71, 0x85, 0xC7,
        0x75, 0x10, 0x69, 0x59, 0xEC, 0x30, 0x81, 0xDE, 0x06, 0x03,
        0x55, 0x1D, 0x23, 0x04, 0x81, 0xD6, 0x30, 0x81, 0xD3, 0x80,
        0x14, 0x81, 0x69, 0x0F, 0xF8, 0xDF, 0xDD, 0xCF, 0x34, 0x29,
        0xD5, 0x67, 0x75, 0x71, 0x85, 0xC7, 0x75, 0x10, 0x69, 0x59,
        0xEC, 0xA1, 0x81, 0xA4, 0xA4, 0x81, 0xA1, 0x30, 0x81, 0x9E,
        0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13,
        0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55,
        0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E,
        0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x07,
        0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31,
        0x15, 0x30, 0x13, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x0C,
        0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C, 0x5F, 0x31, 0x30,
        0x32, 0x34, 0x31, 0x19, 0x30, 0x17, 0x06, 0x03, 0x55, 0x04,
        0x0B, 0x0C, 0x10, 0x50, 0x72, 0x6F, 0x67, 0x72, 0x61, 0x6D,
        0x6D, 0x69, 0x6E, 0x67, 0x2D, 0x31, 0x30, 0x32, 0x34, 0x31,
        0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F,
        0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73,
        0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06,
        0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01,
        0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x82, 0x14,
        0x09, 0x1D, 0x03, 0x41, 0x8B, 0x92, 0xBD, 0x2A, 0x2A, 0x1C,
        0x77, 0xE0, 0x13, 0xA8, 0x3D, 0xF0, 0x33, 0xDA, 0x7F, 0x72,
        0x30, 0x0C, 0x06, 0x03, 0x55, 0x1D, 0x13, 0x04, 0x05, 0x30,
        0x03, 0x01, 0x01, 0xFF, 0x30, 0x1C, 0x06, 0x03, 0x55, 0x1D,
        0x11, 0x04, 0x15, 0x30, 0x13, 0x82, 0x0B, 0x65, 0x78, 0x61,
        0x6D, 0x70, 0x6C, 0x65, 0x2E, 0x63, 0x6F, 0x6D, 0x87, 0x04,
        0x7F, 0x00, 0x00, 0x01, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D,
        0x25, 0x04, 0x16, 0x30, 0x14, 0x06, 0x08, 0x2B, 0x06, 0x01,
        0x05, 0x05, 0x07, 0x03, 0x01, 0x06, 0x08, 0x2B, 0x06, 0x01,
        0x05, 0x05, 0x07, 0x03, 0x02, 0x30, 0x0D, 0x06, 0x09, 0x2A,
        0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B, 0x05, 0x00,
        0x03, 0x81, 0x81, 0x00, 0x9A, 0x1C, 0x8F, 0xC4, 0xBD, 0x54,
        0xDA, 0x63, 0xA7, 0xF8, 0xBA, 0x39, 0xB6, 0x64, 0x60, 0x9D,
        0xBA, 0xA5, 0xFC, 0x43, 0xF5, 0x57, 0x28, 0x31, 0x43, 0x09,
        0x4C, 0x03, 0x4C, 0xB8, 0xC3, 0x49, 0x2B, 0x4E, 0xBF, 0xF2,
        0x9B, 0x13, 0x4E, 0x37, 0x1E, 0xA1, 0x57, 0xC6, 0x0C, 0x7B,
        0x2C, 0x25, 0x19, 0x37, 0x9F, 0x06, 0x53, 0xEF, 0x8D, 0xD1,
        0xBA, 0xC0, 0x73, 0x6E, 0x7F, 0xC2, 0x0B, 0x46, 0x5F, 0x9B,
        0x56, 0xBB, 0x59, 0x19, 0x5C, 0xC9, 0xEE, 0xEA, 0x02, 0xDA,
        0x03, 0x2C, 0xFB, 0x29, 0xB6, 0x07, 0xDD, 0x55, 0xB7, 0xE9,
        0xCE, 0x60, 0x47, 0xE0, 0x6B, 0x44, 0x5A, 0x61, 0x74, 0x5C,
        0x96, 0xF6, 0x30, 0xD8, 0x1B, 0xA4, 0x15, 0x5E, 0x06, 0xC5,
        0x73, 0x4B, 0x8A, 0x4D, 0x94, 0x23, 0x13, 0x1B, 0x3F, 0xDB,
        0x67, 0xCA, 0xA7, 0xA6, 0x41, 0xC5, 0x28, 0x0F, 0xFD, 0x2E,
        0x0E, 0xF0
};
static const int sizeof_client_cert_der_1024 = sizeof(client_cert_der_1024);

/* ./certs/1024/dh1024.der, 1024-bit */
static const unsigned char dh_key_der_1024[] =
{
        0x30, 0x81, 0x87, 0x02, 0x81, 0x81, 0x00, 0xA4, 0xD2, 0xB8,
        0x6E, 0x78, 0xF5, 0xD9, 0xED, 0x2D, 0x7C, 0xDD, 0xB6, 0x16,
        0x86, 0x5A, 0x4B, 0x05, 0x76, 0x90, 0xDD, 0x66, 0x61, 0xB9,
        0x6D, 0x52, 0xA7, 0x1C, 0xAF, 0x62, 0xC6, 0x69, 0x47, 0x7B,
        0x39, 0xF2, 0xFB, 0x94, 0xEC, 0xBC, 0x79, 0xFF, 0x24, 0x5E,
        0xEF, 0x79, 0xBB, 0x59, 0xB2, 0xFC, 0xCA, 0x07, 0xD6, 0xF4,
        0xE9, 0x34, 0xF7, 0xE8, 0x38, 0xE7, 0xD7, 0x33, 0x44, 0x1D,
        0xA3, 0x64, 0x76, 0x1A, 0x84, 0x97, 0x54, 0x74, 0x40, 0x84,
        0x1F, 0x15, 0xFE, 0x7C, 0x25, 0x2A, 0x2B, 0x25, 0xFD, 0x9E,
        0xC1, 0x89, 0x33, 0x8C, 0x39, 0x25, 0x2B, 0x40, 0xE6, 0xCD,
        0xF8, 0xA8, 0xA1, 0x8A, 0x53, 0xC6, 0x47, 0xB2, 0xA0, 0xD7,
        0x8F, 0xEB, 0x2E, 0x60, 0x0A, 0x0D, 0x4B, 0xF8, 0xB4, 0x94,
        0x8C, 0x63, 0x0A, 0xAD, 0xC7, 0x10, 0xEA, 0xC7, 0xA1, 0xB9,
        0x9D, 0xF2, 0xA8, 0x37, 0x73, 0x02, 0x01, 0x02
};
static const int sizeof_dh_key_der_1024 = sizeof(dh_key_der_1024);

/* ./certs/1024/dsa1024.der, 1024-bit */
static const unsigned char dsa_key_der_1024[] =
{
        0x30, 0x82, 0x01, 0xBC, 0x02, 0x01, 0x00, 0x02, 0x81, 0x81,
        0x00, 0xF7, 0x4B, 0xF9, 0xBB, 0x15, 0x98, 0xEB, 0xDD, 0xDE,
        0x1E, 0x4E, 0x71, 0x88, 0x85, 0xF2, 0xB7, 0xBA, 0xE2, 0x4A,
        0xDA, 0x76, 0x40, 0xCD, 0x69, 0x48, 0x9E, 0x83, 0x7C, 0x11,
        0xF7, 0x65, 0x31, 0x78, 0xF5, 0x25, 0x2D, 0xF7, 0xB7, 0xF8,
        0x52, 0x3F, 0xBE, 0xD8, 0xB6, 0xC5, 0xFE, 0x18, 0x15, 0x5B,
        0xB9, 0xD5, 0x92, 0x86, 0xBC, 0xB2, 0x17, 0x7C, 0xD8, 0xB0,
        0xBE, 0xA0, 0x7C, 0xF2, 0xD5, 0x73, 0x7A, 0x58, 0x8F, 0x8D,
        0xE5, 0x4A, 0x00, 0x99, 0x83, 0x4A, 0xC0, 0x9E, 0x16, 0x09,
        0xA1, 0x10, 0x34, 0xD5, 0x19, 0xBB, 0x63, 0xE3, 0xDD, 0x83,
        0x74, 0x7F, 0x10, 0xCA, 0x73, 0x75, 0xEE, 0x31, 0x4A, 0xDD,
        0x9F, 0xE0, 0x02, 0x6A, 0x9D, 0xEE, 0xB2, 0x4B, 0xA7, 0x6B,
        0x2A, 0x6C, 0xC7, 0x86, 0x77, 0xE8, 0x04, 0x15, 0xDC, 0x92,
        0xB4, 0x7A, 0x29, 0x1F, 0x4E, 0x83, 0x63, 0x85, 0x55, 0x02,
        0x15, 0x00, 0xD2, 0x05, 0xE4, 0x73, 0xFB, 0xC1, 0x99, 0xC5,
        0xDC, 0x68, 0xA4, 0x8D, 0x92, 0x27, 0x3D, 0xE2, 0x52, 0x5F,
        0x89, 0x8B, 0x02, 0x81, 0x81, 0x00, 0xAA, 0x21, 0x02, 0x09,
        0x43, 0x6E, 0xFB, 0xA2, 0x54, 0x14, 0x85, 0x0A, 0xF4, 0x28,
        0x7C, 0xCB, 0xCC, 0xDB, 0xF5, 0x1E, 0xA2, 0x18, 0xA9, 0x21,
        0xDE, 0x88, 0x88, 0x33, 0x8C, 0x2E, 0xEB, 0x8D, 0xA3, 0xF0,
        0x1D, 0xC8, 0x8F, 0xF6, 0x7E, 0xF8, 0xCF, 0x12, 0xF5, 0xB4,
        0xA1, 0x11, 0x6F, 0x0C, 0xD4, 0xF0, 0x06, 0xAD, 0xC4, 0xFC,
        0x14, 0x45, 0xC7, 0x94, 0x15, 0xBC, 0x19, 0x4B, 0xAE, 0xEF,
        0x93, 0x6A, 0x4F, 0xCC, 0x14, 0xD8, 0x47, 0x8B, 0x39, 0x66,
        0x87, 0x02, 0xD4, 0x28, 0x0A, 0xB8, 0xEE, 0x09, 0x37, 0xF4,
        0x00, 0xA0, 0x04, 0xA7, 0x79, 0xA7, 0xD2, 0x3C, 0xF7, 0x34,
        0x43, 0x56, 0x8E, 0xD0, 0x7C, 0xC2, 0xD8, 0x4D, 0x0F, 0x89,
        0xED, 0x14, 0xC1, 0x2C, 0x9C, 0x4C, 0x19, 0x9B, 0x9E, 0xDC,
        0x53, 0x09, 0x9F, 0xDF, 0x2D, 0xF0, 0x0C, 0x27, 0x54, 0x3A,
        0x77, 0x14, 0x2D, 0xDE, 0x02, 0x81, 0x81, 0x00, 0xE8, 0x1F,
        0x7C, 0xB7, 0xC0, 0x54, 0x51, 0xA7, 0x28, 0x2D, 0x58, 0x7C,
        0xDE, 0xD4, 0x5C, 0xDD, 0xD5, 0x76, 0x84, 0x3C, 0x36, 0x20,
        0xC0, 0xC3, 0x25, 0xD7, 0x3A, 0x38, 0xE1, 0x54, 0xC8, 0xFD,
        0x40, 0x68, 0x1A, 0x21, 0x54, 0x26, 0x39, 0x14, 0xBF, 0xF6,
        0xA3, 0x9C, 0x5E, 0xD9, 0x2B, 0xF7, 0xC9, 0x25, 0xBA, 0x00,
        0x09, 0xCB, 0x7F, 0x0C, 0x4A, 0x24, 0xFD, 0x15, 0x16, 0x15,
        0x48, 0xCD, 0x0B, 0x52, 0x44, 0x40, 0x7B, 0x90, 0x63, 0x2B,
        0x90, 0x22, 0xC5, 0x18, 0x05, 0x80, 0x53, 0xAF, 0x83, 0x1F,
        0x54, 0xE2, 0xB0, 0xA2, 0x0B, 0x5A, 0x92, 0x24, 0xE1, 0x62,
        0x28, 0x3F, 0xB7, 0xCA, 0xB9, 0x89, 0xD6, 0xA0, 0xB7, 0xAD,
        0xAE, 0x05, 0xE1, 0xC1, 0x59, 0x40, 0xED, 0x4A, 0x1B, 0x68,
        0xA7, 0x7B, 0xFB, 0xC3, 0x20, 0x81, 0xEF, 0x4B, 0xF3, 0x69,
        0x91, 0xB0, 0xCE, 0x3A, 0xB0, 0x38, 0x02, 0x14, 0x25, 0x38,
        0x3B, 0xA1, 0x19, 0x75, 0xDF, 0x9B, 0xF5, 0x72, 0x53, 0x4F,
        0x39, 0xE1, 0x1C, 0xEC, 0x13, 0x84, 0x82, 0x18
};
static const int sizeof_dsa_key_der_1024 = sizeof(dsa_key_der_1024);

/* ./certs/1024/rsa1024.der, 1024-bit */
static const unsigned char rsa_key_der_1024[] =
{
        0x30, 0x82, 0x02, 0x5D, 0x02, 0x01, 0x00, 0x02, 0x81, 0x81,
        0x00, 0xBE, 0x70, 0x70, 0xB8, 0x04, 0x18, 0xE5, 0x28, 0xFE,
        0x66, 0xD8, 0x90, 0x88, 0xE0, 0xF1, 0xB7, 0xC3, 0xD0, 0xD2,
        0x3E, 0xE6, 0x4B, 0x94, 0x74, 0xB0, 0xFF, 0xB0, 0xF7, 0x63,
        0xA5, 0xAB, 0x7E, 0xAF, 0xB6, 0x2B, 0xB7, 0x38, 0x16, 0x1A,
        0x50, 0xBF, 0xF1, 0xCA, 0x87, 0x3A, 0xD5, 0xB0, 0xDA, 0xF8,
        0x43, 0x7A, 0x15, 0xB9, 0x7E, 0xEA, 0x2A, 0x80, 0xD2, 0x51,
        0xB0, 0x35, 0xAF, 0x07, 0xF3, 0xF2, 0x5D, 0x24, 0x3A, 0x4B,
        0x87, 0x56, 0x48, 0x1B, 0x3C, 0x24, 0x9A, 0xDA, 0x70, 0x80,
        0xBD, 0x3C, 0x8B, 0x03, 0x4A, 0x0C, 0x83, 0x71, 0xDE, 0xE3,
        0x03, 0x70, 0xA2, 0xB7, 0x60, 0x09, 0x1B, 0x5E, 0xC7, 0x3D,
        0xA0, 0x64, 0x60, 0xE3, 0xA9, 0x06, 0x8D, 0xD3, 0xFF, 0x42,
        0xBB, 0x0A, 0x94, 0x27, 0x2D, 0x57, 0x42, 0x0D, 0xB0, 0x2D,
        0xE0, 0xBA, 0x18, 0x25, 0x60, 0x92, 0x11, 0x92, 0xF3, 0x02,
        0x03, 0x01, 0x00, 0x01, 0x02, 0x81, 0x80, 0x0E, 0xEE, 0x1D,
        0xC8, 0x2F, 0x7A, 0x0C, 0x2D, 0x44, 0x94, 0xA7, 0x91, 0xDD,
        0x49, 0x55, 0x6A, 0x04, 0xCE, 0x10, 0x4D, 0xA2, 0x1C, 0x76,
        0xCD, 0x17, 0x3B, 0x54, 0x92, 0x70, 0x9B, 0x82, 0x70, 0x72,
        0x32, 0x24, 0x07, 0x3F, 0x3C, 0x6C, 0x5F, 0xBC, 0x4C, 0xA6,
        0x86, 0x27, 0x94, 0xAD, 0x42, 0xDD, 0x87, 0xDC, 0xC0, 0x6B,
        0x44, 0x89, 0xF3, 0x3F, 0x1A, 0x3E, 0x11, 0x44, 0x84, 0x2E,
        0x69, 0x4C, 0xBB, 0x4A, 0x71, 0x1A, 0xBB, 0x9A, 0x52, 0x3C,
        0x6B, 0xDE, 0xBC, 0xB2, 0x7C, 0x51, 0xEF, 0x4F, 0x8F, 0x3A,
        0xDC, 0x50, 0x04, 0x4E, 0xB6, 0x31, 0x66, 0xA8, 0x8E, 0x06,
        0x3B, 0x51, 0xA9, 0xC1, 0x8A, 0xCB, 0xC4, 0x81, 0xCA, 0x2D,
        0x69, 0xEC, 0x88, 0xFC, 0x33, 0x88, 0xD1, 0xD4, 0x29, 0x47,
        0x87, 0x37, 0xF9, 0x6A, 0x22, 0x69, 0xB9, 0xC9, 0xFE, 0xEB,
        0x8C, 0xC5, 0x21, 0x41, 0x71, 0x02, 0x41, 0x00, 0xFD, 0x17,
        0x98, 0x42, 0x54, 0x1C, 0x23, 0xF8, 0xD7, 0x5D, 0xEF, 0x49,
        0x4F, 0xAF, 0xD9, 0x35, 0x6F, 0x08, 0xC6, 0xC7, 0x40, 0x5C,
        0x7E, 0x58, 0x86, 0xC2, 0xB2, 0x16, 0x39, 0x24, 0xC5, 0x06,
        0xB0, 0x3D, 0xAF, 0x02, 0xD2, 0x87, 0x77, 0xD2, 0x76, 0xBA,
        0xE3, 0x59, 0x60, 0x42, 0xF1, 0x16, 0xEF, 0x33, 0x0B, 0xF2,
        0x0B, 0xBA, 0x99, 0xCC, 0xB6, 0x4C, 0x46, 0x3F, 0x33, 0xE4,
        0xD4, 0x67, 0x02, 0x41, 0x00, 0xC0, 0xA0, 0x91, 0x6D, 0xFE,
        0x28, 0xE0, 0x81, 0x5A, 0x15, 0xA7, 0xC9, 0xA8, 0x98, 0xC6,
        0x0A, 0xAB, 0x00, 0xC5, 0x40, 0xC9, 0x21, 0xBB, 0xB2, 0x33,
        0x5A, 0xA7, 0xCB, 0x6E, 0xB8, 0x08, 0x56, 0x4A, 0x76, 0x28,
        0xE8, 0x6D, 0xBD, 0xF5, 0x26, 0x7B, 0xBF, 0xC5, 0x46, 0x45,
        0x0D, 0xEC, 0x7D, 0xEE, 0x82, 0xD6, 0xCA, 0x5F, 0x3D, 0x6E,
        0xCC, 0x94, 0x73, 0xCD, 0xCE, 0x86, 0x6E, 0x95, 0x95, 0x02,
        0x40, 0x38, 0xFD, 0x28, 0x1E, 0xBF, 0x5B, 0xBA, 0xC9, 0xDC,
        0x8C, 0xDD, 0x45, 0xAF, 0xB8, 0xD3, 0xFB, 0x11, 0x2E, 0x73,
        0xBC, 0x08, 0x05, 0x0B, 0xBA, 0x19, 0x56, 0x1B, 0xCD, 0x9F,
        0x3E, 0x65, 0x53, 0x15, 0x3A, 0x3E, 0x7F, 0x2F, 0x32, 0xAB,
        0xCB, 0x6B, 0x4A, 0xB7, 0xC8, 0xB7, 0x41, 0x3B, 0x92, 0x43,
        0x78, 0x46, 0x17, 0x51, 0x86, 0xC9, 0xFC, 0xEB, 0x8B, 0x8F,
        0x41, 0xCA, 0x08, 0x9B, 0xBF, 0x02, 0x41, 0x00, 0xAD, 0x9B,
        0x89, 0xB6, 0xF2, 0x8C, 0x70, 0xDA, 0xE4, 0x10, 0x04, 0x6B,
        0x11, 0x92, 0xAF, 0x5A, 0xCA, 0x08, 0x25, 0xBF, 0x60, 0x07,
        0x11, 0x1D, 0x68, 0x7F, 0x5A, 0x1F, 0x55, 0x28, 0x74, 0x0B,
        0x21, 0x8D, 0x21, 0x0D, 0x6A, 0x6A, 0xFB, 0xD9, 0xB5, 0x4A,
        0x7F, 0x47, 0xF7, 0xD0, 0xB6, 0xC6, 0x41, 0x02, 0x97, 0x07,
        0x49, 0x93, 0x1A, 0x9B, 0x33, 0x68, 0xB3, 0xA2, 0x61, 0x32,
        0xA5, 0x89, 0x02, 0x41, 0x00, 0x8F, 0xEF, 0xAD, 0xB5, 0xB0,
        0xB0, 0x7E, 0x86, 0x03, 0x43, 0x93, 0x6E, 0xDD, 0x3C, 0x2D,
        0x9B, 0x6A, 0x55, 0xFF, 0x6F, 0x3E, 0x70, 0x2A, 0xD4, 0xBF,
        0x1F, 0x8C, 0x93, 0x60, 0x9E, 0x6D, 0x2F, 0x18, 0x6C, 0x11,
        0x36, 0x98, 0x3F, 0x10, 0x78, 0xE8, 0x3E, 0x8F, 0xFE, 0x55,
        0xB9, 0x9E, 0xD5, 0x5B, 0x2E, 0x87, 0x1C, 0x58, 0xD0, 0x37,
        0x89, 0x96, 0xEC, 0x48, 0x54, 0xF5, 0x9F, 0x0F, 0xB3
};
static const int sizeof_rsa_key_der_1024 = sizeof(rsa_key_der_1024);

/* ./certs/1024/ca-key.der, 1024-bit */
static const unsigned char ca_key_der_1024[] =
{
        0x30, 0x82, 0x02, 0x5E, 0x02, 0x01, 0x00, 0x02, 0x81, 0x81,
        0x00, 0xCD, 0xAC, 0xDD, 0x47, 0xEC, 0xBE, 0xB7, 0x24, 0xC3,
        0x63, 0x1B, 0x54, 0x98, 0x79, 0xE1, 0xC7, 0x31, 0x16, 0x59,
        0xD6, 0x9D, 0x77, 0x9D, 0x8D, 0xE2, 0x8B, 0xED, 0x04, 0x17,
        0xB2, 0xC6, 0xEB, 0xE4, 0x9B, 0x91, 0xBE, 0x31, 0x50, 0x62,
        0x97, 0x58, 0xB5, 0x7F, 0x29, 0xDE, 0xB3, 0x71, 0x24, 0x0B,
        0xBF, 0x97, 0x09, 0x7F, 0x26, 0xDC, 0x2D, 0xEC, 0xA8, 0x2E,
        0xB2, 0x64, 0x2B, 0x7A, 0x2B, 0x35, 0x19, 0x2D, 0xA2, 0x80,
        0xCB, 0x99, 0xFD, 0x94, 0x71, 0x1B, 0x23, 0x8D, 0x54, 0xDB,
        0x2E, 0x62, 0x8D, 0x81, 0x08, 0x2D, 0xF4, 0x24, 0x72, 0x27,
        0x6C, 0xF9, 0xC9, 0x8E, 0xDB, 0x4C, 0x75, 0xBA, 0x9B, 0x01,
        0xF8, 0x3F, 0x18, 0xF4, 0xE6, 0x7F, 0xFB, 0x57, 0x94, 0x92,
        0xCC, 0x88, 0xC4, 0xB4, 0x00, 0xC2, 0xAA, 0xD4, 0xE5, 0x88,
        0x18, 0xB3, 0x11, 0x2F, 0x73, 0xC0, 0xD6, 0x29, 0x09, 0x02,
        0x03, 0x01, 0x00, 0x01, 0x02, 0x81, 0x80, 0x52, 0x35, 0x3D,
        0x01, 0x29, 0xA4, 0x95, 0x29, 0x71, 0x9B, 0x64, 0x6A, 0x2C,
        0xC3, 0xD2, 0xB5, 0xBE, 0x6E, 0x13, 0x9C, 0x8F, 0xB6, 0x26,
        0xD8, 0x76, 0x6B, 0xBD, 0x61, 0xBC, 0x63, 0x2D, 0xD5, 0x4D,
        0xBB, 0xCC, 0xC6, 0x3B, 0x89, 0xC8, 0xCE, 0x7B, 0x9B, 0x97,
        0xE7, 0x51, 0x67, 0x61, 0xDA, 0xA9, 0x83, 0x7B, 0xC8, 0x44,
        0xF5, 0x70, 0x5E, 0x3E, 0xD0, 0x7E, 0x51, 0xB9, 0x6E, 0x13,
        0x57, 0x08, 0x5C, 0xE1, 0x67, 0x4F, 0x61, 0x5E, 0xA5, 0x09,
        0xEC, 0x11, 0xDD, 0xE4, 0xB8, 0xB4, 0xF4, 0xE0, 0x63, 0x34,
        0x4C, 0xDA, 0x32, 0x20, 0x1F, 0x85, 0x41, 0x5D, 0xBC, 0xDB,
        0x24, 0xC5, 0xAF, 0xBE, 0x02, 0x5F, 0x22, 0xF1, 0x7C, 0xCC,
        0x05, 0x56, 0xA6, 0xA6, 0x37, 0x9A, 0xEB, 0xFF, 0x52, 0x2D,
        0xBF, 0x30, 0x4B, 0x9A, 0x1D, 0xEE, 0xAB, 0x9C, 0x2C, 0xE2,
        0xC1, 0xB8, 0x9D, 0xC9, 0x31, 0x02, 0x41, 0x00, 0xE9, 0x89,
        0x16, 0xCD, 0xAC, 0x2E, 0xF2, 0x4D, 0x66, 0x17, 0xBD, 0x78,
        0x12, 0x12, 0x8D, 0x8E, 0x84, 0x24, 0xDE, 0x2D, 0x50, 0x41,
        0x85, 0x8C, 0x34, 0x09, 0xFA, 0xFB, 0x6D, 0x87, 0x51, 0x4C,
        0x13, 0x28, 0xF0, 0x60, 0x11, 0x86, 0x3D, 0xC2, 0xA4, 0xCF,
        0x5E, 0xC5, 0x6F, 0x5B, 0x11, 0x32, 0x0A, 0xB5, 0x28, 0xD0,
        0x82, 0x47, 0x44, 0x26, 0x92, 0xE2, 0x78, 0x59, 0xB4, 0x08,
        0xB3, 0xFD, 0x02, 0x41, 0x00, 0xE1, 0x75, 0xB4, 0x6A, 0xB5,
        0x8C, 0x11, 0xFB, 0xCC, 0x42, 0x02, 0xC5, 0xDA, 0x48, 0xCE,
        0x29, 0x43, 0x14, 0x01, 0x9A, 0x2C, 0xB3, 0xA4, 0xCB, 0x73,
        0xEB, 0xA1, 0x35, 0x57, 0xAD, 0xB5, 0x16, 0x17, 0x80, 0x03,
        0x5F, 0x32, 0x37, 0xBE, 0xA2, 0x6F, 0xF9, 0x31, 0x84, 0xBF,
        0x00, 0x6E, 0x8D, 0x03, 0x0E, 0x30, 0x1C, 0xD0, 0x2F, 0x37,
        0xF0, 0x7E, 0xC2, 0x64, 0xBF, 0xEE, 0x4B, 0xE8, 0xFD, 0x02,
        0x41, 0x00, 0xE1, 0x99, 0x8B, 0x2B, 0xD8, 0x9F, 0xE9, 0x76,
        0x97, 0x9F, 0x6B, 0x6B, 0x28, 0x9A, 0x3F, 0xA1, 0x63, 0x4A,
        0x72, 0x4E, 0xF7, 0xEE, 0xB3, 0xE2, 0x43, 0x0B, 0x39, 0x27,
        0xD6, 0x21, 0x18, 0x8A, 0x13, 0x20, 0x43, 0x45, 0xAA, 0xE8,
        0x31, 0x95, 0x6C, 0xBC, 0xDE, 0xE2, 0x7F, 0xB6, 0x4B, 0xA0,
        0x39, 0xF3, 0xD3, 0x9F, 0xC9, 0x9A, 0xAA, 0xDD, 0x50, 0x9B,
        0xF2, 0x83, 0x45, 0x85, 0xFA, 0xC9, 0x02, 0x41, 0x00, 0xAF,
        0xB0, 0xC7, 0x7C, 0xF8, 0x28, 0x44, 0xC3, 0x50, 0xF2, 0x87,
        0xB2, 0xA2, 0x5D, 0x65, 0xBA, 0x25, 0xB9, 0x6B, 0x5E, 0x37,
        0x43, 0x6E, 0x41, 0xD4, 0xFD, 0x63, 0x4C, 0x6C, 0x1C, 0xC3,
        0x26, 0x89, 0xFD, 0x89, 0xA3, 0x1F, 0x40, 0xED, 0x5F, 0x2B,
        0x9E, 0xA6, 0x85, 0xE9, 0x49, 0x6E, 0xDC, 0x97, 0xEA, 0xF0,
        0x77, 0x23, 0x8C, 0x08, 0x2D, 0x72, 0xBA, 0x0D, 0x44, 0xBB,
        0x6F, 0x90, 0x09, 0x02, 0x41, 0x00, 0x91, 0xE4, 0x2E, 0xCA,
        0x8C, 0x0A, 0x69, 0x2F, 0x62, 0xE2, 0x62, 0x3B, 0xA5, 0x8D,
        0x5A, 0x2C, 0x56, 0x3E, 0x7F, 0x67, 0x42, 0x92, 0x12, 0x92,
        0x5F, 0xF3, 0x97, 0xDD, 0xE1, 0xA9, 0x7F, 0xAD, 0x2E, 0x2D,
        0xF4, 0x4A, 0x57, 0xB3, 0x7A, 0x10, 0xBD, 0xD7, 0xE4, 0xEC,
        0x6A, 0x08, 0x21, 0xE9, 0xF2, 0x46, 0x49, 0xD2, 0x69, 0x47,
        0x8A, 0x20, 0x4B, 0xF2, 0xB1, 0x52, 0x83, 0xAB, 0x6F, 0x10

};
static const int sizeof_ca_key_der_1024 = sizeof(ca_key_der_1024);

/* ./certs/1024/ca-cert.der, 1024-bit */
static const unsigned char ca_cert_der_1024[] =
{
        0x30, 0x82, 0x04, 0x09, 0x30, 0x82, 0x03, 0x72, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x14, 0x59, 0x52, 0x6B, 0x92, 0x1A,
        0x25, 0x8F, 0x1B, 0xEE, 0x4C, 0x51, 0x9C, 0x47, 0x2F, 0xFF,
        0xFF, 0x9D, 0x43, 0x29, 0x47, 0x30, 0x0D, 0x06, 0x09, 0x2A,
        0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B, 0x05, 0x00,
        0x30, 0x81, 0x99, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55,
        0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E,
        0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E,
        0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03,
        0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D,
        0x61, 0x6E, 0x31, 0x11, 0x30, 0x0F, 0x06, 0x03, 0x55, 0x04,
        0x0A, 0x0C, 0x08, 0x53, 0x61, 0x77, 0x74, 0x6F, 0x6F, 0x74,
        0x68, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x0B,
        0x0C, 0x0F, 0x43, 0x6F, 0x6E, 0x73, 0x75, 0x6C, 0x74, 0x69,
        0x6E, 0x67, 0x5F, 0x31, 0x30, 0x32, 0x34, 0x31, 0x18, 0x30,
        0x16, 0x06, 0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77,
        0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E,
        0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A,
        0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10,
        0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73,
        0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x30, 0x1E, 0x17, 0x0D,
        0x32, 0x34, 0x31, 0x32, 0x31, 0x38, 0x32, 0x31, 0x32, 0x35,
        0x32, 0x39, 0x5A, 0x17, 0x0D, 0x32, 0x37, 0x30, 0x39, 0x31,
        0x34, 0x32, 0x31, 0x32, 0x35, 0x32, 0x39, 0x5A, 0x30, 0x81,
        0x99, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06,
        0x13, 0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03,
        0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E, 0x74, 0x61,
        0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04,
        0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D, 0x61, 0x6E,
        0x31, 0x11, 0x30, 0x0F, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C,
        0x08, 0x53, 0x61, 0x77, 0x74, 0x6F, 0x6F, 0x74, 0x68, 0x31,
        0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x0F,
        0x43, 0x6F, 0x6E, 0x73, 0x75, 0x6C, 0x74, 0x69, 0x6E, 0x67,
        0x5F, 0x31, 0x30, 0x32, 0x34, 0x31, 0x18, 0x30, 0x16, 0x06,
        0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48,
        0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E,
        0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C,
        0x2E, 0x63, 0x6F, 0x6D, 0x30, 0x81, 0x9F, 0x30, 0x0D, 0x06,
        0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01,
        0x05, 0x00, 0x03, 0x81, 0x8D, 0x00, 0x30, 0x81, 0x89, 0x02,
        0x81, 0x81, 0x00, 0xCD, 0xAC, 0xDD, 0x47, 0xEC, 0xBE, 0xB7,
        0x24, 0xC3, 0x63, 0x1B, 0x54, 0x98, 0x79, 0xE1, 0xC7, 0x31,
        0x16, 0x59, 0xD6, 0x9D, 0x77, 0x9D, 0x8D, 0xE2, 0x8B, 0xED,
        0x04, 0x17, 0xB2, 0xC6, 0xEB, 0xE4, 0x9B, 0x91, 0xBE, 0x31,
        0x50, 0x62, 0x97, 0x58, 0xB5, 0x7F, 0x29, 0xDE, 0xB3, 0x71,
        0x24, 0x0B, 0xBF, 0x97, 0x09, 0x7F, 0x26, 0xDC, 0x2D, 0xEC,
        0xA8, 0x2E, 0xB2, 0x64, 0x2B, 0x7A, 0x2B, 0x35, 0x19, 0x2D,
        0xA2, 0x80, 0xCB, 0x99, 0xFD, 0x94, 0x71, 0x1B, 0x23, 0x8D,
        0x54, 0xDB, 0x2E, 0x62, 0x8D, 0x81, 0x08, 0x2D, 0xF4, 0x24,
        0x72, 0x27, 0x6C, 0xF9, 0xC9, 0x8E, 0xDB, 0x4C, 0x75, 0xBA,
        0x9B, 0x01, 0xF8, 0x3F, 0x18, 0xF4, 0xE6, 0x7F, 0xFB, 0x57,
        0x94, 0x92, 0xCC, 0x88, 0xC4, 0xB4, 0x00, 0xC2, 0xAA, 0xD4,
        0xE5, 0x88, 0x18, 0xB3, 0x11, 0x2F, 0x73, 0xC0, 0xD6, 0x29,
        0x09, 0x02, 0x03, 0x01, 0x00, 0x01, 0xA3, 0x82, 0x01, 0x4A,
        0x30, 0x82, 0x01, 0x46, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D,
        0x0E, 0x04, 0x16, 0x04, 0x14, 0xD3, 0x22, 0x8F, 0x28, 0x2C,
        0xE0, 0x05, 0xEE, 0xD3, 0xED, 0xC3, 0x71, 0x3D, 0xC9, 0xB2,
        0x36, 0x3A, 0x1D, 0xBF, 0xA8, 0x30, 0x81, 0xD9, 0x06, 0x03,
        0x55, 0x1D, 0x23, 0x04, 0x81, 0xD1, 0x30, 0x81, 0xCE, 0x80,
        0x14, 0xD3, 0x22, 0x8F, 0x28, 0x2C, 0xE0, 0x05, 0xEE, 0xD3,
        0xED, 0xC3, 0x71, 0x3D, 0xC9, 0xB2, 0x36, 0x3A, 0x1D, 0xBF,
        0xA8, 0xA1, 0x81, 0x9F, 0xA4, 0x81, 0x9C, 0x30, 0x81, 0x99,
        0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13,
        0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55,
        0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E,
        0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x07,
        0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31,
        0x11, 0x30, 0x0F, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x08,
        0x53, 0x61, 0x77, 0x74, 0x6F, 0x6F, 0x74, 0x68, 0x31, 0x18,
        0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x0F, 0x43,
        0x6F, 0x6E, 0x73, 0x75, 0x6C, 0x74, 0x69, 0x6E, 0x67, 0x5F,
        0x31, 0x30, 0x32, 0x34, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03,
        0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77,
        0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D,
        0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86,
        0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66,
        0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E,
        0x63, 0x6F, 0x6D, 0x82, 0x14, 0x59, 0x52, 0x6B, 0x92, 0x1A,
        0x25, 0x8F, 0x1B, 0xEE, 0x4C, 0x51, 0x9C, 0x47, 0x2F, 0xFF,
        0xFF, 0x9D, 0x43, 0x29, 0x47, 0x30, 0x0C, 0x06, 0x03, 0x55,
        0x1D, 0x13, 0x04, 0x05, 0x30, 0x03, 0x01, 0x01, 0xFF, 0x30,
        0x1C, 0x06, 0x03, 0x55, 0x1D, 0x11, 0x04, 0x15, 0x30, 0x13,
        0x82, 0x0B, 0x65, 0x78, 0x61, 0x6D, 0x70, 0x6C, 0x65, 0x2E,
        0x63, 0x6F, 0x6D, 0x87, 0x04, 0x7F, 0x00, 0x00, 0x01, 0x30,
        0x1D, 0x06, 0x03, 0x55, 0x1D, 0x25, 0x04, 0x16, 0x30, 0x14,
        0x06, 0x08, 0x2B, 0x06, 0x01, 0x05, 0x05, 0x07, 0x03, 0x01,
        0x06, 0x08, 0x2B, 0x06, 0x01, 0x05, 0x05, 0x07, 0x03, 0x02,
        0x30, 0x0D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D,
        0x01, 0x01, 0x0B, 0x05, 0x00, 0x03, 0x81, 0x81, 0x00, 0x09,
        0xC6, 0xDA, 0xFE, 0x2A, 0x45, 0x83, 0x9E, 0x8B, 0x66, 0xCF,
        0x63, 0x1F, 0x11, 0xCB, 0xD9, 0xB4, 0xEB, 0xB0, 0x97, 0x3D,
        0x33, 0xD4, 0xB9, 0x27, 0x56, 0x46, 0x14, 0x3C, 0xFE, 0x2B,
        0xB2, 0x36, 0x6E, 0x38, 0x7F, 0x08, 0xF5, 0x37, 0x3C, 0xF2,
        0xA2, 0x6A, 0x8A, 0xC7, 0xA0, 0xBE, 0x0F, 0xAC, 0xDD, 0xF4,
        0xF0, 0x97, 0xB3, 0x03, 0xA6, 0x70, 0x48, 0x44, 0xFC, 0xEF,
        0xEF, 0x7A, 0xC6, 0x1A, 0x8D, 0x3F, 0x19, 0xF6, 0x71, 0x92,
        0x7E, 0x3A, 0x00, 0x95, 0xF2, 0xB6, 0x57, 0x40, 0x77, 0xC2,
        0x80, 0x4E, 0x61, 0xF2, 0x71, 0x56, 0x22, 0xA0, 0x1E, 0xA9,
        0xDD, 0x5C, 0x54, 0x80, 0xAD, 0xE4, 0x27, 0xF2, 0x17, 0x20,
        0x9B, 0x5B, 0x89, 0x30, 0x6E, 0x6A, 0x31, 0x2A, 0x4E, 0x43,
        0x52, 0xF8, 0x8A, 0x51, 0xB7, 0xED, 0x3A, 0xAA, 0x78, 0x41,
        0x90, 0x95, 0xE8, 0x40, 0x2E, 0x66, 0xFC
};
static const int sizeof_ca_cert_der_1024 = sizeof(ca_cert_der_1024);

/* ./certs/1024/server-key.der, 1024-bit */
static const unsigned char server_key_der_1024[] =
{
        0x30, 0x82, 0x02, 0x5D, 0x02, 0x01, 0x00, 0x02, 0x81, 0x81,
        0x00, 0xAA, 0x3E, 0xA5, 0x9C, 0xD3, 0x17, 0x49, 0x65, 0x43,
        0xDE, 0xD0, 0xF3, 0x4B, 0x1C, 0xDB, 0x49, 0x0C, 0xFC, 0x7A,
        0x65, 0x05, 0x6D, 0xDE, 0x6A, 0xC4, 0xE4, 0x73, 0x2C, 0x8A,
        0x96, 0x82, 0x8F, 0x23, 0xA5, 0x06, 0x71, 0x1C, 0x06, 0x3E,
        0x2F, 0x92, 0x8D, 0x0B, 0x29, 0x34, 0x45, 0x59, 0xE9, 0xA9,
        0xBC, 0x61, 0xD7, 0x24, 0x37, 0x5D, 0xB5, 0xC4, 0x37, 0x8D,
        0xBA, 0x67, 0xB2, 0xEF, 0x03, 0x27, 0xFA, 0xC1, 0xB4, 0xCD,
        0x6B, 0x00, 0x66, 0xB4, 0xD6, 0x73, 0x70, 0x1F, 0x08, 0x3A,
        0xCC, 0x77, 0xAD, 0xE9, 0xF9, 0x34, 0xD4, 0xF3, 0xA0, 0x2D,
        0xA9, 0xE7, 0x58, 0xA9, 0xC0, 0x61, 0x84, 0xB6, 0xEC, 0x3D,
        0x0A, 0xAD, 0xFD, 0x5C, 0x86, 0x73, 0xAA, 0x6B, 0x47, 0xD8,
        0x8B, 0x2E, 0x58, 0x4B, 0x69, 0x12, 0x82, 0x26, 0x55, 0xE6,
        0x14, 0xBF, 0x55, 0x70, 0x88, 0xFE, 0xF9, 0x75, 0xE1, 0x02,
        0x03, 0x01, 0x00, 0x01, 0x02, 0x81, 0x80, 0x0A, 0x4C, 0xC1,
        0xFE, 0x4B, 0xF3, 0x23, 0xB8, 0xA1, 0xB3, 0x90, 0x56, 0xB7,
        0xDB, 0xA6, 0x14, 0xB4, 0x59, 0x6E, 0x1A, 0x40, 0x8A, 0xD6,
        0x23, 0x05, 0x88, 0x80, 0xC3, 0x58, 0x1B, 0x25, 0x08, 0xFD,
        0xF2, 0x15, 0x02, 0xB0, 0xDC, 0x5B, 0xD4, 0xCA, 0xFC, 0x07,
        0x89, 0xD5, 0xA4, 0xC0, 0x7C, 0xD7, 0x8D, 0x13, 0x2A, 0x4E,
        0x01, 0x9F, 0x84, 0xC8, 0xBB, 0x47, 0xB2, 0xD8, 0x65, 0x45,
        0xFA, 0x84, 0x9F, 0x88, 0xD0, 0xF4, 0xF5, 0x22, 0x35, 0x77,
        0x11, 0x67, 0x1C, 0xDE, 0x5F, 0x85, 0x6D, 0x55, 0xD8, 0xA7,
        0x07, 0x15, 0x8C, 0xE1, 0xB0, 0xA7, 0x79, 0xB4, 0x47, 0x9D,
        0x70, 0xB3, 0xD2, 0xF1, 0x1F, 0x41, 0x4C, 0x65, 0x72, 0x26,
        0xEB, 0x66, 0xC8, 0x95, 0xF6, 0x6D, 0x87, 0x35, 0x53, 0xFE,
        0xB1, 0x52, 0x4D, 0x76, 0x5B, 0x61, 0x53, 0x89, 0xB1, 0x20,
        0x1A, 0x8B, 0xE4, 0x7D, 0xF1, 0x02, 0x41, 0x00, 0xD9, 0x6E,
        0xE1, 0xD9, 0x06, 0x56, 0xA1, 0xF6, 0xDF, 0x54, 0x45, 0xC5,
        0xEC, 0x6A, 0xC8, 0x2A, 0x38, 0x4E, 0x6B, 0xC6, 0xE8, 0xEA,
        0xFB, 0x6F, 0x65, 0x2D, 0xBA, 0xDE, 0x27, 0x63, 0x37, 0x21,
        0x2E, 0xA4, 0x55, 0xAB, 0xE7, 0xDB, 0xCE, 0x71, 0xE1, 0x08,
        0xFC, 0xF2, 0xCA, 0x52, 0x33, 0x55, 0xE8, 0x39, 0xB3, 0xDA,
        0xC5, 0xB0, 0x69, 0x84, 0x6E, 0xE3, 0xCF, 0x47, 0x80, 0xA6,
        0xB6, 0x85, 0x02, 0x41, 0x00, 0xC8, 0x71, 0x0D, 0x37, 0x47,
        0xE1, 0x7B, 0x21, 0x2D, 0x11, 0x2D, 0x95, 0x2E, 0xC7, 0xD0,
        0xB6, 0xD3, 0x7C, 0x5C, 0x93, 0x3C, 0x5B, 0x22, 0xE5, 0xE0,
        0x8B, 0x6D, 0x47, 0xF9, 0x14, 0x0F, 0x9E, 0x08, 0x1B, 0x53,
        0xAB, 0x0A, 0xA9, 0xE4, 0x7F, 0x40, 0xD3, 0xDF, 0x62, 0x74,
        0x10, 0xA2, 0xFE, 0x83, 0x1F, 0xCF, 0x55, 0x66, 0xEB, 0x5D,
        0xC5, 0x83, 0xBA, 0xEC, 0x9F, 0xD2, 0xB5, 0x06, 0xAD, 0x02,
        0x41, 0x00, 0xB7, 0x68, 0x19, 0xA7, 0xC7, 0xF9, 0xF1, 0x9A,
        0xDD, 0x5D, 0x27, 0x91, 0xC1, 0x4F, 0x7D, 0x52, 0x67, 0xB6,
        0x76, 0xA1, 0x0D, 0x3D, 0x91, 0x23, 0xB0, 0xB3, 0xF7, 0x49,
        0x86, 0xED, 0xE0, 0xC5, 0xE3, 0xA3, 0x09, 0x04, 0xFD, 0x89,
        0xE2, 0xC5, 0x1A, 0x6E, 0x4B, 0x77, 0xBD, 0x03, 0xC3, 0x7B,
        0xB6, 0x6C, 0x5D, 0xF2, 0xAF, 0x08, 0x94, 0xA8, 0xFA, 0x24,
        0xBD, 0x66, 0x71, 0xF5, 0xAE, 0x45, 0x02, 0x40, 0x15, 0x52,
        0xD1, 0x91, 0x1B, 0xF8, 0x84, 0xDC, 0xD6, 0xAA, 0x89, 0x2A,
        0xE1, 0xBB, 0x28, 0x1D, 0x0B, 0x0A, 0xA3, 0xDE, 0x96, 0x01,
        0x2C, 0x09, 0x40, 0x86, 0x14, 0xAE, 0x1F, 0x75, 0x5E, 0xE3,
        0xF5, 0x00, 0xD3, 0x39, 0xD2, 0xFC, 0x97, 0xEE, 0x61, 0xBB,
        0x28, 0x7C, 0x94, 0xD4, 0x60, 0x42, 0xAB, 0x38, 0x6B, 0x1A,
        0x2E, 0xC4, 0xC3, 0x49, 0x0B, 0xE6, 0x8A, 0xDD, 0xC5, 0xD0,
        0xB4, 0x51, 0x02, 0x41, 0x00, 0xA9, 0x8B, 0xA7, 0xA9, 0xEE,
        0xAE, 0xBB, 0x17, 0xCB, 0x72, 0xF2, 0x50, 0x22, 0x9D, 0xB3,
        0xDF, 0xE0, 0x40, 0x37, 0x08, 0xD5, 0x7F, 0x19, 0x58, 0x80,
        0x70, 0x79, 0x69, 0x99, 0xDF, 0x62, 0x0D, 0x21, 0xAB, 0xDD,
        0xB2, 0xCE, 0x68, 0xB3, 0x9F, 0x87, 0xAF, 0x55, 0xF4, 0xAA,
        0xE1, 0x00, 0x72, 0xBE, 0x6E, 0xC3, 0x94, 0x49, 0xDC, 0xBB,
        0x8E, 0x1A, 0x78, 0xE5, 0x49, 0x1F, 0x55, 0x41, 0xA1
};
static const int sizeof_server_key_der_1024 = sizeof(server_key_der_1024);

/* ./certs/1024/server-cert.der, 1024-bit */
static const unsigned char server_cert_der_1024[] =
{
        0x30, 0x82, 0x03, 0xF2, 0x30, 0x82, 0x03, 0x5B, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x01, 0x01, 0x30, 0x0D, 0x06, 0x09,
        0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B, 0x05,
        0x00, 0x30, 0x81, 0x99, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03,
        0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x10, 0x30,
        0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F,
        0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E, 0x06,
        0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65,
        0x6D, 0x61, 0x6E, 0x31, 0x11, 0x30, 0x0F, 0x06, 0x03, 0x55,
        0x04, 0x0A, 0x0C, 0x08, 0x53, 0x61, 0x77, 0x74, 0x6F, 0x6F,
        0x74, 0x68, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04,
        0x0B, 0x0C, 0x0F, 0x43, 0x6F, 0x6E, 0x73, 0x75, 0x6C, 0x74,
        0x69, 0x6E, 0x67, 0x5F, 0x31, 0x30, 0x32, 0x34, 0x31, 0x18,
        0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77,
        0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C,
        0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09,
        0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16,
        0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66,
        0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x30, 0x1E, 0x17,
        0x0D, 0x32, 0x34, 0x31, 0x32, 0x31, 0x38, 0x32, 0x31, 0x32,
        0x35, 0x33, 0x30, 0x5A, 0x17, 0x0D, 0x32, 0x37, 0x30, 0x39,
        0x31, 0x34, 0x32, 0x31, 0x32, 0x35, 0x33, 0x30, 0x5A, 0x30,
        0x81, 0x95, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04,
        0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E, 0x06,
        0x03, 0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E, 0x74,
        0x61, 0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55,
        0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D, 0x61,
        0x6E, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x0A,
        0x0C, 0x07, 0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C, 0x31,
        0x15, 0x30, 0x13, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x0C,
        0x53, 0x75, 0x70, 0x70, 0x6F, 0x72, 0x74, 0x5F, 0x31, 0x30,
        0x32, 0x34, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04,
        0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F,
        0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D,
        0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x30, 0x81, 0x9F, 0x30, 0x0D, 0x06, 0x09, 0x2A, 0x86,
        0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01, 0x05, 0x00, 0x03,
        0x81, 0x8D, 0x00, 0x30, 0x81, 0x89, 0x02, 0x81, 0x81, 0x00,
        0xAA, 0x3E, 0xA5, 0x9C, 0xD3, 0x17, 0x49, 0x65, 0x43, 0xDE,
        0xD0, 0xF3, 0x4B, 0x1C, 0xDB, 0x49, 0x0C, 0xFC, 0x7A, 0x65,
        0x05, 0x6D, 0xDE, 0x6A, 0xC4, 0xE4, 0x73, 0x2C, 0x8A, 0x96,
        0x82, 0x8F, 0x23, 0xA5, 0x06, 0x71, 0x1C, 0x06, 0x3E, 0x2F,
        0x92, 0x8D, 0x0B, 0x29, 0x34, 0x45, 0x59, 0xE9, 0xA9, 0xBC,
        0x61, 0xD7, 0x24, 0x37, 0x5D, 0xB5, 0xC4, 0x37, 0x8D, 0xBA,
        0x67, 0xB2, 0xEF, 0x03, 0x27, 0xFA, 0xC1, 0xB4, 0xCD, 0x6B,
        0x00, 0x66, 0xB4, 0xD6, 0x73, 0x70, 0x1F, 0x08, 0x3A, 0xCC,
        0x77, 0xAD, 0xE9, 0xF9, 0x34, 0xD4, 0xF3, 0xA0, 0x2D, 0xA9,
        0xE7, 0x58, 0xA9, 0xC0, 0x61, 0x84, 0xB6, 0xEC, 0x3D, 0x0A,
        0xAD, 0xFD, 0x5C, 0x86, 0x73, 0xAA, 0x6B, 0x47, 0xD8, 0x8B,
        0x2E, 0x58, 0x4B, 0x69, 0x12, 0x82, 0x26, 0x55, 0xE6, 0x14,
        0xBF, 0x55, 0x70, 0x88, 0xFE, 0xF9, 0x75, 0xE1, 0x02, 0x03,
        0x01, 0x00, 0x01, 0xA3, 0x82, 0x01, 0x4A, 0x30, 0x82, 0x01,
        0x46, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D, 0x0E, 0x04, 0x16,
        0x04, 0x14, 0xD9, 0x3C, 0x35, 0xEA, 0x74, 0x0E, 0x23, 0xBE,
        0x9C, 0xFC, 0xFA, 0x29, 0x90, 0x09, 0xC1, 0xE7, 0x84, 0x16,
        0x9F, 0x7C, 0x30, 0x81, 0xD9, 0x06, 0x03, 0x55, 0x1D, 0x23,
        0x04, 0x81, 0xD1, 0x30, 0x81, 0xCE, 0x80, 0x14, 0xD3, 0x22,
        0x8F, 0x28, 0x2C, 0xE0, 0x05, 0xEE, 0xD3, 0xED, 0xC3, 0x71,
        0x3D, 0xC9, 0xB2, 0x36, 0x3A, 0x1D, 0xBF, 0xA8, 0xA1, 0x81,
        0x9F, 0xA4, 0x81, 0x9C, 0x30, 0x81, 0x99, 0x31, 0x0B, 0x30,
        0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53,
        0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C,
        0x07, 0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10,
        0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42,
        0x6F, 0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x11, 0x30, 0x0F,
        0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x08, 0x53, 0x61, 0x77,
        0x74, 0x6F, 0x6F, 0x74, 0x68, 0x31, 0x18, 0x30, 0x16, 0x06,
        0x03, 0x55, 0x04, 0x0B, 0x0C, 0x0F, 0x43, 0x6F, 0x6E, 0x73,
        0x75, 0x6C, 0x74, 0x69, 0x6E, 0x67, 0x5F, 0x31, 0x30, 0x32,
        0x34, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x03,
        0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66,
        0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30,
        0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01,
        0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77,
        0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D,
        0x82, 0x14, 0x59, 0x52, 0x6B, 0x92, 0x1A, 0x25, 0x8F, 0x1B,
        0xEE, 0x4C, 0x51, 0x9C, 0x47, 0x2F, 0xFF, 0xFF, 0x9D, 0x43,
        0x29, 0x47, 0x30, 0x0C, 0x06, 0x03, 0x55, 0x1D, 0x13, 0x04,
        0x05, 0x30, 0x03, 0x01, 0x01, 0xFF, 0x30, 0x1C, 0x06, 0x03,
        0x55, 0x1D, 0x11, 0x04, 0x15, 0x30, 0x13, 0x82, 0x0B, 0x65,
        0x78, 0x61, 0x6D, 0x70, 0x6C, 0x65, 0x2E, 0x63, 0x6F, 0x6D,
        0x87, 0x04, 0x7F, 0x00, 0x00, 0x01, 0x30, 0x1D, 0x06, 0x03,
        0x55, 0x1D, 0x25, 0x04, 0x16, 0x30, 0x14, 0x06, 0x08, 0x2B,
        0x06, 0x01, 0x05, 0x05, 0x07, 0x03, 0x01, 0x06, 0x08, 0x2B,
        0x06, 0x01, 0x05, 0x05, 0x07, 0x03, 0x02, 0x30, 0x0D, 0x06,
        0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B,
        0x05, 0x00, 0x03, 0x81, 0x81, 0x00, 0x94, 0x67, 0x03, 0x63,
        0x2A, 0x3E, 0xE4, 0x56, 0xA5, 0x9F, 0x84, 0x89, 0x68, 0x8C,
        0xED, 0xEF, 0xA4, 0xFE, 0x1F, 0xDC, 0x03, 0x04, 0x1E, 0xD0,
        0x87, 0x90, 0x14, 0x7C, 0x82, 0x3F, 0x36, 0xA8, 0x7C, 0x14,
        0x64, 0xAB, 0x88, 0xD4, 0x9D, 0x81, 0xE8, 0xF6, 0xA7, 0xEC,
        0x12, 0x51, 0xEA, 0x25, 0xFD, 0xA4, 0xD1, 0x9C, 0x9B, 0x71,
        0x3D, 0xC8, 0xD0, 0xB3, 0xD2, 0x6D, 0xEB, 0x56, 0x11, 0x66,
        0x05, 0x4E, 0x92, 0x27, 0x0A, 0x76, 0x8C, 0x3A, 0x8B, 0xBD,
        0xE2, 0x46, 0xF5, 0x7B, 0x8E, 0xFF, 0x03, 0xF3, 0x89, 0x92,
        0xDC, 0x9B, 0x46, 0x79, 0xF4, 0xB8, 0x95, 0x7D, 0xB6, 0x29,
        0x79, 0xF3, 0x55, 0xC8, 0x70, 0xDE, 0xF7, 0x9F, 0x59, 0xE1,
        0xE2, 0x8D, 0xA7, 0x73, 0x1F, 0x97, 0x1C, 0x52, 0x64, 0x48,
        0x77, 0xCF, 0x6D, 0xA0, 0x27, 0xAD, 0xC0, 0x16, 0x56, 0x55,
        0x46, 0xB2, 0xBF, 0xF1
};
static const int sizeof_server_cert_der_1024 = sizeof(server_cert_der_1024);

#endif /* USE_CERT_BUFFERS_1024 */

#ifdef USE_CERT_BUFFERS_2048

/* ./certs/client-key.der, 2048-bit */
static const unsigned char client_key_der_2048[] =
{
        0x30, 0x82, 0x04, 0xA4, 0x02, 0x01, 0x00, 0x02, 0x82, 0x01,
        0x01, 0x00, 0xC3, 0x03, 0xD1, 0x2B, 0xFE, 0x39, 0xA4, 0x32,
        0x45, 0x3B, 0x53, 0xC8, 0x84, 0x2B, 0x2A, 0x7C, 0x74, 0x9A,
        0xBD, 0xAA, 0x2A, 0x52, 0x07, 0x47, 0xD6, 0xA6, 0x36, 0xB2,
        0x07, 0x32, 0x8E, 0xD0, 0xBA, 0x69, 0x7B, 0xC6, 0xC3, 0x44,
        0x9E, 0xD4, 0x81, 0x48, 0xFD, 0x2D, 0x68, 0xA2, 0x8B, 0x67,
        0xBB, 0xA1, 0x75, 0xC8, 0x36, 0x2C, 0x4A, 0xD2, 0x1B, 0xF7,
        0x8B, 0xBA, 0xCF, 0x0D, 0xF9, 0xEF, 0xEC, 0xF1, 0x81, 0x1E,
        0x7B, 0x9B, 0x03, 0x47, 0x9A, 0xBF, 0x65, 0xCC, 0x7F, 0x65,
        0x24, 0x69, 0xA6, 0xE8, 0x14, 0x89, 0x5B, 0xE4, 0x34, 0xF7,
        0xC5, 0xB0, 0x14, 0x93, 0xF5, 0x67, 0x7B, 0x3A, 0x7A, 0x78,
        0xE1, 0x01, 0x56, 0x56, 0x91, 0xA6, 0x13, 0x42, 0x8D, 0xD2,
        0x3C, 0x40, 0x9C, 0x4C, 0xEF, 0xD1, 0x86, 0xDF, 0x37, 0x51,
        0x1B, 0x0C, 0xA1, 0x3B, 0xF5, 0xF1, 0xA3, 0x4A, 0x35, 0xE4,
        0xE1, 0xCE, 0x96, 0xDF, 0x1B, 0x7E, 0xBF, 0x4E, 0x97, 0xD0,
        0x10, 0xE8, 0xA8, 0x08, 0x30, 0x81, 0xAF, 0x20, 0x0B, 0x43,
        0x14, 0xC5, 0x74, 0x67, 0xB4, 0x32, 0x82, 0x6F, 0x8D, 0x86,
        0xC2, 0x88, 0x40, 0x99, 0x36, 0x83, 0xBA, 0x1E, 0x40, 0x72,
        0x22, 0x17, 0xD7, 0x52, 0x65, 0x24, 0x73, 0xB0, 0xCE, 0xEF,
        0x19, 0xCD, 0xAE, 0xFF, 0x78, 0x6C, 0x7B, 0xC0, 0x12, 0x03,
        0xD4, 0x4E, 0x72, 0x0D, 0x50, 0x6D, 0x3B, 0xA3, 0x3B, 0xA3,
        0x99, 0x5E, 0x9D, 0xC8, 0xD9, 0x0C, 0x85, 0xB3, 0xD9, 0x8A,
        0xD9, 0x54, 0x26, 0xDB, 0x6D, 0xFA, 0xAC, 0xBB, 0xFF, 0x25,
        0x4C, 0xC4, 0xD1, 0x79, 0xF4, 0x71, 0xD3, 0x86, 0x40, 0x18,
        0x13, 0xB0, 0x63, 0xB5, 0x72, 0x4E, 0x30, 0xC4, 0x97, 0x84,
        0x86, 0x2D, 0x56, 0x2F, 0xD7, 0x15, 0xF7, 0x7F, 0xC0, 0xAE,
        0xF5, 0xFC, 0x5B, 0xE5, 0xFB, 0xA1, 0xBA, 0xD3, 0x02, 0x03,
        0x01, 0x00, 0x01, 0x02, 0x82, 0x01, 0x01, 0x00, 0xA2, 0xE6,
        0xD8, 0x5F, 0x10, 0x71, 0x64, 0x08, 0x9E, 0x2E, 0x6D, 0xD1,
        0x6D, 0x1E, 0x85, 0xD2, 0x0A, 0xB1, 0x8C, 0x47, 0xCE, 0x2C,
        0x51, 0x6A, 0xA0, 0x12, 0x9E, 0x53, 0xDE, 0x91, 0x4C, 0x1D,
        0x6D, 0xEA, 0x59, 0x7B, 0xF2, 0x77, 0xAA, 0xD9, 0xC6, 0xD9,
        0x8A, 0xAB, 0xD8, 0xE1, 0x16, 0xE4, 0x63, 0x26, 0xFF, 0xB5,
        0x6C, 0x13, 0x59, 0xB8, 0xE3, 0xA5, 0xC8, 0x72, 0x17, 0x2E,
        0x0C, 0x9F, 0x6F, 0xE5, 0x59, 0x3F, 0x76, 0x6F, 0x49, 0xB1,
        0x11, 0xC2, 0x5A, 0x2E, 0x16, 0x29, 0x0D, 0xDE, 0xB7, 0x8E,
        0xDC, 0x40, 0xD5, 0xA2, 0xEE, 0xE0, 0x1E, 0xA1, 0xF4, 0xBE,
        0x97, 0xDB, 0x86, 0x63, 0x96, 0x14, 0xCD, 0x98, 0x09, 0x60,
        0x2D, 0x30, 0x76, 0x9C, 0x3C, 0xCD, 0xE6, 0x88, 0xEE, 0x47,
        0x92, 0x79, 0x0B, 0x5A, 0x00, 0xE2, 0x5E, 0x5F, 0x11, 0x7C,
        0x7D, 0xF9, 0x08, 0xB7, 0x20, 0x06, 0x89, 0x2A, 0x5D, 0xFD,
        0x00, 0xAB, 0x22, 0xE1, 0xF0, 0xB3, 0xBC, 0x24, 0xA9, 0x5E,
        0x26, 0x0E, 0x1F, 0x00, 0x2D, 0xFE, 0x21, 0x9A, 0x53, 0x5B,
        0x6D, 0xD3, 0x2B, 0xAB, 0x94, 0x82, 0x68, 0x43, 0x36, 0xD8,
        0xF6, 0x2F, 0xC6, 0x22, 0xFC, 0xB5, 0x41, 0x5D, 0x0D, 0x33,
        0x60, 0xEA, 0xA4, 0x7D, 0x7E, 0xE8, 0x4B, 0x55, 0x91, 0x56,
        0xD3, 0x5C, 0x57, 0x8F, 0x1F, 0x94, 0x17, 0x2F, 0xAA, 0xDE,
        0xE9, 0x9E, 0xA8, 0xF4, 0xCF, 0x8A, 0x4C, 0x8E, 0xA0, 0xE4,
        0x56, 0x73, 0xB2, 0xCF, 0x4F, 0x86, 0xC5, 0x69, 0x3C, 0xF3,
        0x24, 0x20, 0x8B, 0x5C, 0x96, 0x0C, 0xFA, 0x6B, 0x12, 0x3B,
        0x9A, 0x67, 0xC1, 0xDF, 0xC6, 0x96, 0xB2, 0xA5, 0xD5, 0x92,
        0x0D, 0x9B, 0x09, 0x42, 0x68, 0x24, 0x10, 0x45, 0xD4, 0x50,
        0xE4, 0x17, 0x39, 0x48, 0xD0, 0x35, 0x8B, 0x94, 0x6D, 0x11,
        0xDE, 0x8F, 0xCA, 0x59, 0x02, 0x81, 0x81, 0x00, 0xEA, 0x24,
        0xA7, 0xF9, 0x69, 0x33, 0xE9, 0x71, 0xDC, 0x52, 0x7D, 0x88,
        0x21, 0x28, 0x2F, 0x49, 0xDE, 0xBA, 0x72, 0x16, 0xE9, 0xCC,
        0x47, 0x7A, 0x88, 0x0D, 0x94, 0x57, 0x84, 0x58, 0x16, 0x3A,
        0x81, 0xB0, 0x3F, 0xA2, 0xCF, 0xA6, 0x6C, 0x1E, 0xB0, 0x06,
        0x29, 0x00, 0x8F, 0xE7, 0x77, 0x76, 0xAC, 0xDB, 0xCA, 0xC7,
        0xD9, 0x5E, 0x9B, 0x3F, 0x26, 0x90, 0x52, 0xAE, 0xFC, 0x38,
        0x90, 0x00, 0x14, 0xBB, 0xB4, 0x0F, 0x58, 0x94, 0xE7, 0x2F,
        0x6A, 0x7E, 0x1C, 0x4F, 0x41, 0x21, 0xD4, 0x31, 0x59, 0x1F,
        0x4E, 0x8A, 0x1A, 0x8D, 0xA7, 0x57, 0x6C, 0x22, 0xD8, 0xE5,
        0xF4, 0x7E, 0x32, 0xA6, 0x10, 0xCB, 0x64, 0xA5, 0x55, 0x03,
        0x87, 0xA6, 0x27, 0x05, 0x8C, 0xC3, 0xD7, 0xB6, 0x27, 0xB2,
        0x4D, 0xBA, 0x30, 0xDA, 0x47, 0x8F, 0x54, 0xD3, 0x3D, 0x8B,
        0x84, 0x8D, 0x94, 0x98, 0x58, 0xA5, 0x02, 0x81, 0x81, 0x00,
        0xD5, 0x38, 0x1B, 0xC3, 0x8F, 0xC5, 0x93, 0x0C, 0x47, 0x0B,
        0x6F, 0x35, 0x92, 0xC5, 0xB0, 0x8D, 0x46, 0xC8, 0x92, 0x18,
        0x8F, 0xF5, 0x80, 0x0A, 0xF7, 0xEF, 0xA1, 0xFE, 0x80, 0xB9,
        0xB5, 0x2A, 0xBA, 0xCA, 0x18, 0xB0, 0x5D, 0xA5, 0x07, 0xD0,
        0x93, 0x8D, 0xD8, 0x9C, 0x04, 0x1C, 0xD4, 0x62, 0x8E, 0xA6,
        0x26, 0x81, 0x01, 0xFF, 0xCE, 0x8A, 0x2A, 0x63, 0x34, 0x35,
        0x40, 0xAA, 0x6D, 0x80, 0xDE, 0x89, 0x23, 0x6A, 0x57, 0x4D,
        0x9E, 0x6E, 0xAD, 0x93, 0x4E, 0x56, 0x90, 0x0B, 0x6D, 0x9D,
        0x73, 0x8B, 0x0C, 0xAE, 0x27, 0x3D, 0xDE, 0x4E, 0xF0, 0xAA,
        0xC5, 0x6C, 0x78, 0x67, 0x6C, 0x94, 0x52, 0x9C, 0x37, 0x67,
        0x6C, 0x2D, 0xEF, 0xBB, 0xAF, 0xDF, 0xA6, 0x90, 0x3C, 0xC4,
        0x47, 0xCF, 0x8D, 0x96, 0x9E, 0x98, 0xA9, 0xB4, 0x9F, 0xC5,
        0xA6, 0x50, 0xDC, 0xB3, 0xF0, 0xFB, 0x74, 0x17, 0x02, 0x81,
        0x80, 0x5E, 0x83, 0x09, 0x62, 0xBD, 0xBA, 0x7C, 0xA2, 0xBF,
        0x42, 0x74, 0xF5, 0x7C, 0x1C, 0xD2, 0x69, 0xC9, 0x04, 0x0D,
        0x85, 0x7E, 0x3E, 0x3D, 0x24, 0x12, 0xC3, 0x18, 0x7B, 0xF3,
        0x29, 0xF3, 0x5F, 0x0E, 0x76, 0x6C, 0x59, 0x75, 0xE4, 0x41,
        0x84, 0x69, 0x9D, 0x32, 0xF3, 0xCD, 0x22, 0xAB, 0xB0, 0x35,
        0xBA, 0x4A, 0xB2, 0x3C, 0xE5, 0xD9, 0x58, 0xB6, 0x62, 0x4F,
        0x5D, 0xDE, 0xE5, 0x9E, 0x0A, 0xCA, 0x53, 0xB2, 0x2C, 0xF7,
        0x9E, 0xB3, 0x6B, 0x0A, 0x5B, 0x79, 0x65, 0xEC, 0x6E, 0x91,
        0x4E, 0x92, 0x20, 0xF6, 0xFC, 0xFC, 0x16, 0xED, 0xD3, 0x76,
        0x0C, 0xE2, 0xEC, 0x7F, 0xB2, 0x69, 0x13, 0x6B, 0x78, 0x0E,
        0x5A, 0x46, 0x64, 0xB4, 0x5E, 0xB7, 0x25, 0xA0, 0x5A, 0x75,
        0x3A, 0x4B, 0xEF, 0xC7, 0x3C, 0x3E, 0xF7, 0xFD, 0x26, 0xB8,
        0x20, 0xC4, 0x99, 0x0A, 0x9A, 0x73, 0xBE, 0xC3, 0x19, 0x02,
        0x81, 0x81, 0x00, 0xBA, 0x44, 0x93, 0x14, 0xAC, 0x34, 0x19,
        0x3B, 0x5F, 0x91, 0x60, 0xAC, 0xF7, 0xB4, 0xD6, 0x81, 0x05,
        0x36, 0x51, 0x53, 0x3D, 0xE8, 0x65, 0xDC, 0xAF, 0x2E, 0xDC,
        0x61, 0x3E, 0xC9, 0x7D, 0xB8, 0x7F, 0x87, 0xF0, 0x3B, 0x9B,
        0x03, 0x82, 0x29, 0x37, 0xCE, 0x72, 0x4E, 0x11, 0xD5, 0xB1,
        0xC1, 0x0C, 0x07, 0xA0, 0x99, 0x91, 0x4A, 0x8D, 0x7F, 0xEC,
        0x79, 0xCF, 0xF1, 0x39, 0xB5, 0xE9, 0x85, 0xEC, 0x62, 0xF7,
        0xDA, 0x7D, 0xBC, 0x64, 0x4D, 0x22, 0x3C, 0x0E, 0xF2, 0xD6,
        0x51, 0xF5, 0x87, 0xD8, 0x99, 0xC0, 0x11, 0x20, 0x5D, 0x0F,
        0x29, 0xFD, 0x5B, 0xE2, 0xAE, 0xD9, 0x1C, 0xD9, 0x21, 0x56,
        0x6D, 0xFC, 0x84, 0xD0, 0x5F, 0xED, 0x10, 0x15, 0x1C, 0x18,
        0x21, 0xE7, 0xC4, 0x3D, 0x4B, 0xD7, 0xD0, 0x9E, 0x6A, 0x95,
        0xCF, 0x22, 0xC9, 0x03, 0x7B, 0x9E, 0xE3, 0x60, 0x01, 0xFC,
        0x2F, 0x02, 0x81, 0x80, 0x11, 0xD0, 0x4B, 0xCF, 0x1B, 0x67,
        0xB9, 0x9F, 0x10, 0x75, 0x47, 0x86, 0x65, 0xAE, 0x31, 0xC2,
        0xC6, 0x30, 0xAC, 0x59, 0x06, 0x50, 0xD9, 0x0F, 0xB5, 0x70,
        0x06, 0xF7, 0xF0, 0xD3, 0xC8, 0x62, 0x7C, 0xA8, 0xDA, 0x6E,
        0xF6, 0x21, 0x3F, 0xD3, 0x7F, 0x5F, 0xEA, 0x8A, 0xAB, 0x3F,
        0xD9, 0x2A, 0x5E, 0xF3, 0x51, 0xD2, 0xC2, 0x30, 0x37, 0xE3,
        0x2D, 0xA3, 0x75, 0x0D, 0x1E, 0x4D, 0x21, 0x34, 0xD5, 0x57,
        0x70, 0x5C, 0x89, 0xBF, 0x72, 0xEC, 0x4A, 0x6E, 0x68, 0xD5,
        0xCD, 0x18, 0x74, 0x33, 0x4E, 0x8C, 0x3A, 0x45, 0x8F, 0xE6,
        0x96, 0x40, 0xEB, 0x63, 0xF9, 0x19, 0x86, 0x3A, 0x51, 0xDD,
        0x89, 0x4B, 0xB0, 0xF3, 0xF9, 0x9F, 0x5D, 0x28, 0x95, 0x38,
        0xBE, 0x35, 0xAB, 0xCA, 0x5C, 0xE7, 0x93, 0x53, 0x34, 0xA1,
        0x45, 0x5D, 0x13, 0x39, 0x65, 0x42, 0x46, 0xA1, 0x9F, 0xCD,
        0xF5, 0xBF
};
static const int sizeof_client_key_der_2048 = sizeof(client_key_der_2048);

/* ./certs/client-keyPub.der, 2048-bit */
static const unsigned char client_keypub_der_2048[] =
{
        0x30, 0x82, 0x01, 0x22, 0x30, 0x0D, 0x06, 0x09, 0x2A, 0x86,
        0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01, 0x05, 0x00, 0x03,
        0x82, 0x01, 0x0F, 0x00, 0x30, 0x82, 0x01, 0x0A, 0x02, 0x82,
        0x01, 0x01, 0x00, 0xC3, 0x03, 0xD1, 0x2B, 0xFE, 0x39, 0xA4,
        0x32, 0x45, 0x3B, 0x53, 0xC8, 0x84, 0x2B, 0x2A, 0x7C, 0x74,
        0x9A, 0xBD, 0xAA, 0x2A, 0x52, 0x07, 0x47, 0xD6, 0xA6, 0x36,
        0xB2, 0x07, 0x32, 0x8E, 0xD0, 0xBA, 0x69, 0x7B, 0xC6, 0xC3,
        0x44, 0x9E, 0xD4, 0x81, 0x48, 0xFD, 0x2D, 0x68, 0xA2, 0x8B,
        0x67, 0xBB, 0xA1, 0x75, 0xC8, 0x36, 0x2C, 0x4A, 0xD2, 0x1B,
        0xF7, 0x8B, 0xBA, 0xCF, 0x0D, 0xF9, 0xEF, 0xEC, 0xF1, 0x81,
        0x1E, 0x7B, 0x9B, 0x03, 0x47, 0x9A, 0xBF, 0x65, 0xCC, 0x7F,
        0x65, 0x24, 0x69, 0xA6, 0xE8, 0x14, 0x89, 0x5B, 0xE4, 0x34,
        0xF7, 0xC5, 0xB0, 0x14, 0x93, 0xF5, 0x67, 0x7B, 0x3A, 0x7A,
        0x78, 0xE1, 0x01, 0x56, 0x56, 0x91, 0xA6, 0x13, 0x42, 0x8D,
        0xD2, 0x3C, 0x40, 0x9C, 0x4C, 0xEF, 0xD1, 0x86, 0xDF, 0x37,
        0x51, 0x1B, 0x0C, 0xA1, 0x3B, 0xF5, 0xF1, 0xA3, 0x4A, 0x35,
        0xE4, 0xE1, 0xCE, 0x96, 0xDF, 0x1B, 0x7E, 0xBF, 0x4E, 0x97,
        0xD0, 0x10, 0xE8, 0xA8, 0x08, 0x30, 0x81, 0xAF, 0x20, 0x0B,
        0x43, 0x14, 0xC5, 0x74, 0x67, 0xB4, 0x32, 0x82, 0x6F, 0x8D,
        0x86, 0xC2, 0x88, 0x40, 0x99, 0x36, 0x83, 0xBA, 0x1E, 0x40,
        0x72, 0x22, 0x17, 0xD7, 0x52, 0x65, 0x24, 0x73, 0xB0, 0xCE,
        0xEF, 0x19, 0xCD, 0xAE, 0xFF, 0x78, 0x6C, 0x7B, 0xC0, 0x12,
        0x03, 0xD4, 0x4E, 0x72, 0x0D, 0x50, 0x6D, 0x3B, 0xA3, 0x3B,
        0xA3, 0x99, 0x5E, 0x9D, 0xC8, 0xD9, 0x0C, 0x85, 0xB3, 0xD9,
        0x8A, 0xD9, 0x54, 0x26, 0xDB, 0x6D, 0xFA, 0xAC, 0xBB, 0xFF,
        0x25, 0x4C, 0xC4, 0xD1, 0x79, 0xF4, 0x71, 0xD3, 0x86, 0x40,
        0x18, 0x13, 0xB0, 0x63, 0xB5, 0x72, 0x4E, 0x30, 0xC4, 0x97,
        0x84, 0x86, 0x2D, 0x56, 0x2F, 0xD7, 0x15, 0xF7, 0x7F, 0xC0,
        0xAE, 0xF5, 0xFC, 0x5B, 0xE5, 0xFB, 0xA1, 0xBA, 0xD3, 0x02,
        0x03, 0x01, 0x00, 0x01
};
static const int sizeof_client_keypub_der_2048 = sizeof(client_keypub_der_2048);

/* ./certs/client-cert.der, 2048-bit */
static const unsigned char client_cert_der_2048[] =
{
        0x30, 0x82, 0x05, 0x1D, 0x30, 0x82, 0x04, 0x05, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x14, 0x4F, 0x0D, 0x8C, 0xC5, 0xFA,
        0xEE, 0xA2, 0x9B, 0xB7, 0x35, 0x9E, 0xE9, 0x4A, 0x17, 0x99,
        0xF0, 0xCC, 0x23, 0xF2, 0xEC, 0x30, 0x0D, 0x06, 0x09, 0x2A,
        0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B, 0x05, 0x00,
        0x30, 0x81, 0x9E, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55,
        0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E,
        0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E,
        0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03,
        0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D,
        0x61, 0x6E, 0x31, 0x15, 0x30, 0x13, 0x06, 0x03, 0x55, 0x04,
        0x0A, 0x0C, 0x0C, 0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C,
        0x5F, 0x32, 0x30, 0x34, 0x38, 0x31, 0x19, 0x30, 0x17, 0x06,
        0x03, 0x55, 0x04, 0x0B, 0x0C, 0x10, 0x50, 0x72, 0x6F, 0x67,
        0x72, 0x61, 0x6D, 0x6D, 0x69, 0x6E, 0x67, 0x2D, 0x32, 0x30,
        0x34, 0x38, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04,
        0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F,
        0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D,
        0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x30, 0x1E, 0x17, 0x0D, 0x32, 0x34, 0x31, 0x32, 0x31,
        0x38, 0x32, 0x31, 0x32, 0x35, 0x32, 0x39, 0x5A, 0x17, 0x0D,
        0x32, 0x37, 0x30, 0x39, 0x31, 0x34, 0x32, 0x31, 0x32, 0x35,
        0x32, 0x39, 0x5A, 0x30, 0x81, 0x9E, 0x31, 0x0B, 0x30, 0x09,
        0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31,
        0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07,
        0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30,
        0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F,
        0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x15, 0x30, 0x13, 0x06,
        0x03, 0x55, 0x04, 0x0A, 0x0C, 0x0C, 0x77, 0x6F, 0x6C, 0x66,
        0x53, 0x53, 0x4C, 0x5F, 0x32, 0x30, 0x34, 0x38, 0x31, 0x19,
        0x30, 0x17, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x10, 0x50,
        0x72, 0x6F, 0x67, 0x72, 0x61, 0x6D, 0x6D, 0x69, 0x6E, 0x67,
        0x2D, 0x32, 0x30, 0x34, 0x38, 0x31, 0x18, 0x30, 0x16, 0x06,
        0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48,
        0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E,
        0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C,
        0x2E, 0x63, 0x6F, 0x6D, 0x30, 0x82, 0x01, 0x22, 0x30, 0x0D,
        0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01,
        0x01, 0x05, 0x00, 0x03, 0x82, 0x01, 0x0F, 0x00, 0x30, 0x82,
        0x01, 0x0A, 0x02, 0x82, 0x01, 0x01, 0x00, 0xC3, 0x03, 0xD1,
        0x2B, 0xFE, 0x39, 0xA4, 0x32, 0x45, 0x3B, 0x53, 0xC8, 0x84,
        0x2B, 0x2A, 0x7C, 0x74, 0x9A, 0xBD, 0xAA, 0x2A, 0x52, 0x07,
        0x47, 0xD6, 0xA6, 0x36, 0xB2, 0x07, 0x32, 0x8E, 0xD0, 0xBA,
        0x69, 0x7B, 0xC6, 0xC3, 0x44, 0x9E, 0xD4, 0x81, 0x48, 0xFD,
        0x2D, 0x68, 0xA2, 0x8B, 0x67, 0xBB, 0xA1, 0x75, 0xC8, 0x36,
        0x2C, 0x4A, 0xD2, 0x1B, 0xF7, 0x8B, 0xBA, 0xCF, 0x0D, 0xF9,
        0xEF, 0xEC, 0xF1, 0x81, 0x1E, 0x7B, 0x9B, 0x03, 0x47, 0x9A,
        0xBF, 0x65, 0xCC, 0x7F, 0x65, 0x24, 0x69, 0xA6, 0xE8, 0x14,
        0x89, 0x5B, 0xE4, 0x34, 0xF7, 0xC5, 0xB0, 0x14, 0x93, 0xF5,
        0x67, 0x7B, 0x3A, 0x7A, 0x78, 0xE1, 0x01, 0x56, 0x56, 0x91,
        0xA6, 0x13, 0x42, 0x8D, 0xD2, 0x3C, 0x40, 0x9C, 0x4C, 0xEF,
        0xD1, 0x86, 0xDF, 0x37, 0x51, 0x1B, 0x0C, 0xA1, 0x3B, 0xF5,
        0xF1, 0xA3, 0x4A, 0x35, 0xE4, 0xE1, 0xCE, 0x96, 0xDF, 0x1B,
        0x7E, 0xBF, 0x4E, 0x97, 0xD0, 0x10, 0xE8, 0xA8, 0x08, 0x30,
        0x81, 0xAF, 0x20, 0x0B, 0x43, 0x14, 0xC5, 0x74, 0x67, 0xB4,
        0x32, 0x82, 0x6F, 0x8D, 0x86, 0xC2, 0x88, 0x40, 0x99, 0x36,
        0x83, 0xBA, 0x1E, 0x40, 0x72, 0x22, 0x17, 0xD7, 0x52, 0x65,
        0x24, 0x73, 0xB0, 0xCE, 0xEF, 0x19, 0xCD, 0xAE, 0xFF, 0x78,
        0x6C, 0x7B, 0xC0, 0x12, 0x03, 0xD4, 0x4E, 0x72, 0x0D, 0x50,
        0x6D, 0x3B, 0xA3, 0x3B, 0xA3, 0x99, 0x5E, 0x9D, 0xC8, 0xD9,
        0x0C, 0x85, 0xB3, 0xD9, 0x8A, 0xD9, 0x54, 0x26, 0xDB, 0x6D,
        0xFA, 0xAC, 0xBB, 0xFF, 0x25, 0x4C, 0xC4, 0xD1, 0x79, 0xF4,
        0x71, 0xD3, 0x86, 0x40, 0x18, 0x13, 0xB0, 0x63, 0xB5, 0x72,
        0x4E, 0x30, 0xC4, 0x97, 0x84, 0x86, 0x2D, 0x56, 0x2F, 0xD7,
        0x15, 0xF7, 0x7F, 0xC0, 0xAE, 0xF5, 0xFC, 0x5B, 0xE5, 0xFB,
        0xA1, 0xBA, 0xD3, 0x02, 0x03, 0x01, 0x00, 0x01, 0xA3, 0x82,
        0x01, 0x4F, 0x30, 0x82, 0x01, 0x4B, 0x30, 0x1D, 0x06, 0x03,
        0x55, 0x1D, 0x0E, 0x04, 0x16, 0x04, 0x14, 0x33, 0xD8, 0x45,
        0x66, 0xD7, 0x68, 0x87, 0x18, 0x7E, 0x54, 0x0D, 0x70, 0x27,
        0x91, 0xC7, 0x26, 0xD7, 0x85, 0x65, 0xC0, 0x30, 0x81, 0xDE,
        0x06, 0x03, 0x55, 0x1D, 0x23, 0x04, 0x81, 0xD6, 0x30, 0x81,
        0xD3, 0x80, 0x14, 0x33, 0xD8, 0x45, 0x66, 0xD7, 0x68, 0x87,
        0x18, 0x7E, 0x54, 0x0D, 0x70, 0x27, 0x91, 0xC7, 0x26, 0xD7,
        0x85, 0x65, 0xC0, 0xA1, 0x81, 0xA4, 0xA4, 0x81, 0xA1, 0x30,
        0x81, 0x9E, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04,
        0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E, 0x06,
        0x03, 0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E, 0x74,
        0x61, 0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55,
        0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D, 0x61,
        0x6E, 0x31, 0x15, 0x30, 0x13, 0x06, 0x03, 0x55, 0x04, 0x0A,
        0x0C, 0x0C, 0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C, 0x5F,
        0x32, 0x30, 0x34, 0x38, 0x31, 0x19, 0x30, 0x17, 0x06, 0x03,
        0x55, 0x04, 0x0B, 0x0C, 0x10, 0x50, 0x72, 0x6F, 0x67, 0x72,
        0x61, 0x6D, 0x6D, 0x69, 0x6E, 0x67, 0x2D, 0x32, 0x30, 0x34,
        0x38, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x03,
        0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66,
        0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30,
        0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01,
        0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77,
        0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D,
        0x82, 0x14, 0x4F, 0x0D, 0x8C, 0xC5, 0xFA, 0xEE, 0xA2, 0x9B,
        0xB7, 0x35, 0x9E, 0xE9, 0x4A, 0x17, 0x99, 0xF0, 0xCC, 0x23,
        0xF2, 0xEC, 0x30, 0x0C, 0x06, 0x03, 0x55, 0x1D, 0x13, 0x04,
        0x05, 0x30, 0x03, 0x01, 0x01, 0xFF, 0x30, 0x1C, 0x06, 0x03,
        0x55, 0x1D, 0x11, 0x04, 0x15, 0x30, 0x13, 0x82, 0x0B, 0x65,
        0x78, 0x61, 0x6D, 0x70, 0x6C, 0x65, 0x2E, 0x63, 0x6F, 0x6D,
        0x87, 0x04, 0x7F, 0x00, 0x00, 0x01, 0x30, 0x1D, 0x06, 0x03,
        0x55, 0x1D, 0x25, 0x04, 0x16, 0x30, 0x14, 0x06, 0x08, 0x2B,
        0x06, 0x01, 0x05, 0x05, 0x07, 0x03, 0x01, 0x06, 0x08, 0x2B,
        0x06, 0x01, 0x05, 0x05, 0x07, 0x03, 0x02, 0x30, 0x0D, 0x06,
        0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B,
        0x05, 0x00, 0x03, 0x82, 0x01, 0x01, 0x00, 0x46, 0xAB, 0xE4,
        0x6D, 0xAE, 0x49, 0x5B, 0x6A, 0x0B, 0xA9, 0x87, 0xE1, 0x95,
        0x32, 0xA6, 0xD7, 0xAE, 0xDE, 0x28, 0xDC, 0xC7, 0x99, 0x68,
        0xE2, 0x5F, 0xC9, 0x5A, 0x4C, 0x64, 0xB8, 0xF5, 0x28, 0x42,
        0x5A, 0xE8, 0x5C, 0x59, 0x32, 0xFE, 0xD0, 0x1F, 0x0B, 0x55,
        0x89, 0xDB, 0x67, 0xE7, 0x78, 0xF3, 0x70, 0xCF, 0x18, 0x51,
        0x57, 0x8B, 0xF3, 0x2B, 0xA4, 0x66, 0x0B, 0xF6, 0x03, 0x6E,
        0x11, 0xAC, 0x83, 0x52, 0x16, 0x7E, 0xA2, 0x7C, 0x36, 0x77,
        0xF6, 0xBB, 0x13, 0x19, 0x40, 0x2C, 0xB8, 0x8C, 0xCA, 0xD6,
        0x7E, 0x79, 0x7D, 0xF4, 0x14, 0x8D, 0xB5, 0xA4, 0x09, 0xF6,
        0x2D, 0x4C, 0xE7, 0xF9, 0xB8, 0x25, 0x41, 0x15, 0x78, 0xF4,
        0xCA, 0x80, 0x41, 0xEA, 0x3A, 0x05, 0x08, 0xF6, 0xB5, 0x5B,
        0xA1, 0x3B, 0x5B, 0x48, 0xA8, 0x4B, 0x8C, 0x19, 0x8D, 0x6C,
        0x87, 0x31, 0x76, 0x74, 0x02, 0x16, 0x8B, 0xDD, 0x7F, 0xD1,
        0x11, 0x62, 0x27, 0x42, 0x39, 0xE0, 0x9A, 0x63, 0x26, 0x31,
        0x19, 0xCE, 0x3D, 0x41, 0xD5, 0x24, 0x47, 0x32, 0x0F, 0x76,
        0xD6, 0x41, 0x37, 0x44, 0xAD, 0x73, 0xF1, 0xB8, 0xEC, 0x2B,
        0x6E, 0x9C, 0x4F, 0x84, 0xC4, 0x4E, 0xD7, 0x92, 0x10, 0x7E,
        0x23, 0x32, 0xA0, 0x75, 0x6A, 0xE7, 0xFE, 0x55, 0x95, 0x9F,
        0x0A, 0xAD, 0xDF, 0xF9, 0x2A, 0xA2, 0x1A, 0x59, 0xD5, 0x82,
        0x63, 0xD6, 0x5D, 0x7D, 0x79, 0xF4, 0xA7, 0x2D, 0xDC, 0x8C,
        0x04, 0xCD, 0x98, 0xB0, 0x42, 0x0E, 0x84, 0xFA, 0x86, 0x50,
        0x10, 0x61, 0xAC, 0x73, 0xCD, 0x79, 0x45, 0x30, 0xE8, 0x42,
        0xA1, 0x6A, 0xF6, 0x77, 0x55, 0xEC, 0x07, 0xDB, 0x52, 0x29,
        0xCA, 0x7A, 0xC8, 0xA2, 0xDA, 0xE9, 0xF5, 0x98, 0x33, 0x6A,
        0xE8, 0xBC, 0x89, 0xED, 0x01, 0xE2, 0xFE, 0x44, 0x86, 0x86,
        0x80, 0x39, 0xEC
};
static const int sizeof_client_cert_der_2048 = sizeof(client_cert_der_2048);

/* ./certs/dh2048.der, 2048-bit */
static const unsigned char dh_key_der_2048[] =
{
        0x30, 0x82, 0x01, 0x08, 0x02, 0x82, 0x01, 0x01, 0x00, 0xB0,
        0xA1, 0x08, 0x06, 0x9C, 0x08, 0x13, 0xBA, 0x59, 0x06, 0x3C,
        0xBC, 0x30, 0xD5, 0xF5, 0x00, 0xC1, 0x4F, 0x44, 0xA7, 0xD6,
        0xEF, 0x4A, 0xC6, 0x25, 0x27, 0x1C, 0xE8, 0xD2, 0x96, 0x53,
        0x0A, 0x5C, 0x91, 0xDD, 0xA2, 0xC2, 0x94, 0x84, 0xBF, 0x7D,
        0xB2, 0x44, 0x9F, 0x9B, 0xD2, 0xC1, 0x8A, 0xC5, 0xBE, 0x72,
        0x5C, 0xA7, 0xE7, 0x91, 0xE6, 0xD4, 0x9F, 0x73, 0x07, 0x85,
        0x5B, 0x66, 0x48, 0xC7, 0x70, 0xFA, 0xB4, 0xEE, 0x02, 0xC9,
        0x3D, 0x9A, 0x4A, 0xDA, 0x3D, 0xC1, 0x46, 0x3E, 0x19, 0x69,
        0xD1, 0x17, 0x46, 0x07, 0xA3, 0x4D, 0x9F, 0x2B, 0x96, 0x17,
        0x39, 0x6D, 0x30, 0x8D, 0x2A, 0xF3, 0x94, 0xD3, 0x75, 0xCF,
        0xA0, 0x75, 0xE6, 0xF2, 0x92, 0x1F, 0x1A, 0x70, 0x05, 0xAA,
        0x04, 0x83, 0x57, 0x30, 0xFB, 0xDA, 0x76, 0x93, 0x38, 0x50,
        0xE8, 0x27, 0xFD, 0x63, 0xEE, 0x3C, 0xE5, 0xB7, 0xC8, 0x09,
        0xAE, 0x6F, 0x50, 0x35, 0x8E, 0x84, 0xCE, 0x4A, 0x00, 0xE9,
        0x12, 0x7E, 0x5A, 0x31, 0xD7, 0x33, 0xFC, 0x21, 0x13, 0x76,
        0xCC, 0x16, 0x30, 0xDB, 0x0C, 0xFC, 0xC5, 0x62, 0xA7, 0x35,
        0xB8, 0xEF, 0xB7, 0xB0, 0xAC, 0xC0, 0x36, 0xF6, 0xD9, 0xC9,
        0x46, 0x48, 0xF9, 0x40, 0x90, 0x00, 0x2B, 0x1B, 0xAA, 0x6C,
        0xE3, 0x1A, 0xC3, 0x0B, 0x03, 0x9E, 0x1B, 0xC2, 0x46, 0xE4,
        0x48, 0x4E, 0x22, 0x73, 0x6F, 0xC3, 0x5F, 0xD4, 0x9A, 0xD6,
        0x30, 0x07, 0x48, 0xD6, 0x8C, 0x90, 0xAB, 0xD4, 0xF6, 0xF1,
        0xE3, 0x48, 0xD3, 0x58, 0x4B, 0xA6, 0xB9, 0xCD, 0x29, 0xBF,
        0x68, 0x1F, 0x08, 0x4B, 0x63, 0x86, 0x2F, 0x5C, 0x6B, 0xD6,
        0xB6, 0x06, 0x65, 0xF7, 0xA6, 0xDC, 0x00, 0x67, 0x6B, 0xBB,
        0xC3, 0xA9, 0x41, 0x83, 0xFB, 0xC7, 0xFA, 0xC8, 0xE2, 0x1E,
        0x7E, 0xAF, 0x00, 0x3F, 0x93, 0x02, 0x01, 0x02
};
static const int sizeof_dh_key_der_2048 = sizeof(dh_key_der_2048);

/* ./certs/dh-pubkey-2048.der, 2048-bit */
static const unsigned char dh_pub_key_der_2048[] =
{
        0x30, 0x82, 0x02, 0x24, 0x30, 0x82, 0x01, 0x17, 0x06, 0x09,
        0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x03, 0x01, 0x30,
        0x82, 0x01, 0x08, 0x02, 0x82, 0x01, 0x01, 0x00, 0xD3, 0xB2,
        0x99, 0x84, 0x5C, 0x0A, 0x4C, 0xE7, 0x37, 0xCC, 0xFC, 0x18,
        0x37, 0x01, 0x2F, 0x5D, 0xC1, 0x4C, 0xF4, 0x5C, 0xC9, 0x82,
        0x8D, 0xB7, 0xF3, 0xD4, 0xA9, 0x8A, 0x9D, 0x34, 0xD7, 0x76,
        0x57, 0xE5, 0xE5, 0xC3, 0xE5, 0x16, 0x85, 0xCA, 0x4D, 0xD6,
        0x5B, 0xC1, 0xF8, 0xCF, 0x89, 0x26, 0xD0, 0x38, 0x8A, 0xEE,
        0xF3, 0xCD, 0x33, 0xE5, 0x56, 0xBB, 0x90, 0x83, 0x9F, 0x97,
        0x8E, 0x71, 0xFB, 0x27, 0xE4, 0x35, 0x15, 0x45, 0x86, 0x09,
        0x71, 0xA8, 0x9A, 0xB9, 0x3E, 0x0F, 0x51, 0x8A, 0xC2, 0x75,
        0x51, 0x23, 0x12, 0xFB, 0x94, 0x31, 0x44, 0xBF, 0xCE, 0xF6,
        0xED, 0xA6, 0x3A, 0xB7, 0x92, 0xCE, 0x16, 0xA9, 0x14, 0xB3,
        0x88, 0xB7, 0x13, 0x81, 0x71, 0x83, 0x88, 0xCD, 0xB1, 0xA2,
        0x37, 0xE1, 0x59, 0x5C, 0xD0, 0xDC, 0xCA, 0x82, 0x87, 0xFA,
        0x43, 0x44, 0xDD, 0x78, 0x3F, 0xCA, 0x27, 0x7E, 0xE1, 0x6B,
        0x93, 0x19, 0x7C, 0xD9, 0xA6, 0x96, 0x47, 0x0D, 0x12, 0xC1,
        0x13, 0xD7, 0xB9, 0x0A, 0x40, 0xD9, 0x1F, 0xFF, 0xB8, 0xB4,
        0x00, 0xC8, 0xAA, 0x5E, 0xD2, 0x66, 0x4A, 0x05, 0x8E, 0x9E,
        0xF5, 0x34, 0xE7, 0xD7, 0x09, 0x7B, 0x15, 0x49, 0x1D, 0x76,
        0x31, 0xD6, 0x71, 0xEC, 0x13, 0x4E, 0x89, 0x8C, 0x09, 0x22,
        0xD8, 0xE7, 0xA3, 0xE9, 0x7D, 0x21, 0x51, 0x26, 0x6E, 0x9F,
        0x30, 0x8A, 0xBB, 0xBC, 0x74, 0xC1, 0xC3, 0x27, 0x6A, 0xCE,
        0xA3, 0x12, 0x60, 0x68, 0x01, 0xD2, 0x34, 0x07, 0x80, 0xCC,
        0x2D, 0x7F, 0x5C, 0xAE, 0xA2, 0x97, 0x40, 0xC8, 0x3C, 0xAC,
        0xDB, 0x6F, 0xFE, 0x6C, 0x6D, 0xD2, 0x06, 0x1C, 0x43, 0xA2,
        0xB2, 0x2B, 0x82, 0xB7, 0xD0, 0xAB, 0x3F, 0x2C, 0xE7, 0x9C,
        0x19, 0x16, 0xD1, 0x5E, 0x26, 0x86, 0xC7, 0x92, 0xF9, 0x16,
        0x0B, 0xFA, 0x66, 0x83, 0x02, 0x01, 0x02, 0x03, 0x82, 0x01,
        0x05, 0x00, 0x02, 0x82, 0x01, 0x00, 0x34, 0x41, 0xBF, 0xE9,
        0xF2, 0x11, 0xBF, 0x05, 0xDB, 0xB2, 0x72, 0xA8, 0x29, 0xCC,
        0xBD, 0x93, 0xEB, 0x14, 0x5D, 0x2C, 0x6B, 0x84, 0x4E, 0x96,
        0x12, 0xB3, 0x38, 0xBA, 0x8A, 0x46, 0x7C, 0x36, 0xCB, 0xE9,
        0x97, 0x70, 0xC5, 0xC3, 0x85, 0xB5, 0x51, 0xA5, 0x8B, 0x39,
        0xA8, 0xEA, 0x47, 0xD3, 0xD5, 0x11, 0xC0, 0x6D, 0xE3, 0xE3,
        0x9E, 0x00, 0x4C, 0x65, 0x41, 0x9B, 0xF6, 0xD0, 0xAC, 0x26,
        0x88, 0x01, 0xFC, 0x3C, 0x26, 0x5F, 0x67, 0xF7, 0x77, 0xD7,
        0xAC, 0xC5, 0xCA, 0xBB, 0xD8, 0x70, 0x58, 0x41, 0xF5, 0xF1,
        0x21, 0x3B, 0x15, 0xD5, 0x31, 0xF2, 0xC4, 0x8E, 0x0C, 0x38,
        0x01, 0x93, 0xD3, 0x64, 0x63, 0x57, 0xDC, 0x31, 0xE5, 0xFD,
        0x9C, 0x2B, 0xA6, 0xDE, 0x15, 0xB2, 0xC8, 0x8D, 0x65, 0x71,
        0x2E, 0xED, 0xF9, 0x1D, 0x2D, 0xA1, 0x17, 0xDD, 0xA3, 0xDA,
        0xF3, 0x10, 0x81, 0x40, 0xFA, 0x4F, 0x49, 0xB0, 0xDA, 0x16,
        0x64, 0xBE, 0x6F, 0xC5, 0x05, 0xCE, 0xC4, 0x4F, 0x67, 0x80,
        0xB3, 0x8A, 0x81, 0x17, 0xEB, 0xF9, 0x6F, 0x6D, 0x9F, 0x7F,
        0xDE, 0xEE, 0x08, 0xB8, 0xFA, 0x81, 0x68, 0x66, 0xD6, 0xC6,
        0x08, 0x50, 0xAB, 0xF0, 0x29, 0xDE, 0x6B, 0x1D, 0x50, 0x13,
        0x7F, 0x54, 0x31, 0x53, 0x89, 0x5F, 0x48, 0x72, 0x24, 0xD4,
        0xD2, 0x1D, 0x27, 0x7D, 0x74, 0xCF, 0x51, 0x17, 0xF0, 0xC5,
        0x6D, 0x3C, 0x3D, 0x6D, 0x0A, 0x8B, 0xDB, 0xEF, 0x02, 0xD8,
        0xC3, 0xCB, 0xCA, 0x21, 0xCA, 0xD6, 0x9C, 0x18, 0x9E, 0x92,
        0xBE, 0x6E, 0xE2, 0x16, 0x5E, 0x89, 0x9B, 0xAD, 0xD4, 0x04,
        0x5A, 0x24, 0x5A, 0x3F, 0x7C, 0x12, 0xAC, 0xB4, 0x71, 0x51,
        0x25, 0x58, 0x74, 0xE4, 0xB2, 0xD4, 0x45, 0xFC, 0x5F, 0xCD,
        0x81, 0x8F, 0xE7, 0x96, 0x18, 0xD9, 0xE0, 0x97, 0x08, 0x45,
        0x36, 0xC3
};
static const int sizeof_dh_pub_key_der_2048 = sizeof(dh_pub_key_der_2048);

/* ./certs/statickeys/dh-ffdhe2048.der, 2048-bit */
static const unsigned char dh_ffdhe_statickey_der_2048[] =
{
        0x30, 0x82, 0x01, 0x3F, 0x02, 0x01, 0x00, 0x30, 0x82, 0x01,
        0x17, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01,
        0x03, 0x01, 0x30, 0x82, 0x01, 0x08, 0x02, 0x82, 0x01, 0x01,
        0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAD,
        0xF8, 0x54, 0x58, 0xA2, 0xBB, 0x4A, 0x9A, 0xAF, 0xDC, 0x56,
        0x20, 0x27, 0x3D, 0x3C, 0xF1, 0xD8, 0xB9, 0xC5, 0x83, 0xCE,
        0x2D, 0x36, 0x95, 0xA9, 0xE1, 0x36, 0x41, 0x14, 0x64, 0x33,
        0xFB, 0xCC, 0x93, 0x9D, 0xCE, 0x24, 0x9B, 0x3E, 0xF9, 0x7D,
        0x2F, 0xE3, 0x63, 0x63, 0x0C, 0x75, 0xD8, 0xF6, 0x81, 0xB2,
        0x02, 0xAE, 0xC4, 0x61, 0x7A, 0xD3, 0xDF, 0x1E, 0xD5, 0xD5,
        0xFD, 0x65, 0x61, 0x24, 0x33, 0xF5, 0x1F, 0x5F, 0x06, 0x6E,
        0xD0, 0x85, 0x63, 0x65, 0x55, 0x3D, 0xED, 0x1A, 0xF3, 0xB5,
        0x57, 0x13, 0x5E, 0x7F, 0x57, 0xC9, 0x35, 0x98, 0x4F, 0x0C,
        0x70, 0xE0, 0xE6, 0x8B, 0x77, 0xE2, 0xA6, 0x89, 0xDA, 0xF3,
        0xEF, 0xE8, 0x72, 0x1D, 0xF1, 0x58, 0xA1, 0x36, 0xAD, 0xE7,
        0x35, 0x30, 0xAC, 0xCA, 0x4F, 0x48, 0x3A, 0x79, 0x7A, 0xBC,
        0x0A, 0xB1, 0x82, 0xB3, 0x24, 0xFB, 0x61, 0xD1, 0x08, 0xA9,
        0x4B, 0xB2, 0xC8, 0xE3, 0xFB, 0xB9, 0x6A, 0xDA, 0xB7, 0x60,
        0xD7, 0xF4, 0x68, 0x1D, 0x4F, 0x42, 0xA3, 0xDE, 0x39, 0x4D,
        0xF4, 0xAE, 0x56, 0xED, 0xE7, 0x63, 0x72, 0xBB, 0x19, 0x0B,
        0x07, 0xA7, 0xC8, 0xEE, 0x0A, 0x6D, 0x70, 0x9E, 0x02, 0xFC,
        0xE1, 0xCD, 0xF7, 0xE2, 0xEC, 0xC0, 0x34, 0x04, 0xCD, 0x28,
        0x34, 0x2F, 0x61, 0x91, 0x72, 0xFE, 0x9C, 0xE9, 0x85, 0x83,
        0xFF, 0x8E, 0x4F, 0x12, 0x32, 0xEE, 0xF2, 0x81, 0x83, 0xC3,
        0xFE, 0x3B, 0x1B, 0x4C, 0x6F, 0xAD, 0x73, 0x3B, 0xB5, 0xFC,
        0xBC, 0x2E, 0xC2, 0x20, 0x05, 0xC5, 0x8E, 0xF1, 0x83, 0x7D,
        0x16, 0x83, 0xB2, 0xC6, 0xF3, 0x4A, 0x26, 0xC1, 0xB2, 0xEF,
        0xFA, 0x88, 0x6B, 0x42, 0x38, 0x61, 0x28, 0x5C, 0x97, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x02, 0x01, 0x02,
        0x04, 0x1F, 0x02, 0x1D, 0x5C, 0xFB, 0x86, 0xF7, 0xFA, 0x51,
        0x02, 0x79, 0x71, 0x9E, 0xC0, 0x29, 0x98, 0x03, 0xCF, 0x3E,
        0x65, 0x46, 0xF6, 0x34, 0xB8, 0xB0, 0xC1, 0x55, 0x3A, 0xF7,
        0xC8, 0x43, 0xB8
};
static const int sizeof_dh_ffdhe_statickey_der_2048 = sizeof(dh_ffdhe_statickey_der_2048);

/* ./certs/statickeys/dh-ffdhe2048-pub.der, 2048-bit */
static const unsigned char dh_ffdhe_pub_statickey_der_2048[] =
{
        0x30, 0x82, 0x02, 0x24, 0x30, 0x82, 0x01, 0x17, 0x06, 0x09,
        0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x03, 0x01, 0x30,
        0x82, 0x01, 0x08, 0x02, 0x82, 0x01, 0x01, 0x00, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xAD, 0xF8, 0x54, 0x58,
        0xA2, 0xBB, 0x4A, 0x9A, 0xAF, 0xDC, 0x56, 0x20, 0x27, 0x3D,
        0x3C, 0xF1, 0xD8, 0xB9, 0xC5, 0x83, 0xCE, 0x2D, 0x36, 0x95,
        0xA9, 0xE1, 0x36, 0x41, 0x14, 0x64, 0x33, 0xFB, 0xCC, 0x93,
        0x9D, 0xCE, 0x24, 0x9B, 0x3E, 0xF9, 0x7D, 0x2F, 0xE3, 0x63,
        0x63, 0x0C, 0x75, 0xD8, 0xF6, 0x81, 0xB2, 0x02, 0xAE, 0xC4,
        0x61, 0x7A, 0xD3, 0xDF, 0x1E, 0xD5, 0xD5, 0xFD, 0x65, 0x61,
        0x24, 0x33, 0xF5, 0x1F, 0x5F, 0x06, 0x6E, 0xD0, 0x85, 0x63,
        0x65, 0x55, 0x3D, 0xED, 0x1A, 0xF3, 0xB5, 0x57, 0x13, 0x5E,
        0x7F, 0x57, 0xC9, 0x35, 0x98, 0x4F, 0x0C, 0x70, 0xE0, 0xE6,
        0x8B, 0x77, 0xE2, 0xA6, 0x89, 0xDA, 0xF3, 0xEF, 0xE8, 0x72,
        0x1D, 0xF1, 0x58, 0xA1, 0x36, 0xAD, 0xE7, 0x35, 0x30, 0xAC,
        0xCA, 0x4F, 0x48, 0x3A, 0x79, 0x7A, 0xBC, 0x0A, 0xB1, 0x82,
        0xB3, 0x24, 0xFB, 0x61, 0xD1, 0x08, 0xA9, 0x4B, 0xB2, 0xC8,
        0xE3, 0xFB, 0xB9, 0x6A, 0xDA, 0xB7, 0x60, 0xD7, 0xF4, 0x68,
        0x1D, 0x4F, 0x42, 0xA3, 0xDE, 0x39, 0x4D, 0xF4, 0xAE, 0x56,
        0xED, 0xE7, 0x63, 0x72, 0xBB, 0x19, 0x0B, 0x07, 0xA7, 0xC8,
        0xEE, 0x0A, 0x6D, 0x70, 0x9E, 0x02, 0xFC, 0xE1, 0xCD, 0xF7,
        0xE2, 0xEC, 0xC0, 0x34, 0x04, 0xCD, 0x28, 0x34, 0x2F, 0x61,
        0x91, 0x72, 0xFE, 0x9C, 0xE9, 0x85, 0x83, 0xFF, 0x8E, 0x4F,
        0x12, 0x32, 0xEE, 0xF2, 0x81, 0x83, 0xC3, 0xFE, 0x3B, 0x1B,
        0x4C, 0x6F, 0xAD, 0x73, 0x3B, 0xB5, 0xFC, 0xBC, 0x2E, 0xC2,
        0x20, 0x05, 0xC5, 0x8E, 0xF1, 0x83, 0x7D, 0x16, 0x83, 0xB2,
        0xC6, 0xF3, 0x4A, 0x26, 0xC1, 0xB2, 0xEF, 0xFA, 0x88, 0x6B,
        0x42, 0x38, 0x61, 0x28, 0x5C, 0x97, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0x02, 0x01, 0x02, 0x03, 0x82, 0x01,
        0x05, 0x00, 0x02, 0x82, 0x01, 0x00, 0x4D, 0x3F, 0x4C, 0xE4,
        0x6C, 0x46, 0x71, 0xFF, 0x61, 0x02, 0x73, 0x0B, 0x65, 0x4F,
        0x46, 0xE7, 0x2E, 0x0F, 0x9E, 0x41, 0x04, 0x55, 0x41, 0x51,
        0x89, 0xA9, 0x5E, 0x84, 0xBC, 0x7A, 0x0A, 0x35, 0x34, 0x15,
        0xBE, 0xB1, 0xC3, 0x1E, 0xCD, 0xC7, 0x5A, 0x17, 0x98, 0x5F,
        0xFF, 0x96, 0x3D, 0x1B, 0x9B, 0xFA, 0xCC, 0x1F, 0x1E, 0xA7,
        0x22, 0x8B, 0x95, 0xEE, 0x2B, 0xD4, 0x74, 0xE2, 0x63, 0xE2,
        0xFD, 0x9C, 0xAA, 0x4F, 0xBE, 0x64, 0x69, 0x83, 0x31, 0x5E,
        0x90, 0x5C, 0x85, 0x5D, 0xB2, 0x99, 0xE0, 0x05, 0xB5, 0xF2,
        0xB6, 0x19, 0xCD, 0x06, 0xCD, 0xA8, 0xB3, 0x59, 0x8D, 0x87,
        0x84, 0x7F, 0x8F, 0x09, 0xEE, 0xBD, 0x61, 0x6F, 0xC5, 0xCD,
        0xB8, 0x9C, 0xA7, 0x15, 0x97, 0xD0, 0x44, 0x2E, 0x2D, 0x2E,
        0x32, 0x20, 0xC5, 0x3B, 0xDF, 0x50, 0x6F, 0x8D, 0x17, 0xB8,
        0x84, 0xA3, 0x67, 0x24, 0x7E, 0xA3, 0xEC, 0x8C, 0x08, 0x78,
        0x6C, 0x25, 0x88, 0x02, 0xD3, 0xA0, 0x68, 0xCE, 0x1A, 0x25,
        0x46, 0x2E, 0x9B, 0x4C, 0x82, 0xF4, 0xEB, 0xB3, 0xC9, 0x1D,
        0x17, 0x0C, 0x92, 0x94, 0x54, 0x0F, 0xB5, 0xDF, 0x3F, 0x71,
        0x45, 0x33, 0x97, 0x4D, 0x87, 0x1A, 0x4B, 0x40, 0x0A, 0x71,
        0xB4, 0x19, 0xBE, 0x33, 0xBD, 0xCF, 0xF3, 0x15, 0x63, 0x06,
        0x37, 0x5C, 0xA1, 0x9D, 0xC0, 0xE7, 0xA7, 0x8B, 0xD3, 0xA2,
        0x6B, 0x3F, 0xF1, 0x42, 0xC8, 0x36, 0x6B, 0x0A, 0x43, 0x8B,
        0xE9, 0xD0, 0x8C, 0xCD, 0x0F, 0x31, 0xCD, 0x22, 0xE7, 0x39,
        0xC8, 0x8D, 0xEB, 0x5D, 0x91, 0x8B, 0x06, 0x6E, 0x1F, 0x07,
        0xDB, 0xAB, 0x2B, 0x4F, 0x85, 0xF8, 0xBB, 0x55, 0xE3, 0xBD,
        0x70, 0x51, 0x5C, 0x73, 0x66, 0x5D, 0xD1, 0xB6, 0xF8, 0x44,
        0x10, 0xE0, 0xF2, 0x09, 0x1C, 0x6D, 0x02, 0x5D, 0xFC, 0x7A,
        0x08, 0x82
};
static const int sizeof_dh_ffdhe_pub_statickey_der_2048 = sizeof(dh_ffdhe_pub_statickey_der_2048);

/* ./certs/dsa-pubkey-2048.der, 2048-bit */
static const unsigned char dsa_pub_key_der_2048[] =
{
        0x30, 0x82, 0x03, 0x47, 0x30, 0x82, 0x02, 0x39, 0x06, 0x07,
        0x2A, 0x86, 0x48, 0xCE, 0x38, 0x04, 0x01, 0x30, 0x82, 0x02,
        0x2C, 0x02, 0x82, 0x01, 0x01, 0x00, 0xEB, 0x7E, 0x2C, 0x97,
        0x36, 0x67, 0x0E, 0x73, 0x9A, 0xAC, 0xFD, 0xB1, 0x19, 0x03,
        0x52, 0x61, 0x25, 0x12, 0xB2, 0x37, 0x3D, 0xEA, 0xCA, 0x80,
        0x07, 0x5D, 0x2D, 0x33, 0xA2, 0x4E, 0x6B, 0xB7, 0x62, 0xF8,
        0x87, 0x4D, 0x4B, 0x20, 0xDA, 0xEA, 0x6A, 0x96, 0x13, 0xB7,
        0xB9, 0x49, 0xC0, 0x86, 0x14, 0x71, 0xCD, 0x8C, 0x60, 0x61,
        0x94, 0x71, 0x89, 0x95, 0x1A, 0x0F, 0x38, 0xCC, 0x9C, 0x1F,
        0x20, 0xE5, 0xD0, 0x65, 0x75, 0xCD, 0xFE, 0x24, 0x29, 0xE6,
        0x60, 0x97, 0x74, 0xEC, 0x4C, 0x42, 0xE8, 0xBA, 0xE9, 0xC2,
        0xF7, 0xCB, 0x9B, 0xEA, 0x55, 0xD8, 0x40, 0x50, 0x2E, 0xCF,
        0xCD, 0x41, 0x01, 0xA9, 0xE5, 0x29, 0xCA, 0xC3, 0x36, 0x58,
        0x7E, 0x2E, 0x11, 0x96, 0x87, 0xC6, 0xFA, 0xE1, 0x27, 0x53,
        0x3D, 0x60, 0x93, 0x7B, 0xAD, 0xEE, 0xE7, 0xD4, 0xDC, 0xD6,
        0x03, 0x16, 0x92, 0xD4, 0x51, 0x0C, 0xFD, 0xA9, 0x01, 0x3E,
        0x6E, 0x27, 0x67, 0x6E, 0x9F, 0x29, 0x63, 0xFD, 0x51, 0x82,
        0x79, 0x83, 0x2B, 0xCB, 0x12, 0xCD, 0x50, 0x92, 0xAC, 0x16,
        0xC9, 0xEA, 0x9E, 0x68, 0x9E, 0x4B, 0xE1, 0x63, 0xB4, 0x80,
        0xE4, 0xDF, 0x75, 0xBC, 0x27, 0xD1, 0x76, 0x03, 0x48, 0x98,
        0x1D, 0xE3, 0x29, 0x8A, 0x99, 0x59, 0xF3, 0x75, 0x5B, 0xD9,
        0xAC, 0x59, 0x11, 0x52, 0x2F, 0xE0, 0x91, 0x55, 0xB0, 0xF2,
        0x5F, 0x0A, 0xF8, 0xD2, 0x7A, 0xDD, 0x8D, 0xE9, 0x92, 0xE2,
        0xF3, 0xF7, 0x4A, 0xB1, 0x50, 0xD7, 0xFE, 0x07, 0x8D, 0x27,
        0x7D, 0x08, 0x6F, 0x08, 0x7E, 0x25, 0x19, 0x0D, 0xDE, 0x11,
        0xD1, 0x63, 0x31, 0x84, 0x18, 0x25, 0xBE, 0x7D, 0x64, 0x77,
        0xDB, 0x4A, 0x20, 0xC5, 0x51, 0x75, 0xD8, 0xB1, 0x1B, 0xDF,
        0x91, 0x7F, 0xFC, 0x74, 0xBA, 0x9D, 0xD1, 0xFA, 0x8D, 0xBD,
        0x59, 0xFD, 0x02, 0x21, 0x00, 0xFA, 0xF7, 0x62, 0x9A, 0x62,
        0x19, 0x64, 0x6D, 0xC1, 0xF3, 0xC0, 0x9B, 0xAC, 0x90, 0x28,
        0xEA, 0xA1, 0x83, 0xF9, 0xC8, 0xED, 0x31, 0xEE, 0x33, 0x1D,
        0x35, 0x22, 0x00, 0x2B, 0x12, 0x84, 0xFF, 0x02, 0x82, 0x01,
        0x00, 0x73, 0xC9, 0xED, 0x1F, 0xBC, 0xC7, 0xC4, 0xEF, 0x46,
        0x03, 0xD1, 0x72, 0xC3, 0xE5, 0x29, 0xB0, 0x9A, 0x95, 0x13,
        0x5B, 0x4E, 0x59, 0x57, 0x0F, 0x80, 0xEB, 0x74, 0x87, 0x11,
        0x1B, 0xC8, 0x11, 0xB6, 0x97, 0x4C, 0x48, 0x50, 0x3A, 0xB8,
        0x2C, 0x28, 0xF3, 0xB0, 0x9C, 0x7C, 0x3D, 0xFF, 0x8B, 0x43,
        0x43, 0x30, 0x85, 0x5F, 0x97, 0xD2, 0x68, 0x85, 0x35, 0x2E,
        0xD4, 0x61, 0xF6, 0x3E, 0x05, 0xEC, 0xCD, 0x60, 0x13, 0xE2,
        0x16, 0x02, 0x7C, 0x8B, 0x21, 0xCE, 0x36, 0x71, 0xC4, 0xED,
        0x0B, 0x47, 0x76, 0x83, 0x23, 0x2F, 0x98, 0xA4, 0x84, 0x98,
        0x9C, 0xFB, 0xD0, 0xA8, 0xD9, 0xB9, 0xE3, 0xD7, 0x32, 0xD9,
        0xB5, 0x9E, 0x82, 0x93, 0xD0, 0x55, 0x74, 0x5F, 0xDA, 0x87,
        0x91, 0x90, 0x0F, 0x85, 0x74, 0x1A, 0x32, 0x76, 0x4F, 0xCC,
        0x2A, 0x18, 0x11, 0x5B, 0xB4, 0x78, 0x93, 0xB6, 0xE5, 0xF0,
        0xC6, 0x71, 0xE8, 0xD7, 0x31, 0x19, 0x91, 0x27, 0x71, 0x5A,
        0x02, 0x1A, 0x1A, 0x3A, 0x55, 0x95, 0xFF, 0xF8, 0xED, 0xD3,
        0xE1, 0xAE, 0x8A, 0x1D, 0xFF, 0x53, 0x63, 0x79, 0x13, 0xA1,
        0xAD, 0x0A, 0x68, 0x67, 0x43, 0xB2, 0x5B, 0xD5, 0x36, 0xD4,
        0x84, 0xD0, 0xCD, 0x34, 0x82, 0x84, 0xA4, 0x89, 0xAE, 0xA1,
        0x66, 0x57, 0x89, 0x6F, 0xDC, 0x0C, 0x3B, 0x48, 0x14, 0x7C,
        0xCC, 0x63, 0x7C, 0x83, 0x93, 0x55, 0x7D, 0xB4, 0xF3, 0x34,
        0x66, 0x72, 0x85, 0xF5, 0x8D, 0xEF, 0x90, 0x1A, 0x66, 0xF8,
        0x3B, 0xC6, 0xA4, 0x59, 0xB8, 0x25, 0x4E, 0x5D, 0x84, 0xED,
        0x7C, 0x1C, 0xDD, 0x35, 0xA6, 0xBA, 0xED, 0x3B, 0xD6, 0x49,
        0xE6, 0x5A, 0xD1, 0xF8, 0xEA, 0x96, 0x75, 0x92, 0xCF, 0x05,
        0x52, 0x05, 0x3D, 0x78, 0x09, 0xCF, 0xCD, 0xE2, 0x1A, 0x99,
        0xEB, 0x5E, 0xFA, 0x27, 0x73, 0x89, 0x15, 0x03, 0x82, 0x01,
        0x06, 0x00, 0x02, 0x82, 0x01, 0x01, 0x00, 0xC2, 0x35, 0x2D,
        0xEC, 0x83, 0x83, 0x6C, 0x73, 0x13, 0x9E, 0x52, 0x7C, 0x74,
        0xC8, 0x7B, 0xEE, 0xDF, 0x39, 0xC0, 0x33, 0xCD, 0x9F, 0xB2,
        0x22, 0x64, 0x9F, 0xC5, 0xE9, 0xFF, 0xF7, 0x09, 0x47, 0x79,
        0x13, 0x96, 0x77, 0x25, 0xF3, 0x5D, 0xAA, 0x9F, 0x97, 0x67,
        0x62, 0xBC, 0x94, 0x1D, 0xAE, 0x22, 0x7E, 0x08, 0x03, 0xBD,
        0x7E, 0x34, 0x29, 0xCB, 0x62, 0xB7, 0x82, 0x1D, 0xE2, 0xFA,
        0x05, 0xC6, 0xC1, 0x68, 0xE7, 0x01, 0x27, 0x63, 0x51, 0x3E,
        0x37, 0x59, 0x42, 0x92, 0x4F, 0x99, 0x60, 0xFD, 0x63, 0x94,
        0xB7, 0xD0, 0xEE, 0xC1, 0xA0, 0xA5, 0x01, 0x74, 0x4D, 0x0E,
        0x14, 0xB2, 0xE2, 0x2C, 0xE7, 0x82, 0x0A, 0x23, 0xC7, 0x39,
        0x45, 0x40, 0xE9, 0xE9, 0x9D, 0x36, 0xE0, 0x52, 0x03, 0x99,
        0xDC, 0x87, 0x7D, 0x6A, 0x90, 0xE4, 0xDD, 0xA9, 0xC2, 0x57,
        0x90, 0xD6, 0xCA, 0xB4, 0x15, 0x80, 0xEE, 0x00, 0xCB, 0x2A,
        0xC9, 0x59, 0x4C, 0xA7, 0x7D, 0x33, 0x0A, 0x3E, 0x4A, 0x76,
        0xEA, 0x27, 0x89, 0xD8, 0x1A, 0xEA, 0x7E, 0xDB, 0x13, 0x92,
        0x93, 0x6A, 0x57, 0x9B, 0x33, 0xFD, 0xCE, 0x09, 0x0A, 0xB0,
        0x35, 0x24, 0xE4, 0x7D, 0xD8, 0x9D, 0xFF, 0x80, 0x65, 0x0F,
        0x61, 0xF7, 0xF7, 0xED, 0x8B, 0xD5, 0x8F, 0xBF, 0xB3, 0x22,
        0x20, 0x39, 0x89, 0x83, 0xB8, 0x83, 0x96, 0x32, 0x20, 0xAD,
        0xA1, 0x5D, 0x73, 0x8F, 0xE3, 0x27, 0xD9, 0x5D, 0xDB, 0x00,
        0x27, 0xF2, 0xBE, 0x89, 0x13, 0xE2, 0x97, 0x79, 0x10, 0x27,
        0x3D, 0xD8, 0x05, 0x96, 0x59, 0x6E, 0xA0, 0xC1, 0x6F, 0x99,
        0x4F, 0x28, 0xFA, 0xA6, 0x0B, 0x5C, 0x16, 0xEE, 0xB0, 0x98,
        0x8A, 0x06, 0x4A, 0xB0, 0x02, 0x2A, 0x6D, 0xCC, 0xE2, 0xC8,
        0x11, 0xF9, 0x1B, 0xF1, 0x3C, 0x68, 0xDF, 0xC2, 0xF4, 0x98,
        0x5F, 0x6C, 0xC8
};
static const int sizeof_dsa_pub_key_der_2048 = sizeof(dsa_pub_key_der_2048);

/* ./certs/dsa2048.der, 2048-bit */
static const unsigned char dsa_key_der_2048[] =
{
        0x30, 0x82, 0x03, 0x3F, 0x02, 0x01, 0x00, 0x02, 0x82, 0x01,
        0x01, 0x00, 0xCC, 0x8E, 0xC9, 0xA0, 0xD5, 0x9A, 0x27, 0x1C,
        0xDA, 0x52, 0xDF, 0xC7, 0xC0, 0xE6, 0x06, 0xA4, 0x3E, 0x8A,
        0x66, 0x49, 0xD0, 0x59, 0x33, 0x51, 0x69, 0xC4, 0x9C, 0x5E,
        0x64, 0x85, 0xC7, 0xF1, 0xAB, 0xD5, 0xD9, 0x62, 0xAC, 0xFD,
        0xA1, 0xE0, 0x1B, 0x57, 0xFF, 0x96, 0xEF, 0x0C, 0x9F, 0xC8,
        0x44, 0x87, 0xEB, 0x5C, 0x91, 0xD0, 0x46, 0x42, 0x09, 0x50,
        0x6A, 0x23, 0xCB, 0x89, 0x6F, 0x55, 0xE9, 0x6A, 0x11, 0xA9,
        0xA8, 0x32, 0xAB, 0x33, 0x0D, 0x51, 0xB5, 0x79, 0x51, 0xB4,
        0xAB, 0xA2, 0x25, 0x11, 0x8D, 0xE5, 0x24, 0xBE, 0xD8, 0xF1,
        0x9D, 0x4E, 0x12, 0x6F, 0xAC, 0x44, 0x54, 0x80, 0xA9, 0xB4,
        0x81, 0x68, 0x4E, 0x44, 0x0E, 0xB8, 0x39, 0xF3, 0xBE, 0x83,
        0x08, 0x74, 0xA2, 0xC6, 0x7A, 0xD7, 0x6A, 0x7D, 0x0A, 0x88,
        0x57, 0x83, 0x48, 0xDC, 0xCF, 0x5E, 0x6F, 0xEE, 0x68, 0x0C,
        0xF7, 0xFF, 0x03, 0x04, 0x90, 0xAA, 0xF7, 0x07, 0x98, 0xF8,
        0x67, 0x5A, 0x83, 0x23, 0x66, 0x47, 0x60, 0xC3, 0x43, 0x6E,
        0x03, 0x91, 0xAC, 0x28, 0x66, 0xCB, 0xF0, 0xD3, 0x05, 0xC8,
        0x09, 0x97, 0xB5, 0xAE, 0x01, 0x5E, 0x80, 0x3B, 0x9D, 0x4F,
        0xDE, 0x3E, 0x94, 0xFE, 0xCB, 0x82, 0xB0, 0xB1, 0xFC, 0x91,
        0x8B, 0x1D, 0x8A, 0xEE, 0xC6, 0x06, 0x1F, 0x37, 0x91, 0x48,
        0xD2, 0xF8, 0x6C, 0x5D, 0x60, 0x13, 0x83, 0xA7, 0x81, 0xAC,
        0xCA, 0x8D, 0xD0, 0x6A, 0x04, 0x0A, 0xEA, 0x3E, 0x22, 0x4E,
        0x13, 0xF1, 0x0D, 0xBB, 0x60, 0x6B, 0xCD, 0xBC, 0x5C, 0x87,
        0xA3, 0x67, 0x2B, 0x42, 0xA1, 0x9F, 0xCD, 0x39, 0x58, 0xBE,
        0x55, 0xB1, 0x93, 0x84, 0xCE, 0xB2, 0x10, 0x4E, 0xE4, 0xC3,
        0x9F, 0xB2, 0x53, 0x61, 0x01, 0x29, 0xAA, 0x96, 0xCB, 0x20,
        0x60, 0x42, 0x1D, 0xBA, 0x75, 0x4B, 0x63, 0xC1, 0x02, 0x15,
        0x00, 0xE7, 0xA5, 0x39, 0xD4, 0x6A, 0x37, 0x5E, 0x95, 0x06,
        0x39, 0x07, 0x77, 0x0A, 0xEB, 0xA0, 0x03, 0xEB, 0x78, 0x82,
        0x9B, 0x02, 0x82, 0x01, 0x01, 0x00, 0x9A, 0xD4, 0x4C, 0x71,
        0x2F, 0xEC, 0xFA, 0x32, 0xB2, 0x80, 0x7E, 0x61, 0x4A, 0x6B,
        0x5F, 0x18, 0x76, 0x43, 0xC3, 0x69, 0xBA, 0x41, 0xC7, 0xA7,
        0x1D, 0x79, 0x01, 0xEC, 0xAF, 0x34, 0x87, 0x67, 0x4F, 0x29,
        0x80, 0xA8, 0x3B, 0x87, 0xF6, 0xE8, 0xA1, 0xE8, 0xCD, 0x1B,
        0x1C, 0x86, 0x38, 0xF6, 0xD1, 0x0C, 0x46, 0x2E, 0xC8, 0xE0,
        0xC9, 0x30, 0x26, 0xD5, 0x2C, 0x7F, 0xC1, 0x08, 0xBF, 0xCC,
        0x5A, 0x82, 0x8E, 0xD4, 0xD4, 0x49, 0xAA, 0xA2, 0xFA, 0xE6,
        0xC1, 0x9D, 0xF0, 0xD9, 0x96, 0xB0, 0xFF, 0x0C, 0x5B, 0x33,
        0x8E, 0x06, 0xDD, 0x9D, 0x28, 0xA9, 0xE9, 0x80, 0x41, 0x3B,
        0xD8, 0x7A, 0x94, 0x21, 0x8F, 0x56, 0xF1, 0xA2, 0xB4, 0x2B,
        0x89, 0x1C, 0x74, 0xFF, 0x7E, 0x91, 0xDC, 0x1F, 0x91, 0x13,
        0x98, 0xAF, 0xC7, 0x06, 0xD2, 0x4C, 0x90, 0xA2, 0xBD, 0xDA,
        0x16, 0xBA, 0x65, 0xB0, 0x2D, 0x68, 0x87, 0x3C, 0x6E, 0x25,
        0x8D, 0x90, 0xC7, 0xBC, 0x0D, 0xA9, 0x43, 0x03, 0xC9, 0xBE,
        0xCF, 0x85, 0x6F, 0xDB, 0x07, 0x7B, 0x8C, 0xF8, 0xB1, 0xC2,
        0x49, 0x10, 0x69, 0x63, 0x56, 0x37, 0xC5, 0x30, 0xD2, 0xFB,
        0x71, 0x9A, 0xE8, 0x82, 0x07, 0x2E, 0x3E, 0x95, 0x50, 0xF3,
        0x73, 0xCF, 0x34, 0x5B, 0xD5, 0xAB, 0x02, 0x15, 0xF2, 0xCC,
        0xD7, 0x52, 0xC5, 0x28, 0xD8, 0x41, 0x19, 0x55, 0x6F, 0xB8,
        0x5F, 0xF1, 0x99, 0xB3, 0xC7, 0xD9, 0xB3, 0x71, 0xF4, 0x2D,
        0xDF, 0x22, 0x59, 0x35, 0x86, 0xDB, 0x39, 0xCA, 0x1B, 0x4D,
        0x35, 0x90, 0x19, 0x6B, 0x31, 0xE3, 0xC8, 0xC6, 0x09, 0xBF,
        0x7C, 0xED, 0x01, 0xB4, 0xB2, 0xF5, 0x6E, 0xDA, 0x63, 0x41,
        0x3C, 0xE6, 0x3A, 0x72, 0x2D, 0x65, 0x48, 0xF6, 0x07, 0xCD,
        0x92, 0x84, 0x8B, 0x1D, 0xA7, 0x31, 0x6B, 0xD6, 0xF0, 0xFB,
        0xD9, 0xF4, 0x02, 0x82, 0x01, 0x00, 0x66, 0x4B, 0xBB, 0xB7,
        0xC9, 0x48, 0x95, 0x0D, 0x5A, 0xA6, 0x2D, 0xA1, 0x7F, 0xDF,
        0x1F, 0x67, 0x6D, 0xED, 0x52, 0x4B, 0x16, 0x6C, 0x17, 0xC6,
        0xAE, 0xF8, 0x6A, 0xC4, 0x57, 0xED, 0x2F, 0xB3, 0xF0, 0x2A,
        0x55, 0xAB, 0xBA, 0xCA, 0xEA, 0x17, 0xE8, 0x35, 0x7C, 0xE5,
        0x31, 0x0D, 0x4A, 0x95, 0xFC, 0x43, 0x6F, 0x97, 0x3C, 0x5C,
        0x67, 0xAC, 0xBE, 0x67, 0x7F, 0xE9, 0x4E, 0xAA, 0x48, 0xB3,
        0x92, 0xA1, 0x76, 0x75, 0xEA, 0x04, 0x34, 0x7F, 0x87, 0x33,
        0x2D, 0x24, 0xB6, 0x29, 0x97, 0xE3, 0x04, 0x77, 0x93, 0x89,
        0x13, 0xDB, 0x1B, 0x93, 0xB8, 0x2C, 0x90, 0x1A, 0x09, 0x3B,
        0x26, 0xD9, 0x59, 0xF3, 0x2A, 0x09, 0x58, 0xDC, 0xAC, 0x25,
        0xB4, 0xA9, 0x45, 0x3B, 0xA2, 0x3A, 0x6C, 0x61, 0x84, 0xBF,
        0x68, 0xD4, 0xEA, 0x9B, 0xC5, 0x29, 0x48, 0x60, 0x15, 0x10,
        0x35, 0x2C, 0x44, 0x1D, 0xB5, 0x9A, 0xEE, 0xAC, 0xC1, 0x68,
        0xE8, 0x47, 0xB7, 0x41, 0x34, 0x39, 0x9A, 0xF8, 0xA5, 0x20,
        0xE9, 0x24, 0xC4, 0x2C, 0x58, 0x3F, 0x4C, 0x41, 0x30, 0x3A,
        0x14, 0x6E, 0x8D, 0xEA, 0xAD, 0xBA, 0x9B, 0x43, 0xD3, 0x98,
        0x2F, 0x83, 0xD8, 0x14, 0x67, 0xE8, 0xF8, 0xD5, 0x4F, 0xAC,
        0xE0, 0x3B, 0xBF, 0xA7, 0x54, 0x16, 0x5E, 0x49, 0x64, 0x26,
        0x54, 0xA4, 0x6B, 0x69, 0x7C, 0xBA, 0x8A, 0x83, 0xD9, 0x2E,
        0x65, 0x0A, 0xA2, 0x27, 0xEF, 0x99, 0x99, 0x08, 0xD7, 0xB5,
        0x9F, 0xA0, 0x01, 0xEF, 0x7E, 0x17, 0xBF, 0x83, 0x6B, 0x2E,
        0xDD, 0xC0, 0x39, 0x38, 0x23, 0x68, 0xB4, 0x76, 0x6B, 0xE5,
        0xCA, 0xF7, 0x7C, 0xEE, 0xC0, 0x52, 0xE2, 0xDD, 0xAD, 0x59,
        0x3A, 0x42, 0x06, 0x45, 0xB0, 0xC7, 0xC1, 0x77, 0x05, 0xB2,
        0x0C, 0x32, 0x40, 0x46, 0xAA, 0xDA, 0x79, 0x77, 0x04, 0x71,
        0xDF, 0x7A, 0x02, 0x15, 0x00, 0x98, 0xEE, 0xB9, 0x51, 0x37,
        0x3E, 0x75, 0x13, 0x13, 0x06, 0x8F, 0x94, 0xD3, 0xE6, 0xE9,
        0x00, 0xCB, 0x62, 0x6D, 0x9A
};
static const int sizeof_dsa_key_der_2048 = sizeof(dsa_key_der_2048);

/* ./certs/rsa2048.der, 2048-bit */
static const unsigned char rsa_key_der_2048[] =
{
        0x30, 0x82, 0x04, 0xA3, 0x02, 0x01, 0x00, 0x02, 0x82, 0x01,
        0x01, 0x00, 0xE9, 0x8A, 0x5D, 0x15, 0xA4, 0xD4, 0x34, 0xB9,
        0x59, 0xA2, 0xDA, 0xAF, 0x74, 0xC8, 0xC9, 0x03, 0x26, 0x38,
        0xFA, 0x48, 0xFC, 0x4D, 0x30, 0x6E, 0xEA, 0x76, 0x89, 0xCE,
        0x4F, 0xF6, 0x87, 0xDE, 0x32, 0x3A, 0x46, 0x6E, 0x38, 0x12,
        0x58, 0x37, 0x22, 0x0D, 0x80, 0xAC, 0x2D, 0xAF, 0x2F, 0x12,
        0x3E, 0x62, 0x73, 0x60, 0x66, 0x68, 0x90, 0xB2, 0x6F, 0x47,
        0x17, 0x04, 0x2B, 0xCA, 0xB7, 0x26, 0xB7, 0x10, 0xC2, 0x13,
        0xF9, 0x7A, 0x62, 0x0A, 0x93, 0x32, 0x90, 0x42, 0x0D, 0x16,
        0x2E, 0xFA, 0xD7, 0x29, 0xD7, 0x9F, 0x54, 0xE4, 0xFC, 0x65,
        0x74, 0xF8, 0xF6, 0x43, 0x6B, 0x4E, 0x9E, 0x34, 0x7F, 0xCB,
        0x6B, 0x1C, 0x1A, 0xDE, 0x82, 0x81, 0xBF, 0x08, 0x5D, 0x3F,
        0xC0, 0xB6, 0xB1, 0xA8, 0xA5, 0x9C, 0x81, 0x70, 0xA7, 0x4E,
        0x32, 0x87, 0x15, 0x1C, 0x78, 0x0E, 0xF0, 0x18, 0xFE, 0xEB,
        0x4B, 0x37, 0x2B, 0xE9, 0xE1, 0xF7, 0xFA, 0x51, 0xC6, 0x58,
        0xB9, 0xD8, 0x06, 0x03, 0xED, 0xC0, 0x03, 0x18, 0x55, 0x8B,
        0x98, 0xFE, 0xB1, 0xF6, 0xD0, 0x3D, 0xFA, 0x63, 0xC0, 0x38,
        0x19, 0xC7, 0x00, 0xEF, 0x4D, 0x99, 0x60, 0xB4, 0xBA, 0xCE,
        0xE3, 0xCE, 0xD9, 0x6B, 0x2D, 0x76, 0x94, 0xFF, 0xFB, 0x77,
        0x18, 0x4A, 0xFE, 0x65, 0xF0, 0x0A, 0x91, 0x5C, 0x3B, 0x22,
        0x94, 0x85, 0xD0, 0x20, 0x18, 0x59, 0x2E, 0xA5, 0x33, 0x03,
        0xAC, 0x1B, 0x5F, 0x78, 0x32, 0x11, 0x25, 0xEE, 0x7F, 0x96,
        0x21, 0xA9, 0xD6, 0x76, 0x97, 0x8D, 0x66, 0x7E, 0xB2, 0x91,
        0xD0, 0x36, 0x2E, 0xA3, 0x1D, 0xBF, 0xF1, 0x85, 0xED, 0xC0,
        0x3E, 0x60, 0xB8, 0x5A, 0x9F, 0xAB, 0x80, 0xE0, 0xEA, 0x5D,
        0x5F, 0x75, 0x56, 0xC7, 0x4D, 0x51, 0x8E, 0xD4, 0x1F, 0x34,
        0xA6, 0x36, 0xF1, 0x30, 0x1F, 0x51, 0x99, 0x2F, 0x02, 0x03,
        0x01, 0x00, 0x01, 0x02, 0x82, 0x01, 0x00, 0x52, 0x11, 0x33,
        0x40, 0xC5, 0xD9, 0x64, 0x65, 0xB5, 0xE0, 0x0A, 0xA5, 0x19,
        0x8E, 0xED, 0x44, 0x54, 0x0C, 0x35, 0xB7, 0xAC, 0x21, 0x9B,
        0xE1, 0x7E, 0x37, 0x05, 0x9A, 0x20, 0x73, 0x6B, 0xAF, 0x63,
        0x4B, 0x23, 0x30, 0xDC, 0x37, 0x66, 0x14, 0x89, 0xBC, 0xE0,
        0xF8, 0xA0, 0x5D, 0x2D, 0x57, 0x65, 0xE0, 0xC6, 0xD6, 0x9B,
        0x66, 0x27, 0x62, 0xEC, 0xC3, 0xB8, 0x8C, 0xD8, 0xAE, 0xB5,
        0xC9, 0xBF, 0x0E, 0xFE, 0x84, 0x72, 0x68, 0xD5, 0x47, 0x0E,
        0x0E, 0xF8, 0xAE, 0x9D, 0x56, 0xAC, 0x4F, 0xAD, 0x88, 0xA0,
        0xA2, 0xF6, 0xFC, 0x38, 0xCD, 0x96, 0x5B, 0x5E, 0x7E, 0xB6,
        0x98, 0xBB, 0xF3, 0x8A, 0xEC, 0xFA, 0xC8, 0xB7, 0x90, 0x75,
        0xA0, 0x0E, 0x77, 0x6B, 0xFD, 0x59, 0x45, 0x5A, 0x0C, 0xFF,
        0x95, 0x8D, 0xCE, 0xFE, 0x9B, 0xF6, 0x19, 0x8E, 0x0B, 0xA1,
        0x0C, 0xEE, 0xC6, 0x79, 0xDD, 0x9D, 0x61, 0x85, 0x5C, 0x19,
        0x6C, 0x47, 0xCC, 0x08, 0xFF, 0xA5, 0x62, 0xDB, 0xE4, 0x2D,
        0x2D, 0xDD, 0x14, 0x67, 0xD6, 0x4A, 0x64, 0x2A, 0x66, 0x49,
        0x54, 0x9C, 0xE3, 0x85, 0x18, 0xE7, 0x31, 0x42, 0xE2, 0xD0,
        0x2C, 0x20, 0xA0, 0x74, 0x0F, 0x1F, 0x20, 0x89, 0xBA, 0xAB,
        0x80, 0xD8, 0x38, 0xD9, 0x46, 0x69, 0xBB, 0xEF, 0xCC, 0x8B,
        0xA1, 0x73, 0xA7, 0xF2, 0xE4, 0x38, 0x5D, 0xD6, 0x75, 0x9F,
        0x88, 0x0E, 0x56, 0xCD, 0xD8, 0x84, 0x59, 0x29, 0x73, 0xF5,
        0xA1, 0x79, 0xDA, 0x7A, 0x1F, 0xBF, 0x73, 0x83, 0xC0, 0x6D,
        0x9F, 0x8B, 0x34, 0x15, 0xC0, 0x6D, 0x69, 0x6A, 0x20, 0xE6,
        0x51, 0xCF, 0x45, 0x6E, 0xCC, 0x05, 0xC4, 0x3A, 0xC0, 0x9E,
        0xAA, 0xC1, 0x06, 0x2F, 0xAB, 0x99, 0x30, 0xE1, 0x6E, 0x9D,
        0x45, 0x7A, 0xFF, 0xA9, 0xCE, 0x70, 0xB8, 0x16, 0x1A, 0x0E,
        0x20, 0xFA, 0xC1, 0x02, 0x81, 0x81, 0x00, 0xFF, 0x30, 0x11,
        0xC2, 0x3C, 0x6B, 0xB4, 0xD6, 0x9E, 0x6B, 0xC1, 0x93, 0xD1,
        0x48, 0xCE, 0x80, 0x2D, 0xBE, 0xAF, 0xF7, 0xBA, 0xB2, 0xD7,
        0xC3, 0xC4, 0x53, 0x6E, 0x15, 0x02, 0xAA, 0x61, 0xB9, 0xEA,
        0x05, 0x9B, 0x79, 0x67, 0x0B, 0xCE, 0xD9, 0xFB, 0x98, 0x8C,
        0x1D, 0x6B, 0xF4, 0x5A, 0xA7, 0xA0, 0x5E, 0x54, 0x18, 0xE9,
        0x31, 0x44, 0x7C, 0xC7, 0x52, 0xD8, 0x6D, 0xA0, 0x3E, 0xD6,
        0x14, 0x2D, 0x7B, 0x15, 0x9D, 0x1E, 0x39, 0x87, 0x96, 0xDD,
        0xA8, 0x33, 0x55, 0x2A, 0x8E, 0x32, 0xC0, 0xC4, 0xE5, 0xB8,
        0xCB, 0xCD, 0x32, 0x8D, 0xAD, 0x7B, 0xE5, 0xC6, 0x7E, 0x4D,
        0x6F, 0xF3, 0xA4, 0xC5, 0xA6, 0x40, 0xBE, 0x90, 0x3A, 0x33,
        0x6A, 0x24, 0xB2, 0x80, 0x81, 0x12, 0xAC, 0xE3, 0x7B, 0x26,
        0x63, 0xCF, 0x88, 0xB9, 0xFF, 0x74, 0x23, 0x37, 0x52, 0xF0,
        0xC4, 0x27, 0x5D, 0x45, 0x1F, 0x02, 0x81, 0x81, 0x00, 0xEA,
        0x48, 0xA7, 0xDD, 0x73, 0x41, 0x56, 0x21, 0x15, 0xF7, 0x42,
        0x45, 0x4D, 0xA9, 0xE1, 0x66, 0x5B, 0xBD, 0x25, 0x7D, 0xF7,
        0xA8, 0x65, 0x13, 0xAE, 0x2D, 0x38, 0x11, 0xCD, 0x93, 0xFC,
        0x30, 0xA3, 0x2C, 0x44, 0xBB, 0xCF, 0xD0, 0x21, 0x8F, 0xFB,
        0xC1, 0xF9, 0xAD, 0x1D, 0xEE, 0x96, 0xCF, 0x97, 0x49, 0x60,
        0x53, 0x80, 0xA5, 0xA2, 0xF8, 0xEE, 0xB9, 0xD5, 0x77, 0x44,
        0xDD, 0xFD, 0x19, 0x2A, 0xF1, 0x81, 0xF4, 0xD9, 0x3C, 0xEC,
        0x73, 0xD0, 0x2A, 0xD8, 0x3C, 0x27, 0x87, 0x79, 0x12, 0x86,
        0xE7, 0x57, 0x0C, 0x59, 0xD1, 0x44, 0x55, 0xAE, 0xC3, 0x4D,
        0x42, 0xAD, 0xA9, 0xB3, 0x28, 0x61, 0xB4, 0x9C, 0xA6, 0x63,
        0xD3, 0x96, 0xB1, 0x75, 0x9F, 0x2A, 0x78, 0x99, 0xE3, 0x1E,
        0x71, 0x47, 0x39, 0xF4, 0x52, 0xE3, 0x66, 0xF1, 0xEB, 0x7F,
        0xEF, 0xC6, 0x81, 0x93, 0x4C, 0x99, 0xF1, 0x02, 0x81, 0x81,
        0x00, 0xC5, 0xB6, 0x20, 0x8C, 0x34, 0xF3, 0xDD, 0xF0, 0x4A,
        0x5D, 0x82, 0x65, 0x5C, 0x48, 0xE4, 0x75, 0x3A, 0xFB, 0xFA,
        0xAA, 0x1C, 0xE4, 0x63, 0x77, 0x31, 0xAC, 0xD2, 0x25, 0x45,
        0x23, 0x6D, 0x03, 0xF5, 0xE4, 0xD2, 0x48, 0x85, 0x26, 0x08,
        0xE5, 0xAA, 0xA0, 0xCE, 0x2E, 0x1D, 0x6D, 0xFC, 0xAE, 0xD2,
        0xF9, 0x42, 0x7E, 0xEA, 0x6D, 0x59, 0x7A, 0xB3, 0x93, 0xE4,
        0x4B, 0x4B, 0x54, 0x63, 0xD8, 0xCE, 0x44, 0x06, 0xC2, 0xEC,
        0x9F, 0xF6, 0x05, 0x55, 0x46, 0xF4, 0x3E, 0x8F, 0xF2, 0x0C,
        0x30, 0x7E, 0x5C, 0xDD, 0x88, 0x49, 0x3B, 0x59, 0xB9, 0x87,
        0xBC, 0xC6, 0xC5, 0x24, 0x8A, 0x10, 0x63, 0x21, 0x1F, 0x66,
        0x1A, 0x3E, 0xF4, 0x58, 0xD1, 0x6C, 0x0D, 0x40, 0xB2, 0xC0,
        0x1D, 0x63, 0x42, 0x0E, 0xC4, 0x56, 0x0E, 0xC0, 0xCC, 0xC2,
        0xD6, 0x66, 0x0E, 0xC4, 0xAB, 0xB5, 0x33, 0xF6, 0x51, 0x02,
        0x81, 0x80, 0x19, 0x7E, 0xE6, 0xA5, 0xB6, 0xD1, 0x39, 0x6A,
        0x48, 0x55, 0xAC, 0x24, 0x96, 0x9B, 0x12, 0x28, 0x6D, 0x7B,
        0x5C, 0x05, 0x25, 0x5A, 0x72, 0x05, 0x7E, 0x42, 0xF5, 0x83,
        0x1A, 0x78, 0x2C, 0x4D, 0xAE, 0xB4, 0x36, 0x96, 0xA9, 0xBA,
        0xE0, 0xAC, 0x26, 0x9D, 0xA9, 0x6A, 0x29, 0x83, 0xB9, 0x6D,
        0xC5, 0xEC, 0xFA, 0x4A, 0x9C, 0x09, 0x6A, 0x7E, 0xE4, 0x9B,
        0xDC, 0x9B, 0x2A, 0x27, 0x6E, 0x4F, 0xBA, 0xD8, 0xA5, 0x67,
        0xDB, 0xEC, 0x41, 0x5F, 0x29, 0x1C, 0x40, 0x83, 0xEB, 0x59,
        0x56, 0xD7, 0xA9, 0x4E, 0xAB, 0xAE, 0x70, 0x67, 0xD1, 0xA3,
        0xF1, 0x6C, 0xD7, 0x8F, 0x96, 0x0E, 0x8D, 0xAC, 0xAB, 0x55,
        0x58, 0x66, 0xD3, 0x1E, 0x47, 0x9B, 0xF0, 0x4C, 0xED, 0xF6,
        0x49, 0xE8, 0xE9, 0x7B, 0x32, 0x61, 0x20, 0x31, 0x95, 0x05,
        0xB2, 0xF6, 0x09, 0xEA, 0x32, 0x14, 0x0F, 0xCF, 0x9A, 0x41,
        0x02, 0x81, 0x80, 0x77, 0x3F, 0xB6, 0x14, 0x8D, 0xC5, 0x13,
        0x08, 0x7E, 0xC9, 0xC4, 0xEA, 0xD4, 0xBA, 0x0D, 0xA4, 0x9E,
        0xB3, 0x6E, 0xDE, 0x1A, 0x7A, 0xF8, 0x89, 0x88, 0xEF, 0x36,
        0x3C, 0x11, 0xBC, 0x83, 0xE8, 0x30, 0x6C, 0x81, 0x7C, 0x47,
        0xF3, 0x4D, 0xCA, 0xEA, 0x56, 0x01, 0x62, 0x55, 0x2E, 0x4B,
        0x89, 0xA9, 0xBD, 0x6F, 0x01, 0xF6, 0x74, 0x02, 0xAA, 0xE3,
        0x84, 0x66, 0x06, 0x95, 0x34, 0xA1, 0xE2, 0xCA, 0x65, 0xFE,
        0xA3, 0x2D, 0x43, 0x97, 0x95, 0x6C, 0x6F, 0xD5, 0xB4, 0x38,
        0xF6, 0xF9, 0x95, 0x30, 0xFA, 0xF8, 0x9C, 0x25, 0x2B, 0xB6,
        0x14, 0x51, 0xCC, 0x2E, 0xB3, 0x5B, 0xD6, 0xDC, 0x1A, 0xEC,
        0x2D, 0x09, 0x5B, 0x3F, 0x3A, 0xD0, 0xB8, 0x4E, 0x27, 0x1F,
        0xDC, 0x2A, 0xEE, 0xAC, 0xA9, 0x59, 0x5D, 0x07, 0x63, 0x11,
        0x83, 0x0B, 0xD4, 0x74, 0x80, 0xB6, 0x7D, 0x62, 0x45, 0xBF,
        0x56
};
static const int sizeof_rsa_key_der_2048 = sizeof(rsa_key_der_2048);

/* ./certs/ca-key.der, 2048-bit */
static const unsigned char ca_key_der_2048[] =
{
        0x30, 0x82, 0x04, 0xA4, 0x02, 0x01, 0x00, 0x02, 0x82, 0x01,
        0x01, 0x00, 0xBF, 0x0C, 0xCA, 0x2D, 0x14, 0xB2, 0x1E, 0x84,
        0x42, 0x5B, 0xCD, 0x38, 0x1F, 0x4A, 0xF2, 0x4D, 0x75, 0x10,
        0xF1, 0xB6, 0x35, 0x9F, 0xDF, 0xCA, 0x7D, 0x03, 0x98, 0xD3,
        0xAC, 0xDE, 0x03, 0x66, 0xEE, 0x2A, 0xF1, 0xD8, 0xB0, 0x7D,
        0x6E, 0x07, 0x54, 0x0B, 0x10, 0x98, 0x21, 0x4D, 0x80, 0xCB,
        0x12, 0x20, 0xE7, 0xCC, 0x4F, 0xDE, 0x45, 0x7D, 0xC9, 0x72,
        0x77, 0x32, 0xEA, 0xCA, 0x90, 0xBB, 0x69, 0x52, 0x10, 0x03,
        0x2F, 0xA8, 0xF3, 0x95, 0xC5, 0xF1, 0x8B, 0x62, 0x56, 0x1B,
        0xEF, 0x67, 0x6F, 0xA4, 0x10, 0x41, 0x95, 0xAD, 0x0A, 0x9B,
        0xE3, 0xA5, 0xC0, 0xB0, 0xD2, 0x70, 0x76, 0x50, 0x30, 0x5B,
        0xA8, 0xE8, 0x08, 0x2C, 0x7C, 0xED, 0xA7, 0xA2, 0x7A, 0x8D,
        0x38, 0x29, 0x1C, 0xAC, 0xC7, 0xED, 0xF2, 0x7C, 0x95, 0xB0,
        0x95, 0x82, 0x7D, 0x49, 0x5C, 0x38, 0xCD, 0x77, 0x25, 0xEF,
        0xBD, 0x80, 0x75, 0x53, 0x94, 0x3C, 0x3D, 0xCA, 0x63, 0x5B,
        0x9F, 0x15, 0xB5, 0xD3, 0x1D, 0x13, 0x2F, 0x19, 0xD1, 0x3C,
        0xDB, 0x76, 0x3A, 0xCC, 0xB8, 0x7D, 0xC9, 0xE5, 0xC2, 0xD7,
        0xDA, 0x40, 0x6F, 0xD8, 0x21, 0xDC, 0x73, 0x1B, 0x42, 0x2D,
        0x53, 0x9C, 0xFE, 0x1A, 0xFC, 0x7D, 0xAB, 0x7A, 0x36, 0x3F,
        0x98, 0xDE, 0x84, 0x7C, 0x05, 0x67, 0xCE, 0x6A, 0x14, 0x38,
        0x87, 0xA9, 0xF1, 0x8C, 0xB5, 0x68, 0xCB, 0x68, 0x7F, 0x71,
        0x20, 0x2B, 0xF5, 0xA0, 0x63, 0xF5, 0x56, 0x2F, 0xA3, 0x26,
        0xD2, 0xB7, 0x6F, 0xB1, 0x5A, 0x17, 0xD7, 0x38, 0x99, 0x08,
        0xFE, 0x93, 0x58, 0x6F, 0xFE, 0xC3, 0x13, 0x49, 0x08, 0x16,
        0x0B, 0xA7, 0x4D, 0x67, 0x00, 0x52, 0x31, 0x67, 0x23, 0x4E,
        0x98, 0xED, 0x51, 0x45, 0x1D, 0xB9, 0x04, 0xD9, 0x0B, 0xEC,
        0xD8, 0x28, 0xB3, 0x4B, 0xBD, 0xED, 0x36, 0x79, 0x02, 0x03,
        0x01, 0x00, 0x01, 0x02, 0x82, 0x01, 0x00, 0x3D, 0x6E, 0x4E,
        0x60, 0x1A, 0x84, 0x7F, 0x9D, 0x85, 0x7C, 0xE1, 0x4B, 0x07,
        0x7C, 0xE0, 0xD6, 0x99, 0x2A, 0xDE, 0x9D, 0xF9, 0x36, 0x34,
        0x0E, 0x77, 0x0E, 0x3E, 0x08, 0xEA, 0x4F, 0xE5, 0x06, 0x26,
        0xD4, 0xF6, 0x38, 0xF7, 0xDF, 0x0D, 0x0F, 0x1C, 0x2E, 0x06,
        0xA2, 0xF4, 0x2A, 0x68, 0x9C, 0x63, 0x72, 0xE3, 0x35, 0xE6,
        0x04, 0x91, 0x91, 0xB5, 0xC1, 0xB1, 0xA4, 0x54, 0xAC, 0xD7,
        0xC6, 0xFB, 0x41, 0xA0, 0xD6, 0x75, 0x6F, 0xBD, 0x0B, 0x4E,
        0xBF, 0xB1, 0x52, 0xE8, 0x5F, 0x49, 0x26, 0x98, 0x56, 0x47,
        0xC7, 0xDE, 0xE9, 0xEA, 0x3C, 0x60, 0x01, 0xBF, 0x28, 0xDC,
        0x31, 0xBF, 0x49, 0x5F, 0x93, 0x49, 0x87, 0x7A, 0x81, 0x5B,
        0x96, 0x4B, 0x4D, 0xCA, 0x5C, 0x38, 0x4F, 0xB7, 0xE1, 0xB2,
        0xD3, 0xC7, 0x21, 0xDA, 0x3C, 0x12, 0x87, 0x07, 0xE4, 0x1B,
        0xDC, 0x43, 0xEC, 0xE8, 0xEC, 0x54, 0x61, 0xE7, 0xF6, 0xED,
        0xA6, 0x0B, 0x2E, 0xF5, 0xDF, 0x82, 0x7F, 0xC6, 0x1F, 0x61,
        0x19, 0x9C, 0xA4, 0x83, 0x39, 0xDF, 0x21, 0x85, 0x89, 0x6F,
        0x77, 0xAF, 0x86, 0x15, 0x32, 0x08, 0xA2, 0x5A, 0x0B, 0x26,
        0x61, 0xFB, 0x70, 0x0C, 0xCA, 0x9C, 0x38, 0x7D, 0xBC, 0x22,
        0xEE, 0xEB, 0xA3, 0xA8, 0x16, 0x00, 0xF9, 0x8A, 0x80, 0x1E,
        0x00, 0x84, 0xA8, 0x4A, 0x41, 0xF8, 0x84, 0x03, 0x67, 0x2F,
        0x23, 0x5B, 0x2F, 0x9B, 0x6B, 0x26, 0xC3, 0x07, 0x34, 0x94,
        0xA3, 0x03, 0x3B, 0x72, 0xD5, 0x9F, 0x72, 0xE0, 0xAD, 0xCC,
        0x34, 0xAB, 0xBD, 0xC7, 0xD5, 0xF5, 0x26, 0x30, 0x85, 0x0F,
        0x30, 0x23, 0x39, 0x52, 0xFF, 0x3C, 0xCB, 0x99, 0x21, 0x4D,
        0x88, 0xA5, 0xAB, 0xEE, 0x62, 0xB9, 0xC7, 0xE0, 0xBB, 0x47,
        0x87, 0xC1, 0x69, 0xCF, 0x73, 0xF3, 0x30, 0xBE, 0xCE, 0x39,
        0x04, 0x9C, 0xE5, 0x02, 0x81, 0x81, 0x00, 0xE1, 0x76, 0x45,
        0x80, 0x59, 0xB6, 0xD3, 0x49, 0xDF, 0x0A, 0xEF, 0x12, 0xD6,
        0x0F, 0xF0, 0xB7, 0xCB, 0x2A, 0x37, 0xBF, 0xA7, 0xF8, 0xB5,
        0x4D, 0xF5, 0x31, 0x35, 0xAD, 0xE4, 0xA3, 0x94, 0xA1, 0xDB,
        0xF1, 0x96, 0xAD, 0xB5, 0x05, 0x64, 0x85, 0x83, 0xFC, 0x1B,
        0x5B, 0x29, 0xAA, 0xBE, 0xF8, 0x26, 0x3F, 0x76, 0x7E, 0xAD,
        0x1C, 0xF0, 0xCB, 0xD7, 0x26, 0xB4, 0x1B, 0x05, 0x8E, 0x56,
        0x86, 0x7E, 0x08, 0x62, 0x21, 0xC1, 0x86, 0xD6, 0x47, 0x79,
        0x3E, 0xB7, 0x5D, 0xA4, 0xC6, 0x3A, 0xD7, 0xB1, 0x74, 0x20,
        0xF6, 0x50, 0x97, 0x41, 0x04, 0x53, 0xED, 0x3F, 0x26, 0xD6,
        0x6F, 0x91, 0xFA, 0x68, 0x26, 0xEC, 0x2A, 0xDC, 0x9A, 0xF1,
        0xE7, 0xDC, 0xFB, 0x73, 0xF0, 0x79, 0x43, 0x1B, 0x21, 0xA3,
        0x59, 0x04, 0x63, 0x52, 0x07, 0xC9, 0xD7, 0xE6, 0xD1, 0x1B,
        0x5D, 0x5E, 0x96, 0xFA, 0x53, 0x02, 0x81, 0x81, 0x00, 0xD8,
        0xED, 0x4E, 0x64, 0x61, 0x6B, 0x91, 0x0C, 0x61, 0x01, 0xB5,
        0x0F, 0xBB, 0x44, 0x67, 0x53, 0x1E, 0xDC, 0x07, 0xC4, 0x24,
        0x7E, 0x9E, 0x6C, 0x84, 0x23, 0x91, 0x0C, 0xE4, 0x12, 0x04,
        0x16, 0x4D, 0x78, 0x98, 0xCC, 0x96, 0x3D, 0x20, 0x4E, 0x0F,
        0x45, 0x9A, 0xB6, 0xF8, 0xB3, 0x93, 0x0D, 0xB2, 0xA2, 0x1B,
        0x29, 0xF2, 0x26, 0x79, 0xC8, 0xC5, 0xD2, 0x78, 0x7E, 0x5E,
        0x73, 0xF2, 0xD7, 0x70, 0x61, 0xBB, 0x40, 0xCE, 0x61, 0x05,
        0xFE, 0x69, 0x1E, 0x82, 0x29, 0xE6, 0x14, 0xB8, 0xA1, 0xE7,
        0x96, 0xD0, 0x23, 0x3F, 0x05, 0x93, 0x00, 0xF2, 0xE1, 0x4D,
        0x7E, 0xED, 0xB7, 0x96, 0x6C, 0xF7, 0xF0, 0xE4, 0xD1, 0xCF,
        0x01, 0x98, 0x4F, 0xDC, 0x74, 0x54, 0xAA, 0x6D, 0x5E, 0x5A,
        0x41, 0x31, 0xFE, 0xFF, 0x9A, 0xB6, 0xA0, 0x05, 0xDD, 0xA9,
        0x10, 0x54, 0xF8, 0x6B, 0xD0, 0xAA, 0x83, 0x02, 0x81, 0x80,
        0x21, 0xD3, 0x04, 0x8A, 0x44, 0xEB, 0x50, 0xB7, 0x7C, 0x66,
        0xBF, 0x87, 0x2B, 0xE6, 0x28, 0x4E, 0xEA, 0x83, 0xE2, 0xE9,
        0x35, 0xE1, 0xF2, 0x11, 0x47, 0xFF, 0xA1, 0xF5, 0xFC, 0x9F,
        0x2D, 0xE5, 0x3A, 0x81, 0xFC, 0x01, 0x03, 0x6F, 0x53, 0xAD,
        0x54, 0x27, 0xB6, 0x52, 0xEE, 0xE5, 0x56, 0xD1, 0x13, 0xAB,
        0xE1, 0xB3, 0x0F, 0x75, 0x90, 0x0A, 0x84, 0xB4, 0xA1, 0xC0,
        0x8C, 0x0C, 0xD6, 0x9E, 0x46, 0xBA, 0x2B, 0x3E, 0xB5, 0x31,
        0xED, 0x63, 0xBB, 0xA4, 0xD5, 0x0D, 0x8F, 0x72, 0xCD, 0xD1,
        0x1E, 0x26, 0x35, 0xEB, 0xBE, 0x1B, 0x72, 0xFD, 0x9B, 0x39,
        0xB4, 0x87, 0xB7, 0x13, 0xF5, 0xEA, 0x83, 0x45, 0x93, 0x98,
        0xBA, 0x8F, 0xE4, 0x4A, 0xCC, 0xB4, 0x4C, 0xA8, 0x7F, 0x08,
        0xBA, 0x41, 0x49, 0xA8, 0x49, 0x28, 0x3D, 0x5E, 0x3D, 0xC1,
        0xCE, 0x37, 0x00, 0xCB, 0xF9, 0x2C, 0xDD, 0x51, 0x02, 0x81,
        0x81, 0x00, 0xA1, 0x57, 0x9F, 0x3E, 0xB9, 0xD6, 0xAF, 0x83,
        0x6D, 0x83, 0x3F, 0x8F, 0xFB, 0xD0, 0xDC, 0xA8, 0xCE, 0x03,
        0x09, 0x23, 0xB1, 0xA1, 0x1B, 0x63, 0xCA, 0xC4, 0x49, 0x56,
        0x35, 0x2B, 0xD1, 0x2E, 0x65, 0x60, 0x95, 0x05, 0x55, 0x99,
        0x11, 0x35, 0xFD, 0xD5, 0xDF, 0x44, 0xC7, 0xA5, 0x88, 0x72,
        0x5F, 0xB2, 0x82, 0x51, 0xA8, 0x71, 0x45, 0x93, 0x36, 0xCF,
        0x5C, 0x1F, 0x61, 0x51, 0x0C, 0x05, 0x80, 0xE8, 0xAF, 0xC5,
        0x7B, 0xBA, 0x5E, 0x22, 0xE3, 0x3C, 0x75, 0xC3, 0x84, 0x05,
        0x55, 0x6D, 0xD6, 0x3A, 0x2D, 0x84, 0x89, 0x93, 0x33, 0xCB,
        0x38, 0xDA, 0xAA, 0x31, 0x05, 0xCD, 0xCE, 0x6C, 0x2D, 0xDD,
        0x55, 0xD3, 0x57, 0x0B, 0xF0, 0xA5, 0x35, 0x6A, 0xB0, 0xAE,
        0x31, 0xBA, 0x43, 0x96, 0xCA, 0x00, 0xC7, 0x4B, 0xE3, 0x19,
        0x12, 0x43, 0xD3, 0x42, 0xFA, 0x6F, 0xEA, 0x80, 0xC0, 0xD1,
        0x02, 0x81, 0x81, 0x00, 0xB9, 0xDB, 0x89, 0x20, 0x34, 0x27,
        0x70, 0x62, 0x34, 0xEA, 0x5F, 0x25, 0x62, 0x12, 0xF3, 0x9D,
        0x81, 0xBF, 0x48, 0xEE, 0x9A, 0x0E, 0xC1, 0x8D, 0x10, 0xFF,
        0x65, 0x9A, 0x9D, 0x2D, 0x1A, 0x8A, 0x94, 0x5A, 0xC8, 0xC0,
        0xA5, 0xA5, 0x84, 0x61, 0x9E, 0xD4, 0x24, 0xB9, 0xEF, 0xA9,
        0x9D, 0xC9, 0x77, 0x0B, 0xC7, 0x70, 0x66, 0x3D, 0xBA, 0xC8,
        0x54, 0xDF, 0xD2, 0x33, 0xE1, 0xF5, 0x7F, 0xF9, 0x27, 0x61,
        0xBE, 0x57, 0x45, 0xDD, 0xB7, 0x45, 0x17, 0x24, 0xF5, 0x23,
        0xE4, 0x38, 0x0E, 0x91, 0x27, 0xEE, 0xE3, 0x20, 0xD8, 0x14,
        0xC8, 0x94, 0x47, 0x77, 0x40, 0x77, 0x45, 0x18, 0x9E, 0x0D,
        0xCE, 0x79, 0x3F, 0x57, 0x31, 0x56, 0x09, 0x49, 0x67, 0xBE,
        0x94, 0x58, 0x4F, 0xF6, 0xC4, 0xAB, 0xE2, 0x89, 0xE3, 0xE3,
        0x8A, 0xC0, 0x05, 0x55, 0x2C, 0x24, 0xC0, 0x4A, 0x97, 0x04,
        0x27, 0x9A
};
static const int sizeof_ca_key_der_2048 = sizeof(ca_key_der_2048);

/* ./certs/ca-cert.der, 2048-bit */
static const unsigned char ca_cert_der_2048[] =
{
        0x30, 0x82, 0x04, 0xFF, 0x30, 0x82, 0x03, 0xE7, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x14, 0x6B, 0x9B, 0x70, 0xC6, 0xF1,
        0xA3, 0x94, 0x65, 0x19, 0xA1, 0x08, 0x58, 0xEF, 0xA7, 0x8D,
        0x2B, 0x7A, 0x83, 0xC1, 0xDA, 0x30, 0x0D, 0x06, 0x09, 0x2A,
        0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B, 0x05, 0x00,
        0x30, 0x81, 0x94, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55,
        0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E,
        0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E,
        0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03,
        0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D,
        0x61, 0x6E, 0x31, 0x11, 0x30, 0x0F, 0x06, 0x03, 0x55, 0x04,
        0x0A, 0x0C, 0x08, 0x53, 0x61, 0x77, 0x74, 0x6F, 0x6F, 0x74,
        0x68, 0x31, 0x13, 0x30, 0x11, 0x06, 0x03, 0x55, 0x04, 0x0B,
        0x0C, 0x0A, 0x43, 0x6F, 0x6E, 0x73, 0x75, 0x6C, 0x74, 0x69,
        0x6E, 0x67, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04,
        0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F,
        0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D,
        0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x30, 0x1E, 0x17, 0x0D, 0x32, 0x34, 0x31, 0x32, 0x31,
        0x38, 0x32, 0x31, 0x32, 0x35, 0x32, 0x39, 0x5A, 0x17, 0x0D,
        0x32, 0x37, 0x30, 0x39, 0x31, 0x34, 0x32, 0x31, 0x32, 0x35,
        0x32, 0x39, 0x5A, 0x30, 0x81, 0x94, 0x31, 0x0B, 0x30, 0x09,
        0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31,
        0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07,
        0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30,
        0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F,
        0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x11, 0x30, 0x0F, 0x06,
        0x03, 0x55, 0x04, 0x0A, 0x0C, 0x08, 0x53, 0x61, 0x77, 0x74,
        0x6F, 0x6F, 0x74, 0x68, 0x31, 0x13, 0x30, 0x11, 0x06, 0x03,
        0x55, 0x04, 0x0B, 0x0C, 0x0A, 0x43, 0x6F, 0x6E, 0x73, 0x75,
        0x6C, 0x74, 0x69, 0x6E, 0x67, 0x31, 0x18, 0x30, 0x16, 0x06,
        0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48,
        0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E,
        0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C,
        0x2E, 0x63, 0x6F, 0x6D, 0x30, 0x82, 0x01, 0x22, 0x30, 0x0D,
        0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01,
        0x01, 0x05, 0x00, 0x03, 0x82, 0x01, 0x0F, 0x00, 0x30, 0x82,
        0x01, 0x0A, 0x02, 0x82, 0x01, 0x01, 0x00, 0xBF, 0x0C, 0xCA,
        0x2D, 0x14, 0xB2, 0x1E, 0x84, 0x42, 0x5B, 0xCD, 0x38, 0x1F,
        0x4A, 0xF2, 0x4D, 0x75, 0x10, 0xF1, 0xB6, 0x35, 0x9F, 0xDF,
        0xCA, 0x7D, 0x03, 0x98, 0xD3, 0xAC, 0xDE, 0x03, 0x66, 0xEE,
        0x2A, 0xF1, 0xD8, 0xB0, 0x7D, 0x6E, 0x07, 0x54, 0x0B, 0x10,
        0x98, 0x21, 0x4D, 0x80, 0xCB, 0x12, 0x20, 0xE7, 0xCC, 0x4F,
        0xDE, 0x45, 0x7D, 0xC9, 0x72, 0x77, 0x32, 0xEA, 0xCA, 0x90,
        0xBB, 0x69, 0x52, 0x10, 0x03, 0x2F, 0xA8, 0xF3, 0x95, 0xC5,
        0xF1, 0x8B, 0x62, 0x56, 0x1B, 0xEF, 0x67, 0x6F, 0xA4, 0x10,
        0x41, 0x95, 0xAD, 0x0A, 0x9B, 0xE3, 0xA5, 0xC0, 0xB0, 0xD2,
        0x70, 0x76, 0x50, 0x30, 0x5B, 0xA8, 0xE8, 0x08, 0x2C, 0x7C,
        0xED, 0xA7, 0xA2, 0x7A, 0x8D, 0x38, 0x29, 0x1C, 0xAC, 0xC7,
        0xED, 0xF2, 0x7C, 0x95, 0xB0, 0x95, 0x82, 0x7D, 0x49, 0x5C,
        0x38, 0xCD, 0x77, 0x25, 0xEF, 0xBD, 0x80, 0x75, 0x53, 0x94,
        0x3C, 0x3D, 0xCA, 0x63, 0x5B, 0x9F, 0x15, 0xB5, 0xD3, 0x1D,
        0x13, 0x2F, 0x19, 0xD1, 0x3C, 0xDB, 0x76, 0x3A, 0xCC, 0xB8,
        0x7D, 0xC9, 0xE5, 0xC2, 0xD7, 0xDA, 0x40, 0x6F, 0xD8, 0x21,
        0xDC, 0x73, 0x1B, 0x42, 0x2D, 0x53, 0x9C, 0xFE, 0x1A, 0xFC,
        0x7D, 0xAB, 0x7A, 0x36, 0x3F, 0x98, 0xDE, 0x84, 0x7C, 0x05,
        0x67, 0xCE, 0x6A, 0x14, 0x38, 0x87, 0xA9, 0xF1, 0x8C, 0xB5,
        0x68, 0xCB, 0x68, 0x7F, 0x71, 0x20, 0x2B, 0xF5, 0xA0, 0x63,
        0xF5, 0x56, 0x2F, 0xA3, 0x26, 0xD2, 0xB7, 0x6F, 0xB1, 0x5A,
        0x17, 0xD7, 0x38, 0x99, 0x08, 0xFE, 0x93, 0x58, 0x6F, 0xFE,
        0xC3, 0x13, 0x49, 0x08, 0x16, 0x0B, 0xA7, 0x4D, 0x67, 0x00,
        0x52, 0x31, 0x67, 0x23, 0x4E, 0x98, 0xED, 0x51, 0x45, 0x1D,
        0xB9, 0x04, 0xD9, 0x0B, 0xEC, 0xD8, 0x28, 0xB3, 0x4B, 0xBD,
        0xED, 0x36, 0x79, 0x02, 0x03, 0x01, 0x00, 0x01, 0xA3, 0x82,
        0x01, 0x45, 0x30, 0x82, 0x01, 0x41, 0x30, 0x1D, 0x06, 0x03,
        0x55, 0x1D, 0x0E, 0x04, 0x16, 0x04, 0x14, 0x27, 0x8E, 0x67,
        0x11, 0x74, 0xC3, 0x26, 0x1D, 0x3F, 0xED, 0x33, 0x63, 0xB3,
        0xA4, 0xD8, 0x1D, 0x30, 0xE5, 0xE8, 0xD5, 0x30, 0x81, 0xD4,
        0x06, 0x03, 0x55, 0x1D, 0x23, 0x04, 0x81, 0xCC, 0x30, 0x81,
        0xC9, 0x80, 0x14, 0x27, 0x8E, 0x67, 0x11, 0x74, 0xC3, 0x26,
        0x1D, 0x3F, 0xED, 0x33, 0x63, 0xB3, 0xA4, 0xD8, 0x1D, 0x30,
        0xE5, 0xE8, 0xD5, 0xA1, 0x81, 0x9A, 0xA4, 0x81, 0x97, 0x30,
        0x81, 0x94, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04,
        0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E, 0x06,
        0x03, 0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E, 0x74,
        0x61, 0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55,
        0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D, 0x61,
        0x6E, 0x31, 0x11, 0x30, 0x0F, 0x06, 0x03, 0x55, 0x04, 0x0A,
        0x0C, 0x08, 0x53, 0x61, 0x77, 0x74, 0x6F, 0x6F, 0x74, 0x68,
        0x31, 0x13, 0x30, 0x11, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C,
        0x0A, 0x43, 0x6F, 0x6E, 0x73, 0x75, 0x6C, 0x74, 0x69, 0x6E,
        0x67, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x03,
        0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66,
        0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30,
        0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01,
        0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77,
        0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D,
        0x82, 0x14, 0x6B, 0x9B, 0x70, 0xC6, 0xF1, 0xA3, 0x94, 0x65,
        0x19, 0xA1, 0x08, 0x58, 0xEF, 0xA7, 0x8D, 0x2B, 0x7A, 0x83,
        0xC1, 0xDA, 0x30, 0x0C, 0x06, 0x03, 0x55, 0x1D, 0x13, 0x04,
        0x05, 0x30, 0x03, 0x01, 0x01, 0xFF, 0x30, 0x1C, 0x06, 0x03,
        0x55, 0x1D, 0x11, 0x04, 0x15, 0x30, 0x13, 0x82, 0x0B, 0x65,
        0x78, 0x61, 0x6D, 0x70, 0x6C, 0x65, 0x2E, 0x63, 0x6F, 0x6D,
        0x87, 0x04, 0x7F, 0x00, 0x00, 0x01, 0x30, 0x1D, 0x06, 0x03,
        0x55, 0x1D, 0x25, 0x04, 0x16, 0x30, 0x14, 0x06, 0x08, 0x2B,
        0x06, 0x01, 0x05, 0x05, 0x07, 0x03, 0x01, 0x06, 0x08, 0x2B,
        0x06, 0x01, 0x05, 0x05, 0x07, 0x03, 0x02, 0x30, 0x0D, 0x06,
        0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B,
        0x05, 0x00, 0x03, 0x82, 0x01, 0x01, 0x00, 0x77, 0x3B, 0x3D,
        0x66, 0x74, 0xBC, 0x97, 0xFE, 0x40, 0x16, 0xE6, 0xBA, 0xA5,
        0xD5, 0xD1, 0x84, 0x08, 0x89, 0x69, 0x4F, 0x88, 0x0D, 0x57,
        0xA9, 0xEF, 0x8C, 0xC3, 0x97, 0x52, 0xC8, 0xBD, 0x8B, 0xA2,
        0x49, 0x3B, 0xB7, 0xF7, 0x5D, 0x1E, 0xD6, 0x14, 0x7F, 0xB2,
        0x80, 0x33, 0xDA, 0xA0, 0x8A, 0xD3, 0xE1, 0x2F, 0xD5, 0xBC,
        0x33, 0x9F, 0xEA, 0x5A, 0x72, 0x24, 0xE5, 0xF8, 0xB8, 0x4B,
        0xB3, 0xDF, 0x62, 0x90, 0x3B, 0xA8, 0x21, 0xEF, 0x27, 0x42,
        0x75, 0xBC, 0x60, 0x02, 0x8E, 0x37, 0x35, 0x99, 0xEB, 0xA3,
        0x28, 0xF2, 0x65, 0x4C, 0xFF, 0x7A, 0xF8, 0x8E, 0xCC, 0x23,
        0x6D, 0xE5, 0x6A, 0xFE, 0x22, 0x5A, 0xD9, 0xB2, 0x4F, 0x47,
        0xC7, 0xE0, 0xAE, 0x98, 0xEF, 0x94, 0xAC, 0xB6, 0x4F, 0x61,
        0x81, 0x29, 0x8E, 0xE1, 0x79, 0x2C, 0x46, 0xFC, 0xE9, 0x1A,
        0xC3, 0x96, 0x1F, 0x19, 0x93, 0x64, 0x2E, 0x9F, 0x37, 0x72,
        0xC5, 0xE4, 0x93, 0x4E, 0x61, 0x5F, 0x38, 0x8E, 0xAE, 0xE8,
        0x39, 0x19, 0xE6, 0x97, 0xA8, 0x91, 0xD4, 0x23, 0x7E, 0x1E,
        0xD2, 0xD0, 0x53, 0xEC, 0xCC, 0xAC, 0xA0, 0x1D, 0xD0, 0xB7,
        0xDD, 0xB1, 0xB7, 0x01, 0x2E, 0x96, 0xCD, 0x85, 0x27, 0xE0,
        0xE7, 0x47, 0xE2, 0xC1, 0xC1, 0x00, 0xF6, 0x94, 0xDF, 0x77,
        0xE7, 0xFA, 0xC6, 0xEF, 0x8A, 0xC0, 0x7C, 0x67, 0xBC, 0xFF,
        0xA0, 0x7C, 0x94, 0x3B, 0x7D, 0x86, 0x42, 0xAF, 0x3D, 0x83,
        0x31, 0xEE, 0x2A, 0x3B, 0x7B, 0xF0, 0x2C, 0x9E, 0x6F, 0xE9,
        0xC4, 0x07, 0x81, 0x24, 0xDA, 0x05, 0x70, 0x4D, 0xDD, 0x09,
        0xAE, 0x9E, 0x72, 0xB8, 0x21, 0x0E, 0x8C, 0xB2, 0xAB, 0xAA,
        0x4C, 0x49, 0x10, 0xF7, 0x76, 0xF9, 0xB5, 0x0D, 0x6C, 0x20,
        0xD3, 0xDF, 0x7A, 0x06, 0x32, 0x8D, 0x29, 0x1F, 0x28, 0x1D,
        0x8D, 0x26, 0x33
};
static const int sizeof_ca_cert_der_2048 = sizeof(ca_cert_der_2048);

/* ./certs/ca-cert-chain.der, 2048-bit */
static const unsigned char ca_cert_chain_der[] =
{
        0x30, 0x82, 0x03, 0xFA, 0x30, 0x82, 0x03, 0x63, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x14, 0x22, 0x3E, 0x28, 0x4D, 0xF0,
        0xF6, 0xC5, 0x97, 0x06, 0xB3, 0xAD, 0x8A, 0x59, 0x4D, 0xA0,
        0x87, 0x3F, 0xB3, 0xCE, 0x8C, 0x30, 0x0D, 0x06, 0x09, 0x2A,
        0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B, 0x05, 0x00,
        0x30, 0x81, 0x94, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55,
        0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E,
        0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E,
        0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03,
        0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D,
        0x61, 0x6E, 0x31, 0x11, 0x30, 0x0F, 0x06, 0x03, 0x55, 0x04,
        0x0A, 0x0C, 0x08, 0x53, 0x61, 0x77, 0x74, 0x6F, 0x6F, 0x74,
        0x68, 0x31, 0x13, 0x30, 0x11, 0x06, 0x03, 0x55, 0x04, 0x0B,
        0x0C, 0x0A, 0x43, 0x6F, 0x6E, 0x73, 0x75, 0x6C, 0x74, 0x69,
        0x6E, 0x67, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04,
        0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F,
        0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D,
        0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x30, 0x1E, 0x17, 0x0D, 0x32, 0x34, 0x31, 0x32, 0x31,
        0x38, 0x32, 0x31, 0x32, 0x35, 0x32, 0x39, 0x5A, 0x17, 0x0D,
        0x32, 0x37, 0x30, 0x39, 0x31, 0x34, 0x32, 0x31, 0x32, 0x35,
        0x32, 0x39, 0x5A, 0x30, 0x81, 0x94, 0x31, 0x0B, 0x30, 0x09,
        0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31,
        0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07,
        0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30,
        0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F,
        0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x11, 0x30, 0x0F, 0x06,
        0x03, 0x55, 0x04, 0x0A, 0x0C, 0x08, 0x53, 0x61, 0x77, 0x74,
        0x6F, 0x6F, 0x74, 0x68, 0x31, 0x13, 0x30, 0x11, 0x06, 0x03,
        0x55, 0x04, 0x0B, 0x0C, 0x0A, 0x43, 0x6F, 0x6E, 0x73, 0x75,
        0x6C, 0x74, 0x69, 0x6E, 0x67, 0x31, 0x18, 0x30, 0x16, 0x06,
        0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48,
        0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E,
        0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C,
        0x2E, 0x63, 0x6F, 0x6D, 0x30, 0x81, 0x9F, 0x30, 0x0D, 0x06,
        0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01,
        0x05, 0x00, 0x03, 0x81, 0x8D, 0x00, 0x30, 0x81, 0x89, 0x02,
        0x81, 0x81, 0x00, 0xCD, 0xAC, 0xDD, 0x47, 0xEC, 0xBE, 0xB7,
        0x24, 0xC3, 0x63, 0x1B, 0x54, 0x98, 0x79, 0xE1, 0xC7, 0x31,
        0x16, 0x59, 0xD6, 0x9D, 0x77, 0x9D, 0x8D, 0xE2, 0x8B, 0xED,
        0x04, 0x17, 0xB2, 0xC6, 0xEB, 0xE4, 0x9B, 0x91, 0xBE, 0x31,
        0x50, 0x62, 0x97, 0x58, 0xB5, 0x7F, 0x29, 0xDE, 0xB3, 0x71,
        0x24, 0x0B, 0xBF, 0x97, 0x09, 0x7F, 0x26, 0xDC, 0x2D, 0xEC,
        0xA8, 0x2E, 0xB2, 0x64, 0x2B, 0x7A, 0x2B, 0x35, 0x19, 0x2D,
        0xA2, 0x80, 0xCB, 0x99, 0xFD, 0x94, 0x71, 0x1B, 0x23, 0x8D,
        0x54, 0xDB, 0x2E, 0x62, 0x8D, 0x81, 0x08, 0x2D, 0xF4, 0x24,
        0x72, 0x27, 0x6C, 0xF9, 0xC9, 0x8E, 0xDB, 0x4C, 0x75, 0xBA,
        0x9B, 0x01, 0xF8, 0x3F, 0x18, 0xF4, 0xE6, 0x7F, 0xFB, 0x57,
        0x94, 0x92, 0xCC, 0x88, 0xC4, 0xB4, 0x00, 0xC2, 0xAA, 0xD4,
        0xE5, 0x88, 0x18, 0xB3, 0x11, 0x2F, 0x73, 0xC0, 0xD6, 0x29,
        0x09, 0x02, 0x03, 0x01, 0x00, 0x01, 0xA3, 0x82, 0x01, 0x45,
        0x30, 0x82, 0x01, 0x41, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D,
        0x0E, 0x04, 0x16, 0x04, 0x14, 0xD3, 0x22, 0x8F, 0x28, 0x2C,
        0xE0, 0x05, 0xEE, 0xD3, 0xED, 0xC3, 0x71, 0x3D, 0xC9, 0xB2,
        0x36, 0x3A, 0x1D, 0xBF, 0xA8, 0x30, 0x81, 0xD4, 0x06, 0x03,
        0x55, 0x1D, 0x23, 0x04, 0x81, 0xCC, 0x30, 0x81, 0xC9, 0x80,
        0x14, 0xD3, 0x22, 0x8F, 0x28, 0x2C, 0xE0, 0x05, 0xEE, 0xD3,
        0xED, 0xC3, 0x71, 0x3D, 0xC9, 0xB2, 0x36, 0x3A, 0x1D, 0xBF,
        0xA8, 0xA1, 0x81, 0x9A, 0xA4, 0x81, 0x97, 0x30, 0x81, 0x94,
        0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13,
        0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55,
        0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E,
        0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x07,
        0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31,
        0x11, 0x30, 0x0F, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x08,
        0x53, 0x61, 0x77, 0x74, 0x6F, 0x6F, 0x74, 0x68, 0x31, 0x13,
        0x30, 0x11, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x0A, 0x43,
        0x6F, 0x6E, 0x73, 0x75, 0x6C, 0x74, 0x69, 0x6E, 0x67, 0x31,
        0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F,
        0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73,
        0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06,
        0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01,
        0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x82, 0x14,
        0x22, 0x3E, 0x28, 0x4D, 0xF0, 0xF6, 0xC5, 0x97, 0x06, 0xB3,
        0xAD, 0x8A, 0x59, 0x4D, 0xA0, 0x87, 0x3F, 0xB3, 0xCE, 0x8C,
        0x30, 0x0C, 0x06, 0x03, 0x55, 0x1D, 0x13, 0x04, 0x05, 0x30,
        0x03, 0x01, 0x01, 0xFF, 0x30, 0x1C, 0x06, 0x03, 0x55, 0x1D,
        0x11, 0x04, 0x15, 0x30, 0x13, 0x82, 0x0B, 0x65, 0x78, 0x61,
        0x6D, 0x70, 0x6C, 0x65, 0x2E, 0x63, 0x6F, 0x6D, 0x87, 0x04,
        0x7F, 0x00, 0x00, 0x01, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D,
        0x25, 0x04, 0x16, 0x30, 0x14, 0x06, 0x08, 0x2B, 0x06, 0x01,
        0x05, 0x05, 0x07, 0x03, 0x01, 0x06, 0x08, 0x2B, 0x06, 0x01,
        0x05, 0x05, 0x07, 0x03, 0x02, 0x30, 0x0D, 0x06, 0x09, 0x2A,
        0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B, 0x05, 0x00,
        0x03, 0x81, 0x81, 0x00, 0x4E, 0x35, 0x89, 0x4C, 0x99, 0xC8,
        0x51, 0x46, 0x5B, 0x86, 0x21, 0xF3, 0x92, 0x13, 0x2D, 0x0E,
        0x73, 0x78, 0x85, 0xBC, 0x81, 0xBB, 0xD1, 0x4B, 0xC3, 0x1B,
        0x65, 0xB5, 0x39, 0x71, 0xA7, 0x04, 0x39, 0x8D, 0x57, 0x20,
        0x02, 0xA0, 0x33, 0x8C, 0xFF, 0xD5, 0xFC, 0x2E, 0x94, 0x56,
        0x48, 0xFC, 0x08, 0x4A, 0x37, 0x19, 0x98, 0x81, 0xAF, 0x51,
        0x3F, 0xB7, 0x91, 0x0A, 0x86, 0x4E, 0x97, 0xE2, 0x39, 0x92,
        0xF5, 0x3E, 0x99, 0xB2, 0x88, 0x1B, 0xAA, 0x97, 0x95, 0x77,
        0x2E, 0xDA, 0x41, 0x11, 0xD7, 0x8F, 0x74, 0x9D, 0x34, 0xD0,
        0x70, 0xCA, 0x37, 0xAA, 0xF7, 0xD7, 0x39, 0xD6, 0xA8, 0x48,
        0x34, 0x06, 0x3A, 0x6A, 0xDA, 0xDE, 0x8A, 0x1D, 0x3C, 0x41,
        0x56, 0xA3, 0x4E, 0xAE, 0x9F, 0x50, 0xB4, 0x8E, 0x10, 0x39,
        0xD9, 0xA2, 0x38, 0xD0, 0x22, 0x04, 0xCA, 0x31, 0x1C, 0xAC,
        0x3C, 0xF2
};
static const int sizeof_ca_cert_chain_der = sizeof(ca_cert_chain_der);

/* ./certs/server-key.der, 2048-bit */
static const unsigned char server_key_der_2048[] =
{
        0x30, 0x82, 0x04, 0xA5, 0x02, 0x01, 0x00, 0x02, 0x82, 0x01,
        0x01, 0x00, 0xC0, 0x95, 0x08, 0xE1, 0x57, 0x41, 0xF2, 0x71,
        0x6D, 0xB7, 0xD2, 0x45, 0x41, 0x27, 0x01, 0x65, 0xC6, 0x45,
        0xAE, 0xF2, 0xBC, 0x24, 0x30, 0xB8, 0x95, 0xCE, 0x2F, 0x4E,
        0xD6, 0xF6, 0x1C, 0x88, 0xBC, 0x7C, 0x9F, 0xFB, 0xA8, 0x67,
        0x7F, 0xFE, 0x5C, 0x9C, 0x51, 0x75, 0xF7, 0x8A, 0xCA, 0x07,
        0xE7, 0x35, 0x2F, 0x8F, 0xE1, 0xBD, 0x7B, 0xC0, 0x2F, 0x7C,
        0xAB, 0x64, 0xA8, 0x17, 0xFC, 0xCA, 0x5D, 0x7B, 0xBA, 0xE0,
        0x21, 0xE5, 0x72, 0x2E, 0x6F, 0x2E, 0x86, 0xD8, 0x95, 0x73,
        0xDA, 0xAC, 0x1B, 0x53, 0xB9, 0x5F, 0x3F, 0xD7, 0x19, 0x0D,
        0x25, 0x4F, 0xE1, 0x63, 0x63, 0x51, 0x8B, 0x0B, 0x64, 0x3F,
        0xAD, 0x43, 0xB8, 0xA5, 0x1C, 0x5C, 0x34, 0xB3, 0xAE, 0x00,
        0xA0, 0x63, 0xC5, 0xF6, 0x7F, 0x0B, 0x59, 0x68, 0x78, 0x73,
        0xA6, 0x8C, 0x18, 0xA9, 0x02, 0x6D, 0xAF, 0xC3, 0x19, 0x01,
        0x2E, 0xB8, 0x10, 0xE3, 0xC6, 0xCC, 0x40, 0xB4, 0x69, 0xA3,
        0x46, 0x33, 0x69, 0x87, 0x6E, 0xC4, 0xBB, 0x17, 0xA6, 0xF3,
        0xE8, 0xDD, 0xAD, 0x73, 0xBC, 0x7B, 0x2F, 0x21, 0xB5, 0xFD,
        0x66, 0x51, 0x0C, 0xBD, 0x54, 0xB3, 0xE1, 0x6D, 0x5F, 0x1C,
        0xBC, 0x23, 0x73, 0xD1, 0x09, 0x03, 0x89, 0x14, 0xD2, 0x10,
        0xB9, 0x64, 0xC3, 0x2A, 0xD0, 0xA1, 0x96, 0x4A, 0xBC, 0xE1,
        0xD4, 0x1A, 0x5B, 0xC7, 0xA0, 0xC0, 0xC1, 0x63, 0x78, 0x0F,
        0x44, 0x37, 0x30, 0x32, 0x96, 0x80, 0x32, 0x23, 0x95, 0xA1,
        0x77, 0xBA, 0x13, 0xD2, 0x97, 0x73, 0xE2, 0x5D, 0x25, 0xC9,
        0x6A, 0x0D, 0xC3, 0x39, 0x60, 0xA4, 0xB4, 0xB0, 0x69, 0x42,
        0x42, 0x09, 0xE9, 0xD8, 0x08, 0xBC, 0x33, 0x20, 0xB3, 0x58,
        0x22, 0xA7, 0xAA, 0xEB, 0xC4, 0xE1, 0xE6, 0x61, 0x83, 0xC5,
        0xD2, 0x96, 0xDF, 0xD9, 0xD0, 0x4F, 0xAD, 0xD7, 0x02, 0x03,
        0x01, 0x00, 0x01, 0x02, 0x82, 0x01, 0x01, 0x00, 0x9A, 0xD0,
        0x34, 0x0F, 0x52, 0x62, 0x05, 0x50, 0x01, 0xEF, 0x9F, 0xED,
        0x64, 0x6E, 0xC2, 0xC4, 0xDA, 0x1A, 0xF2, 0x84, 0xD7, 0x92,
        0x10, 0x48, 0x92, 0xC4, 0xE9, 0x6A, 0xEB, 0x8B, 0x75, 0x6C,
        0xC6, 0x79, 0x38, 0xF2, 0xC9, 0x72, 0x4A, 0x86, 0x64, 0x54,
        0x95, 0x77, 0xCB, 0xC3, 0x9A, 0x9D, 0xB7, 0xD4, 0x1D, 0xA4,
        0x00, 0xC8, 0x9E, 0x4E, 0xE4, 0xDD, 0xC7, 0xBA, 0x67, 0x16,
        0xC1, 0x74, 0xBC, 0xA9, 0xD6, 0x94, 0x8F, 0x2B, 0x30, 0x1A,
        0xFB, 0xED, 0xDF, 0x21, 0x05, 0x23, 0xD9, 0x4A, 0x39, 0xBD,
        0x98, 0x6B, 0x65, 0x9A, 0xB8, 0xDC, 0xC4, 0x7D, 0xEE, 0xA6,
        0x43, 0x15, 0x2E, 0x3D, 0xBE, 0x1D, 0x22, 0x60, 0x2A, 0x73,
        0x30, 0xD5, 0x3E, 0xD8, 0xA2, 0xAC, 0x86, 0x43, 0x2E, 0xC4,
        0xF5, 0x64, 0x5E, 0x3F, 0x89, 0x75, 0x0F, 0x11, 0xD8, 0x51,
        0x25, 0x4E, 0x9F, 0xD8, 0xAA, 0xA3, 0xCE, 0x60, 0xB3, 0xE2,
        0x8A, 0xD9, 0x7E, 0x1B, 0xF0, 0x64, 0xCA, 0x9A, 0x5B, 0x05,
        0x0B, 0x5B, 0xAA, 0xCB, 0xE5, 0xE3, 0x3F, 0x6E, 0x32, 0x22,
        0x05, 0xF3, 0xD0, 0xFA, 0xEF, 0x74, 0x52, 0x81, 0xE2, 0x5F,
        0x74, 0xD3, 0xBD, 0xFF, 0x31, 0x83, 0x45, 0x75, 0xFA, 0x63,
        0x7A, 0x97, 0x2E, 0xD6, 0xB6, 0x19, 0xC6, 0x92, 0x26, 0xE4,
        0x28, 0x06, 0x50, 0x50, 0x0E, 0x78, 0x2E, 0xA9, 0x78, 0x0D,
        0x14, 0x97, 0xB4, 0x12, 0xD8, 0x31, 0x40, 0xAB, 0xA1, 0x01,
        0x41, 0xC2, 0x30, 0xF8, 0x07, 0x5F, 0x16, 0xE4, 0x61, 0x77,
        0xD2, 0x60, 0xF2, 0x9F, 0x8D, 0xE8, 0xF4, 0xBA, 0xEB, 0x63,
        0xDE, 0x2A, 0x97, 0x81, 0xEF, 0x4C, 0x6C, 0xE6, 0x55, 0x34,
        0x51, 0x2B, 0x28, 0x34, 0xF4, 0x53, 0x1C, 0xC4, 0x58, 0x0A,
        0x3F, 0xBB, 0xAF, 0xB5, 0xF7, 0x4A, 0x85, 0x43, 0x2D, 0x3C,
        0xF1, 0x58, 0x58, 0x81, 0x02, 0x81, 0x81, 0x00, 0xF2, 0x2C,
        0x54, 0x76, 0x39, 0x23, 0x63, 0xC9, 0x10, 0x32, 0xB7, 0x93,
        0xAD, 0xAF, 0xBE, 0x19, 0x75, 0x96, 0x81, 0x64, 0xE6, 0xB5,
        0xB8, 0x89, 0x42, 0x41, 0xD1, 0x6D, 0xD0, 0x1C, 0x1B, 0xF8,
        0x1B, 0xAC, 0x69, 0xCB, 0x36, 0x3C, 0x64, 0x7D, 0xDC, 0xF4,
        0x19, 0xB8, 0xC3, 0x60, 0xB1, 0x57, 0x48, 0x5F, 0x52, 0x4F,
        0x59, 0x3A, 0x55, 0x7F, 0x32, 0xC0, 0x19, 0x43, 0x50, 0x3F,
        0xAE, 0xCE, 0x6F, 0x17, 0xF3, 0x0E, 0x9F, 0x40, 0xCA, 0x4E,
        0xAD, 0x15, 0x3B, 0xC9, 0x79, 0xE9, 0xC0, 0x59, 0x38, 0x73,
        0x70, 0x9C, 0x0A, 0x7C, 0xC9, 0x3A, 0x48, 0x32, 0xA7, 0xD8,
        0x49, 0x75, 0x0A, 0x85, 0xC2, 0xC2, 0xFD, 0x15, 0x73, 0xDA,
        0x99, 0x09, 0x2A, 0x69, 0x9A, 0x9F, 0x0A, 0x71, 0xBF, 0xB0,
        0x04, 0xA6, 0x8C, 0x7A, 0x5A, 0x6F, 0x48, 0x5A, 0x54, 0x3B,
        0xC6, 0xB1, 0x53, 0x17, 0xDF, 0xE7, 0x02, 0x81, 0x81, 0x00,
        0xCB, 0x93, 0xDE, 0x77, 0x15, 0x5D, 0xB7, 0x5C, 0x5C, 0x7C,
        0xD8, 0x90, 0xA9, 0x98, 0x2D, 0xD6, 0x69, 0x0E, 0x63, 0xB3,
        0xA3, 0xDC, 0xA6, 0xCC, 0x8B, 0x6A, 0xA4, 0xA2, 0x12, 0x8C,
        0x8E, 0x7B, 0x48, 0x2C, 0xB2, 0x4B, 0x37, 0xDC, 0x06, 0x18,
        0x7D, 0xEA, 0xFE, 0x76, 0xA1, 0xD4, 0xA1, 0xE9, 0x3F, 0x0D,
        0xCD, 0x1B, 0x5F, 0xAF, 0x5F, 0x9E, 0x96, 0x5B, 0x5B, 0x0F,
        0xA1, 0x7C, 0xAF, 0xB3, 0x9B, 0x90, 0xDB, 0x57, 0x73, 0x3A,
        0xED, 0xB0, 0x23, 0x44, 0xAE, 0x41, 0x4F, 0x1F, 0x07, 0x42,
        0x13, 0x23, 0x4C, 0xCB, 0xFA, 0xF4, 0x14, 0xA4, 0xD5, 0xF7,
        0x9E, 0x36, 0x7C, 0x5B, 0x9F, 0xA8, 0x3C, 0xC1, 0x85, 0x5F,
        0x74, 0xD2, 0x39, 0x2D, 0xFF, 0xD0, 0x84, 0xDF, 0xFB, 0xB3,
        0x20, 0x7A, 0x2E, 0x9B, 0x17, 0xAE, 0xE6, 0xBA, 0x0B, 0xAE,
        0x5F, 0x53, 0xA4, 0x52, 0xED, 0x1B, 0xC4, 0x91, 0x02, 0x81,
        0x81, 0x00, 0xEC, 0x98, 0xDA, 0xBB, 0xD5, 0xFE, 0xF9, 0x52,
        0x4A, 0x7D, 0x02, 0x55, 0x49, 0x6F, 0x55, 0x6E, 0x52, 0x2F,
        0x84, 0xA3, 0x2B, 0xB3, 0x86, 0x62, 0xB3, 0x54, 0xD2, 0x63,
        0x52, 0xDA, 0xE3, 0x88, 0x76, 0xA0, 0xEF, 0x8B, 0x15, 0xA5,
        0xD3, 0x18, 0x14, 0x72, 0x77, 0x5E, 0xC7, 0xA3, 0x04, 0x1F,
        0x9E, 0x19, 0x62, 0xB5, 0x1B, 0x1B, 0x9E, 0xC3, 0xF2, 0xB5,
        0x32, 0xF9, 0x4C, 0xC1, 0xAA, 0xEB, 0x0C, 0x26, 0x7D, 0xD4,
        0x5F, 0x4A, 0x51, 0x5C, 0xA4, 0x45, 0x06, 0x70, 0x44, 0xA7,
        0x56, 0xC0, 0xD4, 0x22, 0x14, 0x76, 0x9E, 0xD8, 0x63, 0x50,
        0x89, 0x90, 0xD3, 0xE2, 0xBF, 0x81, 0x95, 0x92, 0x31, 0x41,
        0x87, 0x39, 0x1A, 0x43, 0x0B, 0x18, 0xA5, 0x53, 0x1F, 0x39,
        0x1A, 0x5F, 0x1F, 0x43, 0xBC, 0x87, 0x6A, 0xDF, 0x6E, 0xD3,
        0x22, 0x00, 0xFE, 0x22, 0x98, 0x70, 0x4E, 0x1A, 0x19, 0x29,
        0x02, 0x81, 0x81, 0x00, 0x8A, 0x41, 0x56, 0x28, 0x51, 0x9E,
        0x5F, 0xD4, 0x9E, 0x0B, 0x3B, 0x98, 0xA3, 0x54, 0xF2, 0x6C,
        0x56, 0xD4, 0xAA, 0xE9, 0x69, 0x33, 0x85, 0x24, 0x0C, 0xDA,
        0xD4, 0x0C, 0x2D, 0xC4, 0xBF, 0x4F, 0x02, 0x69, 0x38, 0x7C,
        0xD4, 0xE6, 0xDC, 0x4C, 0xED, 0xD7, 0x16, 0x11, 0xC3, 0x3E,
        0x00, 0xE7, 0xC3, 0x26, 0xC0, 0x51, 0x02, 0xDE, 0xBB, 0x75,
        0x9C, 0x6F, 0x56, 0x9C, 0x7A, 0xF3, 0x8E, 0xEF, 0xCF, 0x8A,
        0xC5, 0x2B, 0xD2, 0xDA, 0x06, 0x6A, 0x44, 0xC9, 0x73, 0xFE,
        0x6E, 0x99, 0x87, 0xF8, 0x5B, 0xBE, 0xF1, 0x7C, 0xE6, 0x65,
        0xB5, 0x4F, 0x6C, 0xF0, 0xC9, 0xC5, 0xFF, 0x16, 0xCA, 0x8B,
        0x1B, 0x17, 0xE2, 0x58, 0x3D, 0xA2, 0x37, 0xAB, 0x01, 0xBC,
        0xBF, 0x40, 0xCE, 0x53, 0x8C, 0x8E, 0xED, 0xEF, 0xEE, 0x59,
        0x9D, 0xE0, 0x63, 0xE6, 0x7C, 0x5E, 0xF5, 0x8E, 0x4B, 0xF1,
        0x3B, 0xC1, 0x02, 0x81, 0x80, 0x4D, 0x45, 0xF9, 0x40, 0x8C,
        0xC5, 0x5B, 0xF4, 0x2A, 0x1A, 0x8A, 0xB4, 0xF2, 0x1C, 0xAC,
        0x6B, 0xE9, 0x0C, 0x56, 0x36, 0xB7, 0x4E, 0x72, 0x96, 0xD5,
        0xE5, 0x8A, 0xD2, 0xE2, 0xFF, 0xF1, 0xF1, 0x18, 0x13, 0x3D,
        0x86, 0x09, 0xB8, 0xD8, 0x76, 0xA7, 0xC9, 0x1C, 0x71, 0x52,
        0x94, 0x30, 0x43, 0xE0, 0xF1, 0x78, 0x74, 0xFD, 0x61, 0x1B,
        0x4C, 0x09, 0xCC, 0xE6, 0x68, 0x2A, 0x71, 0xAD, 0x1C, 0xDF,
        0x43, 0xBC, 0x56, 0xDB, 0xA5, 0xA4, 0xBE, 0x35, 0x70, 0xA4,
        0x5E, 0xCF, 0x4F, 0xFC, 0x00, 0x55, 0x99, 0x3A, 0x3D, 0x23,
        0xCF, 0x67, 0x5A, 0xF5, 0x22, 0xF8, 0xB5, 0x29, 0xD0, 0x44,
        0x11, 0xEB, 0x35, 0x2E, 0x46, 0xBE, 0xFD, 0x8E, 0x18, 0xB2,
        0x5F, 0xA8, 0xBF, 0x19, 0x32, 0xA1, 0xF5, 0xDC, 0x03, 0xE6,
        0x7C, 0x9A, 0x1F, 0x0C, 0x7C, 0xA9, 0xB0, 0x0E, 0x21, 0x37,
        0x3B, 0xF1, 0xB0
};
static const int sizeof_server_key_der_2048 = sizeof(server_key_der_2048);

/* ./certs/server-cert.der, 2048-bit */
static const unsigned char server_cert_der_2048[] =
{
        0x30, 0x82, 0x04, 0xE8, 0x30, 0x82, 0x03, 0xD0, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x01, 0x01, 0x30, 0x0D, 0x06, 0x09,
        0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B, 0x05,
        0x00, 0x30, 0x81, 0x94, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03,
        0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x10, 0x30,
        0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F,
        0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E, 0x06,
        0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65,
        0x6D, 0x61, 0x6E, 0x31, 0x11, 0x30, 0x0F, 0x06, 0x03, 0x55,
        0x04, 0x0A, 0x0C, 0x08, 0x53, 0x61, 0x77, 0x74, 0x6F, 0x6F,
        0x74, 0x68, 0x31, 0x13, 0x30, 0x11, 0x06, 0x03, 0x55, 0x04,
        0x0B, 0x0C, 0x0A, 0x43, 0x6F, 0x6E, 0x73, 0x75, 0x6C, 0x74,
        0x69, 0x6E, 0x67, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55,
        0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F,
        0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31,
        0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7,
        0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F,
        0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63,
        0x6F, 0x6D, 0x30, 0x1E, 0x17, 0x0D, 0x32, 0x34, 0x31, 0x32,
        0x31, 0x38, 0x32, 0x31, 0x32, 0x35, 0x33, 0x30, 0x5A, 0x17,
        0x0D, 0x32, 0x37, 0x30, 0x39, 0x31, 0x34, 0x32, 0x31, 0x32,
        0x35, 0x33, 0x30, 0x5A, 0x30, 0x81, 0x90, 0x31, 0x0B, 0x30,
        0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53,
        0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C,
        0x07, 0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10,
        0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42,
        0x6F, 0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x10, 0x30, 0x0E,
        0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x07, 0x77, 0x6F, 0x6C,
        0x66, 0x53, 0x53, 0x4C, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03,
        0x55, 0x04, 0x0B, 0x0C, 0x07, 0x53, 0x75, 0x70, 0x70, 0x6F,
        0x72, 0x74, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04,
        0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F,
        0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D,
        0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x30, 0x82, 0x01, 0x22, 0x30, 0x0D, 0x06, 0x09, 0x2A,
        0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01, 0x05, 0x00,
        0x03, 0x82, 0x01, 0x0F, 0x00, 0x30, 0x82, 0x01, 0x0A, 0x02,
        0x82, 0x01, 0x01, 0x00, 0xC0, 0x95, 0x08, 0xE1, 0x57, 0x41,
        0xF2, 0x71, 0x6D, 0xB7, 0xD2, 0x45, 0x41, 0x27, 0x01, 0x65,
        0xC6, 0x45, 0xAE, 0xF2, 0xBC, 0x24, 0x30, 0xB8, 0x95, 0xCE,
        0x2F, 0x4E, 0xD6, 0xF6, 0x1C, 0x88, 0xBC, 0x7C, 0x9F, 0xFB,
        0xA8, 0x67, 0x7F, 0xFE, 0x5C, 0x9C, 0x51, 0x75, 0xF7, 0x8A,
        0xCA, 0x07, 0xE7, 0x35, 0x2F, 0x8F, 0xE1, 0xBD, 0x7B, 0xC0,
        0x2F, 0x7C, 0xAB, 0x64, 0xA8, 0x17, 0xFC, 0xCA, 0x5D, 0x7B,
        0xBA, 0xE0, 0x21, 0xE5, 0x72, 0x2E, 0x6F, 0x2E, 0x86, 0xD8,
        0x95, 0x73, 0xDA, 0xAC, 0x1B, 0x53, 0xB9, 0x5F, 0x3F, 0xD7,
        0x19, 0x0D, 0x25, 0x4F, 0xE1, 0x63, 0x63, 0x51, 0x8B, 0x0B,
        0x64, 0x3F, 0xAD, 0x43, 0xB8, 0xA5, 0x1C, 0x5C, 0x34, 0xB3,
        0xAE, 0x00, 0xA0, 0x63, 0xC5, 0xF6, 0x7F, 0x0B, 0x59, 0x68,
        0x78, 0x73, 0xA6, 0x8C, 0x18, 0xA9, 0x02, 0x6D, 0xAF, 0xC3,
        0x19, 0x01, 0x2E, 0xB8, 0x10, 0xE3, 0xC6, 0xCC, 0x40, 0xB4,
        0x69, 0xA3, 0x46, 0x33, 0x69, 0x87, 0x6E, 0xC4, 0xBB, 0x17,
        0xA6, 0xF3, 0xE8, 0xDD, 0xAD, 0x73, 0xBC, 0x7B, 0x2F, 0x21,
        0xB5, 0xFD, 0x66, 0x51, 0x0C, 0xBD, 0x54, 0xB3, 0xE1, 0x6D,
        0x5F, 0x1C, 0xBC, 0x23, 0x73, 0xD1, 0x09, 0x03, 0x89, 0x14,
        0xD2, 0x10, 0xB9, 0x64, 0xC3, 0x2A, 0xD0, 0xA1, 0x96, 0x4A,
        0xBC, 0xE1, 0xD4, 0x1A, 0x5B, 0xC7, 0xA0, 0xC0, 0xC1, 0x63,
        0x78, 0x0F, 0x44, 0x37, 0x30, 0x32, 0x96, 0x80, 0x32, 0x23,
        0x95, 0xA1, 0x77, 0xBA, 0x13, 0xD2, 0x97, 0x73, 0xE2, 0x5D,
        0x25, 0xC9, 0x6A, 0x0D, 0xC3, 0x39, 0x60, 0xA4, 0xB4, 0xB0,
        0x69, 0x42, 0x42, 0x09, 0xE9, 0xD8, 0x08, 0xBC, 0x33, 0x20,
        0xB3, 0x58, 0x22, 0xA7, 0xAA, 0xEB, 0xC4, 0xE1, 0xE6, 0x61,
        0x83, 0xC5, 0xD2, 0x96, 0xDF, 0xD9, 0xD0, 0x4F, 0xAD, 0xD7,
        0x02, 0x03, 0x01, 0x00, 0x01, 0xA3, 0x82, 0x01, 0x45, 0x30,
        0x82, 0x01, 0x41, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D, 0x0E,
        0x04, 0x16, 0x04, 0x14, 0xB3, 0x11, 0x32, 0xC9, 0x92, 0x98,
        0x84, 0xE2, 0xC9, 0xF8, 0xD0, 0x3B, 0x6E, 0x03, 0x42, 0xCA,
        0x1F, 0x0E, 0x8E, 0x3C, 0x30, 0x81, 0xD4, 0x06, 0x03, 0x55,
        0x1D, 0x23, 0x04, 0x81, 0xCC, 0x30, 0x81, 0xC9, 0x80, 0x14,
        0x27, 0x8E, 0x67, 0x11, 0x74, 0xC3, 0x26, 0x1D, 0x3F, 0xED,
        0x33, 0x63, 0xB3, 0xA4, 0xD8, 0x1D, 0x30, 0xE5, 0xE8, 0xD5,
        0xA1, 0x81, 0x9A, 0xA4, 0x81, 0x97, 0x30, 0x81, 0x94, 0x31,
        0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02,
        0x55, 0x53, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04,
        0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61,
        0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C,
        0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x11,
        0x30, 0x0F, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x08, 0x53,
        0x61, 0x77, 0x74, 0x6F, 0x6F, 0x74, 0x68, 0x31, 0x13, 0x30,
        0x11, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x0A, 0x43, 0x6F,
        0x6E, 0x73, 0x75, 0x6C, 0x74, 0x69, 0x6E, 0x67, 0x31, 0x18,
        0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77,
        0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C,
        0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09,
        0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16,
        0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66,
        0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x82, 0x14, 0x6B,
        0x9B, 0x70, 0xC6, 0xF1, 0xA3, 0x94, 0x65, 0x19, 0xA1, 0x08,
        0x58, 0xEF, 0xA7, 0x8D, 0x2B, 0x7A, 0x83, 0xC1, 0xDA, 0x30,
        0x0C, 0x06, 0x03, 0x55, 0x1D, 0x13, 0x04, 0x05, 0x30, 0x03,
        0x01, 0x01, 0xFF, 0x30, 0x1C, 0x06, 0x03, 0x55, 0x1D, 0x11,
        0x04, 0x15, 0x30, 0x13, 0x82, 0x0B, 0x65, 0x78, 0x61, 0x6D,
        0x70, 0x6C, 0x65, 0x2E, 0x63, 0x6F, 0x6D, 0x87, 0x04, 0x7F,
        0x00, 0x00, 0x01, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D, 0x25,
        0x04, 0x16, 0x30, 0x14, 0x06, 0x08, 0x2B, 0x06, 0x01, 0x05,
        0x05, 0x07, 0x03, 0x01, 0x06, 0x08, 0x2B, 0x06, 0x01, 0x05,
        0x05, 0x07, 0x03, 0x02, 0x30, 0x0D, 0x06, 0x09, 0x2A, 0x86,
        0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B, 0x05, 0x00, 0x03,
        0x82, 0x01, 0x01, 0x00, 0x8A, 0xF1, 0x4E, 0xE8, 0x9F, 0x59,
        0xB2, 0xD9, 0x13, 0xAC, 0xFC, 0x42, 0xC4, 0x81, 0x34, 0x9F,
        0x6B, 0x39, 0x57, 0x9C, 0xE9, 0x92, 0x5D, 0x41, 0xAC, 0x05,
        0x35, 0xB1, 0x26, 0x93, 0x4D, 0x4A, 0xDA, 0xF8, 0x51, 0x82,
        0xD2, 0x8D, 0x7F, 0xD3, 0x5C, 0x6E, 0x29, 0x80, 0x8D, 0x9B,
        0x02, 0x10, 0x2B, 0x64, 0xF5, 0xD1, 0x31, 0x06, 0xFA, 0x85,
        0x2B, 0x8F, 0x63, 0x32, 0x14, 0x76, 0x7A, 0x39, 0x15, 0xF3,
        0x4E, 0xDD, 0xFD, 0xE2, 0x2C, 0x90, 0x15, 0xD1, 0x6F, 0x73,
        0x87, 0xEE, 0xE6, 0xC8, 0xEB, 0xAD, 0x40, 0xD5, 0xE8, 0x94,
        0x1F, 0xA6, 0x7E, 0x26, 0x5B, 0x87, 0xBA, 0x0F, 0x06, 0x5A,
        0x4D, 0x55, 0x7A, 0xAA, 0xC4, 0x09, 0x34, 0x8B, 0xF7, 0xE5,
        0xCC, 0xD6, 0xB7, 0x6C, 0x46, 0x6D, 0xA1, 0xE6, 0x66, 0x66,
        0x4C, 0x4B, 0xE5, 0x12, 0x31, 0x37, 0x54, 0x49, 0x64, 0xA5,
        0x66, 0xEB, 0xE0, 0xC6, 0xA1, 0x49, 0xF8, 0x4D, 0xC3, 0xD3,
        0x55, 0xA4, 0x05, 0xD2, 0xAC, 0xFB, 0xE1, 0xC8, 0x69, 0x30,
        0x4B, 0x98, 0xFD, 0x72, 0x1A, 0xAB, 0x9F, 0x86, 0xEB, 0x0D,
        0xBD, 0x7C, 0xA6, 0x3D, 0x81, 0xD9, 0x01, 0xA7, 0x8A, 0x79,
        0xAB, 0x3C, 0xCE, 0xE5, 0xB6, 0xC3, 0x1B, 0xEF, 0x7D, 0x5E,
        0x37, 0x7B, 0x37, 0x7C, 0x91, 0x89, 0x59, 0x11, 0x21, 0x11,
        0x7C, 0x05, 0x80, 0xE1, 0xA8, 0xD6, 0xF9, 0x35, 0xDA, 0x1B,
        0x86, 0x06, 0x5A, 0x32, 0x67, 0x6C, 0xA9, 0x2B, 0xE0, 0x31,
        0x7B, 0x89, 0x53, 0x37, 0x42, 0xAF, 0x34, 0xA4, 0x53, 0xD2,
        0x7C, 0x91, 0x50, 0x63, 0x3A, 0x8E, 0x4A, 0x1F, 0xA3, 0x90,
        0x4E, 0x7C, 0x41, 0x59, 0x1D, 0xEB, 0x7B, 0xA2, 0x14, 0x87,
        0xBA, 0x76, 0x36, 0xA4, 0x77, 0x46, 0x34, 0xF2, 0x55, 0x50,
        0xF0, 0x24, 0x9F, 0x83, 0x83, 0xDA, 0xA6, 0xAA, 0x3C, 0xC8

};
static const int sizeof_server_cert_der_2048 = sizeof(server_cert_der_2048);

#endif /* USE_CERT_BUFFERS_2048 */

#ifdef USE_CERT_BUFFERS_3072

/* ./certs/dh3072.der, 3072-bit */
static const unsigned char dh_key_der_3072[] =
{
        0x30, 0x82, 0x01, 0x88, 0x02, 0x82, 0x01, 0x81, 0x00, 0x89,
        0x1B, 0x75, 0x3F, 0x84, 0xB6, 0x11, 0xED, 0x21, 0xF1, 0x08,
        0x0F, 0xB8, 0x06, 0xC9, 0xA3, 0xC9, 0x41, 0xDB, 0x5A, 0xC8,
        0xF8, 0x82, 0x73, 0x0F, 0xEB, 0x89, 0x1E, 0x54, 0x18, 0xBE,
        0xE6, 0x48, 0x41, 0x9E, 0xFA, 0xC2, 0x0C, 0x50, 0x67, 0xC3,
        0x5D, 0xB5, 0xF5, 0x0F, 0x23, 0x6A, 0x43, 0x33, 0x91, 0xD9,
        0x40, 0xF3, 0x66, 0xC6, 0x99, 0xFF, 0x97, 0xB6, 0x7B, 0xAF,
        0x27, 0x72, 0x3B, 0x9F, 0x7E, 0x58, 0x18, 0x14, 0x9F, 0x91,
        0x6E, 0x2B, 0x11, 0xC1, 0x57, 0x49, 0x27, 0x36, 0x78, 0xE1,
        0x09, 0x68, 0x9C, 0x05, 0x5A, 0xAC, 0xE6, 0x00, 0x38, 0xBE,
        0x95, 0x74, 0x81, 0x53, 0x28, 0xF0, 0xAD, 0xDF, 0xB5, 0x87,
        0x1C, 0x72, 0x17, 0x4E, 0xEC, 0x00, 0x91, 0x22, 0xAA, 0xE4,
        0x88, 0xD7, 0xF5, 0x3D, 0x1F, 0x03, 0x13, 0x2D, 0x1C, 0xFB,
        0xDE, 0x59, 0x68, 0xAD, 0xE0, 0x17, 0xA1, 0xEE, 0x8D, 0xCC,
        0xBF, 0xFE, 0xCF, 0x24, 0x42, 0xED, 0x26, 0xDD, 0x29, 0xD0,
        0x4E, 0x62, 0x3C, 0x85, 0x36, 0x1B, 0x5F, 0x6A, 0x47, 0x88,
        0x21, 0xE5, 0x1B, 0x85, 0x0A, 0x2C, 0xE9, 0x2F, 0xE0, 0x20,
        0xFC, 0x1D, 0xCD, 0x55, 0x66, 0xF5, 0xAC, 0x32, 0x00, 0x8E,
        0xA3, 0xE9, 0xED, 0xFB, 0x35, 0xA7, 0xE6, 0x76, 0x53, 0x42,
        0xC6, 0x77, 0x77, 0xAB, 0x90, 0x99, 0x7C, 0xC2, 0xEC, 0xC9,
        0x18, 0x4A, 0x3C, 0xF4, 0x11, 0x75, 0x27, 0x83, 0xBD, 0x9E,
        0xC2, 0x8F, 0x23, 0xAB, 0x52, 0x46, 0xE2, 0x52, 0x5D, 0x9A,
        0x04, 0xC3, 0x15, 0x1F, 0x69, 0x9C, 0x72, 0x69, 0x59, 0x52,
        0xD4, 0x69, 0x3D, 0x19, 0x77, 0x36, 0x25, 0xAF, 0x07, 0x71,
        0x82, 0xDE, 0xB7, 0x24, 0x60, 0x82, 0x6A, 0x72, 0xBB, 0xED,
        0xB6, 0x76, 0xAE, 0x7E, 0xBC, 0x7D, 0x2F, 0x73, 0x4B, 0x04,
        0x16, 0xD5, 0xA4, 0xF3, 0x03, 0x26, 0xFB, 0xF3, 0xCD, 0x7B,
        0x77, 0x7E, 0x7C, 0x8D, 0x65, 0xAE, 0xA5, 0xDC, 0x6C, 0xE3,
        0x70, 0xD2, 0x29, 0x6B, 0xF2, 0xEB, 0x76, 0xC9, 0xE5, 0x46,
        0x18, 0x12, 0x57, 0xB0, 0x55, 0xA5, 0x7C, 0xCD, 0x41, 0x93,
        0x26, 0x99, 0xF7, 0xA5, 0xC5, 0x34, 0xBE, 0x59, 0x79, 0xDE,
        0x0A, 0x57, 0x5F, 0x21, 0xF8, 0x98, 0x52, 0xF0, 0x2F, 0x7B,
        0x57, 0xB6, 0x9D, 0xFC, 0x40, 0xA6, 0x55, 0xFB, 0xAF, 0xD9,
        0x16, 0x9B, 0x20, 0x4F, 0xA8, 0xA3, 0x0B, 0x04, 0x48, 0xE3,
        0x77, 0x22, 0xC4, 0xCC, 0x57, 0x14, 0x33, 0xA2, 0xF0, 0x9A,
        0xE3, 0x12, 0xBD, 0xFF, 0x72, 0x8B, 0xEE, 0x52, 0xF3, 0xC9,
        0x59, 0xC2, 0xA2, 0x6B, 0xA5, 0x75, 0x48, 0x51, 0x82, 0x0E,
        0x7A, 0xFF, 0xFE, 0x41, 0xCD, 0x7C, 0x63, 0xD2, 0x53, 0xA8,
        0x11, 0x03, 0xB9, 0x03, 0x07, 0xFE, 0x66, 0x38, 0x5F, 0xA2,
        0x3E, 0x9C, 0x1B, 0x02, 0x01, 0x02
};
static const int sizeof_dh_key_der_3072 = sizeof(dh_key_der_3072);

/* ./certs/dsa3072.der, 3072-bit */
static const unsigned char dsa_key_der_3072[] =
{
        0x30, 0x82, 0x04, 0xD7, 0x02, 0x01, 0x00, 0x02, 0x82, 0x01,
        0x81, 0x00, 0xB5, 0xD0, 0x2F, 0x55, 0xC1, 0x27, 0x4C, 0x5B,
        0x28, 0x81, 0x4E, 0xA4, 0x32, 0x0D, 0x73, 0x54, 0x68, 0x4F,
        0x0A, 0x36, 0x68, 0x4A, 0x51, 0xBE, 0xDE, 0x49, 0xD4, 0x9D,
        0xCE, 0xC6, 0xF7, 0x01, 0x70, 0xD2, 0x88, 0x90, 0x1D, 0x60,
        0x30, 0x9B, 0x0A, 0x9C, 0x23, 0xDA, 0xE0, 0x74, 0x46, 0x5B,
        0xC7, 0x41, 0x40, 0x5C, 0xD9, 0x7A, 0xBE, 0x78, 0xCA, 0x49,
        0xF5, 0x2D, 0x7B, 0xD7, 0xBF, 0x67, 0x0D, 0x84, 0x28, 0xBB,
        0x9D, 0xC2, 0xAB, 0x23, 0x06, 0x28, 0x0C, 0x98, 0x46, 0x43,
        0xCE, 0x6F, 0x9E, 0xD0, 0xE9, 0x0E, 0xF3, 0x7E, 0x30, 0x5D,
        0xD3, 0x45, 0x44, 0x7B, 0x0C, 0x7A, 0x73, 0xA6, 0x95, 0x65,
        0xAA, 0x8B, 0xD8, 0x75, 0x6A, 0x11, 0xB3, 0x10, 0x7C, 0x57,
        0xAF, 0xCE, 0xBE, 0x5B, 0xF7, 0xC8, 0xFE, 0x42, 0xA3, 0x77,
        0xB7, 0x0B, 0x3D, 0x66, 0xB5, 0x08, 0x74, 0x22, 0x74, 0x26,
        0xE6, 0xDB, 0x8E, 0xEF, 0xA3, 0x99, 0xAE, 0x0B, 0x42, 0x8C,
        0x5F, 0x7E, 0x48, 0xE9, 0x19, 0x90, 0xA8, 0x35, 0xA9, 0xFC,
        0x48, 0x0D, 0xC8, 0xB8, 0xE4, 0x1A, 0x0C, 0x26, 0xC7, 0x1A,
        0x20, 0x02, 0xEB, 0x72, 0x2E, 0x94, 0xD6, 0x19, 0x34, 0x39,
        0x55, 0x4E, 0xFC, 0x53, 0x48, 0xD8, 0x10, 0x89, 0xA1, 0x6E,
        0x22, 0x39, 0x71, 0x15, 0xA6, 0x13, 0xBC, 0x77, 0x49, 0x53,
        0xCB, 0x16, 0x4B, 0x56, 0x3D, 0x08, 0xA2, 0x71, 0x0E, 0x06,
        0x0C, 0x3A, 0xDE, 0x82, 0xC0, 0xDF, 0xE7, 0x96, 0x57, 0xD7,
        0x3F, 0x6B, 0xF0, 0xAE, 0xD1, 0x38, 0xB8, 0x5B, 0x83, 0x77,
        0x8B, 0xEB, 0x2B, 0xDA, 0x38, 0xC8, 0x4C, 0xA9, 0x48, 0x52,
        0xD8, 0x41, 0x03, 0xD3, 0x11, 0x1C, 0x66, 0x9E, 0xDE, 0xC9,
        0x78, 0x5A, 0xE1, 0x7B, 0xEA, 0x6F, 0xD6, 0xCA, 0x6A, 0x2F,
        0x01, 0xB2, 0x83, 0x37, 0x25, 0xD9, 0x9C, 0xD4, 0xB0, 0x21,
        0xD9, 0x8F, 0xA6, 0xF8, 0xD6, 0x21, 0x82, 0xBB, 0x08, 0x64,
        0x28, 0x0E, 0x0C, 0x26, 0xE6, 0xA5, 0x69, 0xE0, 0x23, 0xE9,
        0xB3, 0xC4, 0xF9, 0xDE, 0xC6, 0xD6, 0x32, 0x00, 0x66, 0x9B,
        0x8A, 0x0B, 0x6F, 0xDE, 0xB8, 0xDD, 0x68, 0x7F, 0x9D, 0x68,
        0x59, 0x6B, 0x55, 0xD9, 0x53, 0x01, 0x7B, 0x1A, 0x1C, 0x8D,
        0xBF, 0xAF, 0xC0, 0xB1, 0x14, 0x9E, 0xC1, 0x8D, 0x3E, 0x1E,
        0xFB, 0x40, 0xF9, 0x6D, 0x48, 0x43, 0xCD, 0x6C, 0xE8, 0xBC,
        0x3C, 0x7C, 0x35, 0x3C, 0x65, 0x6D, 0xA0, 0x25, 0x87, 0xBF,
        0xEC, 0x9B, 0x12, 0x74, 0x48, 0xC8, 0xE4, 0xBF, 0x53, 0x53,
        0x47, 0x78, 0xD9, 0x9B, 0x1A, 0xA5, 0x07, 0x46, 0x15, 0x16,
        0xD2, 0x33, 0x93, 0xCC, 0x41, 0x9B, 0xB7, 0x22, 0xDF, 0x07,
        0xDD, 0x72, 0xC6, 0x1A, 0x9B, 0x92, 0xE7, 0x32, 0x04, 0xAB,
        0x94, 0x80, 0xBD, 0x58, 0xF2, 0x35, 0x02, 0x21, 0x00, 0x9A,
        0xDD, 0x98, 0x1A, 0x6F, 0xEA, 0xB5, 0x8B, 0xC9, 0x68, 0x18,
        0x81, 0xE4, 0x4C, 0xFD, 0x8E, 0x45, 0xCF, 0x5F, 0x0E, 0x62,
        0x1E, 0x7D, 0x2D, 0x4A, 0x4C, 0x5D, 0x7F, 0xF8, 0xD8, 0x52,
        0xD7, 0x02, 0x82, 0x01, 0x81, 0x00, 0x84, 0xDF, 0xAB, 0x91,
        0x61, 0xE4, 0x2B, 0x07, 0x0A, 0x1C, 0xC7, 0x9C, 0xD7, 0xAC,
        0x8D, 0xA5, 0xAA, 0x41, 0x65, 0x9E, 0x4A, 0xED, 0x21, 0x45,
        0x96, 0xF7, 0xF7, 0xCB, 0x7A, 0x88, 0x19, 0x0F, 0x36, 0x80,
        0x25, 0x2F, 0x23, 0x0D, 0xFF, 0x6C, 0x0D, 0x02, 0xBB, 0x6A,
        0x79, 0x6A, 0xCB, 0x05, 0x00, 0x9B, 0x77, 0xED, 0x6B, 0xF3,
        0xC2, 0xEA, 0x1A, 0xDF, 0xB8, 0x15, 0xA8, 0x92, 0x19, 0x5A,
        0x51, 0x3B, 0x76, 0x06, 0x98, 0x47, 0xC7, 0x6F, 0x76, 0x99,
        0xAD, 0x50, 0xC5, 0x98, 0xE7, 0xFF, 0x88, 0xBC, 0x49, 0x77,
        0xEF, 0x96, 0x75, 0xE2, 0x36, 0x66, 0x1F, 0x0C, 0xFA, 0x57,
        0x1E, 0x11, 0xFF, 0x8F, 0x3C, 0xD0, 0xEA, 0x97, 0x25, 0x3F,
        0xFA, 0xD1, 0x4F, 0xBA, 0xDF, 0xE3, 0x35, 0xFB, 0x6E, 0x5C,
        0x65, 0xF9, 0xA2, 0x26, 0x43, 0xF2, 0xF4, 0xE0, 0x05, 0x3D,
        0xC6, 0x5B, 0xC4, 0x21, 0xE7, 0xB1, 0x02, 0xEB, 0xF2, 0xA9,
        0x06, 0x5E, 0xB7, 0x1B, 0xC1, 0xD8, 0x86, 0x34, 0xED, 0x84,
        0x89, 0xCE, 0xCE, 0xC2, 0x63, 0x78, 0x67, 0xF8, 0xC3, 0xAA,
        0x7C, 0x1C, 0x59, 0x32, 0xE4, 0x77, 0xA2, 0x36, 0x31, 0xFE,
        0x4B, 0x9C, 0x98, 0xCE, 0x01, 0x55, 0x61, 0xCE, 0x23, 0xAE,
        0x0F, 0x7E, 0x24, 0x8B, 0x54, 0x8A, 0xE4, 0xCB, 0x8E, 0xDC,
        0x7A, 0x94, 0x4C, 0xF9, 0x3C, 0xF8, 0x67, 0x68, 0x9D, 0x7A,
        0x82, 0xA1, 0xA0, 0x01, 0xC7, 0x1B, 0x8D, 0xA0, 0xC0, 0x53,
        0x1E, 0x93, 0xC7, 0x86, 0x12, 0xD3, 0x16, 0xDC, 0x28, 0xA0,
        0xD1, 0x0D, 0x1E, 0x42, 0x9A, 0xCB, 0x55, 0x8C, 0x22, 0x7F,
        0x41, 0xC3, 0xC9, 0x14, 0xF2, 0xB0, 0x73, 0xA1, 0x4D, 0x72,
        0xFD, 0x88, 0xB6, 0xDE, 0xE5, 0xF0, 0x3C, 0x3A, 0x7E, 0x68,
        0x3E, 0x82, 0x58, 0x60, 0xCD, 0xB4, 0x08, 0x64, 0x18, 0xB2,
        0x24, 0x97, 0x13, 0xA6, 0x07, 0x75, 0xBE, 0xE0, 0x14, 0x92,
        0x9A, 0x98, 0x6C, 0x08, 0x94, 0xD1, 0x0D, 0x48, 0x44, 0xC3,
        0xE3, 0xD5, 0xC0, 0x93, 0x49, 0x79, 0x2F, 0x67, 0x15, 0x76,
        0xD8, 0x90, 0x11, 0xDB, 0xEC, 0xA7, 0xE2, 0xDB, 0xD4, 0x4F,
        0x49, 0x5E, 0xEF, 0xC5, 0xB9, 0x77, 0x69, 0xDA, 0x02, 0xB7,
        0x23, 0xBC, 0xEA, 0xDC, 0x84, 0xD4, 0xA5, 0x5C, 0xA2, 0x6C,
        0xAD, 0x4A, 0x9F, 0xF0, 0x65, 0x48, 0xE9, 0xBF, 0xDF, 0xA5,
        0xB3, 0x99, 0xD6, 0x76, 0x08, 0x87, 0x2C, 0xF2, 0x29, 0x79,
        0xB2, 0x20, 0x7C, 0x6F, 0xC1, 0xC5, 0x3C, 0xB0, 0x50, 0x3F,
        0x72, 0xA5, 0x57, 0xE3, 0xB0, 0x62, 0x18, 0x80, 0x71, 0xB9,
        0x3F, 0x4D, 0x4E, 0x7C, 0xF6, 0x29, 0xDB, 0xB8, 0xAD, 0xF6,
        0x41, 0x69, 0x06, 0x90, 0x45, 0x7B, 0x95, 0x03, 0xE1, 0x9E,
        0xA5, 0xA1, 0x5A, 0xE3, 0x08, 0x26, 0x73, 0xFC, 0x2B, 0x20,
        0x02, 0x82, 0x01, 0x81, 0x00, 0xA5, 0x52, 0x8F, 0x53, 0xF0,
        0xB9, 0x4F, 0x06, 0xB9, 0xC8, 0xB4, 0x50, 0xA4, 0x39, 0xBA,
        0x12, 0x92, 0x75, 0x27, 0x43, 0xA8, 0x30, 0xA9, 0xF2, 0x2A,
        0xC6, 0x93, 0x26, 0x3C, 0x8C, 0x9F, 0xA2, 0x6F, 0x53, 0xD9,
        0x14, 0xAB, 0x3F, 0x00, 0xC6, 0x11, 0x13, 0x90, 0x6A, 0x42,
        0xF2, 0x9D, 0xA3, 0x8F, 0x31, 0x32, 0x46, 0x73, 0xA3, 0x93,
        0x57, 0x5D, 0x76, 0x45, 0x49, 0x6C, 0xBD, 0xEA, 0xAF, 0xAA,
        0xB3, 0x55, 0x25, 0x11, 0x8E, 0xA5, 0x2A, 0xB1, 0xBA, 0xA5,
        0x06, 0x4A, 0x66, 0xAA, 0x78, 0x9E, 0xF6, 0x5C, 0x1E, 0xB1,
        0x4A, 0xCA, 0x5C, 0x3F, 0x1D, 0x33, 0x75, 0x91, 0xF2, 0xF9,
        0x53, 0x14, 0x2F, 0xDC, 0xF0, 0x4C, 0xA4, 0xF4, 0x50, 0x04,
        0x1F, 0xFF, 0xC9, 0x0C, 0xC6, 0x8A, 0x04, 0x8B, 0x80, 0x87,
        0xA7, 0x70, 0x49, 0xD7, 0xE4, 0xE7, 0x83, 0xF1, 0x86, 0x1A,
        0xB0, 0x85, 0x3C, 0x59, 0x04, 0x96, 0xD1, 0x85, 0x47, 0xA1,
        0x57, 0x7D, 0xC6, 0x8E, 0x60, 0x7D, 0xC6, 0xE8, 0x18, 0xB3,
        0x1F, 0xB8, 0x99, 0xF0, 0xC4, 0xE5, 0x1E, 0xBC, 0x34, 0x07,
        0x8E, 0x40, 0x57, 0xA5, 0x8D, 0x3A, 0xA3, 0x88, 0x96, 0xF1,
        0xB3, 0x61, 0xF1, 0x1C, 0x96, 0x8A, 0xA4, 0x9E, 0xCD, 0x21,
        0xA2, 0x94, 0xAE, 0x5E, 0x1F, 0xCD, 0x5B, 0x5B, 0xE3, 0x88,
        0x1E, 0x17, 0x4A, 0x46, 0xAB, 0x9C, 0xE0, 0x59, 0x03, 0x4A,
        0xB8, 0xC8, 0x83, 0xE7, 0xFF, 0x39, 0x27, 0x68, 0x80, 0xA0,
        0x8E, 0xB3, 0xA2, 0x00, 0xC6, 0x2D, 0x2C, 0x76, 0xBA, 0x90,
        0x7C, 0x03, 0x1B, 0x19, 0xC8, 0x33, 0xB2, 0x12, 0x3A, 0xC8,
        0x8D, 0x32, 0xFE, 0xC0, 0xF9, 0xA5, 0x6A, 0x63, 0xE2, 0xA4,
        0x12, 0x43, 0x19, 0xF5, 0x14, 0xF2, 0x27, 0xF8, 0x0B, 0xBD,
        0x1A, 0x22, 0x64, 0x2D, 0xC9, 0x05, 0xFA, 0xD8, 0xDD, 0x11,
        0x1A, 0xD3, 0xF2, 0xBC, 0x99, 0x3A, 0xCD, 0x21, 0xCF, 0x10,
        0x14, 0x36, 0xDF, 0xED, 0x66, 0x02, 0x03, 0x4A, 0x42, 0x70,
        0x71, 0x22, 0xAD, 0xE7, 0x53, 0x91, 0xF4, 0x40, 0x8F, 0x72,
        0x7E, 0x54, 0xA0, 0x5D, 0x58, 0x93, 0xD6, 0xF6, 0xBC, 0x87,
        0x1A, 0x68, 0x0F, 0xAB, 0x94, 0x20, 0x70, 0xC2, 0x11, 0xA1,
        0x14, 0xBC, 0x06, 0xA8, 0x44, 0xB9, 0x1F, 0x04, 0x49, 0x7E,
        0xB3, 0x9A, 0x53, 0x46, 0x05, 0x75, 0x5D, 0x29, 0x77, 0x28,
        0xA9, 0xB1, 0xDC, 0xF1, 0x0D, 0x8A, 0x1C, 0x5E, 0xCD, 0xD7,
        0x4C, 0x16, 0x6F, 0x88, 0xBF, 0xB3, 0x34, 0xE2, 0xAD, 0x9A,
        0xC4, 0x89, 0xE2, 0x2E, 0x5C, 0x20, 0xE1, 0x5F, 0x39, 0xBF,
        0xB7, 0x45, 0xD3, 0x0F, 0x98, 0xB0, 0xD8, 0xC9, 0x18, 0x91,
        0x17, 0x25, 0xBC, 0x53, 0x62, 0xFF, 0x27, 0x85, 0xBD, 0xE2,
        0xE3, 0x9C, 0xA8, 0x06, 0x7A, 0x54, 0xEA, 0xFD, 0xEA, 0x02,
        0x20, 0x4C, 0xAC, 0x69, 0x62, 0x08, 0xE5, 0xCD, 0x14, 0xC8,
        0x2D, 0x4E, 0xDF, 0x1F, 0x60, 0x1D, 0x93, 0x44, 0x86, 0x5D,
        0x73, 0x99, 0x40, 0x1B, 0xDC, 0xA9, 0xBA, 0xC4, 0x1B, 0x12,
        0x6C, 0xFF, 0x53
};
static const int sizeof_dsa_key_der_3072 = sizeof(dsa_key_der_3072);

/* ./certs/rsa3072.der, 3072-bit */
static const unsigned char rsa_key_der_3072[] =
{
        0x30, 0x82, 0x06, 0xE4, 0x02, 0x01, 0x00, 0x02, 0x82, 0x01,
        0x81, 0x00, 0xBC, 0x6D, 0x68, 0xFF, 0xC0, 0x07, 0x0E, 0x0C,
        0x4A, 0xE6, 0x76, 0x1F, 0x7A, 0x25, 0x3A, 0x75, 0xA7, 0xE2,
        0xF1, 0x17, 0x00, 0xF8, 0x85, 0xE6, 0x8F, 0x59, 0x14, 0xA7,
        0xDE, 0x8C, 0x74, 0x4B, 0xEB, 0x85, 0xEC, 0x49, 0x9B, 0xFF,
        0x4B, 0x43, 0x0A, 0x08, 0xA1, 0xEC, 0x64, 0x58, 0x47, 0x28,
        0xD5, 0xCE, 0x48, 0xE9, 0xCF, 0x34, 0xDF, 0x15, 0x20, 0x37,
        0x99, 0x0E, 0x3C, 0x81, 0xBE, 0x2E, 0xE4, 0x6C, 0xBB, 0xDE,
        0xD1, 0x93, 0xC5, 0xEC, 0x6C, 0xCC, 0x40, 0x0B, 0x46, 0xA1,
        0xE6, 0xCA, 0xA0, 0xD5, 0x3B, 0x44, 0x48, 0x79, 0x67, 0x52,
        0x6F, 0xDA, 0xED, 0x73, 0x8B, 0x7C, 0x33, 0xDA, 0x17, 0x96,
        0xE8, 0xA2, 0x91, 0x3C, 0x57, 0xDD, 0xC9, 0x2E, 0x01, 0x74,
        0x87, 0x33, 0xA0, 0x12, 0x7C, 0xBB, 0xF9, 0x53, 0xF4, 0xC4,
        0x31, 0x48, 0x53, 0xCB, 0xBB, 0x3C, 0x42, 0x43, 0x0C, 0x7A,
        0x7B, 0xB8, 0x2A, 0xFC, 0xDC, 0x70, 0xD5, 0x64, 0x16, 0x74,
        0xA8, 0x80, 0xDE, 0x16, 0xE0, 0xB2, 0x6C, 0x04, 0x47, 0x6C,
        0x25, 0xA6, 0x7F, 0xB4, 0x73, 0x49, 0xBC, 0xF3, 0xAE, 0xE3,
        0x93, 0x36, 0x87, 0x2B, 0xB7, 0x8F, 0xB5, 0x88, 0x88, 0x22,
        0x47, 0xDF, 0xBF, 0x4D, 0x3C, 0x2A, 0xBD, 0x3F, 0x2F, 0x11,
        0x29, 0xCC, 0x1C, 0x33, 0x40, 0x4E, 0x23, 0xF6, 0x25, 0xF0,
        0xAF, 0x02, 0x16, 0x48, 0xED, 0x1C, 0xD8, 0xC9, 0x92, 0x2F,
        0x5B, 0xAF, 0xBA, 0xDB, 0x60, 0x1E, 0x0E, 0xE1, 0x65, 0x91,
        0x96, 0xF8, 0x7D, 0x73, 0x4C, 0x72, 0x23, 0x33, 0xD5, 0x32,
        0x2B, 0x0F, 0x4F, 0xBC, 0x81, 0x45, 0x9E, 0x31, 0x76, 0xEF,
        0xE1, 0x76, 0x2D, 0x3F, 0x8F, 0xC4, 0x19, 0x8F, 0x27, 0x2A,
        0x8F, 0x6E, 0x76, 0xCC, 0xE0, 0x5D, 0xB0, 0x86, 0x66, 0xFE,
        0x72, 0xD9, 0x06, 0x40, 0xB6, 0xCE, 0x85, 0xC6, 0x2D, 0x34,
        0x33, 0xAA, 0x8E, 0xE5, 0x54, 0x8E, 0xB8, 0xBA, 0xEE, 0x92,
        0x07, 0x5D, 0xB5, 0xF1, 0x67, 0xBF, 0xCA, 0xE4, 0xCA, 0xCB,
        0xD9, 0x01, 0x73, 0x22, 0x01, 0x32, 0x39, 0xF4, 0x0A, 0xEC,
        0x5F, 0x4A, 0x00, 0x10, 0x3F, 0x01, 0x3D, 0x15, 0xBB, 0x55,
        0x91, 0x80, 0xBE, 0xD8, 0xD3, 0x59, 0xCC, 0xB0, 0x7C, 0x56,
        0xF7, 0xFF, 0xE0, 0x28, 0x40, 0x02, 0xB3, 0x98, 0x8A, 0x54,
        0x52, 0x60, 0xA5, 0x0B, 0x95, 0x53, 0x86, 0x6B, 0xA4, 0x35,
        0xCA, 0x04, 0xC7, 0xFB, 0x0A, 0xC8, 0x9D, 0x5A, 0x11, 0x40,
        0xF7, 0x60, 0x07, 0xB1, 0xB3, 0x42, 0xB6, 0x80, 0x8F, 0xE4,
        0x25, 0xC9, 0xE8, 0xBC, 0x8E, 0x21, 0x0D, 0x47, 0xCF, 0xB8,
        0x37, 0x09, 0xAF, 0xBF, 0x2C, 0x34, 0x09, 0x22, 0xC2, 0x6E,
        0x0D, 0x06, 0x30, 0x80, 0x1E, 0xA5, 0x8A, 0x46, 0x2D, 0xDC,
        0x57, 0xD4, 0x57, 0x82, 0x6A, 0x11, 0x02, 0x03, 0x01, 0x00,
        0x01, 0x02, 0x82, 0x01, 0x81, 0x00, 0xAD, 0x99, 0xAF, 0xCF,
        0x51, 0x40, 0x2E, 0xB5, 0x2C, 0x9C, 0xBF, 0xDF, 0xA8, 0x4D,
        0x7C, 0x5A, 0xC1, 0xDE, 0xD8, 0x78, 0x75, 0x30, 0x83, 0x4D,
        0x34, 0x6C, 0xC2, 0x17, 0x17, 0x77, 0x17, 0xFE, 0x8A, 0x73,
        0xCC, 0x8A, 0xD4, 0xEA, 0x94, 0x90, 0xA3, 0x41, 0xE8, 0xCD,
        0x3E, 0x76, 0x06, 0xB9, 0x9C, 0xA2, 0x7D, 0x92, 0xCC, 0x90,
        0xCD, 0xA7, 0x4D, 0x13, 0x6C, 0x34, 0x2D, 0x92, 0xEB, 0x81,
        0x90, 0x7A, 0x8D, 0x6C, 0x70, 0x72, 0x51, 0x3B, 0xCD, 0xD1,
        0x30, 0x80, 0x33, 0x07, 0x1E, 0xF7, 0x38, 0xCE, 0xBB, 0xD7,
        0xE1, 0x5D, 0xD8, 0xCF, 0x9E, 0xB6, 0x79, 0x66, 0xA6, 0xF0,
        0x3B, 0x65, 0x87, 0xAE, 0x45, 0x8E, 0xE1, 0x78, 0x53, 0x0B,
        0xC7, 0x3A, 0x57, 0xA4, 0xE0, 0x9B, 0xB3, 0xB2, 0xD4, 0xB0,
        0xEA, 0xB9, 0x6B, 0x1D, 0x06, 0xBA, 0xB8, 0x59, 0x4F, 0x9B,
        0xE9, 0x00, 0x95, 0x12, 0x93, 0xC1, 0xCD, 0xF9, 0x41, 0xAF,
        0xC3, 0x2A, 0x7F, 0x75, 0xE3, 0x79, 0x37, 0x24, 0xA4, 0xC8,
        0x3D, 0xB4, 0x83, 0x89, 0x23, 0xF7, 0x0E, 0x59, 0x56, 0x8E,
        0x6D, 0x43, 0xA5, 0xB1, 0x8E, 0x04, 0x02, 0xED, 0x48, 0x25,
        0x62, 0xFE, 0xF3, 0x4D, 0x82, 0x22, 0xA6, 0xC1, 0xA5, 0xD9,
        0x4A, 0x9A, 0x57, 0xE6, 0xDC, 0x37, 0x6D, 0x13, 0xDA, 0xFF,
        0x23, 0x2A, 0xB9, 0x31, 0xD2, 0x4B, 0x7D, 0xF3, 0x02, 0x90,
        0xF6, 0x28, 0x3D, 0x98, 0x3C, 0xF6, 0x43, 0x45, 0xAE, 0xAB,
        0x91, 0x15, 0xC7, 0xC4, 0x90, 0x9C, 0x3E, 0xDA, 0xD4, 0x20,
        0x12, 0xB2, 0xE1, 0x2B, 0x56, 0xE2, 0x38, 0x32, 0x9C, 0xE6,
        0xA9, 0x1D, 0xFE, 0xA5, 0xEE, 0xD7, 0x52, 0xB4, 0xE3, 0xE4,
        0x65, 0xEA, 0x41, 0x9D, 0xD4, 0x91, 0x83, 0x5D, 0xFF, 0x52,
        0xA7, 0xC3, 0x42, 0x9F, 0x14, 0x70, 0x9F, 0x98, 0x14, 0xB2,
        0x33, 0xEE, 0x4C, 0x5A, 0xC9, 0x5F, 0x16, 0xF6, 0x06, 0xE9,
        0xF3, 0x39, 0xD2, 0xC5, 0x31, 0x53, 0x2A, 0x39, 0xED, 0x3A,
        0x4D, 0x2A, 0xC1, 0x4C, 0x87, 0x82, 0xC6, 0xCA, 0xCF, 0xF5,
        0x9A, 0x71, 0x27, 0xAE, 0xFB, 0xFE, 0xD0, 0x66, 0xDB, 0xAA,
        0x03, 0x16, 0x4B, 0xEF, 0xB4, 0x28, 0xAB, 0xCF, 0xBE, 0x9B,
        0x58, 0xCF, 0xA4, 0x58, 0x82, 0xD2, 0x37, 0x8C, 0xEA, 0x3D,
        0x75, 0x4D, 0x0B, 0x46, 0x7A, 0x04, 0xDE, 0xF1, 0x6E, 0xBB,
        0x03, 0xBF, 0xF7, 0x8E, 0xE6, 0xF4, 0x9A, 0xE1, 0xCA, 0x26,
        0x2C, 0x41, 0x08, 0xAD, 0x21, 0xA7, 0xC2, 0x40, 0xF5, 0x9C,
        0xDD, 0xAB, 0xC5, 0x5A, 0x4C, 0xF4, 0xE6, 0x9A, 0x50, 0xFD,
        0xAA, 0x47, 0xD6, 0xA6, 0x07, 0x25, 0xB2, 0x4B, 0x9C, 0x1D,
        0x90, 0xA2, 0x4A, 0x98, 0xE0, 0x05, 0x8A, 0x5C, 0xD1, 0x2C,
        0xC0, 0x28, 0xD1, 0x84, 0x3C, 0x72, 0xFF, 0x83, 0xEA, 0xB1,
        0x02, 0x81, 0xC1, 0x00, 0xF8, 0xA0, 0x5F, 0x25, 0x2E, 0x23,
        0x73, 0x30, 0xB6, 0x97, 0xAF, 0x08, 0xE7, 0xD2, 0xD8, 0xC3,
        0x95, 0xEA, 0x9D, 0x8E, 0x9F, 0xF1, 0x36, 0x81, 0xD7, 0x7A,
        0x21, 0x2B, 0x90, 0x38, 0x9C, 0xA6, 0x08, 0x40, 0xEA, 0xD2,
        0x6E, 0x29, 0xB5, 0x0B, 0x3E, 0x91, 0xB2, 0x04, 0x92, 0xCF,
        0x94, 0xFF, 0xA6, 0xA7, 0x1A, 0x5F, 0x93, 0x0C, 0x86, 0xE6,
        0x4B, 0x61, 0xD4, 0x5E, 0xD7, 0xE3, 0x66, 0x0B, 0x83, 0xDB,
        0x16, 0x49, 0x27, 0xD5, 0xA3, 0xB3, 0xF5, 0x5D, 0x8F, 0xC9,
        0x48, 0x10, 0xD7, 0x77, 0x1E, 0x7B, 0x01, 0xC4, 0xFD, 0x14,
        0x0C, 0xAB, 0x40, 0xF7, 0x9B, 0x07, 0xDE, 0x55, 0xEF, 0x36,
        0x4C, 0x22, 0x37, 0x37, 0x09, 0x9D, 0x2A, 0x73, 0xA6, 0xA5,
        0xF4, 0xAF, 0x39, 0x2B, 0x87, 0xB4, 0xB2, 0x28, 0x9E, 0x08,
        0xA6, 0xCA, 0xB4, 0x39, 0x5A, 0x3A, 0xFB, 0x41, 0x93, 0xEC,
        0x44, 0xBB, 0xD2, 0x7C, 0x3B, 0x27, 0x3E, 0x26, 0xFD, 0x7B,
        0x20, 0xFC, 0x44, 0x67, 0xC0, 0x84, 0xD1, 0xA0, 0xCC, 0xBB,
        0x26, 0xC7, 0x32, 0x0E, 0x01, 0x9B, 0x2B, 0x1F, 0x58, 0x85,
        0x5A, 0x6C, 0xD0, 0xC1, 0xAC, 0x14, 0x5E, 0x06, 0x07, 0xCA,
        0x69, 0x52, 0xF5, 0xA6, 0x16, 0x75, 0x42, 0x8A, 0xE1, 0xBA,
        0x8B, 0x46, 0x38, 0x17, 0x7B, 0xF1, 0x7D, 0x79, 0x1F, 0x7E,
        0x4C, 0x6A, 0x75, 0xDC, 0xA8, 0x3B, 0x02, 0x81, 0xC1, 0x00,
        0xC2, 0x03, 0xFE, 0x57, 0xDF, 0x26, 0xD8, 0x79, 0xDC, 0x2C,
        0x47, 0x9B, 0x92, 0x9B, 0x53, 0x40, 0x82, 0xEC, 0xBD, 0x0B,
        0xC0, 0x96, 0x89, 0x21, 0xC5, 0x26, 0x7E, 0x7A, 0x59, 0xA7,
        0x85, 0x11, 0xCC, 0x39, 0x33, 0xA7, 0xE6, 0x42, 0x9C, 0x12,
        0x81, 0xA0, 0x87, 0xBC, 0x57, 0x07, 0xC4, 0x51, 0x93, 0x59,
        0xC6, 0xAB, 0x11, 0xCC, 0xCB, 0xC8, 0xC1, 0x40, 0xDF, 0xCB,
        0xE8, 0x45, 0x31, 0x20, 0x91, 0x88, 0x5F, 0x76, 0x76, 0xEE,
        0x30, 0x37, 0xFA, 0xA7, 0x22, 0x72, 0x82, 0x50, 0x31, 0xE9,
        0xA0, 0x44, 0xCA, 0xDD, 0xD6, 0xAC, 0xEC, 0x82, 0xE8, 0x62,
        0xD8, 0x43, 0xFD, 0x77, 0x0F, 0x1C, 0x23, 0x12, 0x91, 0x1C,
        0xFE, 0x93, 0x2C, 0x87, 0x52, 0xBF, 0x96, 0x79, 0x5E, 0x3A,
        0x5A, 0x33, 0x28, 0x27, 0x3F, 0x20, 0x2C, 0xB3, 0x26, 0xE2,
        0x0D, 0x44, 0xA9, 0x2F, 0x39, 0x7B, 0x7B, 0xAD, 0xA3, 0x21,
        0xD2, 0x7F, 0x3C, 0x89, 0x63, 0xDD, 0x13, 0xB1, 0x2E, 0xD6,
        0x34, 0xFB, 0x2A, 0x83, 0x29, 0xE7, 0x8A, 0x88, 0xD7, 0xA3,
        0x38, 0x3C, 0x43, 0x62, 0x8F, 0x69, 0xFA, 0x4B, 0x15, 0xB5,
        0xF6, 0x59, 0x90, 0x62, 0x7D, 0xCF, 0x1D, 0xDD, 0x49, 0x43,
        0x33, 0x96, 0xA9, 0xF7, 0x76, 0x9F, 0xE4, 0x0D, 0x6E, 0x1C,
        0xEA, 0x18, 0x5B, 0xBD, 0x5C, 0x98, 0x90, 0x09, 0xCA, 0x59,
        0x9E, 0x23, 0x02, 0x81, 0xC0, 0x66, 0xFF, 0x99, 0x2A, 0xFF,
        0xF8, 0x33, 0xAA, 0x44, 0x9A, 0x86, 0x2A, 0xBC, 0x4F, 0x3E,
        0xF9, 0x97, 0xCB, 0xC0, 0x45, 0xEB, 0xC0, 0xB4, 0x02, 0x0A,
        0x50, 0x50, 0x19, 0x89, 0xFF, 0xC9, 0xF5, 0x86, 0x89, 0xCE,
        0x3E, 0x2A, 0xE1, 0x20, 0x5D, 0x6E, 0x28, 0x51, 0x85, 0x4F,
        0x84, 0xAB, 0x87, 0x55, 0x74, 0xF8, 0x9A, 0x0B, 0x83, 0x2F,
        0x07, 0x8C, 0xC7, 0x14, 0x81, 0xCE, 0x12, 0x28, 0x9E, 0x30,
        0x9B, 0xBC, 0x99, 0xC5, 0xE4, 0xDD, 0x92, 0x99, 0xDD, 0x8E,
        0xC9, 0xA6, 0x0F, 0x44, 0x13, 0xD7, 0x0E, 0xC2, 0x66, 0xE7,
        0x29, 0x3D, 0x2E, 0x5D, 0x15, 0xB6, 0xA6, 0x05, 0xD7, 0xB7,
        0xE7, 0xD8, 0x96, 0x7C, 0x25, 0x52, 0xD8, 0x47, 0x53, 0xED,
        0xFF, 0xE6, 0x64, 0x08, 0xDD, 0x1D, 0xB5, 0x1F, 0xF1, 0x6F,
        0xB6, 0xC9, 0xD2, 0x43, 0xE3, 0x56, 0x9C, 0x04, 0xA6, 0xE0,
        0x2F, 0x0B, 0x32, 0x7C, 0x3A, 0x77, 0x0F, 0x04, 0xD2, 0x86,
        0x44, 0x52, 0x1F, 0xEF, 0xFE, 0xC3, 0x64, 0xC2, 0xAB, 0x48,
        0xE5, 0x67, 0x65, 0x32, 0x39, 0x57, 0x34, 0xFF, 0x22, 0x57,
        0x3B, 0xB7, 0x80, 0x48, 0xE3, 0x52, 0xF4, 0x85, 0x17, 0x1E,
        0x77, 0x1E, 0x36, 0xFE, 0x09, 0x36, 0x58, 0x91, 0x9E, 0x93,
        0x71, 0x02, 0x6D, 0xAE, 0xA3, 0x1B, 0xF7, 0xA9, 0x31, 0x5A,
        0x78, 0xAA, 0x13, 0x98, 0x8C, 0x37, 0x2D, 0x02, 0x81, 0xC1,
        0x00, 0xBE, 0x01, 0xD9, 0x3A, 0xC7, 0x81, 0xAC, 0xAA, 0x13,
        0x75, 0x8E, 0x1F, 0x8F, 0x41, 0xED, 0x13, 0x95, 0xE5, 0x31,
        0xF3, 0x6B, 0x86, 0x42, 0x00, 0xBF, 0xAA, 0xC6, 0x5D, 0x1E,
        0xA6, 0x90, 0x0C, 0xF1, 0x1B, 0xE8, 0x39, 0xFB, 0xA8, 0xAA,
        0x5E, 0xF9, 0x72, 0x74, 0xDC, 0x7F, 0xC3, 0x4C, 0x81, 0xB3,
        0xB4, 0x4D, 0x7B, 0xC6, 0x2F, 0xF2, 0x37, 0xC7, 0x03, 0xB8,
        0xE9, 0x62, 0xAD, 0x38, 0xC2, 0xB3, 0xA4, 0x82, 0x11, 0x6B,
        0xC2, 0x33, 0x98, 0xEF, 0x32, 0x75, 0xEA, 0xFD, 0x32, 0x7A,
        0xDF, 0x59, 0xA5, 0x65, 0xA4, 0x42, 0x95, 0x11, 0xFF, 0xD6,
        0x84, 0xCF, 0x56, 0x2E, 0xCA, 0x46, 0x13, 0x01, 0x4A, 0x32,
        0xB1, 0xD9, 0xA3, 0xDB, 0x0D, 0x20, 0x7E, 0x1F, 0x68, 0xF7,
        0x5E, 0x60, 0x6E, 0x0F, 0x59, 0xF8, 0x59, 0x93, 0x4D, 0x54,
        0xBC, 0x37, 0xD0, 0x51, 0x7C, 0xBD, 0x67, 0xF0, 0xA5, 0x09,
        0xC9, 0x9A, 0xF4, 0x1F, 0x1E, 0x52, 0x9D, 0xF5, 0xA6, 0x25,
        0xBF, 0x85, 0x1D, 0xA1, 0xF1, 0xD8, 0xBD, 0x39, 0x10, 0x71,
        0x57, 0x19, 0x40, 0xF3, 0xA1, 0x77, 0xE0, 0x8B, 0x4E, 0xB3,
        0x91, 0x84, 0x15, 0x0C, 0xF1, 0x58, 0x52, 0xD9, 0xE5, 0x98,
        0xD5, 0x66, 0x95, 0x9C, 0x19, 0x8D, 0xA4, 0x63, 0x5C, 0xBF,
        0xC5, 0x33, 0x81, 0xED, 0x7E, 0x93, 0x4B, 0x9A, 0x6C, 0xEC,
        0x2E, 0x3E, 0x4F, 0x02, 0x81, 0xC0, 0x34, 0xF8, 0xDF, 0x74,
        0xC6, 0xC1, 0xD9, 0x03, 0x9B, 0x3B, 0x53, 0x19, 0xEB, 0x43,
        0xC4, 0xAA, 0x1E, 0x73, 0xE3, 0x13, 0x25, 0x32, 0x04, 0x22,
        0x79, 0x4A, 0x07, 0xF0, 0x06, 0x38, 0xBD, 0x57, 0xE6, 0x01,
        0x33, 0x8C, 0xF1, 0x02, 0xCC, 0x34, 0x2C, 0x60, 0x32, 0xA4,
        0x22, 0x1D, 0x0E, 0x39, 0x6B, 0xAB, 0xF7, 0xCE, 0xDB, 0xA7,
        0xC3, 0xD8, 0xA2, 0x3B, 0x70, 0x31, 0x91, 0x68, 0xB9, 0xBF,
        0xE0, 0xA1, 0x39, 0x80, 0xFE, 0x47, 0x99, 0x56, 0x6D, 0x76,
        0x90, 0x17, 0xF5, 0x67, 0x41, 0x44, 0x27, 0x10, 0x07, 0x98,
        0x4D, 0x4C, 0x53, 0xD4, 0x15, 0xDC, 0x0A, 0x2F, 0xE0, 0x83,
        0x28, 0x22, 0x8D, 0x61, 0x3B, 0xE4, 0x8E, 0xE5, 0xE7, 0x24,
        0x98, 0x19, 0xA8, 0xA3, 0xED, 0x70, 0x59, 0x06, 0x86, 0x76,
        0xC2, 0x4B, 0xCB, 0x17, 0xC5, 0x77, 0x12, 0x07, 0xB8, 0xAB,
        0x1A, 0x91, 0xFC, 0x72, 0x8E, 0xB7, 0xB1, 0xE6, 0x74, 0xDD,
        0x3D, 0x92, 0xA7, 0xDE, 0x6C, 0x6E, 0xCB, 0x50, 0x44, 0x2F,
        0xAC, 0x99, 0xF7, 0x36, 0x4D, 0x62, 0xC7, 0xAC, 0xCE, 0x7D,
        0x26, 0xC9, 0xD2, 0x4E, 0x49, 0xD7, 0x8E, 0x66, 0x6C, 0xC1,
        0x53, 0xDF, 0x31, 0xAB, 0x25, 0x35, 0xCA, 0xD6, 0xC4, 0xA3,
        0xA6, 0x9F, 0x7E, 0x3D, 0x2D, 0x1A, 0x44, 0x31, 0x3D, 0x81,
        0x91, 0xB8, 0x36, 0x08, 0x27, 0x42, 0x9E, 0x08
};
static const int sizeof_rsa_key_der_3072 = sizeof(rsa_key_der_3072);

/* ./certs/3072/client-key.der, 3072-bit */
static const unsigned char client_key_der_3072[] =
{
        0x30, 0x82, 0x06, 0xFE, 0x02, 0x01, 0x00, 0x30, 0x0D, 0x06,
        0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01,
        0x05, 0x00, 0x04, 0x82, 0x06, 0xE8, 0x30, 0x82, 0x06, 0xE4,
        0x02, 0x01, 0x00, 0x02, 0x82, 0x01, 0x81, 0x00, 0xAC, 0x39,
        0x50, 0x68, 0x8F, 0x78, 0xF8, 0x10, 0x9B, 0x68, 0x96, 0xD3,
        0xE1, 0x9C, 0x56, 0x68, 0x5A, 0x41, 0x62, 0xE3, 0xB3, 0x41,
        0xB0, 0x55, 0x80, 0x17, 0xB0, 0x88, 0x16, 0x9B, 0xE0, 0x97,
        0x74, 0x5F, 0x42, 0x79, 0x73, 0x42, 0xDF, 0x93, 0xF3, 0xAA,
        0x9D, 0xEE, 0x2D, 0x6F, 0xAA, 0xBC, 0x27, 0x90, 0x84, 0xC0,
        0x5D, 0xC7, 0xEC, 0x49, 0xEA, 0x5C, 0x66, 0x1D, 0x70, 0x9C,
        0x53, 0x5C, 0xBA, 0xA1, 0xB3, 0x58, 0xC9, 0x3E, 0x8E, 0x9B,
        0x72, 0x3D, 0x6E, 0x02, 0x02, 0x00, 0x9C, 0x65, 0x56, 0x82,
        0xA3, 0x22, 0xB4, 0x08, 0x5F, 0x2A, 0xEF, 0xDF, 0x9A, 0xD0,
        0xE7, 0x31, 0x59, 0x26, 0x5B, 0x0B, 0x1C, 0x63, 0x61, 0xFF,
        0xD5, 0x69, 0x32, 0x19, 0x06, 0x7E, 0x0F, 0x40, 0x3C, 0x7A,
        0x1E, 0xC8, 0xFC, 0x58, 0x6C, 0x64, 0xAE, 0x10, 0x3D, 0xA8,
        0x23, 0xFF, 0x8E, 0x1A, 0xCA, 0x6A, 0x82, 0xE2, 0xF9, 0x01,
        0x64, 0x2C, 0x97, 0xA0, 0x1A, 0x89, 0xA0, 0x74, 0xD3, 0xB6,
        0x05, 0x11, 0xF2, 0x62, 0x06, 0x48, 0x2A, 0xF7, 0x66, 0xCE,
        0xC1, 0x85, 0xE1, 0xD2, 0x27, 0xEA, 0xCA, 0x12, 0xA5, 0x91,
        0x97, 0x3E, 0xFC, 0x94, 0x06, 0x59, 0x51, 0xC0, 0xE7, 0x13,
        0xB6, 0x87, 0x7B, 0x5F, 0xD2, 0xC0, 0x56, 0x2F, 0x5E, 0x1D,
        0x02, 0xC3, 0x11, 0x2C, 0xDF, 0xF7, 0x01, 0xDA, 0xBD, 0x85,
        0x54, 0x35, 0x32, 0x5F, 0xC5, 0xC8, 0xF9, 0x7A, 0x9F, 0x89,
        0xF7, 0x03, 0x0E, 0x7E, 0x79, 0x5D, 0x04, 0x82, 0x35, 0x10,
        0xFE, 0x6D, 0x9B, 0xBF, 0xB8, 0xEE, 0xE2, 0x62, 0x87, 0x26,
        0x5E, 0x2F, 0x50, 0x2F, 0x78, 0x0C, 0xE8, 0x73, 0x4F, 0x88,
        0x6A, 0xD6, 0x26, 0xA4, 0xC9, 0xFC, 0xFA, 0x1E, 0x8A, 0xB0,
        0xF4, 0x32, 0xCF, 0x57, 0xCD, 0xA1, 0x58, 0x8A, 0x49, 0x0F,
        0xBB, 0xA9, 0x1D, 0x86, 0xAB, 0xB9, 0x8F, 0x8D, 0x57, 0x19,
        0xB2, 0x5A, 0x7E, 0xA4, 0xEA, 0xCC, 0xB7, 0x96, 0x7A, 0x3B,
        0x38, 0xCD, 0xDE, 0xE0, 0x61, 0xFC, 0xC9, 0x06, 0x8F, 0x93,
        0x5A, 0xCE, 0xAD, 0x2A, 0xE3, 0x2D, 0x3E, 0x39, 0x5D, 0x41,
        0x83, 0x01, 0x1F, 0x0F, 0xE1, 0x7F, 0x76, 0xC7, 0x28, 0xDA,
        0x56, 0xEF, 0xBF, 0xDC, 0x26, 0x35, 0x40, 0xBE, 0xAD, 0xC7,
        0x38, 0xAD, 0xA4, 0x06, 0xAC, 0xCA, 0xE8, 0x51, 0xEB, 0xC0,
        0xF8, 0x68, 0x02, 0x2C, 0x9B, 0xA1, 0x14, 0xBC, 0xF8, 0x61,
        0x86, 0xD7, 0x56, 0xD7, 0x73, 0xF4, 0xAB, 0xBB, 0x6A, 0x21,
        0xD3, 0x88, 0x22, 0xB4, 0xE7, 0x6F, 0x7F, 0x91, 0xE5, 0x0E,
        0xC6, 0x08, 0x49, 0xDE, 0xEA, 0x13, 0x58, 0x72, 0xA0, 0xAA,
        0x3A, 0xF9, 0x36, 0x03, 0x45, 0x57, 0x5E, 0x87, 0xD2, 0x73,
        0x65, 0xC4, 0x8C, 0xA3, 0xEE, 0xC9, 0xD6, 0x73, 0x7C, 0x96,
        0x41, 0x93, 0x02, 0x03, 0x01, 0x00, 0x01, 0x02, 0x82, 0x01,
        0x80, 0x40, 0x19, 0x74, 0xDB, 0xF5, 0xCA, 0x48, 0x49, 0xA6,
        0x0D, 0xDF, 0x55, 0x2C, 0xFB, 0x4B, 0x0D, 0xBB, 0xC9, 0xEA,
        0x4C, 0x65, 0x43, 0x65, 0xA5, 0xEC, 0xEE, 0xE4, 0x3D, 0x42,
        0x6C, 0xF1, 0xC2, 0x6D, 0x05, 0xA7, 0x70, 0x1C, 0x7E, 0x1F,
        0x48, 0xA9, 0xC0, 0x2E, 0xD7, 0x9F, 0x01, 0x98, 0xC2, 0x3E,
        0xD7, 0x83, 0x11, 0x35, 0xD6, 0x5B, 0x13, 0x87, 0xAE, 0xAC,
        0x32, 0xF8, 0xDE, 0xB6, 0x08, 0x25, 0x4E, 0x59, 0xBA, 0x09,
        0xEC, 0xC6, 0x97, 0x04, 0x85, 0xE8, 0x93, 0xC6, 0xBB, 0x03,
        0x7A, 0x94, 0x20, 0x3B, 0x27, 0x87, 0x6A, 0x36, 0x41, 0x7C,
        0xD5, 0xF4, 0x81, 0x1C, 0x0B, 0x39, 0xEB, 0x14, 0xA7, 0xA6,
        0x01, 0x37, 0x50, 0x48, 0xD5, 0xC6, 0x57, 0x9A, 0x1B, 0x01,
        0x02, 0x1F, 0x80, 0x34, 0x45, 0x09, 0xE6, 0xBF, 0x31, 0x19,
        0xB7, 0xE1, 0xBA, 0xDA, 0xEB, 0x1A, 0xB0, 0xCD, 0xF5, 0xA6,
        0x91, 0x63, 0xAC, 0x28, 0xE4, 0x8F, 0xEA, 0x7E, 0xF6, 0x0A,
        0x4A, 0x71, 0x21, 0xA5, 0xF1, 0x70, 0x0D, 0x1B, 0xD9, 0x70,
        0x64, 0x74, 0x57, 0x2F, 0x9F, 0xEC, 0xD4, 0x93, 0x16, 0xC7,
        0xEE, 0xF8, 0xC0, 0x9F, 0x52, 0x4A, 0x1F, 0xAD, 0xDD, 0x40,
        0x98, 0x53, 0x68, 0xFA, 0xDE, 0xA2, 0x04, 0xA0, 0x24, 0x05,
        0xEF, 0xCB, 0x4F, 0x70, 0xDF, 0xB9, 0x5C, 0xC2, 0x5E, 0xE4,
        0xC9, 0xCD, 0x0F, 0x5E, 0x4B, 0x77, 0xBB, 0x84, 0x69, 0x54,
        0x98, 0x41, 0xB7, 0x9C, 0x0E, 0x38, 0xD8, 0xF7, 0xF3, 0x9F,
        0xEF, 0xE5, 0x9B, 0xB6, 0x4B, 0xD6, 0x7A, 0x65, 0xF5, 0x69,
        0xFA, 0xC2, 0x13, 0x70, 0x6C, 0x28, 0xA4, 0x29, 0xAC, 0xD9,
        0xBF, 0xEC, 0x6A, 0x2E, 0xED, 0xE4, 0xBA, 0xDF, 0xD0, 0xF1,
        0xF3, 0x3C, 0x6C, 0x84, 0xDF, 0xB7, 0x5A, 0x94, 0xCF, 0xD9,
        0x2D, 0xEA, 0xEA, 0xB4, 0xD0, 0x91, 0x2E, 0x77, 0x15, 0x18,
        0x0D, 0x6B, 0xBA, 0x2A, 0x0C, 0xF1, 0x92, 0x9D, 0xD6, 0x04,
        0x05, 0xB6, 0x38, 0xC2, 0xE0, 0xA7, 0x2D, 0x64, 0xF8, 0xDF,
        0x0C, 0x3A, 0x93, 0x83, 0xE1, 0x88, 0x83, 0x5F, 0x67, 0x90,
        0x9F, 0x2B, 0xE0, 0x60, 0x8E, 0xCA, 0x30, 0x13, 0xCA, 0x9F,
        0xCF, 0x7B, 0x6D, 0xD8, 0xCD, 0xEE, 0xF9, 0x96, 0xDD, 0x5E,
        0xF4, 0x47, 0xC9, 0x4C, 0xE6, 0x8F, 0x7F, 0x33, 0x2A, 0x38,
        0x30, 0xAF, 0xD5, 0x4A, 0x79, 0x47, 0x06, 0xCC, 0x96, 0x44,
        0x29, 0x8C, 0x60, 0x2B, 0x08, 0xC7, 0xD0, 0xD3, 0xC3, 0xC5,
        0x2C, 0x63, 0x6C, 0x87, 0xD2, 0xAE, 0x2A, 0xA4, 0x86, 0xE7,
        0x76, 0x74, 0x90, 0xD1, 0x04, 0x37, 0x64, 0x1A, 0xED, 0x08,
        0xD9, 0x98, 0x07, 0x1A, 0x98, 0x0B, 0x89, 0x99, 0xA4, 0xB0,
        0x8C, 0x1A, 0x10, 0xEB, 0xEC, 0xF4, 0xEE, 0x3C, 0xC4, 0x00,
        0xCC, 0x30, 0x9C, 0x43, 0x01, 0x02, 0x81, 0xC1, 0x00, 0xD9,
        0x43, 0xF6, 0x2C, 0x78, 0x26, 0xD2, 0xE7, 0x15, 0xA7, 0x0A,
        0x88, 0x5E, 0xDB, 0x2D, 0xAF, 0xC6, 0xA9, 0x6F, 0x73, 0x88,
        0x3B, 0x6A, 0x08, 0x1F, 0xF5, 0x80, 0xB5, 0x2E, 0x29, 0x8B,
        0x72, 0xF8, 0x35, 0xC8, 0x23, 0x18, 0x1C, 0x0D, 0x0E, 0x38,
        0x82, 0xBB, 0x5B, 0x2F, 0xB4, 0x5C, 0x4E, 0x24, 0x05, 0xA7,
        0x4C, 0x79, 0x48, 0x89, 0x8D, 0x1C, 0x1D, 0x0A, 0x2C, 0xFE,
        0xD9, 0x99, 0xDF, 0x25, 0x8A, 0x2D, 0xF8, 0xEB, 0x2F, 0xDA,
        0x1B, 0x63, 0xE1, 0xCD, 0x09, 0x97, 0x64, 0x14, 0xAB, 0xEA,
        0x0B, 0xD8, 0xE2, 0xA8, 0x2A, 0x63, 0x35, 0x90, 0xEE, 0x7F,
        0xEA, 0xCE, 0xA5, 0xEF, 0x7F, 0xAB, 0x87, 0x47, 0x9B, 0x45,
        0x35, 0x9A, 0xDA, 0x8C, 0xF4, 0xD3, 0x8A, 0x0B, 0x9B, 0xE6,
        0xEA, 0x92, 0xBB, 0x05, 0xE1, 0xAC, 0x3E, 0x35, 0xDB, 0xED,
        0x65, 0x1D, 0xB6, 0x92, 0xEB, 0x29, 0x79, 0xF8, 0x3F, 0xC2,
        0x58, 0x40, 0x32, 0x66, 0x87, 0x56, 0x50, 0xFF, 0xBF, 0x3E,
        0xBD, 0xE9, 0x94, 0xBF, 0x31, 0xBE, 0x87, 0x2D, 0xEF, 0x64,
        0x1E, 0x0E, 0x67, 0x3A, 0x9C, 0x94, 0xDA, 0x5B, 0x0C, 0x8C,
        0x3D, 0xEE, 0x9D, 0xCD, 0x92, 0xDE, 0x40, 0x02, 0x65, 0x36,
        0xC9, 0x1B, 0xF5, 0x7E, 0x4E, 0x07, 0xB4, 0x7F, 0x14, 0x0E,
        0x03, 0x2E, 0x86, 0xF0, 0x45, 0x5F, 0xDC, 0xA2, 0xE8, 0xC7,
        0x83, 0x02, 0x81, 0xC1, 0x00, 0xCA, 0xED, 0xA5, 0x3F, 0x59,
        0xAC, 0x4C, 0xAD, 0xAB, 0x23, 0x02, 0x95, 0x80, 0xA0, 0xAF,
        0x35, 0x17, 0xDB, 0xE7, 0x7F, 0x72, 0x41, 0x2C, 0x5C, 0xB4,
        0x43, 0x85, 0x46, 0x73, 0x9F, 0x58, 0xE9, 0x40, 0x8B, 0xEC,
        0xB0, 0xEF, 0x86, 0x4C, 0x31, 0xDE, 0xC8, 0x6C, 0x74, 0x75,
        0xA2, 0xDB, 0x65, 0xF4, 0x50, 0xC6, 0x99, 0xA2, 0x70, 0xDE,
        0xB6, 0x22, 0xC2, 0x01, 0x15, 0x49, 0x13, 0xA0, 0xE2, 0x20,
        0x78, 0x44, 0xEC, 0x1F, 0x42, 0xB3, 0x25, 0x09, 0xCE, 0x75,
        0x13, 0x75, 0x36, 0x11, 0x47, 0x2C, 0x3C, 0x15, 0x1F, 0xF0,
        0x54, 0xD5, 0x18, 0xAE, 0x61, 0x07, 0xAC, 0x3D, 0x83, 0x46,
        0x03, 0x8C, 0xBF, 0x63, 0x26, 0xA8, 0x19, 0x7C, 0xFF, 0xDE,
        0x20, 0x78, 0xD0, 0xDA, 0x70, 0x2E, 0xBD, 0xFA, 0x96, 0xDD,
        0x15, 0x78, 0x9B, 0xEF, 0xED, 0x17, 0x90, 0x6F, 0x14, 0x35,
        0x50, 0x8E, 0x1D, 0x78, 0xB0, 0x8A, 0xA0, 0x53, 0x10, 0x15,
        0x64, 0xCC, 0x47, 0x05, 0xB6, 0xC6, 0x48, 0xC0, 0x5D, 0xB4,
        0x4B, 0x1A, 0x5F, 0xB8, 0x9E, 0x75, 0xCD, 0xC3, 0x64, 0x66,
        0x88, 0x10, 0x9C, 0x8B, 0x87, 0x14, 0x34, 0xE6, 0x60, 0x3C,
        0xA5, 0xB7, 0x81, 0x1D, 0x0B, 0x79, 0x93, 0x5D, 0x4A, 0x42,
        0x7A, 0x7F, 0x33, 0xF0, 0x3E, 0x9E, 0x63, 0xBD, 0xB6, 0x5F,
        0xF9, 0x47, 0xA7, 0x0A, 0x49, 0x70, 0xB1, 0x02, 0x81, 0xC0,
        0x6F, 0xC6, 0xF4, 0x3E, 0xDA, 0xAD, 0xF6, 0xB1, 0x66, 0xC5,
        0x62, 0xB8, 0xD8, 0x3C, 0x61, 0x1B, 0xDE, 0xD4, 0x4A, 0xFF,
        0xA0, 0x66, 0x18, 0xDE, 0x07, 0x3B, 0x32, 0x35, 0x84, 0x83,
        0x61, 0x38, 0x0C, 0x14, 0xF7, 0x5B, 0x7E, 0xCA, 0xE7, 0xB8,
        0x9A, 0x40, 0x40, 0x0D, 0xE0, 0xD4, 0x24, 0xED, 0x1A, 0xC1,
        0x41, 0xDA, 0x29, 0x47, 0xB5, 0x64, 0xC0, 0xC2, 0xFB, 0xFA,
        0x3C, 0x3F, 0x4D, 0x57, 0xAD, 0xA3, 0x92, 0x95, 0x4E, 0xC2,
        0x76, 0xAE, 0xC2, 0xCB, 0x67, 0xC6, 0x78, 0x79, 0xC7, 0xDC,
        0xCE, 0x73, 0xBB, 0xE8, 0x98, 0x65, 0xFE, 0x56, 0x8F, 0xB2,
        0xF4, 0x62, 0xA4, 0x60, 0x60, 0x80, 0x49, 0x8A, 0x36, 0xBF,
        0xDE, 0x72, 0x7E, 0xB1, 0xD3, 0xF5, 0x1D, 0x64, 0x17, 0x26,
        0xE5, 0x3D, 0x67, 0xB2, 0x0A, 0x8B, 0x99, 0x27, 0x04, 0x64,
        0x9A, 0x94, 0xFC, 0x1D, 0x73, 0x26, 0xC3, 0x56, 0xF9, 0xEE,
        0x2B, 0x99, 0x65, 0xA5, 0xC8, 0x73, 0xF6, 0x67, 0x83, 0xBC,
        0x2B, 0x96, 0x5F, 0x36, 0xE4, 0xCA, 0xBD, 0xE0, 0x24, 0x34,
        0xD6, 0x48, 0x54, 0x56, 0xAD, 0xA3, 0xE3, 0x3D, 0x17, 0xBC,
        0xB3, 0xE6, 0x24, 0xFE, 0x50, 0xC6, 0x2F, 0xCB, 0xB4, 0xAF,
        0xC7, 0xE8, 0xDD, 0x96, 0x86, 0x9D, 0xB4, 0x7F, 0x1B, 0x26,
        0x01, 0x33, 0x87, 0xDB, 0x6A, 0x7F, 0xF6, 0x9A, 0xB7, 0xC1,
        0x94, 0xEB, 0x02, 0x81, 0xC1, 0x00, 0xB0, 0x6D, 0x20, 0x68,
        0x0D, 0x7C, 0x81, 0x45, 0xD4, 0x2E, 0x22, 0x06, 0xFC, 0xC7,
        0xB6, 0xCC, 0x40, 0x2C, 0x0D, 0xFE, 0x7D, 0xC5, 0x2F, 0xDE,
        0x81, 0x52, 0xDA, 0xC2, 0x3F, 0xAF, 0xE0, 0x4B, 0x1A, 0xB5,
        0x0C, 0x59, 0x60, 0x45, 0xB0, 0x65, 0x03, 0x3D, 0xD9, 0x1C,
        0xFF, 0x51, 0x51, 0xD2, 0x38, 0x31, 0x2A, 0x19, 0x54, 0x63,
        0x31, 0x1D, 0xC4, 0xE6, 0x4A, 0xAE, 0xC8, 0xD3, 0xE9, 0xE1,
        0xEF, 0x3C, 0xE1, 0x1F, 0x30, 0xA6, 0x7A, 0xBD, 0xCE, 0xE2,
        0xD2, 0x62, 0xD2, 0x5A, 0xE9, 0x76, 0xA9, 0x7C, 0xAB, 0x19,
        0x13, 0x87, 0x8D, 0xA5, 0x61, 0xA6, 0x36, 0x57, 0x87, 0x3B,
        0x64, 0x59, 0x9D, 0xBA, 0x9F, 0x67, 0x72, 0x6A, 0x86, 0x84,
        0xA6, 0x08, 0x31, 0x41, 0xD3, 0x48, 0x09, 0x3B, 0x5E, 0x6C,
        0x5F, 0x56, 0x55, 0x7F, 0xAD, 0x7E, 0xC2, 0x27, 0xEE, 0x8A,
        0xF1, 0x37, 0x51, 0xF7, 0x49, 0x80, 0xA3, 0x65, 0x74, 0x11,
        0xDD, 0xA7, 0xBE, 0xFA, 0x58, 0x7B, 0x69, 0xB4, 0xC2, 0x9A,
        0x35, 0x2F, 0xBE, 0x84, 0x4E, 0x2C, 0x66, 0x5B, 0x38, 0x6F,
        0x47, 0xBD, 0x30, 0x44, 0x0A, 0x02, 0xAC, 0x8C, 0xB9, 0x66,
        0x1E, 0x14, 0x2D, 0x90, 0x71, 0x42, 0x12, 0xB7, 0x0E, 0x3A,
        0x8B, 0xC5, 0x98, 0x65, 0xFD, 0x8F, 0x53, 0x81, 0x7F, 0xE4,
        0xD9, 0x58, 0x0E, 0xF5, 0xA9, 0x39, 0xE4, 0x61, 0x02, 0x81,
        0xC1, 0x00, 0xB3, 0x94, 0x8F, 0x2B, 0xFD, 0x84, 0x2E, 0x83,
        0x42, 0x86, 0x56, 0x7E, 0xB5, 0xF8, 0x3C, 0xC5, 0x0C, 0xCB,
        0xBD, 0x32, 0x0C, 0xD7, 0xAA, 0xA7, 0xB0, 0xE9, 0xA4, 0x6A,
        0xD1, 0x01, 0xDB, 0x87, 0x2A, 0xF7, 0xDF, 0xEC, 0xC2, 0x03,
        0x5D, 0x55, 0xA8, 0x66, 0x73, 0x79, 0xA9, 0xAB, 0xBD, 0xAF,
        0x69, 0x37, 0xFE, 0x41, 0xB5, 0x53, 0xB3, 0xB2, 0xC0, 0xB1,
        0x80, 0x34, 0xE6, 0xE1, 0x7B, 0xAE, 0x67, 0xC7, 0xF3, 0x57,
        0xFE, 0x12, 0xBC, 0x78, 0xAA, 0x75, 0x0D, 0xAC, 0x79, 0x90,
        0x14, 0x49, 0xFE, 0x6B, 0x51, 0xE3, 0xE4, 0x46, 0xB2, 0x10,
        0x4D, 0x05, 0x6A, 0x12, 0x80, 0x2A, 0x8F, 0x39, 0x42, 0x0E,
        0x3B, 0x24, 0x2B, 0x50, 0x5D, 0xF3, 0xA7, 0x7F, 0x2F, 0x82,
        0x89, 0x87, 0x9F, 0xF8, 0x7B, 0x1E, 0x05, 0x6E, 0x75, 0x83,
        0x04, 0x35, 0x66, 0x4A, 0x06, 0x57, 0x39, 0xAB, 0x21, 0x0B,
        0x94, 0x41, 0x6A, 0x2A, 0xC7, 0xDE, 0x98, 0x45, 0x8F, 0x96,
        0x1C, 0xF2, 0xD8, 0xFB, 0x9C, 0x10, 0x8E, 0x41, 0x7A, 0xDD,
        0xDD, 0x1D, 0xEF, 0xA5, 0x67, 0xEC, 0xFE, 0xA3, 0x2D, 0xA9,
        0xFD, 0xF3, 0xEE, 0x35, 0xF4, 0xA7, 0xBC, 0xF9, 0x71, 0xCC,
        0xB9, 0xC0, 0x5F, 0x58, 0x5B, 0xBD, 0x1A, 0x9E, 0xC7, 0x08,
        0x67, 0x7C, 0xC7, 0x51, 0x5B, 0xBE, 0xE3, 0xF8, 0xBE, 0x1E,
        0xC7, 0xD2, 0x28, 0x97
};
static const int sizeof_client_key_der_3072 = sizeof(client_key_der_3072);

/* ./certs/3072/client-keyPub.der, 3072-bit */
static const unsigned char client_keypub_der_3072[] =
{
        0x30, 0x82, 0x01, 0xA2, 0x30, 0x0D, 0x06, 0x09, 0x2A, 0x86,
        0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01, 0x05, 0x00, 0x03,
        0x82, 0x01, 0x8F, 0x00, 0x30, 0x82, 0x01, 0x8A, 0x02, 0x82,
        0x01, 0x81, 0x00, 0xAC, 0x39, 0x50, 0x68, 0x8F, 0x78, 0xF8,
        0x10, 0x9B, 0x68, 0x96, 0xD3, 0xE1, 0x9C, 0x56, 0x68, 0x5A,
        0x41, 0x62, 0xE3, 0xB3, 0x41, 0xB0, 0x55, 0x80, 0x17, 0xB0,
        0x88, 0x16, 0x9B, 0xE0, 0x97, 0x74, 0x5F, 0x42, 0x79, 0x73,
        0x42, 0xDF, 0x93, 0xF3, 0xAA, 0x9D, 0xEE, 0x2D, 0x6F, 0xAA,
        0xBC, 0x27, 0x90, 0x84, 0xC0, 0x5D, 0xC7, 0xEC, 0x49, 0xEA,
        0x5C, 0x66, 0x1D, 0x70, 0x9C, 0x53, 0x5C, 0xBA, 0xA1, 0xB3,
        0x58, 0xC9, 0x3E, 0x8E, 0x9B, 0x72, 0x3D, 0x6E, 0x02, 0x02,
        0x00, 0x9C, 0x65, 0x56, 0x82, 0xA3, 0x22, 0xB4, 0x08, 0x5F,
        0x2A, 0xEF, 0xDF, 0x9A, 0xD0, 0xE7, 0x31, 0x59, 0x26, 0x5B,
        0x0B, 0x1C, 0x63, 0x61, 0xFF, 0xD5, 0x69, 0x32, 0x19, 0x06,
        0x7E, 0x0F, 0x40, 0x3C, 0x7A, 0x1E, 0xC8, 0xFC, 0x58, 0x6C,
        0x64, 0xAE, 0x10, 0x3D, 0xA8, 0x23, 0xFF, 0x8E, 0x1A, 0xCA,
        0x6A, 0x82, 0xE2, 0xF9, 0x01, 0x64, 0x2C, 0x97, 0xA0, 0x1A,
        0x89, 0xA0, 0x74, 0xD3, 0xB6, 0x05, 0x11, 0xF2, 0x62, 0x06,
        0x48, 0x2A, 0xF7, 0x66, 0xCE, 0xC1, 0x85, 0xE1, 0xD2, 0x27,
        0xEA, 0xCA, 0x12, 0xA5, 0x91, 0x97, 0x3E, 0xFC, 0x94, 0x06,
        0x59, 0x51, 0xC0, 0xE7, 0x13, 0xB6, 0x87, 0x7B, 0x5F, 0xD2,
        0xC0, 0x56, 0x2F, 0x5E, 0x1D, 0x02, 0xC3, 0x11, 0x2C, 0xDF,
        0xF7, 0x01, 0xDA, 0xBD, 0x85, 0x54, 0x35, 0x32, 0x5F, 0xC5,
        0xC8, 0xF9, 0x7A, 0x9F, 0x89, 0xF7, 0x03, 0x0E, 0x7E, 0x79,
        0x5D, 0x04, 0x82, 0x35, 0x10, 0xFE, 0x6D, 0x9B, 0xBF, 0xB8,
        0xEE, 0xE2, 0x62, 0x87, 0x26, 0x5E, 0x2F, 0x50, 0x2F, 0x78,
        0x0C, 0xE8, 0x73, 0x4F, 0x88, 0x6A, 0xD6, 0x26, 0xA4, 0xC9,
        0xFC, 0xFA, 0x1E, 0x8A, 0xB0, 0xF4, 0x32, 0xCF, 0x57, 0xCD,
        0xA1, 0x58, 0x8A, 0x49, 0x0F, 0xBB, 0xA9, 0x1D, 0x86, 0xAB,
        0xB9, 0x8F, 0x8D, 0x57, 0x19, 0xB2, 0x5A, 0x7E, 0xA4, 0xEA,
        0xCC, 0xB7, 0x96, 0x7A, 0x3B, 0x38, 0xCD, 0xDE, 0xE0, 0x61,
        0xFC, 0xC9, 0x06, 0x8F, 0x93, 0x5A, 0xCE, 0xAD, 0x2A, 0xE3,
        0x2D, 0x3E, 0x39, 0x5D, 0x41, 0x83, 0x01, 0x1F, 0x0F, 0xE1,
        0x7F, 0x76, 0xC7, 0x28, 0xDA, 0x56, 0xEF, 0xBF, 0xDC, 0x26,
        0x35, 0x40, 0xBE, 0xAD, 0xC7, 0x38, 0xAD, 0xA4, 0x06, 0xAC,
        0xCA, 0xE8, 0x51, 0xEB, 0xC0, 0xF8, 0x68, 0x02, 0x2C, 0x9B,
        0xA1, 0x14, 0xBC, 0xF8, 0x61, 0x86, 0xD7, 0x56, 0xD7, 0x73,
        0xF4, 0xAB, 0xBB, 0x6A, 0x21, 0xD3, 0x88, 0x22, 0xB4, 0xE7,
        0x6F, 0x7F, 0x91, 0xE5, 0x0E, 0xC6, 0x08, 0x49, 0xDE, 0xEA,
        0x13, 0x58, 0x72, 0xA0, 0xAA, 0x3A, 0xF9, 0x36, 0x03, 0x45,
        0x57, 0x5E, 0x87, 0xD2, 0x73, 0x65, 0xC4, 0x8C, 0xA3, 0xEE,
        0xC9, 0xD6, 0x73, 0x7C, 0x96, 0x41, 0x93, 0x02, 0x03, 0x01,
        0x00, 0x01
};
static const int sizeof_client_keypub_der_3072 = sizeof(client_keypub_der_3072);

/* ./certs/3072/client-cert.der, 3072-bit */
static const unsigned char client_cert_der_3072[] =
{
        0x30, 0x82, 0x06, 0x1D, 0x30, 0x82, 0x04, 0x85, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x14, 0x1E, 0xD5, 0xB7, 0x66, 0x40,
        0x3A, 0xE9, 0x9B, 0xDD, 0x58, 0xE4, 0xE4, 0x9A, 0xC0, 0xDA,
        0x1E, 0xD7, 0xB9, 0x5A, 0x1F, 0x30, 0x0D, 0x06, 0x09, 0x2A,
        0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B, 0x05, 0x00,
        0x30, 0x81, 0x9E, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55,
        0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E,
        0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E,
        0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03,
        0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D,
        0x61, 0x6E, 0x31, 0x15, 0x30, 0x13, 0x06, 0x03, 0x55, 0x04,
        0x0A, 0x0C, 0x0C, 0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C,
        0x5F, 0x33, 0x30, 0x37, 0x32, 0x31, 0x19, 0x30, 0x17, 0x06,
        0x03, 0x55, 0x04, 0x0B, 0x0C, 0x10, 0x50, 0x72, 0x6F, 0x67,
        0x72, 0x61, 0x6D, 0x6D, 0x69, 0x6E, 0x67, 0x2D, 0x33, 0x30,
        0x37, 0x32, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04,
        0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F,
        0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D,
        0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x30, 0x1E, 0x17, 0x0D, 0x32, 0x34, 0x31, 0x32, 0x31,
        0x38, 0x32, 0x31, 0x32, 0x35, 0x32, 0x39, 0x5A, 0x17, 0x0D,
        0x32, 0x37, 0x30, 0x39, 0x31, 0x34, 0x32, 0x31, 0x32, 0x35,
        0x32, 0x39, 0x5A, 0x30, 0x81, 0x9E, 0x31, 0x0B, 0x30, 0x09,
        0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31,
        0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07,
        0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30,
        0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F,
        0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x15, 0x30, 0x13, 0x06,
        0x03, 0x55, 0x04, 0x0A, 0x0C, 0x0C, 0x77, 0x6F, 0x6C, 0x66,
        0x53, 0x53, 0x4C, 0x5F, 0x33, 0x30, 0x37, 0x32, 0x31, 0x19,
        0x30, 0x17, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x10, 0x50,
        0x72, 0x6F, 0x67, 0x72, 0x61, 0x6D, 0x6D, 0x69, 0x6E, 0x67,
        0x2D, 0x33, 0x30, 0x37, 0x32, 0x31, 0x18, 0x30, 0x16, 0x06,
        0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48,
        0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E,
        0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C,
        0x2E, 0x63, 0x6F, 0x6D, 0x30, 0x82, 0x01, 0xA2, 0x30, 0x0D,
        0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01,
        0x01, 0x05, 0x00, 0x03, 0x82, 0x01, 0x8F, 0x00, 0x30, 0x82,
        0x01, 0x8A, 0x02, 0x82, 0x01, 0x81, 0x00, 0xAC, 0x39, 0x50,
        0x68, 0x8F, 0x78, 0xF8, 0x10, 0x9B, 0x68, 0x96, 0xD3, 0xE1,
        0x9C, 0x56, 0x68, 0x5A, 0x41, 0x62, 0xE3, 0xB3, 0x41, 0xB0,
        0x55, 0x80, 0x17, 0xB0, 0x88, 0x16, 0x9B, 0xE0, 0x97, 0x74,
        0x5F, 0x42, 0x79, 0x73, 0x42, 0xDF, 0x93, 0xF3, 0xAA, 0x9D,
        0xEE, 0x2D, 0x6F, 0xAA, 0xBC, 0x27, 0x90, 0x84, 0xC0, 0x5D,
        0xC7, 0xEC, 0x49, 0xEA, 0x5C, 0x66, 0x1D, 0x70, 0x9C, 0x53,
        0x5C, 0xBA, 0xA1, 0xB3, 0x58, 0xC9, 0x3E, 0x8E, 0x9B, 0x72,
        0x3D, 0x6E, 0x02, 0x02, 0x00, 0x9C, 0x65, 0x56, 0x82, 0xA3,
        0x22, 0xB4, 0x08, 0x5F, 0x2A, 0xEF, 0xDF, 0x9A, 0xD0, 0xE7,
        0x31, 0x59, 0x26, 0x5B, 0x0B, 0x1C, 0x63, 0x61, 0xFF, 0xD5,
        0x69, 0x32, 0x19, 0x06, 0x7E, 0x0F, 0x40, 0x3C, 0x7A, 0x1E,
        0xC8, 0xFC, 0x58, 0x6C, 0x64, 0xAE, 0x10, 0x3D, 0xA8, 0x23,
        0xFF, 0x8E, 0x1A, 0xCA, 0x6A, 0x82, 0xE2, 0xF9, 0x01, 0x64,
        0x2C, 0x97, 0xA0, 0x1A, 0x89, 0xA0, 0x74, 0xD3, 0xB6, 0x05,
        0x11, 0xF2, 0x62, 0x06, 0x48, 0x2A, 0xF7, 0x66, 0xCE, 0xC1,
        0x85, 0xE1, 0xD2, 0x27, 0xEA, 0xCA, 0x12, 0xA5, 0x91, 0x97,
        0x3E, 0xFC, 0x94, 0x06, 0x59, 0x51, 0xC0, 0xE7, 0x13, 0xB6,
        0x87, 0x7B, 0x5F, 0xD2, 0xC0, 0x56, 0x2F, 0x5E, 0x1D, 0x02,
        0xC3, 0x11, 0x2C, 0xDF, 0xF7, 0x01, 0xDA, 0xBD, 0x85, 0x54,
        0x35, 0x32, 0x5F, 0xC5, 0xC8, 0xF9, 0x7A, 0x9F, 0x89, 0xF7,
        0x03, 0x0E, 0x7E, 0x79, 0x5D, 0x04, 0x82, 0x35, 0x10, 0xFE,
        0x6D, 0x9B, 0xBF, 0xB8, 0xEE, 0xE2, 0x62, 0x87, 0x26, 0x5E,
        0x2F, 0x50, 0x2F, 0x78, 0x0C, 0xE8, 0x73, 0x4F, 0x88, 0x6A,
        0xD6, 0x26, 0xA4, 0xC9, 0xFC, 0xFA, 0x1E, 0x8A, 0xB0, 0xF4,
        0x32, 0xCF, 0x57, 0xCD, 0xA1, 0x58, 0x8A, 0x49, 0x0F, 0xBB,
        0xA9, 0x1D, 0x86, 0xAB, 0xB9, 0x8F, 0x8D, 0x57, 0x19, 0xB2,
        0x5A, 0x7E, 0xA4, 0xEA, 0xCC, 0xB7, 0x96, 0x7A, 0x3B, 0x38,
        0xCD, 0xDE, 0xE0, 0x61, 0xFC, 0xC9, 0x06, 0x8F, 0x93, 0x5A,
        0xCE, 0xAD, 0x2A, 0xE3, 0x2D, 0x3E, 0x39, 0x5D, 0x41, 0x83,
        0x01, 0x1F, 0x0F, 0xE1, 0x7F, 0x76, 0xC7, 0x28, 0xDA, 0x56,
        0xEF, 0xBF, 0xDC, 0x26, 0x35, 0x40, 0xBE, 0xAD, 0xC7, 0x38,
        0xAD, 0xA4, 0x06, 0xAC, 0xCA, 0xE8, 0x51, 0xEB, 0xC0, 0xF8,
        0x68, 0x02, 0x2C, 0x9B, 0xA1, 0x14, 0xBC, 0xF8, 0x61, 0x86,
        0xD7, 0x56, 0xD7, 0x73, 0xF4, 0xAB, 0xBB, 0x6A, 0x21, 0xD3,
        0x88, 0x22, 0xB4, 0xE7, 0x6F, 0x7F, 0x91, 0xE5, 0x0E, 0xC6,
        0x08, 0x49, 0xDE, 0xEA, 0x13, 0x58, 0x72, 0xA0, 0xAA, 0x3A,
        0xF9, 0x36, 0x03, 0x45, 0x57, 0x5E, 0x87, 0xD2, 0x73, 0x65,
        0xC4, 0x8C, 0xA3, 0xEE, 0xC9, 0xD6, 0x73, 0x7C, 0x96, 0x41,
        0x93, 0x02, 0x03, 0x01, 0x00, 0x01, 0xA3, 0x82, 0x01, 0x4F,
        0x30, 0x82, 0x01, 0x4B, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D,
        0x0E, 0x04, 0x16, 0x04, 0x14, 0x3D, 0xD1, 0x84, 0xC2, 0xAF,
        0xB0, 0x20, 0x49, 0xBC, 0x74, 0x87, 0x41, 0x38, 0xAB, 0xBA,
        0xD2, 0xD4, 0x0C, 0xA3, 0xA8, 0x30, 0x81, 0xDE, 0x06, 0x03,
        0x55, 0x1D, 0x23, 0x04, 0x81, 0xD6, 0x30, 0x81, 0xD3, 0x80,
        0x14, 0x3D, 0xD1, 0x84, 0xC2, 0xAF, 0xB0, 0x20, 0x49, 0xBC,
        0x74, 0x87, 0x41, 0x38, 0xAB, 0xBA, 0xD2, 0xD4, 0x0C, 0xA3,
        0xA8, 0xA1, 0x81, 0xA4, 0xA4, 0x81, 0xA1, 0x30, 0x81, 0x9E,
        0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13,
        0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55,
        0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E,
        0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x07,
        0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31,
        0x15, 0x30, 0x13, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x0C,
        0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C, 0x5F, 0x33, 0x30,
        0x37, 0x32, 0x31, 0x19, 0x30, 0x17, 0x06, 0x03, 0x55, 0x04,
        0x0B, 0x0C, 0x10, 0x50, 0x72, 0x6F, 0x67, 0x72, 0x61, 0x6D,
        0x6D, 0x69, 0x6E, 0x67, 0x2D, 0x33, 0x30, 0x37, 0x32, 0x31,
        0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F,
        0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73,
        0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06,
        0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01,
        0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x82, 0x14,
        0x1E, 0xD5, 0xB7, 0x66, 0x40, 0x3A, 0xE9, 0x9B, 0xDD, 0x58,
        0xE4, 0xE4, 0x9A, 0xC0, 0xDA, 0x1E, 0xD7, 0xB9, 0x5A, 0x1F,
        0x30, 0x0C, 0x06, 0x03, 0x55, 0x1D, 0x13, 0x04, 0x05, 0x30,
        0x03, 0x01, 0x01, 0xFF, 0x30, 0x1C, 0x06, 0x03, 0x55, 0x1D,
        0x11, 0x04, 0x15, 0x30, 0x13, 0x82, 0x0B, 0x65, 0x78, 0x61,
        0x6D, 0x70, 0x6C, 0x65, 0x2E, 0x63, 0x6F, 0x6D, 0x87, 0x04,
        0x7F, 0x00, 0x00, 0x01, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D,
        0x25, 0x04, 0x16, 0x30, 0x14, 0x06, 0x08, 0x2B, 0x06, 0x01,
        0x05, 0x05, 0x07, 0x03, 0x01, 0x06, 0x08, 0x2B, 0x06, 0x01,
        0x05, 0x05, 0x07, 0x03, 0x02, 0x30, 0x0D, 0x06, 0x09, 0x2A,
        0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B, 0x05, 0x00,
        0x03, 0x82, 0x01, 0x81, 0x00, 0x5E, 0xB0, 0xED, 0x38, 0x36,
        0xB8, 0xF7, 0xE4, 0x0C, 0xB0, 0xC3, 0x6A, 0xBB, 0x7A, 0xB9,
        0x61, 0x05, 0x9D, 0xB9, 0x82, 0x12, 0x2D, 0x9C, 0x9E, 0x91,
        0x7B, 0xEC, 0xD0, 0x9B, 0x81, 0xCA, 0x51, 0xE8, 0xD4, 0x55,
        0x2D, 0x1A, 0xFF, 0x88, 0x5A, 0xC3, 0xE1, 0xD8, 0x82, 0x17,
        0xC5, 0x4A, 0x7A, 0xD4, 0x17, 0xC8, 0xA2, 0x1C, 0x97, 0x61,
        0xA7, 0xCF, 0xDE, 0x12, 0xF9, 0x5A, 0xD8, 0xB0, 0x63, 0x63,
        0x84, 0xD4, 0x7B, 0xB9, 0x81, 0x37, 0xA0, 0x49, 0xF3, 0x68,
        0x30, 0x0C, 0x84, 0xF8, 0x6C, 0x18, 0x54, 0x34, 0x6F, 0x8D,
        0xA3, 0x22, 0xD3, 0xD2, 0x3B, 0x42, 0xBC, 0x3B, 0x28, 0x0F,
        0x95, 0x35, 0xF4, 0x9F, 0xDC, 0x18, 0x9D, 0x4F, 0xC5, 0x5F,
        0x0D, 0xD2, 0xBD, 0x88, 0xB8, 0xA7, 0x88, 0x82, 0xD3, 0x74,
        0x5B, 0xA6, 0xAD, 0xB0, 0x2B, 0x70, 0x33, 0xC9, 0x08, 0x7E,
        0x5F, 0x9B, 0x99, 0x3C, 0x61, 0xF0, 0x1B, 0x3C, 0x1C, 0x4A,
        0x2A, 0x05, 0x84, 0xF1, 0x47, 0x17, 0xA2, 0xEA, 0x06, 0x3A,
        0xDC, 0xF6, 0xB3, 0x83, 0x30, 0x9C, 0x12, 0xB1, 0x4C, 0xE9,
        0xBE, 0x40, 0x86, 0x3E, 0x72, 0x58, 0x4E, 0x44, 0xB8, 0x99,
        0x59, 0xC3, 0x58, 0x0F, 0xD7, 0xCF, 0x02, 0x60, 0x77, 0xAD,
        0x6F, 0x9C, 0x41, 0x58, 0xEF, 0x78, 0x63, 0xC0, 0xF7, 0x7D,
        0xA7, 0xED, 0x67, 0xC2, 0x49, 0xAE, 0x06, 0xFC, 0x46, 0xF7,
        0x70, 0x53, 0x88, 0xEB, 0x53, 0x2F, 0x25, 0x8D, 0x7A, 0xAC,
        0xAB, 0xC4, 0xB5, 0xB0, 0x27, 0x90, 0x57, 0xD0, 0x31, 0x79,
        0x2F, 0xAD, 0xDA, 0x20, 0xC1, 0x6A, 0x00, 0xCC, 0xD9, 0xB4,
        0x36, 0x5A, 0x90, 0x99, 0x3D, 0xE3, 0xE2, 0xF4, 0xB6, 0xE7,
        0x85, 0x16, 0x77, 0x3D, 0x69, 0xBB, 0x42, 0x6C, 0xA5, 0x83,
        0x45, 0x9F, 0x53, 0xC4, 0x43, 0x78, 0x17, 0x43, 0xBD, 0x27,
        0xC0, 0x6E, 0x4B, 0x40, 0x0F, 0x64, 0x0B, 0xAC, 0x38, 0x1E,
        0x09, 0x6D, 0x62, 0x5A, 0x54, 0x8A, 0x2C, 0x96, 0x99, 0x23,
        0xDB, 0xF5, 0x4B, 0x4A, 0xAA, 0x69, 0xBE, 0x6E, 0x8A, 0x9A,
        0x3E, 0xD5, 0xE6, 0xA3, 0xA9, 0xA9, 0xE9, 0xE8, 0xA9, 0x28,
        0x28, 0x3B, 0xF9, 0x9D, 0xD9, 0x5F, 0xE3, 0xCB, 0x2B, 0x2B,
        0x38, 0xBA, 0xF1, 0xBC, 0x45, 0xD8, 0x4A, 0x5A, 0xB1, 0xB3,
        0x8A, 0x48, 0x64, 0x78, 0x33, 0x21, 0x55, 0xCD, 0x04, 0x14,
        0xE7, 0x7B, 0x73, 0xC2, 0xB6, 0xF2, 0xDE, 0x81, 0x01, 0xD8,
        0x8D, 0xC6, 0xCF, 0xF2, 0x85, 0x0F, 0x32, 0x72, 0x0F, 0x6C,
        0x60, 0xBE, 0xF5, 0x31, 0x75, 0x39, 0x4B, 0xE3, 0xAE, 0xED,
        0x0C, 0x1E, 0x15, 0x83, 0xAC, 0xF9, 0x4C, 0x86, 0xCF, 0xDF,
        0x54, 0xB0, 0x7C, 0x6F, 0xF5, 0xDE, 0x26, 0x66, 0xC0, 0xBA,
        0x85, 0x38, 0xD0, 0x25, 0xFE, 0xB9, 0xBF, 0x12, 0x98
};
static const int sizeof_client_cert_der_3072 = sizeof(client_cert_der_3072);

#endif /* USE_CERT_BUFFERS_3072 */

#ifdef USE_CERT_BUFFERS_4096

/* ./certs/4096/client-key.der, 4096-bit */
static const unsigned char client_key_der_4096[] =
{
        0x30, 0x82, 0x09, 0x42, 0x02, 0x01, 0x00, 0x30, 0x0D, 0x06,
        0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01,
        0x05, 0x00, 0x04, 0x82, 0x09, 0x2C, 0x30, 0x82, 0x09, 0x28,
        0x02, 0x01, 0x00, 0x02, 0x82, 0x02, 0x01, 0x00, 0xF5, 0xD0,
        0x31, 0xE4, 0x71, 0x59, 0x58, 0xB3, 0x07, 0x50, 0xDD, 0x16,
        0x79, 0xFC, 0xC6, 0x95, 0x50, 0xFC, 0x46, 0x0E, 0x57, 0x12,
        0x86, 0x71, 0x8D, 0xE3, 0x9B, 0x4A, 0x33, 0xEA, 0x4F, 0xD9,
        0x17, 0x13, 0x6D, 0x48, 0x69, 0xDF, 0x59, 0x11, 0x08, 0x02,
        0x9D, 0xAF, 0x2B, 0xC7, 0x30, 0xBE, 0x0C, 0xDC, 0x87, 0xD4,
        0x5A, 0x12, 0x09, 0x23, 0x5D, 0xE1, 0x76, 0x5A, 0x62, 0x37,
        0x46, 0x74, 0xEF, 0x03, 0x05, 0xBB, 0x1E, 0x6D, 0x29, 0x75,
        0x6C, 0x2E, 0x9D, 0x87, 0x0D, 0x8F, 0x87, 0xCB, 0x14, 0x95,
        0x9B, 0xBE, 0x17, 0x6B, 0x51, 0xD1, 0x4C, 0xDA, 0xD7, 0x91,
        0x66, 0xC5, 0x36, 0xEB, 0xE0, 0x07, 0x1A, 0x76, 0x4D, 0xB0,
        0xFB, 0xC1, 0xF5, 0x5E, 0x05, 0xDB, 0xBA, 0xCB, 0x25, 0xD9,
        0x99, 0x13, 0x1C, 0xC0, 0x35, 0xDC, 0x40, 0xE9, 0x36, 0xCD,
        0xC4, 0xD5, 0x7A, 0x41, 0x70, 0x0F, 0x36, 0xEB, 0xA5, 0x4E,
        0x17, 0x05, 0xD5, 0x75, 0x1B, 0x64, 0x62, 0x7A, 0x3F, 0x0D,
        0x28, 0x48, 0x6A, 0xE3, 0xAC, 0x9C, 0xA8, 0x8F, 0xE9, 0xED,
        0xF7, 0xCD, 0x24, 0xA0, 0xB1, 0xA0, 0x03, 0xAC, 0xE3, 0x03,
        0xF5, 0x3F, 0xD1, 0x96, 0xFF, 0x2A, 0x7E, 0x08, 0xB1, 0xD3,
        0xE0, 0x18, 0x14, 0xEC, 0x65, 0x37, 0x50, 0x43, 0xC2, 0x6A,
        0x8C, 0xF4, 0x5B, 0xFE, 0xC4, 0xCB, 0x8D, 0x3F, 0x81, 0x02,
        0xF7, 0xC2, 0xDD, 0xE4, 0xC1, 0x8E, 0x80, 0x0C, 0x04, 0x25,
        0x2D, 0x80, 0x5A, 0x2E, 0x0F, 0x22, 0x35, 0x4A, 0xF4, 0x85,
        0xED, 0x51, 0xD8, 0xAB, 0x6D, 0x8F, 0xA2, 0x3B, 0x24, 0x00,
        0x6E, 0x81, 0xE2, 0x1E, 0x76, 0xD6, 0xAC, 0x31, 0x12, 0xDB,
        0xF3, 0x8E, 0x07, 0xA1, 0xDE, 0x89, 0x4A, 0x39, 0x60, 0x77,
        0xC5, 0xAA, 0xF1, 0x51, 0xE6, 0x06, 0xF1, 0x95, 0x56, 0x2A,
        0xE1, 0x8E, 0x92, 0x30, 0x9F, 0xFE, 0x58, 0x44, 0xAC, 0x46,
        0xF2, 0xFD, 0x9A, 0xFC, 0xA8, 0x1D, 0xA1, 0xD3, 0x55, 0x37,
        0x4A, 0x8B, 0xFC, 0x9C, 0x33, 0xF8, 0xA7, 0x61, 0x48, 0x41,
        0x7C, 0x9C, 0x77, 0x3F, 0xF5, 0x80, 0x23, 0x7D, 0x43, 0xB4,
        0xD5, 0x88, 0x0A, 0xC9, 0x75, 0xD7, 0x44, 0x19, 0x4D, 0x77,
        0x6C, 0x0B, 0x0A, 0x49, 0xAA, 0x1C, 0x2F, 0xD6, 0x5A, 0x44,
        0xA6, 0x47, 0x4D, 0xE5, 0x36, 0x96, 0x40, 0x99, 0x2C, 0x56,
        0x26, 0xB1, 0xF2, 0x92, 0x31, 0x59, 0xD7, 0x2C, 0xD4, 0xB4,
        0x21, 0xD6, 0x65, 0x13, 0x0B, 0x3E, 0xFB, 0xFF, 0x04, 0xEB,
        0xB9, 0x85, 0xB9, 0xD8, 0xD8, 0x28, 0x4F, 0x5C, 0x17, 0x96,
        0xA3, 0x51, 0xBE, 0xFE, 0x7D, 0x0B, 0x1B, 0x48, 0x40, 0x25,
        0x76, 0x94, 0xDC, 0x41, 0xFB, 0xBF, 0x73, 0x76, 0xDA, 0xEB,
        0xB3, 0x62, 0xE7, 0xC1, 0xC8, 0x54, 0x6A, 0x93, 0xE1, 0x8D,
        0x31, 0xE8, 0x3E, 0x3E, 0xDF, 0xBC, 0x87, 0x02, 0x30, 0x22,
        0x57, 0xC4, 0xE0, 0x18, 0x7A, 0xD3, 0xAE, 0xE4, 0x02, 0x9B,
        0xAA, 0xBD, 0x4E, 0x49, 0x47, 0x72, 0xE9, 0x8D, 0x13, 0x2D,
        0x54, 0x9B, 0x00, 0xA7, 0x91, 0x61, 0x71, 0xC9, 0xCC, 0x48,
        0x4F, 0xEE, 0xDF, 0x5E, 0x1B, 0x1A, 0xDF, 0x67, 0xD3, 0x20,
        0xE6, 0x44, 0x45, 0x98, 0x7E, 0xE7, 0x0E, 0x63, 0x16, 0x83,
        0xC9, 0x26, 0x5D, 0x90, 0xC1, 0xE5, 0x2A, 0x5C, 0x45, 0x54,
        0x13, 0xB2, 0x81, 0x18, 0x06, 0x20, 0x2E, 0x2E, 0x66, 0x5A,
        0xB5, 0x7B, 0x6E, 0xD6, 0x0C, 0x4E, 0x89, 0x01, 0x56, 0x70,
        0xBB, 0xAE, 0xDE, 0xE9, 0x99, 0x5E, 0xD1, 0xB9, 0x3A, 0xB7,
        0x6C, 0x17, 0xB6, 0x03, 0xA9, 0x08, 0xDD, 0x9C, 0xF4, 0x14,
        0xC9, 0xC9, 0x59, 0x39, 0x72, 0xD4, 0x7E, 0x02, 0x37, 0x31,
        0xCD, 0x0E, 0xA7, 0x3D, 0xF8, 0xF2, 0xCF, 0x6B, 0x15, 0xAB,
        0x02, 0x03, 0x01, 0x00, 0x01, 0x02, 0x82, 0x02, 0x01, 0x00,
        0xC5, 0x76, 0x57, 0x7D, 0xF1, 0x68, 0x1A, 0x8E, 0xC6, 0x63,
        0xB9, 0x16, 0xA3, 0x2B, 0xE1, 0xC2, 0x74, 0xEA, 0x12, 0xC4,
        0xD6, 0x41, 0x75, 0x6A, 0xA6, 0xD6, 0x9E, 0x1A, 0x7F, 0x95,
        0xCC, 0x4A, 0xD1, 0xF4, 0xB3, 0x27, 0x26, 0x95, 0x5A, 0x91,
        0x09, 0xE4, 0x40, 0x13, 0x45, 0x91, 0x9F, 0xA0, 0x2B, 0xE8,
        0xC3, 0xDC, 0x5B, 0xF6, 0x7D, 0x0C, 0xC2, 0x0F, 0xA9, 0xE9,
        0x75, 0x58, 0x7D, 0xEA, 0xD5, 0x4D, 0x92, 0x3E, 0xFC, 0x74,
        0x28, 0x87, 0xC1, 0x3D, 0xB9, 0x21, 0x92, 0x4D, 0x28, 0x82,
        0x84, 0xA8, 0xA2, 0x11, 0x93, 0xF2, 0x8C, 0x29, 0x1C, 0x19,
        0xF8, 0x6D, 0x3F, 0x27, 0x51, 0xB5, 0x2D, 0xA3, 0xC7, 0x28,
        0x1D, 0xC4, 0xFC, 0x98, 0x94, 0xA8, 0xD0, 0xFF, 0xF0, 0x0F,
        0xDC, 0xF9, 0xED, 0xB3, 0xA2, 0xB6, 0xED, 0x0D, 0x5F, 0xBF,
        0x78, 0x5C, 0xD7, 0xAF, 0xBD, 0xA3, 0xEF, 0x86, 0xE9, 0x51,
        0x66, 0xDB, 0x52, 0x37, 0x47, 0x7F, 0xE9, 0x5F, 0x3C, 0x94,
        0x83, 0x2D, 0xE8, 0x9C, 0x33, 0xF1, 0x6C, 0xE9, 0xF3, 0xA6,
        0x97, 0xFE, 0xA7, 0xBF, 0x4D, 0x9B, 0x20, 0xD5, 0x2F, 0xDE,
        0xA4, 0x06, 0xBB, 0xEE, 0x66, 0x49, 0x6B, 0xF5, 0x10, 0x85,
        0x9F, 0x84, 0x5A, 0x52, 0x3E, 0x0C, 0xA0, 0x4A, 0x4C, 0xDA,
        0x01, 0xC5, 0x62, 0x31, 0xB1, 0xEC, 0xF8, 0xDD, 0xA3, 0x3B,
        0xCE, 0x41, 0x3A, 0x12, 0x79, 0xF9, 0x97, 0x5B, 0x07, 0x95,
        0x9F, 0x86, 0xD6, 0x04, 0x73, 0x6C, 0xE8, 0x8F, 0x4C, 0x4C,
        0x48, 0x1D, 0x85, 0xC4, 0xE7, 0xCE, 0xDE, 0x16, 0x31, 0xF6,
        0x5C, 0x37, 0x54, 0x8E, 0x55, 0xBC, 0xAF, 0x2E, 0x47, 0xE8,
        0xAC, 0x03, 0xB0, 0xA4, 0xF9, 0x90, 0x98, 0x99, 0xA4, 0xDC,
        0x6E, 0x98, 0x08, 0x5C, 0x07, 0xBB, 0x08, 0x93, 0xAF, 0x61,
        0x8D, 0x74, 0xA8, 0xF8, 0xC4, 0x89, 0x64, 0x10, 0xE1, 0xE6,
        0xC0, 0xCD, 0x1D, 0x39, 0x20, 0xD6, 0x5A, 0x89, 0x83, 0xFC,
        0x37, 0xE2, 0x12, 0x66, 0xA8, 0x12, 0xCC, 0x72, 0xBB, 0x1E,
        0xFB, 0x6A, 0xE3, 0x7C, 0x71, 0x7E, 0xB9, 0x2E, 0x8E, 0x84,
        0x66, 0xE1, 0xB9, 0xD0, 0x25, 0x9A, 0x6F, 0x9D, 0x19, 0xE6,
        0x7E, 0xE8, 0xD8, 0xF0, 0xC5, 0x23, 0x16, 0x9A, 0x68, 0x2C,
        0x1D, 0x55, 0xAE, 0x8E, 0x90, 0xEE, 0x8E, 0xEC, 0x5E, 0x46,
        0x9D, 0x60, 0x52, 0x32, 0x17, 0x28, 0x59, 0xC4, 0x49, 0x2A,
        0x20, 0x3E, 0x95, 0xC5, 0xDF, 0xF6, 0x3D, 0xF7, 0xC5, 0xCF,
        0xB1, 0xC2, 0xC9, 0x76, 0xF8, 0x3D, 0xBE, 0xF4, 0x63, 0xFC,
        0x2A, 0x00, 0x6F, 0x99, 0xA6, 0xB6, 0xAD, 0x35, 0xEE, 0xDE,
        0xC5, 0xE0, 0x97, 0xC6, 0x73, 0xEE, 0x33, 0xA0, 0xA8, 0xFC,
        0x4C, 0x8F, 0xF2, 0x8C, 0x61, 0xFB, 0x03, 0x19, 0xA1, 0xE8,
        0x17, 0x4E, 0xE3, 0x21, 0x58, 0xCE, 0xFE, 0xF2, 0x5F, 0xBB,
        0xDD, 0x4F, 0xF7, 0x18, 0xCB, 0x35, 0x57, 0xDD, 0xE5, 0x50,
        0x2A, 0x7B, 0x1A, 0xE9, 0x12, 0xF2, 0x7A, 0x11, 0xB1, 0x43,
        0xB9, 0x70, 0x07, 0x0C, 0x8F, 0x69, 0xB9, 0xE5, 0xA5, 0xC9,
        0xE2, 0x1B, 0x96, 0x74, 0x11, 0xF5, 0x95, 0xB9, 0x58, 0xC0,
        0xBD, 0x37, 0xFB, 0x28, 0x2A, 0xBD, 0x84, 0xB1, 0x2B, 0x67,
        0x42, 0x82, 0xC3, 0x95, 0x55, 0x45, 0xD5, 0xEA, 0xC3, 0x8A,
        0x42, 0x3A, 0x43, 0x17, 0x5E, 0xCD, 0xD2, 0xEA, 0xFC, 0xDF,
        0x67, 0xEC, 0xE1, 0x6C, 0xA8, 0x03, 0x19, 0xB2, 0x1D, 0x4A,
        0x5F, 0x4F, 0xE7, 0xD3, 0xE0, 0x86, 0xC5, 0x1A, 0x10, 0xC3,
        0x08, 0xD2, 0xED, 0x85, 0x93, 0x08, 0x51, 0x05, 0xA6, 0x37,
        0x15, 0x32, 0xBD, 0x6C, 0x73, 0x63, 0x01, 0x5D, 0x5B, 0x4F,
        0x6A, 0xDC, 0x6D, 0x1D, 0x55, 0x91, 0x21, 0xE4, 0x8E, 0xB7,
        0xF0, 0x81, 0x02, 0x82, 0x01, 0x01, 0x00, 0xFD, 0x27, 0xC8,
        0xFE, 0x76, 0x5C, 0x89, 0x32, 0xCB, 0x8A, 0x22, 0x87, 0x61,
        0x48, 0x91, 0x4A, 0x05, 0xAD, 0xA4, 0x5C, 0x8A, 0xCA, 0x5C,
        0x02, 0x88, 0x7E, 0x51, 0xC5, 0x66, 0x90, 0x2C, 0xA3, 0xED,
        0xA7, 0x43, 0x19, 0x0B, 0xA2, 0x42, 0xB4, 0xE0, 0xE0, 0x45,
        0xBF, 0xFE, 0xA0, 0xF2, 0x75, 0x0B, 0x8E, 0x7D, 0x9D, 0x73,
        0x67, 0xD3, 0x10, 0x09, 0xC5, 0xD9, 0x8C, 0xAD, 0x3A, 0x64,
        0x72, 0xAD, 0x96, 0x35, 0x91, 0x0F, 0x4B, 0xC9, 0xBD, 0x4F,
        0x65, 0x47, 0xA6, 0x2D, 0xEB, 0x3F, 0xE2, 0x99, 0x72, 0x66,
        0x12, 0xED, 0xEB, 0xD2, 0x7C, 0xFF, 0x3A, 0x20, 0x37, 0x2A,
        0xD3, 0x65, 0x51, 0x9B, 0xC3, 0xAA, 0x18, 0xB1, 0x1F, 0x6E,
        0x9D, 0x40, 0x47, 0xA4, 0x1F, 0x82, 0x9B, 0xDB, 0x50, 0x6B,
        0x86, 0x2F, 0xFB, 0x3F, 0x31, 0xB9, 0x81, 0x11, 0x04, 0x14,
        0x63, 0x86, 0x4F, 0x40, 0x2A, 0xF5, 0xF9, 0x7C, 0xA1, 0x78,
        0x19, 0x13, 0xD0, 0x51, 0x51, 0x0F, 0x79, 0x88, 0x8D, 0x14,
        0xA3, 0xDE, 0xB6, 0x33, 0x29, 0x42, 0xB9, 0xE8, 0x59, 0x76,
        0xF7, 0x43, 0x1A, 0xB6, 0xA6, 0xDF, 0x0A, 0xC1, 0x42, 0xC7,
        0x3F, 0x1C, 0x7E, 0x5C, 0x2C, 0x91, 0x4B, 0x1E, 0xF8, 0x46,
        0x91, 0x1F, 0xEE, 0x56, 0xB3, 0x0E, 0xC8, 0xD0, 0x31, 0xD3,
        0x3D, 0xED, 0x3D, 0xD9, 0xC5, 0x30, 0x0C, 0x58, 0xD8, 0xB7,
        0xB5, 0xEC, 0x14, 0xAC, 0x41, 0x64, 0x6D, 0xE4, 0xC6, 0x59,
        0xFD, 0x14, 0x05, 0x60, 0x65, 0xD8, 0xC4, 0x84, 0x44, 0x7E,
        0x1B, 0xB4, 0xA4, 0x16, 0x75, 0xC1, 0x27, 0x96, 0xB2, 0x19,
        0xD6, 0x39, 0x54, 0xC0, 0x93, 0xF3, 0xD7, 0x1F, 0xCD, 0x1B,
        0xDF, 0xF8, 0x12, 0x88, 0x14, 0x9F, 0x98, 0x05, 0x47, 0x46,
        0x71, 0x81, 0x6C, 0xDF, 0x91, 0xEF, 0x53, 0xE3, 0xC5, 0xB1,
        0x89, 0x2F, 0xE1, 0x02, 0x82, 0x01, 0x01, 0x00, 0xF8, 0x93,
        0x4A, 0x28, 0x77, 0x94, 0xEF, 0xE9, 0xC4, 0x0A, 0xC3, 0xE8,
        0x52, 0x59, 0xB6, 0x1D, 0x8D, 0xCE, 0x14, 0xE7, 0x43, 0xC6,
        0xED, 0x09, 0x27, 0x5D, 0xF3, 0x8E, 0x08, 0x6A, 0x19, 0x6B,
        0x2C, 0x97, 0x9B, 0x88, 0x53, 0x2B, 0xDA, 0xFE, 0x4B, 0x94,
        0x66, 0x84, 0xD5, 0xA9, 0xCE, 0xA5, 0x43, 0x70, 0xFB, 0x01,
        0x5A, 0x6F, 0xCD, 0xF7, 0xD1, 0x9D, 0x51, 0xEE, 0xA0, 0xDC,
        0x46, 0xF5, 0x7D, 0xA7, 0xEE, 0xA0, 0x86, 0xB7, 0x83, 0xFF,
        0x21, 0x8B, 0x76, 0x05, 0x7D, 0xDE, 0xC4, 0x26, 0x36, 0xBC,
        0xB4, 0x8A, 0x48, 0xC3, 0x06, 0x90, 0x97, 0xE5, 0xA6, 0x38,
        0xC3, 0xE6, 0x7C, 0xD0, 0xF8, 0x23, 0xD2, 0x33, 0x1F, 0x81,
        0xC3, 0xE3, 0x7D, 0x85, 0x5A, 0x38, 0x10, 0x03, 0xE6, 0x88,
        0xDB, 0xC8, 0x4C, 0xD0, 0xF7, 0xB2, 0x4D, 0x27, 0x33, 0x85,
        0xCD, 0x3A, 0x74, 0x83, 0x6B, 0x82, 0x58, 0xD9, 0xDF, 0xEE,
        0xF5, 0xD3, 0xE9, 0xFE, 0x1C, 0xEF, 0x06, 0x12, 0x16, 0xD1,
        0x4C, 0xAE, 0x54, 0x4B, 0x0D, 0x1A, 0xBD, 0xE2, 0xCF, 0x56,
        0xB3, 0x74, 0xBE, 0x44, 0x4F, 0xA4, 0x73, 0x0A, 0x98, 0x8D,
        0x61, 0x84, 0x38, 0x46, 0xDC, 0x95, 0xCF, 0x3F, 0x6B, 0xE7,
        0x65, 0x87, 0x02, 0xBF, 0x4B, 0x57, 0xE2, 0x3D, 0xC4, 0x2B,
        0x1C, 0x82, 0x1D, 0xCC, 0x13, 0x7F, 0xC0, 0x06, 0x12, 0x8C,
        0x6F, 0x97, 0x50, 0x7B, 0x8C, 0x81, 0xC3, 0x23, 0x15, 0xEB,
        0x70, 0x07, 0x8E, 0xA1, 0x07, 0x1E, 0x59, 0xFA, 0x10, 0xCA,
        0x7E, 0x0F, 0xE2, 0xBB, 0xEE, 0x86, 0x26, 0x1E, 0x55, 0xB9,
        0x98, 0x66, 0x85, 0xEC, 0x27, 0xC5, 0xD9, 0x63, 0x8D, 0x51,
        0x77, 0xAA, 0xA0, 0x36, 0x55, 0x33, 0x10, 0x21, 0x5E, 0xEC,
        0x47, 0x67, 0x71, 0xD1, 0xAF, 0xFC, 0x3E, 0x50, 0xF5, 0xBE,
        0xD6, 0x92, 0xE7, 0x0B, 0x02, 0x82, 0x01, 0x00, 0x21, 0x7C,
        0x8A, 0xC4, 0xC6, 0x29, 0x55, 0x68, 0xA7, 0xAD, 0xDD, 0x05,
        0x65, 0x63, 0xF0, 0xFC, 0x06, 0xA6, 0x42, 0x70, 0x8F, 0x57,
        0x57, 0x36, 0x6A, 0x91, 0xB3, 0x05, 0x56, 0x9C, 0xC9, 0x9A,
        0xE1, 0x8B, 0xD7, 0x7F, 0x4F, 0x9F, 0xA6, 0x0D, 0x41, 0x15,
        0xC9, 0x84, 0x2D, 0x0D, 0x63, 0x25, 0x02, 0x63, 0x55, 0xD0,
        0x66, 0xFC, 0x9B, 0xD9, 0xAA, 0x41, 0x46, 0x96, 0xAA, 0x2F,
        0x68, 0x2C, 0x17, 0x34, 0x20, 0x5F, 0xD0, 0xD3, 0x28, 0x9B,
        0x67, 0x0E, 0x31, 0x9D, 0x14, 0xC3, 0xE2, 0x8E, 0x79, 0xD7,
        0xBD, 0x12, 0xD1, 0xEF, 0xF8, 0xC6, 0xDA, 0x07, 0xF9, 0x4C,
        0xF2, 0xD8, 0x45, 0xB5, 0xB6, 0xD1, 0xFA, 0x05, 0x0C, 0x20,
        0xE9, 0x43, 0xD9, 0xC5, 0xE0, 0x3A, 0xDE, 0xCE, 0xF9, 0x02,
        0xB9, 0x46, 0x65, 0xC0, 0x69, 0x4A, 0x8D, 0x8C, 0x3A, 0x10,
        0xFD, 0x15, 0x71, 0x25, 0xB8, 0x8A, 0x36, 0x41, 0x4B, 0x30,
        0x1C, 0xAF, 0xCC, 0x84, 0x28, 0xCD, 0x7D, 0x2B, 0x89, 0x59,
        0x88, 0x1A, 0x69, 0x12, 0x56, 0xD0, 0x25, 0x68, 0x6C, 0x08,
        0xB1, 0x88, 0xE1, 0x92, 0x7E, 0x08, 0xB2, 0xC6, 0x3C, 0x6C,
        0x35, 0xE8, 0xEE, 0x3E, 0xF4, 0xB8, 0x5C, 0x7B, 0xC0, 0x5B,
        0xFD, 0x11, 0xA3, 0x54, 0xA6, 0x99, 0x46, 0xE2, 0x5F, 0x4F,
        0xC7, 0xEE, 0x90, 0x1C, 0x37, 0x5B, 0x33, 0x10, 0xDF, 0x0B,
        0xC3, 0xB9, 0x47, 0xC2, 0x30, 0x4A, 0xF2, 0x1A, 0xEB, 0x41,
        0x25, 0x94, 0x29, 0x7A, 0xD0, 0x96, 0x88, 0x46, 0xEE, 0x6C,
        0x14, 0xF6, 0x5B, 0x3D, 0xBD, 0x4E, 0xD4, 0x3F, 0x05, 0x5B,
        0x07, 0xB9, 0xE3, 0x99, 0x87, 0x63, 0xCA, 0xC4, 0x71, 0x0B,
        0x73, 0x9D, 0x7B, 0xB6, 0x0F, 0xD4, 0x12, 0x8C, 0x4C, 0x5E,
        0x72, 0x3D, 0xFF, 0x6D, 0xC4, 0x61, 0x0C, 0x74, 0x5F, 0x53,
        0xBE, 0x39, 0x34, 0x61, 0x02, 0x82, 0x01, 0x00, 0x5F, 0xF2,
        0xF2, 0xB0, 0x16, 0x20, 0x8E, 0x4E, 0xCC, 0x96, 0x5F, 0x32,
        0x80, 0xFF, 0x11, 0xF5, 0xEC, 0x73, 0xBC, 0xCB, 0xDB, 0xF4,
        0xA0, 0x30, 0x65, 0x5A, 0xB5, 0x95, 0x80, 0x97, 0xFB, 0xC1,
        0xCB, 0xCF, 0xA5, 0x80, 0x84, 0xA2, 0x2C, 0x00, 0xF6, 0x89,
        0x8C, 0xDC, 0xFF, 0x60, 0x71, 0x5C, 0x87, 0x60, 0xC7, 0xF2,
        0xA8, 0xC6, 0xF9, 0x59, 0x0C, 0x37, 0x4E, 0x95, 0xEE, 0xCF,
        0xB8, 0x30, 0x30, 0x55, 0xAF, 0x1D, 0x95, 0x82, 0xA6, 0xD7,
        0xC7, 0x49, 0xFE, 0xBF, 0x75, 0xEB, 0x94, 0x09, 0x30, 0x1D,
        0xBD, 0x0E, 0x97, 0xB1, 0x78, 0x0A, 0x3E, 0x27, 0xAD, 0xF6,
        0xC1, 0x5F, 0x69, 0x94, 0x7C, 0x03, 0xCF, 0xB2, 0x5E, 0x1A,
        0x07, 0xD3, 0xFA, 0xF2, 0x8B, 0x75, 0x92, 0x70, 0xFE, 0xFE,
        0x9A, 0xDF, 0x81, 0x0F, 0x34, 0x5D, 0x45, 0xBC, 0xB8, 0xFD,
        0x8F, 0xCF, 0x5D, 0x84, 0x10, 0xEE, 0x9A, 0x7F, 0x57, 0x19,
        0xF5, 0x17, 0xDC, 0x7D, 0x73, 0x0B, 0xAC, 0x6B, 0x35, 0x15,
        0x8B, 0x24, 0xCB, 0x72, 0xC0, 0xD7, 0x2E, 0xAE, 0xAA, 0xDB,
        0xCB, 0x9F, 0x67, 0x86, 0x14, 0xBB, 0xE4, 0x90, 0x15, 0x7C,
        0x95, 0x44, 0xA5, 0x38, 0x6D, 0x13, 0x02, 0x91, 0x77, 0x84,
        0x35, 0x43, 0x5D, 0x03, 0x1C, 0x01, 0x0B, 0x5A, 0x4E, 0x2B,
        0x59, 0xF0, 0xBB, 0xB1, 0xB7, 0x61, 0x1B, 0x6C, 0xFC, 0xA1,
        0xEA, 0xBD, 0x1C, 0x9A, 0xE4, 0x0C, 0x7E, 0x97, 0x3F, 0x71,
        0xC6, 0xA7, 0x94, 0x1D, 0x82, 0x12, 0xEC, 0x26, 0x43, 0x6E,
        0xF6, 0x24, 0x09, 0xA0, 0x03, 0x1D, 0x12, 0xFF, 0xA8, 0x95,
        0x60, 0x47, 0x4A, 0xB0, 0x72, 0x55, 0xC3, 0x68, 0xD2, 0xF6,
        0xBC, 0x5B, 0x47, 0x46, 0x51, 0xB2, 0xC9, 0x2A, 0x28, 0x6A,
        0xC9, 0xD1, 0x1B, 0x35, 0x16, 0x5A, 0x26, 0x6F, 0xB7, 0xBB,
        0xF7, 0x35, 0x73, 0x2B, 0x02, 0x82, 0x01, 0x00, 0x56, 0xBA,
        0xD8, 0x02, 0xD7, 0x4B, 0x30, 0x5E, 0x1B, 0x1E, 0x2F, 0xF3,
        0x0D, 0xBC, 0xF1, 0x05, 0x6A, 0x68, 0x4A, 0xE1, 0xEA, 0xB3,
        0xDE, 0x61, 0x8C, 0x89, 0x44, 0xBA, 0x63, 0x5E, 0xDF, 0x05,
        0x24, 0x32, 0x71, 0x65, 0x1A, 0x36, 0x2F, 0xBC, 0x07, 0x75,
        0xA3, 0xCE, 0x9E, 0x52, 0x92, 0x95, 0x4D, 0x3F, 0xC9, 0x06,
        0xBC, 0xA1, 0x14, 0x33, 0x37, 0x95, 0xAB, 0x9A, 0xEB, 0x04,
        0xF6, 0x15, 0xC3, 0x9B, 0x10, 0x56, 0x53, 0xA2, 0x28, 0xF2,
        0x68, 0xDA, 0x7D, 0x97, 0x52, 0x63, 0xAC, 0x9B, 0x56, 0xA9,
        0xAB, 0x2E, 0x1E, 0x9E, 0x01, 0x70, 0xFF, 0x2B, 0x6D, 0x0C,
        0x4B, 0xA6, 0xC3, 0x3A, 0xB3, 0xD1, 0xA7, 0x4B, 0x5E, 0x49,
        0x2E, 0x95, 0xD6, 0x6A, 0xAE, 0x58, 0x13, 0x66, 0x8F, 0x2F,
        0x93, 0xE4, 0x6E, 0x8B, 0xFA, 0x94, 0x30, 0x3E, 0xEC, 0x96,
        0xAB, 0x46, 0x20, 0x3E, 0xC5, 0x30, 0xB4, 0xEB, 0x41, 0x00,
        0x39, 0x60, 0x1D, 0xE1, 0x20, 0xCE, 0x31, 0x70, 0x17, 0x39,
        0xCB, 0x76, 0x56, 0x6C, 0x55, 0x7B, 0x90, 0x20, 0xBC, 0x39,
        0xB2, 0x5B, 0xD1, 0x28, 0x6F, 0x0C, 0x4F, 0x45, 0x6B, 0x82,
        0xC4, 0x57, 0x23, 0x0C, 0x3F, 0x3F, 0x2D, 0x83, 0xB3, 0x3D,
        0x8E, 0xF9, 0x1A, 0xDA, 0x77, 0x54, 0x2E, 0xFE, 0x16, 0x2E,
        0xBA, 0x99, 0xDD, 0xCA, 0xB3, 0xD1, 0xD8, 0xBB, 0x87, 0xE1,
        0xD0, 0xA9, 0xD4, 0xE6, 0x8F, 0xE8, 0x00, 0x3E, 0x49, 0x8A,
        0xDD, 0xA6, 0x32, 0x91, 0x00, 0x31, 0x31, 0x21, 0x98, 0x18,
        0x94, 0xC9, 0x2D, 0x27, 0x05, 0xB7, 0x9B, 0x09, 0x2E, 0xBB,
        0x5D, 0xBF, 0x67, 0xE8, 0x0E, 0xD1, 0x44, 0x75, 0x80, 0x1D,
        0x0A, 0x21, 0x8F, 0x95, 0x76, 0xB0, 0xFC, 0x19, 0x3C, 0xFF,
        0x92, 0xEA, 0x01, 0x45, 0x89, 0xD1, 0x4E, 0xFE, 0x4D, 0x2B,
        0x4B, 0x18, 0xE6, 0xCE
};
static const int sizeof_client_key_der_4096 = sizeof(client_key_der_4096);

/* ./certs/4096/client-keyPub.der, 4096-bit */
static const unsigned char client_keypub_der_4096[] =
{
        0x30, 0x82, 0x02, 0x22, 0x30, 0x0D, 0x06, 0x09, 0x2A, 0x86,
        0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01, 0x05, 0x00, 0x03,
        0x82, 0x02, 0x0F, 0x00, 0x30, 0x82, 0x02, 0x0A, 0x02, 0x82,
        0x02, 0x01, 0x00, 0xF5, 0xD0, 0x31, 0xE4, 0x71, 0x59, 0x58,
        0xB3, 0x07, 0x50, 0xDD, 0x16, 0x79, 0xFC, 0xC6, 0x95, 0x50,
        0xFC, 0x46, 0x0E, 0x57, 0x12, 0x86, 0x71, 0x8D, 0xE3, 0x9B,
        0x4A, 0x33, 0xEA, 0x4F, 0xD9, 0x17, 0x13, 0x6D, 0x48, 0x69,
        0xDF, 0x59, 0x11, 0x08, 0x02, 0x9D, 0xAF, 0x2B, 0xC7, 0x30,
        0xBE, 0x0C, 0xDC, 0x87, 0xD4, 0x5A, 0x12, 0x09, 0x23, 0x5D,
        0xE1, 0x76, 0x5A, 0x62, 0x37, 0x46, 0x74, 0xEF, 0x03, 0x05,
        0xBB, 0x1E, 0x6D, 0x29, 0x75, 0x6C, 0x2E, 0x9D, 0x87, 0x0D,
        0x8F, 0x87, 0xCB, 0x14, 0x95, 0x9B, 0xBE, 0x17, 0x6B, 0x51,
        0xD1, 0x4C, 0xDA, 0xD7, 0x91, 0x66, 0xC5, 0x36, 0xEB, 0xE0,
        0x07, 0x1A, 0x76, 0x4D, 0xB0, 0xFB, 0xC1, 0xF5, 0x5E, 0x05,
        0xDB, 0xBA, 0xCB, 0x25, 0xD9, 0x99, 0x13, 0x1C, 0xC0, 0x35,
        0xDC, 0x40, 0xE9, 0x36, 0xCD, 0xC4, 0xD5, 0x7A, 0x41, 0x70,
        0x0F, 0x36, 0xEB, 0xA5, 0x4E, 0x17, 0x05, 0xD5, 0x75, 0x1B,
        0x64, 0x62, 0x7A, 0x3F, 0x0D, 0x28, 0x48, 0x6A, 0xE3, 0xAC,
        0x9C, 0xA8, 0x8F, 0xE9, 0xED, 0xF7, 0xCD, 0x24, 0xA0, 0xB1,
        0xA0, 0x03, 0xAC, 0xE3, 0x03, 0xF5, 0x3F, 0xD1, 0x96, 0xFF,
        0x2A, 0x7E, 0x08, 0xB1, 0xD3, 0xE0, 0x18, 0x14, 0xEC, 0x65,
        0x37, 0x50, 0x43, 0xC2, 0x6A, 0x8C, 0xF4, 0x5B, 0xFE, 0xC4,
        0xCB, 0x8D, 0x3F, 0x81, 0x02, 0xF7, 0xC2, 0xDD, 0xE4, 0xC1,
        0x8E, 0x80, 0x0C, 0x04, 0x25, 0x2D, 0x80, 0x5A, 0x2E, 0x0F,
        0x22, 0x35, 0x4A, 0xF4, 0x85, 0xED, 0x51, 0xD8, 0xAB, 0x6D,
        0x8F, 0xA2, 0x3B, 0x24, 0x00, 0x6E, 0x81, 0xE2, 0x1E, 0x76,
        0xD6, 0xAC, 0x31, 0x12, 0xDB, 0xF3, 0x8E, 0x07, 0xA1, 0xDE,
        0x89, 0x4A, 0x39, 0x60, 0x77, 0xC5, 0xAA, 0xF1, 0x51, 0xE6,
        0x06, 0xF1, 0x95, 0x56, 0x2A, 0xE1, 0x8E, 0x92, 0x30, 0x9F,
        0xFE, 0x58, 0x44, 0xAC, 0x46, 0xF2, 0xFD, 0x9A, 0xFC, 0xA8,
        0x1D, 0xA1, 0xD3, 0x55, 0x37, 0x4A, 0x8B, 0xFC, 0x9C, 0x33,
        0xF8, 0xA7, 0x61, 0x48, 0x41, 0x7C, 0x9C, 0x77, 0x3F, 0xF5,
        0x80, 0x23, 0x7D, 0x43, 0xB4, 0xD5, 0x88, 0x0A, 0xC9, 0x75,
        0xD7, 0x44, 0x19, 0x4D, 0x77, 0x6C, 0x0B, 0x0A, 0x49, 0xAA,
        0x1C, 0x2F, 0xD6, 0x5A, 0x44, 0xA6, 0x47, 0x4D, 0xE5, 0x36,
        0x96, 0x40, 0x99, 0x2C, 0x56, 0x26, 0xB1, 0xF2, 0x92, 0x31,
        0x59, 0xD7, 0x2C, 0xD4, 0xB4, 0x21, 0xD6, 0x65, 0x13, 0x0B,
        0x3E, 0xFB, 0xFF, 0x04, 0xEB, 0xB9, 0x85, 0xB9, 0xD8, 0xD8,
        0x28, 0x4F, 0x5C, 0x17, 0x96, 0xA3, 0x51, 0xBE, 0xFE, 0x7D,
        0x0B, 0x1B, 0x48, 0x40, 0x25, 0x76, 0x94, 0xDC, 0x41, 0xFB,
        0xBF, 0x73, 0x76, 0xDA, 0xEB, 0xB3, 0x62, 0xE7, 0xC1, 0xC8,
        0x54, 0x6A, 0x93, 0xE1, 0x8D, 0x31, 0xE8, 0x3E, 0x3E, 0xDF,
        0xBC, 0x87, 0x02, 0x30, 0x22, 0x57, 0xC4, 0xE0, 0x18, 0x7A,
        0xD3, 0xAE, 0xE4, 0x02, 0x9B, 0xAA, 0xBD, 0x4E, 0x49, 0x47,
        0x72, 0xE9, 0x8D, 0x13, 0x2D, 0x54, 0x9B, 0x00, 0xA7, 0x91,
        0x61, 0x71, 0xC9, 0xCC, 0x48, 0x4F, 0xEE, 0xDF, 0x5E, 0x1B,
        0x1A, 0xDF, 0x67, 0xD3, 0x20, 0xE6, 0x44, 0x45, 0x98, 0x7E,
        0xE7, 0x0E, 0x63, 0x16, 0x83, 0xC9, 0x26, 0x5D, 0x90, 0xC1,
        0xE5, 0x2A, 0x5C, 0x45, 0x54, 0x13, 0xB2, 0x81, 0x18, 0x06,
        0x20, 0x2E, 0x2E, 0x66, 0x5A, 0xB5, 0x7B, 0x6E, 0xD6, 0x0C,
        0x4E, 0x89, 0x01, 0x56, 0x70, 0xBB, 0xAE, 0xDE, 0xE9, 0x99,
        0x5E, 0xD1, 0xB9, 0x3A, 0xB7, 0x6C, 0x17, 0xB6, 0x03, 0xA9,
        0x08, 0xDD, 0x9C, 0xF4, 0x14, 0xC9, 0xC9, 0x59, 0x39, 0x72,
        0xD4, 0x7E, 0x02, 0x37, 0x31, 0xCD, 0x0E, 0xA7, 0x3D, 0xF8,
        0xF2, 0xCF, 0x6B, 0x15, 0xAB, 0x02, 0x03, 0x01, 0x00, 0x01

};
static const int sizeof_client_keypub_der_4096 = sizeof(client_keypub_der_4096);

/* ./certs/4096/client-cert.der, 4096-bit */
static const unsigned char client_cert_der_4096[] =
{
        0x30, 0x82, 0x07, 0x1D, 0x30, 0x82, 0x05, 0x05, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x14, 0x12, 0x66, 0xC3, 0xA2, 0x08,
        0x5C, 0xF7, 0xD0, 0x6E, 0xE9, 0xA8, 0x82, 0xA2, 0xAB, 0x9C,
        0x0F, 0x76, 0x9E, 0x96, 0xF4, 0x30, 0x0D, 0x06, 0x09, 0x2A,
        0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B, 0x05, 0x00,
        0x30, 0x81, 0x9E, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55,
        0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E,
        0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E,
        0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03,
        0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D,
        0x61, 0x6E, 0x31, 0x15, 0x30, 0x13, 0x06, 0x03, 0x55, 0x04,
        0x0A, 0x0C, 0x0C, 0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C,
        0x5F, 0x34, 0x30, 0x39, 0x36, 0x31, 0x19, 0x30, 0x17, 0x06,
        0x03, 0x55, 0x04, 0x0B, 0x0C, 0x10, 0x50, 0x72, 0x6F, 0x67,
        0x72, 0x61, 0x6D, 0x6D, 0x69, 0x6E, 0x67, 0x2D, 0x34, 0x30,
        0x39, 0x36, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04,
        0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F,
        0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D,
        0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x30, 0x1E, 0x17, 0x0D, 0x32, 0x34, 0x31, 0x32, 0x31,
        0x38, 0x32, 0x31, 0x32, 0x35, 0x32, 0x39, 0x5A, 0x17, 0x0D,
        0x32, 0x37, 0x30, 0x39, 0x31, 0x34, 0x32, 0x31, 0x32, 0x35,
        0x32, 0x39, 0x5A, 0x30, 0x81, 0x9E, 0x31, 0x0B, 0x30, 0x09,
        0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31,
        0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07,
        0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30,
        0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F,
        0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x15, 0x30, 0x13, 0x06,
        0x03, 0x55, 0x04, 0x0A, 0x0C, 0x0C, 0x77, 0x6F, 0x6C, 0x66,
        0x53, 0x53, 0x4C, 0x5F, 0x34, 0x30, 0x39, 0x36, 0x31, 0x19,
        0x30, 0x17, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x10, 0x50,
        0x72, 0x6F, 0x67, 0x72, 0x61, 0x6D, 0x6D, 0x69, 0x6E, 0x67,
        0x2D, 0x34, 0x30, 0x39, 0x36, 0x31, 0x18, 0x30, 0x16, 0x06,
        0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48,
        0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E,
        0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C,
        0x2E, 0x63, 0x6F, 0x6D, 0x30, 0x82, 0x02, 0x22, 0x30, 0x0D,
        0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01,
        0x01, 0x05, 0x00, 0x03, 0x82, 0x02, 0x0F, 0x00, 0x30, 0x82,
        0x02, 0x0A, 0x02, 0x82, 0x02, 0x01, 0x00, 0xF5, 0xD0, 0x31,
        0xE4, 0x71, 0x59, 0x58, 0xB3, 0x07, 0x50, 0xDD, 0x16, 0x79,
        0xFC, 0xC6, 0x95, 0x50, 0xFC, 0x46, 0x0E, 0x57, 0x12, 0x86,
        0x71, 0x8D, 0xE3, 0x9B, 0x4A, 0x33, 0xEA, 0x4F, 0xD9, 0x17,
        0x13, 0x6D, 0x48, 0x69, 0xDF, 0x59, 0x11, 0x08, 0x02, 0x9D,
        0xAF, 0x2B, 0xC7, 0x30, 0xBE, 0x0C, 0xDC, 0x87, 0xD4, 0x5A,
        0x12, 0x09, 0x23, 0x5D, 0xE1, 0x76, 0x5A, 0x62, 0x37, 0x46,
        0x74, 0xEF, 0x03, 0x05, 0xBB, 0x1E, 0x6D, 0x29, 0x75, 0x6C,
        0x2E, 0x9D, 0x87, 0x0D, 0x8F, 0x87, 0xCB, 0x14, 0x95, 0x9B,
        0xBE, 0x17, 0x6B, 0x51, 0xD1, 0x4C, 0xDA, 0xD7, 0x91, 0x66,
        0xC5, 0x36, 0xEB, 0xE0, 0x07, 0x1A, 0x76, 0x4D, 0xB0, 0xFB,
        0xC1, 0xF5, 0x5E, 0x05, 0xDB, 0xBA, 0xCB, 0x25, 0xD9, 0x99,
        0x13, 0x1C, 0xC0, 0x35, 0xDC, 0x40, 0xE9, 0x36, 0xCD, 0xC4,
        0xD5, 0x7A, 0x41, 0x70, 0x0F, 0x36, 0xEB, 0xA5, 0x4E, 0x17,
        0x05, 0xD5, 0x75, 0x1B, 0x64, 0x62, 0x7A, 0x3F, 0x0D, 0x28,
        0x48, 0x6A, 0xE3, 0xAC, 0x9C, 0xA8, 0x8F, 0xE9, 0xED, 0xF7,
        0xCD, 0x24, 0xA0, 0xB1, 0xA0, 0x03, 0xAC, 0xE3, 0x03, 0xF5,
        0x3F, 0xD1, 0x96, 0xFF, 0x2A, 0x7E, 0x08, 0xB1, 0xD3, 0xE0,
        0x18, 0x14, 0xEC, 0x65, 0x37, 0x50, 0x43, 0xC2, 0x6A, 0x8C,
        0xF4, 0x5B, 0xFE, 0xC4, 0xCB, 0x8D, 0x3F, 0x81, 0x02, 0xF7,
        0xC2, 0xDD, 0xE4, 0xC1, 0x8E, 0x80, 0x0C, 0x04, 0x25, 0x2D,
        0x80, 0x5A, 0x2E, 0x0F, 0x22, 0x35, 0x4A, 0xF4, 0x85, 0xED,
        0x51, 0xD8, 0xAB, 0x6D, 0x8F, 0xA2, 0x3B, 0x24, 0x00, 0x6E,
        0x81, 0xE2, 0x1E, 0x76, 0xD6, 0xAC, 0x31, 0x12, 0xDB, 0xF3,
        0x8E, 0x07, 0xA1, 0xDE, 0x89, 0x4A, 0x39, 0x60, 0x77, 0xC5,
        0xAA, 0xF1, 0x51, 0xE6, 0x06, 0xF1, 0x95, 0x56, 0x2A, 0xE1,
        0x8E, 0x92, 0x30, 0x9F, 0xFE, 0x58, 0x44, 0xAC, 0x46, 0xF2,
        0xFD, 0x9A, 0xFC, 0xA8, 0x1D, 0xA1, 0xD3, 0x55, 0x37, 0x4A,
        0x8B, 0xFC, 0x9C, 0x33, 0xF8, 0xA7, 0x61, 0x48, 0x41, 0x7C,
        0x9C, 0x77, 0x3F, 0xF5, 0x80, 0x23, 0x7D, 0x43, 0xB4, 0xD5,
        0x88, 0x0A, 0xC9, 0x75, 0xD7, 0x44, 0x19, 0x4D, 0x77, 0x6C,
        0x0B, 0x0A, 0x49, 0xAA, 0x1C, 0x2F, 0xD6, 0x5A, 0x44, 0xA6,
        0x47, 0x4D, 0xE5, 0x36, 0x96, 0x40, 0x99, 0x2C, 0x56, 0x26,
        0xB1, 0xF2, 0x92, 0x31, 0x59, 0xD7, 0x2C, 0xD4, 0xB4, 0x21,
        0xD6, 0x65, 0x13, 0x0B, 0x3E, 0xFB, 0xFF, 0x04, 0xEB, 0xB9,
        0x85, 0xB9, 0xD8, 0xD8, 0x28, 0x4F, 0x5C, 0x17, 0x96, 0xA3,
        0x51, 0xBE, 0xFE, 0x7D, 0x0B, 0x1B, 0x48, 0x40, 0x25, 0x76,
        0x94, 0xDC, 0x41, 0xFB, 0xBF, 0x73, 0x76, 0xDA, 0xEB, 0xB3,
        0x62, 0xE7, 0xC1, 0xC8, 0x54, 0x6A, 0x93, 0xE1, 0x8D, 0x31,
        0xE8, 0x3E, 0x3E, 0xDF, 0xBC, 0x87, 0x02, 0x30, 0x22, 0x57,
        0xC4, 0xE0, 0x18, 0x7A, 0xD3, 0xAE, 0xE4, 0x02, 0x9B, 0xAA,
        0xBD, 0x4E, 0x49, 0x47, 0x72, 0xE9, 0x8D, 0x13, 0x2D, 0x54,
        0x9B, 0x00, 0xA7, 0x91, 0x61, 0x71, 0xC9, 0xCC, 0x48, 0x4F,
        0xEE, 0xDF, 0x5E, 0x1B, 0x1A, 0xDF, 0x67, 0xD3, 0x20, 0xE6,
        0x44, 0x45, 0x98, 0x7E, 0xE7, 0x0E, 0x63, 0x16, 0x83, 0xC9,
        0x26, 0x5D, 0x90, 0xC1, 0xE5, 0x2A, 0x5C, 0x45, 0x54, 0x13,
        0xB2, 0x81, 0x18, 0x06, 0x20, 0x2E, 0x2E, 0x66, 0x5A, 0xB5,
        0x7B, 0x6E, 0xD6, 0x0C, 0x4E, 0x89, 0x01, 0x56, 0x70, 0xBB,
        0xAE, 0xDE, 0xE9, 0x99, 0x5E, 0xD1, 0xB9, 0x3A, 0xB7, 0x6C,
        0x17, 0xB6, 0x03, 0xA9, 0x08, 0xDD, 0x9C, 0xF4, 0x14, 0xC9,
        0xC9, 0x59, 0x39, 0x72, 0xD4, 0x7E, 0x02, 0x37, 0x31, 0xCD,
        0x0E, 0xA7, 0x3D, 0xF8, 0xF2, 0xCF, 0x6B, 0x15, 0xAB, 0x02,
        0x03, 0x01, 0x00, 0x01, 0xA3, 0x82, 0x01, 0x4F, 0x30, 0x82,
        0x01, 0x4B, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D, 0x0E, 0x04,
        0x16, 0x04, 0x14, 0xFA, 0x54, 0x89, 0x67, 0xE5, 0x5F, 0xB7,
        0x31, 0x40, 0xEA, 0xFD, 0xE7, 0xF6, 0xA3, 0xC6, 0x5A, 0x56,
        0x16, 0xA5, 0x6E, 0x30, 0x81, 0xDE, 0x06, 0x03, 0x55, 0x1D,
        0x23, 0x04, 0x81, 0xD6, 0x30, 0x81, 0xD3, 0x80, 0x14, 0xFA,
        0x54, 0x89, 0x67, 0xE5, 0x5F, 0xB7, 0x31, 0x40, 0xEA, 0xFD,
        0xE7, 0xF6, 0xA3, 0xC6, 0x5A, 0x56, 0x16, 0xA5, 0x6E, 0xA1,
        0x81, 0xA4, 0xA4, 0x81, 0xA1, 0x30, 0x81, 0x9E, 0x31, 0x0B,
        0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55,
        0x53, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08,
        0x0C, 0x07, 0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31,
        0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07,
        0x42, 0x6F, 0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x15, 0x30,
        0x13, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x0C, 0x77, 0x6F,
        0x6C, 0x66, 0x53, 0x53, 0x4C, 0x5F, 0x34, 0x30, 0x39, 0x36,
        0x31, 0x19, 0x30, 0x17, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C,
        0x10, 0x50, 0x72, 0x6F, 0x67, 0x72, 0x61, 0x6D, 0x6D, 0x69,
        0x6E, 0x67, 0x2D, 0x34, 0x30, 0x39, 0x36, 0x31, 0x18, 0x30,
        0x16, 0x06, 0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77,
        0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E,
        0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A,
        0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10,
        0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73,
        0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x82, 0x14, 0x12, 0x66,
        0xC3, 0xA2, 0x08, 0x5C, 0xF7, 0xD0, 0x6E, 0xE9, 0xA8, 0x82,
        0xA2, 0xAB, 0x9C, 0x0F, 0x76, 0x9E, 0x96, 0xF4, 0x30, 0x0C,
        0x06, 0x03, 0x55, 0x1D, 0x13, 0x04, 0x05, 0x30, 0x03, 0x01,
        0x01, 0xFF, 0x30, 0x1C, 0x06, 0x03, 0x55, 0x1D, 0x11, 0x04,
        0x15, 0x30, 0x13, 0x82, 0x0B, 0x65, 0x78, 0x61, 0x6D, 0x70,
        0x6C, 0x65, 0x2E, 0x63, 0x6F, 0x6D, 0x87, 0x04, 0x7F, 0x00,
        0x00, 0x01, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D, 0x25, 0x04,
        0x16, 0x30, 0x14, 0x06, 0x08, 0x2B, 0x06, 0x01, 0x05, 0x05,
        0x07, 0x03, 0x01, 0x06, 0x08, 0x2B, 0x06, 0x01, 0x05, 0x05,
        0x07, 0x03, 0x02, 0x30, 0x0D, 0x06, 0x09, 0x2A, 0x86, 0x48,
        0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B, 0x05, 0x00, 0x03, 0x82,
        0x02, 0x01, 0x00, 0xB0, 0x00, 0x28, 0x7B, 0xC8, 0x3F, 0xAE,
        0x93, 0xF5, 0x16, 0x87, 0x30, 0xD6, 0x07, 0x2B, 0x71, 0x16,
        0x34, 0x1E, 0x5C, 0x48, 0x0F, 0x4A, 0xE7, 0x50, 0x07, 0x9D,
        0xF4, 0x75, 0x5B, 0x90, 0x53, 0x72, 0x87, 0x2A, 0xBB, 0xEF,
        0x04, 0xBC, 0x52, 0xD2, 0xBF, 0xFF, 0x27, 0x58, 0x2F, 0x5C,
        0xAF, 0xBE, 0xF3, 0xF6, 0x00, 0xA2, 0x37, 0x8B, 0xEC, 0x2C,
        0xD7, 0xB7, 0xE7, 0xBB, 0x3B, 0xCA, 0x6F, 0x9D, 0x42, 0xB7,
        0x00, 0xB8, 0xC2, 0xA2, 0x8E, 0x8E, 0xE4, 0x57, 0xFD, 0x83,
        0x4B, 0xB8, 0x47, 0xAA, 0xA1, 0x28, 0xAC, 0xBD, 0xC1, 0x59,
        0x04, 0x90, 0x17, 0x40, 0x40, 0x35, 0x04, 0xC6, 0x40, 0xA9,
        0x21, 0xD3, 0x79, 0x45, 0x0E, 0x22, 0xC8, 0x6F, 0xEC, 0xAE,
        0x58, 0xA5, 0xC2, 0xD8, 0x1B, 0x11, 0x49, 0x94, 0x58, 0xC2,
        0x11, 0x7D, 0xF8, 0x0A, 0xBB, 0x47, 0xFD, 0xAC, 0xCF, 0xF7,
        0x23, 0x05, 0x3F, 0xAB, 0x1D, 0x0E, 0x30, 0xC5, 0x98, 0x29,
        0x13, 0x1A, 0x90, 0x6F, 0xF9, 0x3F, 0xF2, 0xD6, 0xDF, 0x03,
        0xCC, 0xF1, 0x48, 0xE7, 0x71, 0xE6, 0xC4, 0xCE, 0xF3, 0xF9,
        0xBF, 0x07, 0xC9, 0xCF, 0xDD, 0x63, 0x0E, 0xFE, 0xBC, 0x93,
        0x1C, 0x9A, 0x52, 0x7D, 0x63, 0xF9, 0x6D, 0xA5, 0x50, 0xF3,
        0xEF, 0x54, 0xD7, 0xDA, 0x42, 0x74, 0x85, 0xB1, 0xB4, 0x7C,
        0xD5, 0x03, 0xCC, 0xB8, 0xC3, 0xBA, 0x1F, 0xB8, 0x4F, 0x5A,
        0xF9, 0x05, 0xBA, 0x4B, 0x0D, 0x57, 0x8D, 0x05, 0xCF, 0x4F,
        0xB7, 0xC4, 0x64, 0x2E, 0x2C, 0x10, 0xF3, 0xFA, 0x79, 0x0C,
        0x8C, 0x1F, 0xCC, 0x84, 0x33, 0x88, 0xFB, 0x77, 0xB5, 0x6E,
        0x45, 0x35, 0x15, 0xCC, 0x28, 0x80, 0x2B, 0x2D, 0x6B, 0x3F,
        0xD0, 0xA3, 0x10, 0xD1, 0x53, 0xC0, 0xBB, 0x70, 0x43, 0x79,
        0x2F, 0xFF, 0x3F, 0x63, 0x26, 0xC5, 0x60, 0x9B, 0x87, 0xE9,
        0xA2, 0x5B, 0x40, 0x13, 0x41, 0x25, 0xD2, 0x9C, 0x3E, 0x42,
        0x79, 0x00, 0xE1, 0x12, 0x0E, 0xAA, 0x06, 0xE0, 0x65, 0x59,
        0xA1, 0xFA, 0xDB, 0xC4, 0xC2, 0x97, 0xA8, 0x87, 0x35, 0x96,
        0x1C, 0x8E, 0xFF, 0xEB, 0x91, 0xE0, 0x8B, 0xE3, 0x3E, 0xC8,
        0xB2, 0x8C, 0xD3, 0x84, 0x5E, 0x76, 0x80, 0xD7, 0x29, 0x0A,
        0x59, 0xCC, 0x71, 0xD5, 0xE5, 0x65, 0x3C, 0x30, 0x38, 0x6E,
        0xF5, 0x3F, 0x7E, 0x28, 0x0F, 0x3D, 0x15, 0x10, 0x86, 0x30,
        0x39, 0x56, 0x23, 0x13, 0x30, 0xB4, 0x70, 0xF7, 0x7B, 0xC3,
        0x0D, 0x51, 0xAD, 0x18, 0xB1, 0x87, 0xB3, 0x3F, 0x1C, 0x69,
        0xF5, 0xD4, 0x1E, 0x72, 0x66, 0x5E, 0x44, 0xB9, 0x53, 0xBA,
        0x9E, 0xF0, 0xB8, 0x4A, 0xB1, 0x34, 0x50, 0x98, 0xD8, 0xF2,
        0xB9, 0xB2, 0xC5, 0xED, 0x73, 0xC9, 0xEE, 0xDD, 0x33, 0x8C,
        0xCF, 0x72, 0x35, 0xE0, 0x3D, 0x0F, 0x45, 0x2A, 0x89, 0xF9,
        0xA3, 0x76, 0x40, 0x07, 0x0F, 0xF6, 0x48, 0x6C, 0xF1, 0x8C,
        0x30, 0x3A, 0xC2, 0x51, 0x06, 0xC2, 0x51, 0x5E, 0x75, 0x98,
        0x06, 0xE0, 0x1E, 0x29, 0xF7, 0x12, 0x9A, 0x56, 0xA4, 0x38,
        0x83, 0xB1, 0x8B, 0x86, 0xB6, 0xAB, 0x87, 0xAA, 0x3C, 0x39,
        0x9D, 0x4D, 0x0C, 0xE8, 0x78, 0x9F, 0x52, 0x47, 0x66, 0x69,
        0xC8, 0x66, 0x0C, 0xFE, 0xD9, 0x74, 0x1D, 0x78, 0x0B, 0x51,
        0xE4, 0xD9, 0xC8, 0x35, 0x97, 0x95, 0xC7, 0x31, 0x97, 0x13,
        0x49, 0xED, 0xAA, 0x9E, 0x9C, 0xFD, 0x66, 0x04, 0x79, 0xD2,
        0x24, 0x4D, 0x64, 0x8D, 0x3F, 0xCD, 0x94, 0xB0, 0x05, 0x0A,
        0x30, 0x3B, 0x1C, 0x96, 0xE7, 0x79, 0x00, 0x03, 0x47, 0x55,
        0x34, 0x51, 0x1F, 0x46, 0x3A, 0x24, 0x47, 0xE6, 0xDD, 0x78,
        0x89, 0x18, 0x29, 0x32, 0xC5, 0xAD, 0xFB, 0x9C, 0xF7, 0x26,
        0xAC, 0x56, 0x3E, 0xF7, 0x73
};
static const int sizeof_client_cert_der_4096 = sizeof(client_cert_der_4096);

/* ./certs/dh4096.der, 4096-bit */
static const unsigned char dh_key_der_4096[] =
{
        0x30, 0x82, 0x02, 0x08, 0x02, 0x82, 0x02, 0x01, 0x00, 0xE9,
        0x0E, 0x3E, 0x79, 0x4F, 0xC9, 0xB2, 0xA0, 0xB1, 0xDB, 0x2F,
        0x1E, 0x24, 0x21, 0x90, 0x5C, 0x50, 0xA4, 0x34, 0xDB, 0x99,
        0x90, 0xAC, 0xF7, 0xBF, 0x2F, 0x01, 0x4B, 0xAC, 0x87, 0x70,
        0xBA, 0xEC, 0xD1, 0x64, 0xDE, 0x04, 0xCA, 0xFC, 0xF9, 0x51,
        0x69, 0x1E, 0xB7, 0x99, 0xE2, 0xB4, 0x0D, 0xDB, 0x5D, 0x78,
        0x38, 0x38, 0x41, 0x05, 0xE8, 0x67, 0x48, 0x65, 0x54, 0x71,
        0xCC, 0xC9, 0xAA, 0x95, 0x1E, 0xD4, 0xBF, 0xBC, 0xCA, 0x5D,
        0xC2, 0x9C, 0x9E, 0x7E, 0x5E, 0x94, 0x5B, 0x2F, 0x60, 0x72,
        0xED, 0xEB, 0x54, 0x0C, 0x48, 0x2B, 0x21, 0x74, 0x4D, 0x37,
        0x04, 0x5A, 0x2F, 0x8B, 0x24, 0x4A, 0xDB, 0xEE, 0xFA, 0xA9,
        0x94, 0x13, 0x8F, 0x52, 0x4A, 0x1B, 0xAE, 0xE6, 0xC8, 0x7F,
        0x99, 0x09, 0x23, 0x84, 0x89, 0xE9, 0xA6, 0x53, 0x82, 0xB6,
        0x03, 0x6D, 0x38, 0x5D, 0x2E, 0xEB, 0x0B, 0xF0, 0xE6, 0xAA,
        0xB1, 0x8B, 0x51, 0xFC, 0xD6, 0x13, 0xFB, 0x20, 0xCB, 0xDF,
        0x79, 0x97, 0xDB, 0x55, 0x74, 0xC2, 0x21, 0xE8, 0xDB, 0x8C,
        0x6A, 0x95, 0x2D, 0x51, 0x91, 0xA7, 0xA1, 0x3C, 0x9B, 0xEF,
        0xF5, 0x43, 0xAC, 0xA6, 0x69, 0xCE, 0x66, 0x5C, 0xD5, 0xB1,
        0xF8, 0xBA, 0xD4, 0x86, 0x25, 0x29, 0x2E, 0x0E, 0x23, 0x05,
        0xDA, 0x7C, 0x7C, 0xC2, 0x7B, 0xC8, 0xB5, 0x79, 0x84, 0x6D,
        0x68, 0x2D, 0x82, 0x4A, 0x35, 0x9F, 0xDC, 0x0E, 0x63, 0x2B,
        0x58, 0x5F, 0x34, 0x7E, 0xA8, 0x73, 0xCE, 0x44, 0x53, 0x11,
        0xE3, 0xDB, 0x46, 0xFA, 0x3A, 0xC3, 0xDA, 0x63, 0xA5, 0x65,
        0x56, 0x99, 0xA5, 0x91, 0x27, 0xD6, 0xE7, 0xDF, 0x2D, 0xEF,
        0xA0, 0x81, 0xB6, 0x07, 0x3A, 0xC6, 0xC1, 0x2B, 0xA1, 0x3A,
        0x74, 0xB4, 0xE9, 0xE1, 0x2F, 0x6B, 0x2B, 0xE4, 0xF0, 0x98,
        0xBE, 0x6F, 0xCB, 0xBB, 0xAE, 0x8D, 0xD2, 0x7E, 0x1B, 0x6F,
        0xBA, 0xF2, 0xB2, 0xB8, 0xB1, 0x5D, 0x9E, 0x79, 0x19, 0xF7,
        0x94, 0xB2, 0xC1, 0x17, 0x5E, 0x9B, 0xB3, 0x05, 0x67, 0x6D,
        0x5C, 0x62, 0x64, 0xA8, 0x2B, 0xB0, 0x36, 0x3D, 0xF9, 0x4C,
        0x65, 0x53, 0xEE, 0x2E, 0x55, 0x69, 0xCC, 0x1C, 0xF5, 0x96,
        0xDC, 0xBE, 0x60, 0x5E, 0x37, 0xEE, 0xD4, 0x63, 0x96, 0x51,
        0x97, 0x96, 0x14, 0x3C, 0x61, 0xBF, 0x53, 0xAA, 0x24, 0xB5,
        0x24, 0x5B, 0x26, 0x67, 0xAD, 0x02, 0x67, 0xB8, 0xD3, 0x05,
        0x6E, 0xA4, 0x8F, 0x46, 0x91, 0x9D, 0x84, 0xA6, 0x2C, 0x44,
        0x9F, 0x2D, 0x18, 0x2F, 0x73, 0xA5, 0xE5, 0xC4, 0xD9, 0x4F,
        0xD9, 0x9F, 0xF5, 0xC0, 0xC5, 0x48, 0xE8, 0x23, 0x32, 0xC4,
        0x4A, 0xCE, 0xFF, 0x3B, 0x16, 0x87, 0x85, 0xA5, 0x1F, 0x22,
        0xA8, 0x0B, 0x91, 0x97, 0x24, 0x95, 0x07, 0xC8, 0x73, 0xD2,
        0xB0, 0x01, 0xF8, 0x20, 0xA9, 0xAB, 0x6B, 0x71, 0x79, 0x24,
        0xF3, 0x79, 0xB5, 0x9B, 0x00, 0xF5, 0xF9, 0xAE, 0x23, 0xAC,
        0xEA, 0xE1, 0x48, 0x88, 0x28, 0x53, 0xE0, 0xC8, 0x76, 0x29,
        0xAE, 0x3E, 0x25, 0x9F, 0x1C, 0xC5, 0x8A, 0x86, 0x33, 0x02,
        0x21, 0xAB, 0xA5, 0x10, 0xF0, 0x07, 0x1B, 0x56, 0x8F, 0xCD,
        0xFC, 0x87, 0x9E, 0x2E, 0xD0, 0x44, 0x98, 0x44, 0x99, 0xB3,
        0xC2, 0x14, 0xCE, 0xD8, 0x93, 0xEA, 0xD1, 0x82, 0x3C, 0x1B,
        0x49, 0xE8, 0x6F, 0x04, 0xB2, 0xF5, 0xAF, 0x9B, 0x37, 0x7D,
        0xE5, 0xE0, 0x56, 0xE9, 0xEE, 0x00, 0x58, 0x25, 0x16, 0x23,
        0xC3, 0x8E, 0xF0, 0xB9, 0xE2, 0x98, 0x5D, 0xF2, 0x4F, 0x5C,
        0xC3, 0x27, 0x2A, 0x67, 0x7D, 0x43, 0xF6, 0x36, 0x76, 0xD4,
        0x2C, 0x7E, 0x16, 0x80, 0xCB, 0xF1, 0x07, 0xDC, 0xB9, 0xF5,
        0xF3, 0x56, 0xBD, 0xF0, 0xFC, 0x00, 0x78, 0x00, 0x56, 0xB4,
        0x3B, 0x02, 0x01, 0x02
};
static const int sizeof_dh_key_der_4096 = sizeof(dh_key_der_4096);

#endif /* USE_CERT_BUFFERS_4096 */

#if defined(HAVE_FALCON)

/* certs/falcon/bench_falcon_level1_key.der */
static const unsigned char bench_falcon_level1_key[] =
{
        0x30, 0x82, 0x08, 0x96, 0x02, 0x01, 0x00, 0x30, 0x07, 0x06,
        0x05, 0x2B, 0xCE, 0x0F, 0x03, 0x01, 0x04, 0x82, 0x08, 0x86,
        0x04, 0x82, 0x08, 0x82, 0x59, 0xFC, 0x32, 0x7D, 0x04, 0x41,
        0x01, 0xEC, 0x41, 0x7B, 0x04, 0x1F, 0x7F, 0xFC, 0x30, 0xBF,
        0x08, 0x80, 0x41, 0x10, 0x3F, 0x81, 0xFB, 0xFF, 0xC6, 0x07,
        0xCF, 0xC6, 0x0C, 0x11, 0x79, 0xFC, 0x2F, 0xC1, 0xFB, 0xFF,
        0x40, 0x1F, 0xA1, 0x3F, 0x0B, 0xF1, 0xC3, 0x0F, 0xB0, 0x43,
        0xFC, 0x61, 0x40, 0x0C, 0x1E, 0xFE, 0x08, 0x5E, 0xBE, 0x00,
        0x41, 0x3D, 0x13, 0xA0, 0xC0, 0xDB, 0xCF, 0x80, 0xF0, 0x20,
        0x80, 0x1C, 0x5F, 0x07, 0x07, 0xBF, 0xC3, 0x18, 0x5E, 0x85,
        0x0C, 0x1F, 0xC7, 0xD8, 0x0F, 0x86, 0x04, 0x10, 0xC0, 0xFC,
        0x32, 0x7E, 0xEF, 0xEF, 0x81, 0xF0, 0x2F, 0xC1, 0x04, 0x0E,
        0xC3, 0xF3, 0xD1, 0x47, 0xF7, 0xE0, 0xC0, 0xF8, 0x40, 0x42,
        0x13, 0xED, 0x82, 0xF0, 0x3E, 0xFB, 0xFF, 0xE0, 0x00, 0xFF,
        0xFF, 0xFA, 0x1F, 0xF1, 0x3D, 0x03, 0xCF, 0xBA, 0xFC, 0x90,
        0x43, 0x0B, 0x6F, 0xC7, 0xEF, 0xDF, 0x02, 0xFF, 0xCF, 0x41,
        0x03, 0xDF, 0xC2, 0x1B, 0xDE, 0xF9, 0x00, 0x90, 0x03, 0xFC,
        0x20, 0xBD, 0x04, 0x10, 0x38, 0xEF, 0xF1, 0x07, 0xE8, 0x51,
        0x07, 0xF7, 0x9F, 0xC2, 0x14, 0x00, 0x04, 0x04, 0x00, 0x00,
        0x04, 0x0F, 0x3F, 0xFF, 0xFF, 0x7D, 0xE0, 0x2F, 0x00, 0xF7,
        0xC1, 0x83, 0xFF, 0xBF, 0x87, 0x03, 0xA0, 0xC4, 0xEF, 0xEF,
        0xFF, 0xFC, 0x00, 0xFE, 0x20, 0x6F, 0xBE, 0x14, 0x9F, 0x01,
        0x17, 0xAE, 0xFE, 0x08, 0x3F, 0x7A, 0x17, 0xFF, 0x80, 0x14,
        0x3E, 0xC2, 0x04, 0x1F, 0xFB, 0x08, 0x4F, 0x41, 0xFC, 0x41,
        0xC2, 0x0C, 0x20, 0x82, 0x03, 0xC2, 0x43, 0x20, 0x20, 0xF8,
        0x07, 0xFF, 0x7E, 0xF8, 0x3E, 0x81, 0xEF, 0xDE, 0xC3, 0x00,
        0x4F, 0x85, 0x03, 0xD0, 0x7E, 0xF8, 0x2F, 0x42, 0xDF, 0xB1,
        0x3E, 0xFB, 0xF1, 0x45, 0x0B, 0xF1, 0x41, 0x1B, 0xF0, 0xC0,
        0x03, 0xEF, 0x03, 0xEB, 0x9E, 0xFE, 0xE8, 0x21, 0x42, 0x14,
        0x4E, 0xC4, 0xFC, 0x3E, 0x00, 0xF3, 0xFF, 0xBC, 0x18, 0x40,
        0x87, 0x08, 0x51, 0x7D, 0x03, 0xFF, 0x42, 0x18, 0x00, 0x43,
        0xF8, 0x6F, 0x7F, 0xFC, 0x21, 0x05, 0xFB, 0xF0, 0xFA, 0x00,
        0x2F, 0xC2, 0x04, 0x00, 0x43, 0x1B, 0xE1, 0x07, 0xF0, 0x50,
        0x05, 0x00, 0x11, 0x41, 0xF4, 0x4E, 0xC9, 0x0B, 0xDF, 0xBD,
        0x20, 0xB0, 0x7B, 0x04, 0xE0, 0xFF, 0xF3, 0xEF, 0x3F, 0x20,
        0x5F, 0xC8, 0xFF, 0xF0, 0xFE, 0xFB, 0x9F, 0x3C, 0xFB, 0xFF,
        0xC1, 0x13, 0x51, 0x05, 0x03, 0xFF, 0x03, 0xFB, 0xE1, 0xBE,
        0x1B, 0xC0, 0x8B, 0x03, 0xF1, 0x7F, 0x10, 0x6E, 0x39, 0x08,
        0x3F, 0xC9, 0xFC, 0x40, 0x82, 0xFB, 0xE0, 0xBC, 0xF0, 0x21,
        0x03, 0xE4, 0x6F, 0x7E, 0xD8, 0x00, 0x81, 0xF8, 0x41, 0x46,
        0x07, 0xC0, 0x45, 0x24, 0x2F, 0x80, 0x0C, 0x3F, 0x05, 0x20,
        0x8F, 0x84, 0xF8, 0x6F, 0xFF, 0xEC, 0x30, 0x85, 0xFF, 0xF0,
        0xBD, 0xF4, 0x50, 0x87, 0x00, 0x3F, 0xC2, 0xFB, 0xC0, 0xC1,
        0x03, 0xEF, 0xB8, 0xFF, 0xFF, 0x45, 0x13, 0xBE, 0xC1, 0x03,
        0xC0, 0x40, 0x07, 0x90, 0x3E, 0xE0, 0x0E, 0xFD, 0xFB, 0xA0,
        0xFF, 0xF8, 0x0F, 0xC5, 0xFF, 0xFF, 0x43, 0xE3, 0xFF, 0x06,
        0x0F, 0xB0, 0x43, 0xF0, 0x90, 0x7B, 0xE7, 0xD1, 0x79, 0x07,
        0xDF, 0xFB, 0x03, 0xE0, 0x04, 0xF7, 0xD0, 0x45, 0xFF, 0xEF,
        0xBB, 0x20, 0x01, 0x42, 0xD0, 0x7E, 0xC3, 0x13, 0xEF, 0xC8,
        0x0B, 0x8E, 0xBE, 0xF8, 0x1F, 0xFE, 0xDF, 0xCF, 0xBE, 0x13,
        0xD1, 0xFC, 0x0C, 0x61, 0xC2, 0x04, 0x40, 0x01, 0x00, 0x2F,
        0xFC, 0x0B, 0xDF, 0x00, 0x14, 0x61, 0x02, 0xE4, 0x0F, 0x81,
        0xF0, 0x3F, 0x81, 0xFC, 0x00, 0x82, 0xF4, 0x21, 0x7E, 0x0B,
        0xF0, 0x48, 0x00, 0x00, 0x3A, 0xE8, 0x5F, 0x05, 0x17, 0xF0,
        0xC0, 0x00, 0x61, 0x39, 0xF8, 0x4F, 0x81, 0xEB, 0x5F, 0x06,
        0x08, 0x2F, 0xC0, 0x04, 0x1F, 0xFF, 0x0B, 0xBF, 0xBA, 0x0B,
        0xFF, 0x40, 0x07, 0xCF, 0x42, 0x10, 0x1F, 0x3D, 0xDB, 0xC0,
        0x85, 0x03, 0xD1, 0xC0, 0x04, 0x0F, 0x7E, 0x03, 0xBF, 0x81,
        0xF7, 0xD0, 0x7D, 0xE8, 0x1E, 0x00, 0x00, 0x31, 0x46, 0x04,
        0x21, 0x40, 0xF3, 0xBF, 0x05, 0x03, 0xD1, 0x40, 0xFB, 0xEE,
        0xFD, 0x03, 0xF0, 0xBA, 0xE7, 0xFE, 0xBD, 0xE8, 0x6F, 0xBC,
        0x07, 0xEF, 0x82, 0x0C, 0x40, 0x3F, 0x00, 0x20, 0x80, 0x0F,
        0xB1, 0x42, 0xFC, 0x10, 0x3F, 0x07, 0xEF, 0xBF, 0xF0, 0x1E,
        0x82, 0xFC, 0x3F, 0x80, 0x1C, 0x50, 0xC3, 0x13, 0xD0, 0xFC,
        0xEF, 0xEF, 0x7C, 0x0C, 0x6F, 0xBF, 0x1B, 0x90, 0xFF, 0x00,
        0x70, 0xC5, 0x04, 0x00, 0x03, 0xEF, 0x8F, 0x80, 0xF8, 0x00,
        0x7F, 0x04, 0x01, 0x41, 0xE8, 0x21, 0x43, 0x08, 0x9F, 0x44,
        0xF7, 0xF2, 0x7C, 0x0F, 0xC0, 0x3E, 0xEC, 0x4F, 0xFF, 0x00,
        0x31, 0x3E, 0x13, 0xEE, 0x40, 0xFB, 0xE0, 0xFA, 0xEB, 0xD0,
        0xBF, 0x04, 0x0F, 0x46, 0xFF, 0xE2, 0x7D, 0x03, 0xDE, 0x81,
        0xF4, 0x2F, 0xBD, 0x08, 0x0F, 0x42, 0xEF, 0xA0, 0x40, 0xF8,
        0x01, 0x41, 0xF3, 0xD1, 0x00, 0x00, 0x01, 0x00, 0x13, 0xB0,
        0x7E, 0xE7, 0xE0, 0x7F, 0x08, 0x2F, 0x3E, 0x03, 0xD2, 0xC3,
        0x23, 0x9F, 0xFF, 0x17, 0xEE, 0xBB, 0xF0, 0x2E, 0xBE, 0xFC,
        0x2F, 0x3F, 0xF4, 0x00, 0x48, 0xE3, 0xDE, 0x7B, 0x0B, 0xFF,
        0xC4, 0x04, 0x01, 0x02, 0xFF, 0xB1, 0x84, 0x00, 0x7E, 0xC5,
        0x08, 0x11, 0xBE, 0xF7, 0x90, 0xC5, 0xEF, 0xDE, 0x09, 0x0B,
        0xD0, 0x86, 0xEC, 0x4F, 0xFC, 0x0B, 0xA1, 0x06, 0xEC, 0x50,
        0xC3, 0xFF, 0xA1, 0x3D, 0x20, 0x4F, 0xFF, 0x13, 0x8F, 0x45,
        0xF3, 0xF0, 0x83, 0x04, 0x06, 0x09, 0xE4, 0xEB, 0xFA, 0xFD,
        0xD6, 0x04, 0x2A, 0x25, 0x0F, 0xF3, 0xE6, 0x10, 0xFD, 0xEB,
        0x0C, 0x0B, 0x0C, 0xF3, 0x1F, 0x06, 0x21, 0xFF, 0x2E, 0xDD,
        0xFA, 0xF3, 0xF1, 0x21, 0xEE, 0x1B, 0x06, 0xC6, 0x05, 0x21,
        0xF7, 0x07, 0xE1, 0x01, 0x19, 0x0D, 0xEE, 0xE2, 0xD2, 0x0F,
        0xF9, 0xEE, 0xEC, 0x13, 0xF3, 0xE5, 0x1C, 0xF6, 0xF4, 0xE5,
        0x09, 0xF0, 0x32, 0x09, 0x0D, 0xD4, 0x17, 0x1F, 0xE4, 0xDA,
        0xF7, 0x08, 0xD9, 0xD6, 0x01, 0xED, 0x07, 0xDD, 0xE4, 0x1A,
        0x09, 0x34, 0xE0, 0xFE, 0x0D, 0xD4, 0xF8, 0x03, 0xF8, 0xF6,
        0xFB, 0x1C, 0xDF, 0x11, 0x1C, 0x08, 0x13, 0x0F, 0xE8, 0xF5,
        0xFA, 0x19, 0x06, 0xEF, 0xFC, 0x09, 0x1B, 0x22, 0xED, 0x02,
        0xE6, 0x09, 0x04, 0xD8, 0xF5, 0x10, 0x02, 0x24, 0x15, 0x26,
        0xEA, 0xFA, 0x1D, 0x15, 0x09, 0xFD, 0xD1, 0x02, 0x0D, 0xF5,
        0xF7, 0xF4, 0x1E, 0x40, 0x0E, 0xFD, 0xEE, 0x19, 0x15, 0xF2,
        0xE6, 0xE5, 0xF0, 0xFC, 0x03, 0x07, 0x03, 0xFE, 0xFC, 0x03,
        0x09, 0xE8, 0xEF, 0xF9, 0xEF, 0xED, 0x0A, 0x07, 0x0A, 0x02,
        0x08, 0xD0, 0xEE, 0x1D, 0xD3, 0xF0, 0xB2, 0xEA, 0x07, 0xFA,
        0x39, 0x13, 0x18, 0xF4, 0xEA, 0xF5, 0x0A, 0xE6, 0xE1, 0x1E,
        0x0B, 0x14, 0xF3, 0x26, 0xDA, 0xEA, 0x18, 0xE6, 0x10, 0x06,
        0xFF, 0xE9, 0x08, 0xE9, 0x10, 0xD0, 0xF7, 0x08, 0xEE, 0xEF,
        0x05, 0xEB, 0xF5, 0x10, 0xD5, 0x12, 0x1F, 0xDF, 0xFF, 0xF5,
        0x33, 0x05, 0x31, 0xFD, 0x19, 0xF8, 0xEE, 0x2B, 0x08, 0x01,
        0x17, 0x0A, 0xF6, 0x21, 0xF7, 0x11, 0x00, 0x23, 0xFF, 0x03,
        0xD7, 0xEF, 0x16, 0xF5, 0xED, 0x0A, 0xF7, 0x00, 0x00, 0xDD,
        0xF8, 0x07, 0x0A, 0xF0, 0xFB, 0xF1, 0xCC, 0x11, 0x1F, 0xFC,
        0xE1, 0x03, 0x46, 0x01, 0x06, 0x0C, 0xE7, 0x36, 0x05, 0xDC,
        0xD1, 0x0A, 0x43, 0x09, 0x04, 0xF6, 0xFE, 0x09, 0x1F, 0x06,
        0xEB, 0xE7, 0x08, 0xFD, 0xDA, 0x0D, 0x23, 0xC6, 0x13, 0x30,
        0x02, 0x23, 0x03, 0xED, 0xE1, 0x0B, 0x12, 0x27, 0x1E, 0x1B,
        0x01, 0xF5, 0xF5, 0xF4, 0xED, 0xDC, 0xF7, 0x14, 0xF1, 0x3E,
        0x2F, 0x18, 0x04, 0x22, 0x08, 0x03, 0x1F, 0x0E, 0x19, 0xEB,
        0x21, 0xDC, 0xCE, 0xD2, 0x23, 0x14, 0xE3, 0x16, 0xF7, 0x29,
        0x0A, 0x0F, 0x04, 0xF7, 0xEF, 0x15, 0xFA, 0x30, 0xE7, 0xD3,
        0x13, 0x05, 0x0E, 0x06, 0x04, 0x11, 0xF9, 0x2D, 0x1C, 0xEF,
        0x0F, 0x03, 0x05, 0xF4, 0x09, 0xE7, 0xE3, 0xFC, 0x04, 0xED,
        0x15, 0xF2, 0x2F, 0xF1, 0xF9, 0x1D, 0x08, 0x46, 0xED, 0xF1,
        0x0C, 0xF9, 0x38, 0xEF, 0xE6, 0xEB, 0xD1, 0xDE, 0x04, 0xFD,
        0x1B, 0xFB, 0x30, 0xE5, 0xCD, 0xF6, 0xDE, 0xEA, 0x09, 0x00,
        0xE8, 0x0C, 0x27, 0x01, 0xC1, 0x2A, 0x05, 0xF4, 0xED, 0x11,
        0xF9, 0xF7, 0xFD, 0x0A, 0xD6, 0xF8, 0x03, 0x0C, 0x05, 0x16,
        0xFB, 0x1F, 0xEC, 0x13, 0x07, 0xF3, 0x04, 0xC6, 0x1E, 0x0A,
        0x01, 0x0A, 0x09, 0xDE, 0x0D, 0xDC, 0xDF, 0xD8, 0x0E, 0x25,
        0xDF, 0xC3, 0xF4, 0x0C, 0xE9, 0xE5, 0xEC, 0xE9, 0xEE, 0xEC,
        0x23, 0xF2, 0xEE, 0x17, 0xF7, 0x1D, 0xE4, 0x0D, 0x0E, 0x0E,
        0x0A, 0xF5, 0xFF, 0x0B, 0x0F, 0xF3, 0xF1, 0x18, 0x08, 0xEB,
        0x03, 0x01, 0x1E, 0xF7, 0xF2, 0x0E, 0xDF, 0xF8, 0xD3, 0xCB,
        0xFB, 0xFE, 0xEF, 0xED, 0x17, 0x24, 0xD9, 0x06, 0xF5, 0xDD,
        0xFF, 0xEF, 0x1E, 0xE7, 0xC6, 0xFB, 0x03, 0x14, 0x12, 0x00,
        0x1E, 0x2B, 0x2B, 0xEB, 0x0E, 0x2D, 0xE9, 0x17, 0x13, 0x05,
        0x2F, 0x23, 0xF5, 0xF6, 0xEE, 0xF1, 0xD3, 0xE3, 0xFC, 0xEC,
        0xE4, 0xFE, 0xEA, 0xFC, 0x05, 0x2E, 0x0E, 0xF6, 0x0F, 0xE5,
        0xEF, 0x08, 0xEF, 0x01, 0xE1, 0x09, 0x00, 0x81, 0xF2, 0x10,
        0x17, 0x97, 0x32, 0xA5, 0x89, 0x54, 0x64, 0xC7, 0x47, 0x1C,
        0x52, 0xBC, 0xBF, 0xF6, 0x8A, 0x47, 0x0A, 0x87, 0x1E, 0x61,
        0x92, 0xEF, 0x56, 0x48, 0x88, 0x88, 0xAF, 0x14, 0x0B, 0x88,
        0xBD, 0x82, 0xB8, 0x32, 0x62, 0xF4, 0xD7, 0x40, 0xAD, 0xA6,
        0xE9, 0x1A, 0x03, 0x44, 0x60, 0x71, 0x58, 0xC3, 0x76, 0x2A,
        0x8F, 0x5B, 0x9F, 0x24, 0x8A, 0x92, 0x4E, 0xC4, 0x99, 0x3D,
        0x39, 0x8B, 0x25, 0xEB, 0xDA, 0x39, 0x91, 0x21, 0x70, 0x2B,
        0x77, 0x63, 0x1C, 0xA9, 0xD6, 0xD5, 0x30, 0x26, 0x10, 0xCF,
        0x1D, 0xD8, 0x32, 0xB2, 0xE8, 0x5C, 0x88, 0x04, 0x11, 0x9E,
        0x7A, 0xB4, 0x6E, 0xF2, 0x4D, 0x78, 0x3C, 0xF1, 0xA7, 0xA5,
        0xF3, 0x98, 0xAA, 0x8A, 0x08, 0xED, 0x17, 0xCA, 0xA1, 0x9E,
        0x6B, 0xC6, 0xEB, 0x4D, 0x47, 0x7B, 0x04, 0x41, 0x1B, 0x19,
        0x61, 0x19, 0x0F, 0xB5, 0xAF, 0x02, 0xE8, 0x8A, 0x43, 0xB8,
        0xF9, 0xA6, 0x43, 0x5A, 0xEA, 0x1B, 0x94, 0x8A, 0xD2, 0x71,
        0x2B, 0x2F, 0x6A, 0xBF, 0x26, 0x5E, 0x84, 0xAC, 0x4F, 0x03,
        0x40, 0x0C, 0x68, 0xA4, 0x36, 0xE3, 0xFD, 0x0B, 0xD0, 0x31,
        0xD9, 0x58, 0xD3, 0xC2, 0x76, 0xA2, 0x25, 0x69, 0x59, 0x00,
        0xA8, 0x43, 0x12, 0x85, 0x69, 0x16, 0x24, 0x50, 0x15, 0x74,
        0x1B, 0x02, 0xB9, 0xC0, 0x7B, 0x81, 0x64, 0x7E, 0x63, 0xB7,
        0x16, 0x68, 0x49, 0x92, 0x60, 0x7B, 0xC0, 0xC4, 0xFB, 0x9E,
        0x7E, 0x23, 0x87, 0x81, 0x48, 0xD4, 0x5F, 0xAD, 0xDA, 0xA7,
        0xA3, 0x5C, 0x6C, 0x4E, 0x60, 0x43, 0xB4, 0x48, 0x5B, 0xD1,
        0x3C, 0xE4, 0x5C, 0x5A, 0x8A, 0x85, 0xBC, 0x76, 0xEC, 0xA5,
        0x40, 0x1E, 0x02, 0xFE, 0x06, 0x8D, 0x60, 0xD6, 0x37, 0x82,
        0x47, 0x22, 0xA5, 0x18, 0xA6, 0x62, 0x44, 0x08, 0x0D, 0xE2,
        0x46, 0xA6, 0x6C, 0xCB, 0x8D, 0x9B, 0xD5, 0xFA, 0x31, 0x4C,
        0xCF, 0x74, 0x6D, 0xAA, 0x93, 0x21, 0xEB, 0xA4, 0xC9, 0xB2,
        0x95, 0xFE, 0x42, 0x28, 0xE7, 0x1A, 0x3A, 0xB9, 0xA8, 0x9B,
        0x6D, 0x59, 0xFC, 0x38, 0xA6, 0xC9, 0xC6, 0x5D, 0xEC, 0xF2,
        0x1D, 0xF0, 0x64, 0x86, 0x63, 0x19, 0x93, 0x57, 0x71, 0xCF,
        0x85, 0xA5, 0x27, 0x43, 0xB3, 0x81, 0xF2, 0x14, 0x17, 0x05,
        0x2E, 0x26, 0xE2, 0x4C, 0xF3, 0xCB, 0x42, 0xC7, 0x23, 0x91,
        0xF2, 0x51, 0x54, 0xDB, 0x95, 0x6B, 0x26, 0x3F, 0xF0, 0xF3,
        0x29, 0xDE, 0x64, 0x59, 0x10, 0x5A, 0x70, 0x81, 0x96, 0x0D,
        0xD6, 0xBD, 0x9B, 0xBA, 0x52, 0xE4, 0x47, 0xB4, 0xC5, 0x81,
        0x4F, 0xA3, 0x1C, 0x53, 0x29, 0xBE, 0xF0, 0x0A, 0x19, 0xA8,
        0x79, 0x5A, 0x08, 0x17, 0x9B, 0x1C, 0x54, 0x0C, 0xDD, 0x9F,
        0xB0, 0xFE, 0xA2, 0xBD, 0x7E, 0xD1, 0x44, 0xFA, 0x6A, 0x19,
        0x15, 0x9B, 0xCA, 0x77, 0x24, 0x8A, 0xC4, 0x6F, 0x02, 0xBC,
        0x2C, 0x07, 0x1C, 0x6A, 0x69, 0x89, 0x9D, 0x84, 0xE9, 0x14,
        0xE1, 0x8E, 0x49, 0xA0, 0x1D, 0x76, 0x9F, 0x54, 0x49, 0x5E,
        0x42, 0x90, 0x1A, 0xE4, 0x2B, 0xF0, 0x36, 0xF6, 0xB7, 0x38,
        0x9A, 0xD3, 0xBB, 0x08, 0xFA, 0x96, 0xC9, 0xE8, 0xEF, 0x7E,
        0x79, 0xE9, 0xDE, 0xA5, 0xA6, 0x97, 0x15, 0x9E, 0xEA, 0xB4,
        0x8B, 0xC8, 0xA1, 0xB7, 0x50, 0x78, 0x04, 0x00, 0xFC, 0x13,
        0xF0, 0x96, 0xCC, 0x45, 0xC8, 0x74, 0x2B, 0xCA, 0x98, 0x7E,
        0x6C, 0x91, 0x22, 0x0B, 0x49, 0x07, 0xB6, 0xE0, 0x44, 0xD8,
        0x44, 0x4A, 0x0E, 0x65, 0x3F, 0x45, 0x90, 0x95, 0x59, 0x72,
        0xCD, 0xB3, 0xE7, 0x56, 0x65, 0x23, 0xB0, 0x9D, 0x13, 0x66,
        0x13, 0xCC, 0xB9, 0x96, 0xB9, 0xAD, 0x2B, 0x15, 0x48, 0xB3,
        0x84, 0xC2, 0x9D, 0xC3, 0xC8, 0x8C, 0x41, 0x32, 0xBE, 0x1A,
        0x98, 0xAE, 0xD8, 0x66, 0x5B, 0xA6, 0x4A, 0xA8, 0x75, 0xCC,
        0x58, 0x63, 0x34, 0xD6, 0x23, 0xDF, 0xA3, 0x72, 0x07, 0xA2,
        0x7B, 0xD5, 0x81, 0x14, 0xDB, 0x56, 0xF4, 0x07, 0x22, 0x0E,
        0x2C, 0x03, 0x9A, 0xB8, 0x48, 0x58, 0xB2, 0xE8, 0x21, 0x55,
        0x14, 0x2A, 0xA3, 0x45, 0x89, 0xCD, 0x29, 0x76, 0x3F, 0x0A,
        0x54, 0x60, 0x06, 0x94, 0x82, 0x53, 0x49, 0x5C, 0x92, 0x2E,
        0x0E, 0x7C, 0x10, 0x97, 0x35, 0x25, 0x22, 0xA7, 0x28, 0x24,
        0x30, 0x68, 0x2F, 0x46, 0x78, 0xD6, 0x55, 0xCA, 0xE3, 0x88,
        0x56, 0x94, 0x78, 0x4D, 0x66, 0x26, 0xD7, 0x9F, 0x7A, 0x36,
        0x23, 0x77, 0x35, 0xB0, 0x00, 0x25, 0xB0, 0x41, 0x67, 0x82,
        0xCC, 0x61, 0x3D, 0x26, 0x46, 0xC7, 0xB6, 0x95, 0xA5, 0x1F,
        0x49, 0x13, 0xB9, 0x7A, 0x91, 0x82, 0x99, 0x06, 0xAE, 0x01,
        0x45, 0xAE, 0x53, 0x1B, 0xE6, 0x51, 0x34, 0x96, 0x81, 0x62,
        0x06, 0xA9, 0x0E, 0x46, 0xB8, 0x18, 0x1E, 0xAF, 0x24, 0x54,
        0x89, 0x80, 0xE3, 0xD2, 0x11, 0x8D, 0xB7, 0x80, 0x31, 0x21,
        0xEF, 0x81, 0x9D, 0x22, 0x08, 0xE5, 0x0E, 0x3E, 0x9A, 0x8C,
        0xA4, 0xBA, 0xAB, 0x1A, 0xC9, 0x75, 0xD1, 0x6B, 0x16, 0x37,
        0x5B, 0x36, 0x12, 0x34, 0x06, 0xA6, 0xD7, 0x4B, 0x82, 0x35,
        0xC9, 0x64, 0xC3, 0x66, 0x4E, 0x66, 0x0F, 0x62, 0xA5, 0x2B,
        0x71, 0x8A, 0x23, 0x0A, 0xD1, 0x06, 0x94, 0xE1, 0x44, 0x8E,
        0x59, 0x41, 0x41, 0x09, 0xC9, 0x4D, 0xB7, 0x1D, 0xEE, 0x2E,
        0xB7, 0x35, 0x01, 0x89, 0x6B, 0xF4, 0xE2, 0xB1, 0xE7, 0x94,
        0xD3, 0x30, 0x34, 0xB8, 0x36, 0xA4, 0x59, 0xBE, 0x0E, 0xC9,
        0x49, 0x34, 0x70, 0x55, 0xDB, 0x87, 0x46, 0x2F, 0x68, 0xED,
        0x59, 0xB3, 0x7F, 0x2E, 0x0B, 0x8B, 0x90, 0x1F, 0x51, 0x3D,
        0x3E, 0xC2, 0xB5, 0x13, 0xCE, 0xAE, 0x6C, 0x56, 0x0D, 0x4A,
        0xAB, 0x99, 0x8B, 0x89, 0xEC, 0xBE, 0xCA, 0x02, 0x47, 0x6C,
        0xA8, 0x31, 0xEC, 0x98, 0xAA, 0xC1, 0xE1, 0x36, 0x22, 0x2E,
        0x81, 0xB0, 0xA2, 0xD2, 0x27, 0x49, 0xCF, 0x60, 0x9E, 0x8C,
        0xC3, 0xA0, 0x6E, 0x98, 0x8A, 0xF7, 0x2C, 0x36, 0x3B, 0x7E,
        0x25, 0x90, 0xD6, 0xC2, 0x70, 0x0C, 0xF5, 0xEC, 0x07, 0xA5,
        0xAE, 0x55, 0x64, 0x04, 0x03, 0xC3, 0x80, 0x55, 0x3C, 0x37,
        0x3E, 0xD1, 0x83, 0x7C, 0x2F, 0x11, 0x4C, 0xDE, 0xB8, 0x44,
        0xCA, 0x0F, 0xBF, 0x7B, 0x82, 0x2B, 0xB5, 0xD3, 0x10, 0x66,
        0x86, 0x8E, 0x39, 0xF6, 0x33, 0x9D, 0x0D, 0x70, 0xB0, 0x02,
        0x50, 0xC5, 0x0F, 0x27, 0x35, 0x2C, 0xEF, 0x53, 0x4E, 0x13,
        0x58, 0xB8, 0xA8, 0xC0, 0x4A, 0x95, 0x61, 0xFB, 0x48, 0x05,
        0xCB, 0xC9, 0x40, 0xD2, 0x3A, 0xBE, 0xA9, 0xB7, 0x78, 0x76,
        0x9F, 0x1E
};
static const int sizeof_bench_falcon_level1_key = sizeof(bench_falcon_level1_key);

/* certs/falcon/bench_falcon_level5_key.der */
static const unsigned char bench_falcon_level5_key[] =
{
        0x30, 0x82, 0x10, 0x16, 0x02, 0x01, 0x00, 0x30, 0x07, 0x06,
        0x05, 0x2B, 0xCE, 0x0F, 0x03, 0x04, 0x04, 0x82, 0x10, 0x06,
        0x04, 0x82, 0x10, 0x02, 0x5A, 0xDF, 0x13, 0xB0, 0x70, 0x81,
        0x21, 0x02, 0x3D, 0x7F, 0xFF, 0xD0, 0x47, 0xFE, 0x0F, 0xC2,
        0xE6, 0xC5, 0xFF, 0x9F, 0xDB, 0xF0, 0x0D, 0xE0, 0xFC, 0x43,
        0x07, 0xFE, 0x01, 0x80, 0x20, 0x00, 0xC3, 0xD0, 0x74, 0x3F,
        0x07, 0x41, 0xFF, 0xF7, 0x81, 0xEF, 0xFF, 0xF1, 0x10, 0x04,
        0x10, 0x4C, 0x32, 0x7C, 0x42, 0x30, 0x08, 0x42, 0x70, 0x45,
        0xFF, 0x3F, 0xCF, 0x07, 0x60, 0x00, 0x82, 0x00, 0x84, 0x01,
        0x20, 0x04, 0x00, 0x7B, 0xC0, 0x18, 0x42, 0x11, 0x00, 0x22,
        0x17, 0x46, 0x00, 0x70, 0x43, 0xDF, 0x82, 0x10, 0x0B, 0xDA,
        0x17, 0x01, 0xE0, 0x0B, 0xFF, 0xF7, 0xBD, 0xE0, 0x7C, 0x1E,
        0xF7, 0x7E, 0x40, 0x78, 0x1E, 0x17, 0xC5, 0xD0, 0x18, 0x3D,
        0x19, 0x3E, 0x31, 0x68, 0x3F, 0xF8, 0x87, 0xCF, 0xF8, 0x9F,
        0x37, 0x7F, 0xE0, 0x7B, 0xC0, 0xF1, 0x40, 0x1F, 0x04, 0x5F,
        0x08, 0x42, 0x2E, 0x03, 0xA5, 0xD8, 0x06, 0x52, 0xF0, 0x24,
        0x00, 0x01, 0xEF, 0xE8, 0x42, 0xE9, 0xBD, 0xF2, 0x9B, 0xBA,
        0x16, 0xFD, 0xCF, 0x83, 0x81, 0x08, 0x3F, 0xFE, 0x88, 0x40,
        0x01, 0xB0, 0x12, 0x8B, 0xA1, 0x10, 0xBD, 0xF1, 0x7C, 0x1F,
        0x18, 0x47, 0xC0, 0x13, 0xC1, 0xF7, 0x84, 0x00, 0x03, 0x5F,
        0xF8, 0x46, 0x21, 0xF8, 0x1D, 0x17, 0x80, 0x0F, 0x8B, 0xC0,
        0x0E, 0xC3, 0xEE, 0x87, 0xBE, 0xF0, 0xF4, 0x01, 0xEC, 0x1D,
        0xF7, 0xC6, 0x11, 0xF8, 0x1B, 0x18, 0x7F, 0xCE, 0xFF, 0xBA,
        0x10, 0x46, 0x3E, 0xF3, 0xFE, 0x31, 0x3E, 0x40, 0xFB, 0x80,
        0x18, 0x43, 0xD0, 0x04, 0xFE, 0x00, 0x70, 0x0E, 0x08, 0x3F,
        0xF7, 0xFE, 0x0F, 0x70, 0x22, 0xE7, 0x86, 0x20, 0x94, 0x3F,
        0x10, 0xF6, 0x4F, 0x80, 0x00, 0x17, 0x86, 0x3F, 0x6F, 0x41,
        0xE8, 0x44, 0x0F, 0x8B, 0xE3, 0xF0, 0x82, 0x2E, 0xEC, 0x00,
        0x20, 0xFE, 0x00, 0x78, 0x1B, 0x27, 0xF8, 0x10, 0x8F, 0xBD,
        0x17, 0x78, 0x5F, 0xE0, 0x04, 0xF8, 0xC2, 0x0F, 0xFB, 0xFE,
        0xF9, 0xF5, 0xF0, 0x8F, 0xC2, 0x01, 0x3D, 0xD1, 0x77, 0x61,
        0x10, 0xC4, 0x41, 0x04, 0x9F, 0x0E, 0xC9, 0x9F, 0xFF, 0xC3,
        0xD8, 0x7A, 0x00, 0x07, 0x61, 0xF0, 0x04, 0x0F, 0x6F, 0x61,
        0xE7, 0x78, 0x1E, 0xFF, 0xDD, 0x31, 0x02, 0x00, 0x84, 0x40,
        0xFF, 0x42, 0x00, 0xF8, 0x02, 0x10, 0xFE, 0x71, 0x7C, 0x41,
        0x37, 0xC2, 0x22, 0x83, 0x9B, 0xEF, 0xFD, 0xFF, 0x78, 0x5E,
        0xF8, 0x05, 0xD0, 0x73, 0xE3, 0xFF, 0x3E, 0x5E, 0xFC, 0x21,
        0x1F, 0xBC, 0x00, 0x07, 0xC2, 0xF0, 0xC2, 0x3E, 0x07, 0xE0,
        0x08, 0x02, 0x10, 0xFC, 0x63, 0xF0, 0x05, 0xFF, 0xEF, 0x7D,
        0x07, 0x86, 0x02, 0x07, 0xE5, 0x08, 0x46, 0x42, 0x0B, 0xA1,
        0xE7, 0xFD, 0xEF, 0x6C, 0x42, 0xF0, 0x04, 0x00, 0x84, 0x60,
        0xF9, 0x84, 0x1F, 0x0F, 0x98, 0xD8, 0xBF, 0xEF, 0x77, 0xC2,
        0x08, 0x41, 0xF1, 0xFB, 0xE0, 0x17, 0x7B, 0xED, 0x78, 0x5C,
        0x10, 0x42, 0x2F, 0x83, 0xDE, 0x50, 0xB6, 0x22, 0x07, 0xE3,
        0x10, 0x45, 0xE0, 0x7F, 0xA1, 0xE9, 0x00, 0x11, 0x7B, 0xE1,
        0xD8, 0xC4, 0x4E, 0xFF, 0xFF, 0x17, 0xFC, 0x31, 0x1B, 0xA2,
        0xF8, 0x41, 0xDF, 0x10, 0x3F, 0xE8, 0x3F, 0xFF, 0x87, 0xC2,
        0x1F, 0x44, 0x20, 0x83, 0xBF, 0x17, 0x02, 0x20, 0x04, 0x3B,
        0xF8, 0x01, 0xC0, 0x83, 0xE2, 0x30, 0x40, 0x5F, 0x80, 0x23,
        0x07, 0x83, 0xC0, 0xF0, 0x60, 0x00, 0xC4, 0x10, 0x08, 0x9F,
        0xE8, 0xBA, 0x2F, 0xFF, 0xE3, 0x0E, 0x41, 0xA3, 0x7C, 0x62,
        0x17, 0x7C, 0x0F, 0xFB, 0xC0, 0x07, 0x7D, 0xD2, 0xEC, 0x82,
        0xFF, 0x49, 0xB0, 0x74, 0x9F, 0x07, 0xFC, 0x5F, 0x14, 0x7F,
        0x17, 0x3C, 0x42, 0x08, 0x5C, 0xD9, 0x36, 0x50, 0xFB, 0xC1,
        0x09, 0x80, 0x41, 0x93, 0xE6, 0xF8, 0xBC, 0x11, 0x0C, 0x05,
        0x0E, 0x83, 0xBF, 0xFB, 0x9C, 0xF8, 0x3E, 0x20, 0x7C, 0x3F,
        0x09, 0x3E, 0x01, 0x8B, 0xA1, 0xFF, 0xFC, 0x3F, 0x00, 0x24,
        0x17, 0x82, 0x23, 0x7C, 0x3D, 0xD7, 0x76, 0x1F, 0x0C, 0x3E,
        0x1F, 0xFE, 0x22, 0xFF, 0xFE, 0xE0, 0x82, 0x00, 0x97, 0xC3,
        0xE0, 0x45, 0xD1, 0x06, 0xC2, 0x17, 0xF7, 0xEE, 0x7B, 0xA8,
        0x00, 0x84, 0x3F, 0xFF, 0x81, 0xF0, 0xB0, 0x11, 0xF4, 0x00,
        0x16, 0xB8, 0x5F, 0x7B, 0xDD, 0xEF, 0xC0, 0x3F, 0x08, 0x02,
        0x0F, 0xD1, 0xF0, 0x94, 0x42, 0xF0, 0x0D, 0xC0, 0x7C, 0x21,
        0x0F, 0xC6, 0x01, 0x7C, 0x7E, 0x0F, 0x01, 0xD1, 0x01, 0x01,
        0x1F, 0x39, 0xE0, 0x83, 0xDF, 0x07, 0x7E, 0x1F, 0x0B, 0xFB,
        0x2F, 0xC7, 0xE3, 0x70, 0x63, 0x1F, 0xC6, 0x10, 0x7C, 0x5E,
        0x00, 0x3E, 0x02, 0x98, 0x41, 0xF7, 0x84, 0x0F, 0xF4, 0x01,
        0x07, 0x05, 0xF0, 0x23, 0xBE, 0xF0, 0x46, 0x30, 0x83, 0xE0,
        0x00, 0x79, 0xDF, 0x0C, 0xE3, 0x0F, 0x44, 0x00, 0xFF, 0xDF,
        0xE8, 0xC4, 0x14, 0x13, 0xBF, 0x08, 0x7D, 0x92, 0x03, 0xE0,
        0x20, 0x03, 0x91, 0x10, 0xA1, 0xF1, 0x01, 0xCE, 0x73, 0x84,
        0xF7, 0xBA, 0x23, 0x68, 0x3F, 0xF0, 0x7C, 0x2D, 0x03, 0xFF,
        0x07, 0xBE, 0x3F, 0x7F, 0xD9, 0x10, 0x03, 0xC1, 0x03, 0xC2,
        0xEA, 0x0A, 0x1E, 0x00, 0x03, 0xF8, 0x46, 0x3E, 0xF8, 0x02,
        0x18, 0x44, 0x3F, 0x00, 0xA0, 0xF7, 0x3C, 0x0D, 0x73, 0xFF,
        0xEE, 0xC0, 0x30, 0x10, 0x5F, 0xFF, 0xCA, 0x3E, 0xF3, 0xE1,
        0xE9, 0x02, 0x0D, 0x77, 0xDF, 0xF8, 0xBF, 0xCF, 0xFC, 0x62,
        0xE8, 0xBE, 0x40, 0xF4, 0x01, 0x0E, 0xF9, 0xE1, 0x7B, 0xC1,
        0x18, 0x7C, 0x3F, 0xF7, 0xE4, 0x10, 0xC2, 0x20, 0x88, 0x41,
        0x1F, 0x0F, 0xFF, 0x0C, 0x45, 0x10, 0xFF, 0xCF, 0x7B, 0xA0,
        0xFF, 0x4B, 0xF0, 0x78, 0x39, 0x07, 0x84, 0x2F, 0x04, 0xBC,
        0xE8, 0x04, 0x0F, 0x00, 0x3E, 0xFF, 0xC1, 0xCF, 0x93, 0xFC,
        0xF0, 0x33, 0xCE, 0x80, 0x21, 0x01, 0x4B, 0xD1, 0xF7, 0x9B,
        0xD8, 0x44, 0x00, 0x8F, 0xC3, 0x37, 0x38, 0x12, 0x7F, 0xA0,
        0x09, 0x00, 0x22, 0x00, 0x7E, 0xF8, 0x44, 0x5F, 0x17, 0xFE,
        0xF8, 0x7C, 0x1F, 0x88, 0x82, 0x17, 0x36, 0x0F, 0x0C, 0x24,
        0x08, 0x39, 0x80, 0x6F, 0xC1, 0x10, 0x44, 0x1F, 0x9B, 0xFE,
        0x27, 0x03, 0xF2, 0x04, 0xBF, 0xE0, 0xCC, 0x0E, 0xFB, 0xC4,
        0x00, 0x40, 0x50, 0x74, 0x23, 0x17, 0xBE, 0x0F, 0x7C, 0xDB,
        0xE9, 0x00, 0x41, 0x80, 0xBF, 0x20, 0xF8, 0x00, 0x87, 0xFE,
        0x08, 0x3F, 0xFF, 0x00, 0x21, 0x18, 0x83, 0x50, 0xF7, 0xC2,
        0x09, 0x81, 0xC2, 0x07, 0x9D, 0x27, 0x83, 0xD0, 0x04, 0x1C,
        0xE8, 0x06, 0x41, 0xFC, 0x44, 0xD9, 0x3F, 0xFF, 0x04, 0x41,
        0x29, 0x80, 0x0B, 0x7F, 0xC0, 0xF0, 0x7C, 0x00, 0x77, 0xA2,
        0x08, 0xB7, 0xE0, 0xF7, 0xDD, 0xF7, 0xC7, 0xF1, 0x6B, 0xE1,
        0x00, 0x09, 0xF4, 0xFB, 0xDD, 0xF6, 0xC9, 0xE2, 0x0B, 0xE1,
        0x00, 0xB9, 0xDD, 0x78, 0x41, 0x18, 0xFF, 0xE1, 0xFC, 0x3F,
        0xF7, 0xBF, 0xE0, 0x93, 0xBD, 0x28, 0x4D, 0xFF, 0xF8, 0x01,
        0x00, 0x09, 0xDE, 0x88, 0x24, 0xE8, 0x85, 0xD0, 0x7B, 0x80,
        0xFF, 0xC2, 0x10, 0xF0, 0x5D, 0xEF, 0x7F, 0xFE, 0xF4, 0x1F,
        0x0F, 0xC2, 0x10, 0x84, 0x3D, 0xD7, 0x7C, 0x20, 0x80, 0x17,
        0xE8, 0x85, 0xED, 0x8C, 0x03, 0xD7, 0x85, 0xDE, 0xF4, 0x9E,
        0x00, 0x80, 0x01, 0x08, 0x03, 0x08, 0x3D, 0xFE, 0x08, 0x3F,
        0xE8, 0x08, 0x31, 0x08, 0x02, 0x06, 0xFF, 0xFE, 0x8C, 0xC0,
        0xF8, 0x40, 0x00, 0x14, 0x60, 0x06, 0xC2, 0x2C, 0x88, 0x01,
        0x30, 0x34, 0x20, 0x94, 0x5D, 0x17, 0x7F, 0xBF, 0x9B, 0x62,
        0xDF, 0xBE, 0x4F, 0xF4, 0x21, 0xFF, 0xC2, 0x30, 0x88, 0xA1,
        0xEF, 0x40, 0x00, 0xFC, 0x43, 0xF8, 0x3B, 0xAF, 0x78, 0x01,
        0xF0, 0x45, 0xEC, 0x8B, 0xFE, 0x10, 0x3A, 0x2F, 0xF7, 0xFF,
        0x08, 0x00, 0x21, 0x17, 0xBD, 0x07, 0xC2, 0x0E, 0xFC, 0x45,
        0x3F, 0xC1, 0xEE, 0xFB, 0x3A, 0xF0, 0xC1, 0xD1, 0x84, 0x5A,
        0xE8, 0x09, 0xC1, 0x78, 0x00, 0x0E, 0xF4, 0x1E, 0x7F, 0x3C,
        0x18, 0xC1, 0xDE, 0x88, 0xA2, 0x0F, 0x84, 0x10, 0x04, 0x7A,
        0x17, 0xBF, 0xC1, 0x74, 0x26, 0x18, 0x81, 0xFF, 0x6C, 0x3B,
        0x0F, 0xCC, 0x2F, 0x80, 0x1F, 0xF0, 0x3E, 0x10, 0x88, 0xBD,
        0xFE, 0xC9, 0xB0, 0x03, 0xFE, 0xF8, 0x3E, 0x21, 0xFC, 0xC2,
        0x3F, 0x80, 0x00, 0x8F, 0xE0, 0x1F, 0x85, 0xCE, 0x8B, 0xC5,
        0x0F, 0x41, 0xB1, 0x7F, 0x83, 0x09, 0x02, 0x3E, 0xFC, 0x21,
        0xE8, 0x7E, 0x22, 0x07, 0xA1, 0x38, 0x3D, 0xF0, 0x80, 0x3C,
        0x10, 0x44, 0x20, 0x0F, 0xDF, 0x0F, 0x7A, 0x01, 0x88, 0x00,
        0x0F, 0xFA, 0x0E, 0x73, 0xDF, 0xF0, 0x83, 0xD2, 0x7C, 0xDF,
        0xEE, 0xCC, 0x00, 0xF4, 0x43, 0x00, 0x3F, 0xFF, 0x00, 0x3D,
        0xFE, 0x83, 0xA0, 0xFC, 0x43, 0x07, 0x81, 0xED, 0xFC, 0x01,
        0x20, 0x42, 0x2F, 0x87, 0xFE, 0xF7, 0x47, 0xB1, 0x1B, 0xDE,
        0x10, 0x08, 0x10, 0x78, 0xBD, 0xE9, 0xB9, 0xCE, 0x7C, 0x9D,
        0x00, 0x36, 0x1F, 0xEF, 0xDE, 0xF7, 0x87, 0xD0, 0x87, 0xC4,
        0x0E, 0xB6, 0x8D, 0x8B, 0xE1, 0xF8, 0xCD, 0xDF, 0x0B, 0x3D,
        0x0F, 0xCF, 0xEE, 0x80, 0x01, 0x01, 0x3D, 0xE0, 0xFC, 0x62,
        0x29, 0x41, 0xF0, 0x07, 0xFF, 0xFF, 0x46, 0x61, 0x80, 0x1D,
        0x27, 0xFE, 0x1F, 0xF4, 0x3B, 0x0F, 0xFF, 0xCF, 0x8B, 0xFE,
        0xE8, 0x84, 0x4F, 0x84, 0x24, 0x0F, 0x1D, 0xFB, 0xDA, 0xFE,
        0x0E, 0x16, 0x10, 0x03, 0x15, 0xE6, 0xE3, 0xF8, 0xFF, 0xD8,
        0x4B, 0xC8, 0xE5, 0xF4, 0x26, 0x03, 0xDB, 0x2A, 0xFD, 0x05,
        0xE1, 0xEF, 0x0B, 0xFC, 0x20, 0xFB, 0x31, 0x11, 0x1C, 0x03,
        0x15, 0xE1, 0xD6, 0x0B, 0xDC, 0xF5, 0x12, 0xED, 0xEA, 0x28,
        0x20, 0xE5, 0x21, 0xDC, 0xE7, 0xDF, 0x05, 0xDE, 0xF4, 0xFF,
        0x1F, 0xEF, 0x4A, 0xE6, 0x15, 0x0A, 0x07, 0x06, 0xF7, 0xE6,
        0xEE, 0xEC, 0x00, 0xF0, 0x03, 0xFF, 0xBB, 0x0D, 0xFB, 0x35,
        0xE3, 0xF5, 0xE6, 0xEA, 0x26, 0x00, 0x0E, 0x10, 0xFB, 0xDA,
        0x1B, 0xD1, 0xEF, 0x09, 0x02, 0x03, 0x16, 0x1F, 0x06, 0xDC,
        0xF8, 0x25, 0x17, 0xE9, 0xEA, 0x0F, 0xD5, 0x15, 0x0B, 0xE3,
        0x04, 0x20, 0xF5, 0x09, 0xE9, 0x1A, 0xE8, 0x0F, 0xEB, 0xE4,
        0xE9, 0x36, 0x03, 0xF5, 0xFF, 0x42, 0xFD, 0xE7, 0xFA, 0xF7,
        0xD2, 0x19, 0xCA, 0xE8, 0x22, 0x00, 0xEE, 0xDB, 0x00, 0xFE,
        0x18, 0x19, 0x14, 0x0C, 0x0A, 0xD0, 0x14, 0x21, 0x2E, 0xFD,
        0x36, 0xED, 0xAE, 0x1B, 0x00, 0x15, 0xF4, 0x13, 0xDC, 0x12,
        0xCB, 0x0A, 0xD4, 0xDD, 0x01, 0xCB, 0xED, 0xC2, 0xE5, 0xF2,
        0xD7, 0x02, 0xEF, 0xDB, 0x1D, 0x2F, 0xE2, 0x20, 0x3A, 0xE4,
        0x25, 0x1B, 0x14, 0x18, 0xF5, 0x04, 0x06, 0x18, 0x0D, 0x39,
        0xFC, 0x06, 0xF9, 0x0A, 0x20, 0xE1, 0xF7, 0x2E, 0xFB, 0xE3,
        0xF2, 0x21, 0xF9, 0xD3, 0x04, 0x07, 0xF9, 0x0B, 0xFC, 0xFA,
        0x27, 0x05, 0xF8, 0x2A, 0xFA, 0x0B, 0xFB, 0xEF, 0xB9, 0x08,
        0xFE, 0x0C, 0x14, 0x05, 0xE2, 0xFA, 0xFD, 0xC9, 0x0C, 0xF4,
        0x38, 0xCD, 0xFA, 0xD6, 0xE4, 0x05, 0x05, 0x1E, 0xF8, 0x3B,
        0x2E, 0x0E, 0x16, 0x07, 0x2C, 0x10, 0xF3, 0x1B, 0xF1, 0x00,
        0x08, 0xF0, 0xC7, 0x14, 0xD7, 0xDD, 0x06, 0xC2, 0xF3, 0xFD,
        0x10, 0x05, 0x18, 0x24, 0xE6, 0xDD, 0x0A, 0xDC, 0xDB, 0xEE,
        0xFE, 0xF6, 0xF9, 0x19, 0x40, 0x0B, 0x2B, 0x37, 0x28, 0xC7,
        0xFD, 0xF7, 0x07, 0xD5, 0x0C, 0x0C, 0x01, 0xDC, 0x2B, 0xD8,
        0xFD, 0x0C, 0xD1, 0xFC, 0x12, 0xE8, 0x02, 0xDC, 0x05, 0xDA,
        0xEB, 0x00, 0x1C, 0xDC, 0xFF, 0x07, 0xF8, 0x0F, 0xC8, 0x13,
        0x06, 0x08, 0x03, 0xEC, 0x11, 0xE8, 0xFD, 0xFD, 0xFF, 0x0D,
        0xF2, 0xD8, 0xF7, 0xFF, 0xFB, 0x11, 0xF4, 0xE2, 0xE9, 0x07,
        0xE7, 0xF6, 0xE9, 0x53, 0x13, 0x33, 0xEE, 0x1C, 0x15, 0x13,
        0x00, 0x24, 0xFC, 0xEA, 0x09, 0x15, 0xEC, 0x28, 0xF5, 0x1F,
        0xDB, 0xE3, 0xFC, 0x02, 0xF9, 0x0C, 0x0D, 0x26, 0x05, 0xF6,
        0x04, 0x02, 0xE4, 0x2F, 0x02, 0xF1, 0xE9, 0xE6, 0xEC, 0x03,
        0xEA, 0x16, 0xFA, 0x09, 0x13, 0xF6, 0x29, 0xE7, 0x13, 0xFB,
        0xF9, 0xDD, 0x03, 0xF7, 0xD4, 0x0B, 0xE7, 0xF2, 0x24, 0x2C,
        0x24, 0x35, 0x02, 0xFE, 0x21, 0xE9, 0x20, 0x01, 0x2A, 0x10,
        0x01, 0x1F, 0x03, 0x28, 0x2D, 0x20, 0xF8, 0x10, 0x0A, 0x09,
        0xF5, 0x32, 0xD5, 0xF6, 0xE4, 0x1F, 0xF8, 0xF4, 0xD8, 0xF4,
        0xD8, 0x0A, 0x07, 0x16, 0x0A, 0xFD, 0x14, 0xF8, 0x22, 0xDC,
        0x03, 0xDD, 0xFA, 0xEB, 0xD3, 0xE4, 0x0D, 0x04, 0xF6, 0x1D,
        0x1E, 0xDF, 0xF6, 0x28, 0x25, 0x17, 0x0B, 0xCF, 0xF1, 0x14,
        0xD9, 0x17, 0xEB, 0x34, 0xF7, 0xEC, 0xCE, 0x20, 0x1E, 0xD5,
        0x20, 0x07, 0xEB, 0xE8, 0x13, 0x04, 0x3D, 0x07, 0xF7, 0xF8,
        0xFE, 0xF6, 0xEA, 0xE9, 0xEE, 0xD7, 0xEE, 0xFC, 0xEA, 0xFA,
        0xFB, 0xDA, 0x33, 0x0F, 0xED, 0x0B, 0x2B, 0x00, 0x1A, 0xED,
        0x12, 0xF4, 0x0D, 0x15, 0x14, 0xFB, 0xF0, 0xF4, 0x0F, 0x0C,
        0xEC, 0x27, 0x11, 0x13, 0xF0, 0x0E, 0x2D, 0xE5, 0x00, 0xD2,
        0x0F, 0xD5, 0xF5, 0xFA, 0xF3, 0x03, 0x00, 0xDB, 0x17, 0xDD,
        0x2C, 0xE8, 0xFE, 0xE7, 0x46, 0xF6, 0xEF, 0xFD, 0x1F, 0xFF,
        0x0F, 0xEF, 0xE7, 0xEE, 0x18, 0x02, 0x27, 0x2B, 0xFC, 0x2D,
        0xF6, 0x13, 0xFC, 0xFD, 0xBB, 0xF8, 0xEE, 0xDD, 0x12, 0x0D,
        0x01, 0x22, 0x2F, 0xF0, 0xF1, 0x1B, 0x0C, 0x09, 0xE7, 0x33,
        0xE5, 0xFF, 0xFA, 0xFC, 0x1E, 0x15, 0x08, 0xF2, 0xDA, 0xE3,
        0xF4, 0xEA, 0xEF, 0x29, 0x3A, 0x02, 0x0B, 0x1E, 0xF4, 0x46,
        0xC5, 0x1C, 0xBD, 0xFC, 0xE5, 0x05, 0x0E, 0x24, 0xF9, 0xDE,
        0xD6, 0x1B, 0x05, 0xFF, 0xD3, 0xF3, 0xF2, 0x0D, 0xF0, 0x03,
        0xF5, 0x19, 0xFE, 0xDB, 0x0B, 0x00, 0x02, 0xC5, 0x12, 0x04,
        0xED, 0x1A, 0xF0, 0x12, 0x1C, 0x28, 0x1F, 0xEF, 0xDD, 0xCB,
        0xFC, 0x02, 0xF2, 0xED, 0xD1, 0xEC, 0xE5, 0xCC, 0xFE, 0xF6,
        0x03, 0xEE, 0xDF, 0x3F, 0x0F, 0xFD, 0x04, 0x05, 0x32, 0x11,
        0x1C, 0xF2, 0xF0, 0x1E, 0x07, 0xEF, 0x07, 0x1E, 0x17, 0x15,
        0x04, 0x01, 0x03, 0xF7, 0xFA, 0x02, 0xD9, 0xEB, 0x19, 0xCD,
        0xEA, 0x2B, 0x22, 0xFD, 0xD7, 0x08, 0xF0, 0xF4, 0xF4, 0xF1,
        0x44, 0x09, 0xE5, 0x0B, 0xFE, 0xEE, 0x1F, 0x1A, 0xED, 0xFF,
        0xFB, 0x07, 0xFD, 0xF6, 0x1E, 0x1D, 0x02, 0xFF, 0xDA, 0x01,
        0x1E, 0xEE, 0x04, 0xD2, 0xDF, 0xDA, 0x0D, 0xFC, 0xFA, 0xF9,
        0xFE, 0x12, 0x0B, 0x42, 0x26, 0x0E, 0x00, 0xD9, 0xEF, 0x17,
        0x02, 0xD3, 0x1B, 0x03, 0xEB, 0xDE, 0x0C, 0x11, 0xFF, 0x0E,
        0x1D, 0xCD, 0xF9, 0xEF, 0x12, 0x0E, 0x0F, 0x03, 0xF7, 0x21,
        0x16, 0x1F, 0x11, 0xD9, 0x02, 0x01, 0xE4, 0x0D, 0xDB, 0x2D,
        0xD6, 0x16, 0xE0, 0xEE, 0x1D, 0xF7, 0xFA, 0xE8, 0x10, 0xF3,
        0x39, 0xF0, 0xEA, 0xEC, 0x2D, 0xCE, 0x22, 0xCE, 0x2C, 0xF8,
        0x1F, 0xEA, 0xFC, 0xD9, 0x28, 0x14, 0xFC, 0x22, 0x15, 0xE4,
        0xCF, 0x19, 0xFD, 0xE0, 0xEC, 0xEE, 0xFC, 0xEE, 0x0F, 0xE4,
        0x03, 0xF8, 0xDF, 0xF3, 0x12, 0xF3, 0xF1, 0xED, 0xEB, 0x14,
        0xD4, 0x4E, 0x0B, 0x25, 0xC8, 0x14, 0x01, 0x10, 0xFA, 0x18,
        0x12, 0x13, 0x0A, 0xE0, 0xED, 0xD7, 0x01, 0xD5, 0xF6, 0xDD,
        0xBC, 0x0C, 0xD9, 0xFC, 0xE1, 0x07, 0x06, 0xEE, 0xBD, 0x1F,
        0xEC, 0x1A, 0x22, 0x04, 0xFC, 0x18, 0xF2, 0xEA, 0x01, 0x0B,
        0xF6, 0xFA, 0x13, 0x26, 0x09, 0x07, 0xEC, 0x0B, 0x08, 0x0F,
        0xFE, 0x0E, 0xD2, 0x15, 0xB3, 0xE0, 0xD8, 0x34, 0xE3, 0xEC,
        0xF6, 0x21, 0xEB, 0x13, 0x01, 0xF3, 0x0B, 0x02, 0x07, 0xF7,
        0x0D, 0xF6, 0x2F, 0x22, 0xFE, 0x21, 0x1F, 0xE7, 0xFA, 0xFF,
        0x0F, 0x03, 0xFD, 0xE5, 0xE3, 0x0F, 0xF5, 0x08, 0xF6, 0xFF,
        0x02, 0x30, 0xE0, 0x21, 0xEC, 0x21, 0x3A, 0xF6, 0x1F, 0xF4,
        0x01, 0xCC, 0x0D, 0xD7, 0x1B, 0x24, 0x1F, 0x0F, 0x0D, 0xFB,
        0xFC, 0x16, 0x13, 0x1C, 0xE0, 0xEC, 0xED, 0xE9, 0x07, 0xE0,
        0xFC, 0xF1, 0xDB, 0x07, 0x23, 0xF9, 0x10, 0x08, 0xE4, 0xF7,
        0xF4, 0xEC, 0xCD, 0xE0, 0xF9, 0xCD, 0xF3, 0x13, 0x02, 0x05,
        0x0C, 0xF8, 0xFA, 0xFF, 0x14, 0xE3, 0x01, 0xEA, 0xFB, 0x04,
        0x2C, 0xF5, 0xF3, 0x10, 0x19, 0xE7, 0x03, 0xDC, 0x2A, 0xFB,
        0x06, 0x0B, 0x05, 0x3D, 0xE8, 0xD4, 0x2F, 0xFD, 0xDE, 0x14,
        0x19, 0xD5, 0xEE, 0xFA, 0x0D, 0x06, 0x0B, 0x41, 0x1F, 0xDA,
        0x0E, 0x1C, 0xF5, 0xEF, 0x36, 0x0A, 0x1E, 0x0F, 0x07, 0x0D,
        0x19, 0x03, 0xDA, 0xE2, 0x12, 0x1C, 0xEA, 0xF7, 0x13, 0xCE,
        0x27, 0x18, 0xD2, 0x04, 0xD5, 0xFB, 0xFD, 0xD7, 0xDF, 0xEB,
        0x1B, 0xFC, 0xFD, 0x16, 0xEB, 0x1B, 0xF1, 0xDD, 0x34, 0xD7,
        0x3D, 0x00, 0x24, 0x04, 0x19, 0xF5, 0x0E, 0x14, 0x1F, 0x06,
        0xE4, 0xEF, 0xCF, 0x17, 0xE2, 0x0D, 0x17, 0x06, 0xF7, 0x0A,
        0x21, 0xAA, 0xC5, 0xB8, 0x1B, 0x6C, 0xEB, 0x28, 0x70, 0x4A,
        0x9A, 0x80, 0x17, 0xDD, 0x39, 0xB2, 0xDA, 0x07, 0x9E, 0xC3,
        0xD2, 0x6C, 0xF1, 0x0F, 0x42, 0xB7, 0x48, 0xC4, 0x1A, 0x01,
        0xAD, 0x83, 0x05, 0x4D, 0x8A, 0x4D, 0x56, 0xBC, 0x10, 0x38,
        0x11, 0xD8, 0x6A, 0x34, 0x02, 0xF4, 0x8F, 0x86, 0x64, 0x70,
        0x92, 0xB7, 0xD5, 0xF5, 0xAD, 0x9B, 0x0A, 0x76, 0x8B, 0x42,
        0x39, 0xA0, 0x3B, 0x5F, 0xBA, 0x46, 0x27, 0xF3, 0x4C, 0xC7,
        0x7F, 0x78, 0xB5, 0xE3, 0xC2, 0x95, 0x67, 0x32, 0x3C, 0x28,
        0x88, 0x2A, 0xEE, 0x4F, 0x9E, 0xC2, 0xEC, 0xF5, 0xD7, 0xE2,
        0xA1, 0x4D, 0x9C, 0xB1, 0xC3, 0x64, 0x21, 0xD2, 0x19, 0xD8,
        0xA6, 0x33, 0x95, 0xC0, 0xD8, 0x07, 0x82, 0xC7, 0xB3, 0xC4,
        0x25, 0xCC, 0x24, 0xE8, 0xB7, 0xD2, 0xBA, 0x6F, 0x2B, 0x79,
        0x08, 0xF0, 0xF0, 0x47, 0x65, 0xEF, 0x46, 0x65, 0x46, 0xB9,
        0xD7, 0x0E, 0xC5, 0x52, 0x5C, 0x75, 0x5B, 0xDB, 0xA8, 0x39,
        0x99, 0x4A, 0xA8, 0x47, 0x08, 0xC9, 0xA8, 0x9F, 0x40, 0x1A,
        0x67, 0xFB, 0x46, 0x48, 0xB3, 0x82, 0x11, 0xF2, 0x66, 0xA2,
        0xF6, 0xBB, 0xCC, 0xC2, 0x08, 0xFA, 0xAF, 0x1F, 0x36, 0x10,
        0x9C, 0xF5, 0x5F, 0x6D, 0x54, 0x27, 0xB9, 0x80, 0x88, 0xF8,
        0x50, 0x5F, 0x90, 0x05, 0xC1, 0x94, 0xCA, 0xED, 0xAE, 0x59,
        0xE8, 0x76, 0x1A, 0x16, 0xDA, 0x88, 0xAA, 0x4D, 0xEA, 0xD5,
        0xB6, 0xD3, 0xA1, 0x13, 0x5C, 0xA2, 0x1A, 0x46, 0x47, 0xFC,
        0xB5, 0x4A, 0x47, 0x04, 0xD4, 0xEF, 0xBF, 0xBF, 0x61, 0xAC,
        0x91, 0x05, 0x59, 0xF6, 0x91, 0x3D, 0x98, 0x90, 0xEB, 0x1E,
        0x9A, 0x9D, 0x42, 0x9A, 0xD4, 0x10, 0x21, 0x61, 0x12, 0x66,
        0xE4, 0xDB, 0xC6, 0x06, 0x52, 0x77, 0x91, 0xA7, 0x01, 0x09,
        0x94, 0x92, 0x5D, 0xB5, 0x3C, 0x29, 0xA5, 0x4D, 0xEE, 0xBD,
        0xA6, 0xC8, 0xC7, 0x81, 0x8B, 0x93, 0x64, 0x6C, 0xD6, 0xA8,
        0xAC, 0x18, 0x3C, 0x5B, 0x48, 0x69, 0x77, 0xA9, 0x2E, 0x65,
        0xA2, 0xE6, 0x26, 0x73, 0xF7, 0x8A, 0x9E, 0x31, 0x56, 0xD6,
        0xE6, 0x2F, 0xCD, 0xDD, 0xBF, 0xF2, 0x31, 0xC6, 0x87, 0x2F,
        0xE6, 0x2C, 0x68, 0x4F, 0x79, 0xE4, 0xC2, 0x0A, 0x48, 0xFC,
        0x4D, 0xF4, 0xF8, 0x40, 0x85, 0x9C, 0xE1, 0x4C, 0x47, 0x4D,
        0x16, 0x88, 0xB5, 0xCA, 0x51, 0xBB, 0x6B, 0x12, 0x50, 0x37,
        0x6E, 0xBA, 0x06, 0x10, 0x0E, 0x5A, 0x9F, 0x9C, 0xB5, 0x0B,
        0x32, 0x81, 0x96, 0x1F, 0xC1, 0x24, 0xE8, 0x85, 0x6E, 0x3F,
        0xB9, 0xD8, 0xE4, 0x69, 0x30, 0x1A, 0xF3, 0x12, 0x98, 0x32,
        0xF2, 0xB8, 0x5F, 0xA1, 0xB4, 0x71, 0x0B, 0x19, 0x09, 0x22,
        0xD5, 0x84, 0x1A, 0x49, 0xE7, 0x10, 0x07, 0xDF, 0x68, 0x8D,
        0xFB, 0x02, 0xB0, 0x9E, 0xF4, 0x06, 0x36, 0x8C, 0x81, 0x70,
        0xD6, 0x5D, 0x31, 0x98, 0x5E, 0xC2, 0xF4, 0x5B, 0xDC, 0xA3,
        0x38, 0x7F, 0x82, 0x58, 0x48, 0x50, 0x51, 0x24, 0x2C, 0x80,
        0xE0, 0x1A, 0xAA, 0x8C, 0xDC, 0x4F, 0xD0, 0x21, 0x14, 0x6A,
        0xBD, 0x2C, 0x5D, 0x51, 0xFA, 0x94, 0x18, 0x89, 0x5C, 0x69,
        0xD9, 0xB5, 0x23, 0xCB, 0x00, 0x1C, 0x67, 0x27, 0x58, 0x62,
        0xE3, 0xB3, 0xD5, 0x6A, 0x57, 0xE8, 0x6C, 0x18, 0x60, 0xCA,
        0x20, 0x14, 0x3E, 0xA2, 0x76, 0xA6, 0xBC, 0xE9, 0x83, 0x3A,
        0x20, 0x2E, 0x61, 0x5E, 0xAB, 0x20, 0x1F, 0x0A, 0x32, 0x35,
        0x8E, 0x3B, 0x30, 0x4C, 0xEA, 0x74, 0xB8, 0x72, 0xC5, 0x95,
        0xD9, 0x4A, 0x36, 0xA0, 0x0D, 0xA1, 0x91, 0x64, 0x89, 0x21,
        0x73, 0xC8, 0x8D, 0x08, 0x0F, 0x12, 0xED, 0x85, 0x81, 0xAE,
        0xA1, 0xB8, 0x8B, 0xBF, 0x12, 0xDA, 0x30, 0xBB, 0x68, 0xA7,
        0xFA, 0xB7, 0xA8, 0x68, 0xC5, 0x97, 0xA2, 0xAD, 0x02, 0x01,
        0xE8, 0x11, 0x16, 0x51, 0xF9, 0x3A, 0x39, 0xE4, 0x56, 0x43,
        0x5D, 0xC9, 0xA7, 0x01, 0xB2, 0x14, 0x78, 0x85, 0x0B, 0x75,
        0x1D, 0x9A, 0x68, 0x0A, 0x9C, 0x77, 0x4A, 0xC0, 0xDD, 0xE4,
        0xB4, 0xDE, 0x46, 0x41, 0x8E, 0x00, 0x91, 0xF4, 0xC6, 0xDA,
        0xB9, 0xA1, 0x15, 0x64, 0xBC, 0x5A, 0x57, 0x82, 0xDA, 0xF3,
        0xB8, 0xEE, 0xDE, 0x69, 0x25, 0x30, 0x84, 0xE8, 0xFF, 0x5E,
        0xF2, 0x26, 0xF0, 0x41, 0x02, 0xE9, 0xAA, 0xA7, 0xA1, 0xA8,
        0x88, 0x6A, 0xC8, 0x6D, 0x06, 0x3E, 0x86, 0x6B, 0xA9, 0x39,
        0x28, 0x40, 0x55, 0xE4, 0xA9, 0x11, 0xDF, 0xE5, 0xB9, 0x98,
        0x2D, 0xF2, 0x36, 0x5E, 0xAC, 0x58, 0x54, 0x3A, 0x7C, 0xEA,
        0x77, 0x97, 0x00, 0x66, 0xD2, 0x9B, 0x53, 0x73, 0x4C, 0xC1,
        0x1F, 0xAC, 0xEE, 0x90, 0x6B, 0xC8, 0xE0, 0xBC, 0x9E, 0x7C,
        0xC1, 0x88, 0x1C, 0x90, 0x12, 0x55, 0xE5, 0x17, 0x14, 0xC0,
        0xBF, 0x2C, 0x31, 0xCC, 0x74, 0xEF, 0x50, 0xF3, 0xAA, 0xE4,
        0x91, 0x15, 0xE6, 0x68, 0x2A, 0x86, 0xE4, 0x39, 0x73, 0x11,
        0x93, 0x46, 0x41, 0x22, 0x4B, 0xDB, 0xA7, 0x84, 0x91, 0x6F,
        0x68, 0x8C, 0x05, 0x08, 0x69, 0xE3, 0x4F, 0x95, 0x5F, 0x3A,
        0x1A, 0xDA, 0x34, 0x4C, 0x44, 0x75, 0x1B, 0x5C, 0x09, 0xEB,
        0x2B, 0x6E, 0x9F, 0x18, 0x18, 0x2C, 0x3C, 0x87, 0x7C, 0xA8,
        0xEA, 0x7F, 0x4A, 0xB9, 0xB6, 0x6E, 0x67, 0x49, 0xCA, 0xA1,
        0x00, 0x77, 0xA6, 0x33, 0xE5, 0x2C, 0xC6, 0xED, 0x66, 0xF4,
        0x4E, 0xF4, 0x1F, 0xEB, 0xFD, 0x62, 0x5C, 0xAF, 0x10, 0x3D,
        0x8E, 0x2B, 0xAE, 0xF9, 0x46, 0xB6, 0xF6, 0x98, 0x35, 0x77,
        0xD0, 0x9C, 0x0A, 0xA9, 0x2C, 0xD7, 0x6F, 0x46, 0x42, 0x45,
        0x8B, 0xDF, 0xF7, 0x0A, 0x2C, 0xE6, 0x41, 0xC3, 0xEA, 0xAC,
        0x06, 0x30, 0xDA, 0xC9, 0xE7, 0x22, 0x29, 0x0B, 0x2C, 0x70,
        0x49, 0x5B, 0x05, 0x29, 0x48, 0x14, 0x32, 0xE6, 0x75, 0x6E,
        0x81, 0x35, 0xE4, 0xA0, 0xE2, 0x4C, 0x1E, 0x53, 0xB7, 0x36,
        0x19, 0xC1, 0xEF, 0x44, 0xE1, 0x2A, 0x49, 0xC8, 0x0B, 0xC0,
        0x43, 0xF6, 0x77, 0x54, 0xD6, 0x80, 0xE0, 0xE5, 0x23, 0x64,
        0x4A, 0xF0, 0x18, 0xBF, 0x91, 0x14, 0x71, 0x9A, 0x03, 0x77,
        0xF5, 0x52, 0x4E, 0xA0, 0xDC, 0xAB, 0x64, 0x7C, 0xD0, 0xE5,
        0x1D, 0x92, 0x4A, 0xDB, 0xE8, 0xD3, 0xF4, 0x01, 0xE0, 0xE6,
        0x1B, 0x77, 0x8D, 0x6B, 0x8F, 0xD2, 0x93, 0x0A, 0xE3, 0x5D,
        0x60, 0x1C, 0x19, 0xD5, 0xA8, 0x9B, 0xDE, 0x0F, 0x2C, 0xB2,
        0xF5, 0x2B, 0x3C, 0x1E, 0x50, 0x42, 0xB4, 0x54, 0xC9, 0x28,
        0xE4, 0x2F, 0x3D, 0xD0, 0x6B, 0x24, 0x13, 0x1B, 0xED, 0x85,
        0xA2, 0x0C, 0x95, 0x50, 0xD8, 0x5F, 0x73, 0x96, 0xA1, 0x64,
        0xEF, 0xA6, 0xE8, 0x65, 0x55, 0xF8, 0xA4, 0xF9, 0x54, 0x24,
        0x76, 0x05, 0xD7, 0xE5, 0x35, 0x98, 0xCD, 0x34, 0xE1, 0xF0,
        0x36, 0x27, 0x8C, 0x16, 0x1B, 0xB6, 0xE2, 0x73, 0xDA, 0x57,
        0xDD, 0x11, 0xE1, 0x15, 0x46, 0xD1, 0xC7, 0xA3, 0x2C, 0x9D,
        0xE2, 0xE5, 0xED, 0xAA, 0xB5, 0x70, 0xC5, 0xDD, 0x99, 0x77,
        0x0E, 0x50, 0x28, 0xF2, 0xBD, 0xF1, 0xEB, 0x16, 0xAA, 0xB4,
        0x99, 0xE8, 0x42, 0xAE, 0x81, 0x96, 0x99, 0x40, 0x0E, 0xF1,
        0x30, 0xA8, 0x5C, 0x65, 0x2C, 0x75, 0x21, 0xCA, 0xCC, 0x0E,
        0x95, 0x61, 0x04, 0x04, 0xD4, 0x43, 0x11, 0x7D, 0x8E, 0x21,
        0x84, 0x7A, 0x5B, 0xF7, 0x1D, 0xEA, 0xB6, 0xC8, 0x54, 0x02,
        0xE8, 0x95, 0x26, 0xC3, 0x51, 0xC2, 0x65, 0x14, 0xAB, 0xA9,
        0xBF, 0x43, 0x7E, 0x0E, 0x57, 0x68, 0xFC, 0x4D, 0xDA, 0x08,
        0xD4, 0x73, 0x38, 0x7C, 0x0A, 0xF4, 0x92, 0xA4, 0xD4, 0x6D,
        0x8D, 0xB8, 0xB7, 0xE8, 0xD2, 0x7D, 0x35, 0x4E, 0x44, 0xE2,
        0x84, 0xE0, 0xF0, 0x3D, 0xD0, 0x31, 0xB8, 0xED, 0xD4, 0x75,
        0x8C, 0x98, 0xED, 0x30, 0xBE, 0xD4, 0xDB, 0xA6, 0x02, 0x2C,
        0xEB, 0x9E, 0x6B, 0x83, 0xBE, 0x71, 0x25, 0x9A, 0x8C, 0xDC,
        0x5F, 0x86, 0x09, 0x33, 0x74, 0x32, 0x25, 0xD8, 0xA3, 0x29,
        0x21, 0x6A, 0xE6, 0x68, 0xDD, 0x82, 0x96, 0x87, 0x62, 0xE6,
        0x83, 0xFB, 0x85, 0xD9, 0x00, 0xC8, 0x32, 0x8B, 0xA8, 0x33,
        0x86, 0x2C, 0xE0, 0xDF, 0x1F, 0x76, 0x7D, 0x5C, 0xB1, 0x92,
        0xE3, 0x84, 0xDB, 0x73, 0xEC, 0x3D, 0x49, 0xEB, 0xD8, 0x62,
        0x4A, 0xEE, 0x2D, 0xA1, 0x13, 0xCA, 0x65, 0x7E, 0xBA, 0xAD,
        0x61, 0xEE, 0xA2, 0x8E, 0x23, 0x6E, 0xA7, 0x97, 0x75, 0x93,
        0x62, 0xBF, 0x40, 0x3E, 0x01, 0xE8, 0xE7, 0x40, 0x19, 0xEC,
        0x3F, 0xD2, 0xCE, 0x16, 0x5C, 0xB3, 0x08, 0x29, 0x48, 0xCE,
        0x42, 0x81, 0x59, 0x7A, 0x67, 0xB2, 0x03, 0xEB, 0xBC, 0x1E,
        0x45, 0x64, 0x8B, 0xED, 0xD0, 0xA5, 0x5C, 0x2D, 0xEA, 0xEA,
        0xB5, 0x2A, 0xF2, 0x39, 0x1A, 0x2C, 0xB4, 0x35, 0x4E, 0xC2,
        0xBB, 0x98, 0x66, 0x12, 0x8A, 0xC1, 0xE1, 0x26, 0xFE, 0x3C,
        0x48, 0x1E, 0x40, 0xE3, 0x58, 0x39, 0xFD, 0x33, 0x1B, 0xE9,
        0x91, 0x70, 0x60, 0xD0, 0x22, 0xD0, 0xD6, 0x7D, 0x20, 0x59,
        0x08, 0x84, 0x3B, 0x98, 0x99, 0xB5, 0x5C, 0x44, 0x19, 0xE8,
        0xA5, 0x99, 0x15, 0x61, 0x98, 0xE2, 0x4E, 0x62, 0x59, 0x97,
        0xF9, 0x61, 0x6B, 0xF0, 0x57, 0x89, 0x9F, 0xBD, 0x33, 0xBB,
        0xA9, 0x56, 0xB8, 0x5D, 0x3D, 0x6A, 0x10, 0x35, 0x41, 0xEE,
        0x40, 0xAC, 0xF2, 0xAA, 0x34, 0x6A, 0x09, 0xA0, 0x35, 0x83,
        0xD2, 0xAB, 0x24, 0xA1, 0x97, 0x70, 0xCE, 0xA1, 0xEE, 0x2F,
        0x0D, 0x5D, 0xC9, 0x3E, 0x8B, 0xF6, 0x25, 0xA9, 0xB5, 0xF8,
        0x2C, 0xE0, 0x69, 0x43, 0x08, 0x64, 0x19, 0xC5, 0x23, 0x20,
        0xEC, 0xD8, 0x04, 0x00, 0xA8, 0x86, 0x61, 0x00, 0x1F, 0x6E,
        0x5C, 0x9E, 0xDA, 0x61, 0x68, 0x87, 0x5F, 0xB1, 0x22, 0x49,
        0x7B, 0x17, 0x67, 0x7E, 0x51, 0x4A, 0xE1, 0x05, 0x1B, 0x31,
        0x39, 0xDD, 0x75, 0x42, 0x4C, 0xE1, 0x10, 0xBB, 0xB8, 0x08,
        0xA8, 0x0C, 0x90, 0xC3, 0x59, 0x90, 0xE2, 0x99, 0x83, 0x65,
        0xBC, 0x5D, 0xC4, 0x90, 0x87, 0x8B, 0xE4, 0xF3, 0x32, 0x39,
        0x9D, 0x46, 0xFF, 0x24, 0x60, 0x1F, 0x32, 0x49, 0xA9, 0x57,
        0x97, 0xF7, 0x8C, 0x59, 0x3E, 0x70, 0x09, 0x93, 0x4A, 0x0C,
        0xFC, 0x32, 0x51, 0x86, 0x41, 0x65, 0x04, 0x4D, 0xF7, 0x02,
        0xB2, 0x6A, 0xB1, 0xAA, 0x18, 0x6B, 0xA2, 0x05, 0xE9, 0xD7,
        0x59, 0xC2, 0x93, 0x51, 0x57, 0xC8, 0x42, 0x9A, 0x74, 0xBA,
        0x98, 0x51, 0x9E, 0xB9, 0xA7, 0x41, 0x81, 0x71, 0x54, 0x45,
        0x51, 0xBE, 0xC5, 0x72, 0x02, 0xEB, 0xA8, 0x97, 0x5B, 0x4E,
        0x25, 0xD0, 0x40, 0xD1, 0x45, 0x7D, 0x30, 0xBF, 0x44, 0xBE,
        0x1C, 0xE1, 0x4C, 0x9C, 0xDA, 0xA3, 0x54, 0xEC, 0xFF, 0x46,
        0x26, 0xE9, 0x97, 0x63, 0x82, 0x4F, 0xA6, 0x9A, 0xA0, 0xF3,
        0x89, 0x83, 0x8E, 0x49, 0xAE, 0x21, 0xF4, 0x5C, 0x6E, 0x0D,
        0x89, 0x08, 0xCA, 0x34, 0xCC, 0xC2, 0xC3, 0x47, 0x8C, 0xDC,
        0xC5, 0x9D, 0xA4, 0xAF, 0xA2, 0x0A, 0x75, 0x3A, 0x72, 0x8A,
        0x1D, 0xA0, 0x3A, 0xDA, 0x5B, 0x0F, 0x1A, 0xDC, 0x70, 0x21,
        0xC8, 0xE3, 0x3C, 0xD5, 0xCF, 0x48, 0x74, 0x49, 0xF9, 0x6E,
        0x8D, 0xD6, 0x9D, 0x8A, 0x9A, 0x32, 0x6B, 0xE0, 0x77, 0xA6,
        0x84, 0x54, 0xF5, 0xA3, 0x11, 0x55, 0xB7, 0xA0, 0xA8, 0xF3,
        0xBC, 0xED, 0xEC, 0x6F, 0xB8, 0xAF, 0xA3, 0xE4, 0xDC, 0x13,
        0xA4, 0x76, 0x63, 0x83, 0x92, 0xC4, 0xC6, 0x22, 0x20, 0x37,
        0x58, 0xA3, 0xAC, 0x63, 0x66, 0x88, 0x3B, 0xD8, 0xBA, 0xA4,
        0x06, 0x26, 0xBE, 0xA6, 0x23, 0x9E, 0xC0, 0xD1, 0x61, 0x5D,
        0xCF, 0x64, 0x34, 0x04, 0x4D, 0x05, 0x9C, 0xD2, 0x03, 0xE8,
        0xC8, 0x0B, 0x14, 0xBE, 0x47, 0x45, 0x1E, 0x1B, 0x04, 0x94,
        0xDA, 0xAD, 0x72, 0xCA, 0x1B, 0xE2, 0xB3, 0xC5, 0xA1, 0x69,
        0x45, 0x07, 0xED, 0x2E, 0xF9, 0x1A, 0xC3, 0x3C, 0xC7, 0xB5,
        0x1D, 0xD9, 0x85, 0x27, 0xB8, 0xD4, 0x91, 0x03, 0xD2, 0x35,
        0xF3, 0x5F, 0x5D, 0x3F, 0x3E, 0x56, 0x25, 0xBB, 0x37, 0x0C,
        0xDC, 0x48, 0x1D, 0xCF, 0x82, 0xFD, 0x24, 0xD0, 0xBF, 0xCA,
        0x69, 0x91, 0xC5, 0x8C, 0x7D, 0x7B, 0x78, 0x31, 0xBA, 0xC3,
        0x9F, 0x7C, 0x6E, 0x7D, 0x39, 0xF3, 0xEE, 0x46, 0x9C, 0x81,
        0xE5, 0xAA, 0xA8, 0xD7, 0x02, 0x17, 0x27, 0x94, 0xF8, 0xF1,
        0x54, 0x0A, 0xA2, 0x47, 0x3C, 0xE7, 0xA6, 0xA5, 0x00, 0x31,
        0x81, 0xA1, 0xE3, 0x86, 0xEF, 0x56, 0xE3, 0x36, 0x2C, 0x1F,
        0x86, 0xED, 0xD3, 0x6D, 0x05, 0xBA, 0x36, 0xF1, 0xF0, 0x2E,
        0x84, 0x9A, 0xFA, 0xA7, 0x09, 0x59, 0x14, 0x2B, 0x0A, 0xFC,
        0xC0, 0x16, 0x3E, 0xAA, 0x13, 0xAF, 0x25, 0x02, 0x4A, 0x97,
        0xD0, 0xAD, 0x7E, 0x2D, 0xC2, 0x4A, 0xFD, 0x1F, 0x2B, 0x4D,
        0x46, 0x60, 0x42, 0xB1, 0xAE, 0x12, 0x32, 0x5E, 0x23, 0x19,
        0x47, 0x4B, 0x23, 0x7C, 0x1A, 0xA2, 0xC6, 0x4F, 0x03, 0xBE,
        0x47, 0x95, 0x97, 0x86, 0xFF, 0x0E, 0xCC, 0x40, 0xB0, 0xCA,
        0x45, 0xE5, 0x14, 0x93, 0xA1, 0xD0, 0xC1, 0x37, 0xEE, 0x1E,
        0x13, 0x69, 0xB9, 0x8D, 0xE1, 0x86, 0xCC, 0xDD, 0x0D, 0xC2,
        0xED, 0xD3, 0x59, 0x13, 0x9D, 0xB5, 0xD0, 0x50, 0x68, 0x1A,
        0xA5, 0xA6
};
static const int sizeof_bench_falcon_level5_key = sizeof(bench_falcon_level5_key);

#endif /* HAVE_FALCON */

#if defined(HAVE_DILITHIUM)
#ifndef WOLFSSL_DILITHIUM_NO_SIGN

/* raw private key without ASN1 syntax from
 * ./certs/dilithium/bench_dilithium_level2_key.der */
static const unsigned char bench_dilithium_level2_key[] = {
    0xea, 0x05, 0x24, 0x0d, 0x80, 0x72, 0x25, 0x55, 0xf4, 0x5b,
    0xc2, 0x13, 0x8b, 0x87, 0x5d, 0x31, 0x99, 0x2f, 0x1d, 0xa9,
    0x41, 0x09, 0x05, 0x76, 0xa7, 0xb7, 0x5e, 0x8c, 0x44, 0xe2,
    0x64, 0x79, 0xd8, 0x79, 0x4c, 0xee, 0x92, 0x2b, 0x37, 0xab,
    0xb1, 0x16, 0x65, 0x72, 0xc3, 0x49, 0xc2, 0xec, 0xfd, 0x9a,
    0xe6, 0x2d, 0x1e, 0x5b, 0xe3, 0x04, 0x96, 0x16, 0xad, 0x97,
    0x5d, 0xac, 0xf2, 0xcc, 0x62, 0x2e, 0x34, 0x5d, 0x67, 0x19,
    0x47, 0xee, 0x0f, 0x8b, 0x97, 0x60, 0xb4, 0x0b, 0xeb, 0x6a,
    0x7a, 0x75, 0x14, 0x27, 0x00, 0x39, 0xd6, 0x60, 0xce, 0x39,
    0x6e, 0x69, 0x46, 0xe1, 0x0d, 0xf9, 0xa6, 0xfa, 0x8c, 0xcf,
    0x65, 0x50, 0x59, 0x1d, 0xb0, 0x26, 0xc2, 0xe2, 0xf1, 0xb9,
    0xcd, 0x09, 0x60, 0xcc, 0xbb, 0x57, 0xd6, 0xac, 0xcc, 0xf9,
    0x58, 0x73, 0xa8, 0x81, 0x61, 0x2f, 0xd2, 0xa4, 0x5b, 0x98,
    0x0d, 0x12, 0x88, 0x51, 0x63, 0x38, 0x6e, 0xa2, 0x46, 0x64,
    0x52, 0xc0, 0x71, 0xc1, 0x42, 0x68, 0xd8, 0x42, 0x32, 0x5c,
    0xb4, 0x44, 0x08, 0x95, 0x48, 0xa2, 0x46, 0x6c, 0x0b, 0x10,
    0x09, 0xc8, 0x24, 0x4d, 0x18, 0x37, 0x4c, 0x4c, 0x82, 0x05,
    0x02, 0x22, 0x10, 0x4a, 0x86, 0x30, 0x03, 0x03, 0x11, 0x44,
    0x22, 0x62, 0x01, 0xa9, 0x51, 0x13, 0x02, 0x2c, 0x19, 0x85,
    0x65, 0x51, 0x14, 0x01, 0x9c, 0xb2, 0x81, 0x0a, 0x49, 0x52,
    0xa2, 0xb2, 0x4c, 0x98, 0x34, 0x01, 0x0a, 0x07, 0x06, 0x58,
    0xb2, 0x69, 0x51, 0x24, 0x2d, 0x59, 0x12, 0x52, 0xe0, 0xb4,
    0x04, 0x14, 0x40, 0x29, 0xa2, 0xb0, 0x31, 0x54, 0xc0, 0x40,
    0x63, 0x00, 0x69, 0x18, 0x47, 0x85, 0xc8, 0x30, 0x81, 0x0b,
    0x15, 0x0a, 0xd8, 0xa0, 0x0c, 0x5c, 0x20, 0x4a, 0x11, 0x38,
    0x64, 0x04, 0x94, 0x84, 0xd3, 0x24, 0x72, 0x58, 0x38, 0x28,
    0x18, 0x37, 0x6d, 0x94, 0xc0, 0x4d, 0xa0, 0xa6, 0x0c, 0x9a,
    0x82, 0x31, 0xc2, 0x40, 0x48, 0xda, 0x46, 0x85, 0x03, 0x00,
    0x05, 0xd8, 0x02, 0x4d, 0x0b, 0x85, 0x40, 0xe2, 0x32, 0x86,
    0x4c, 0xa0, 0x65, 0x8a, 0x36, 0x65, 0x42, 0x18, 0x6e, 0x60,
    0x36, 0x0d, 0x40, 0xc0, 0x01, 0x5a, 0x44, 0x42, 0xc4, 0xa4,
    0x0d, 0xd4, 0x88, 0x8d, 0x88, 0x22, 0x52, 0x00, 0xc0, 0x0c,
    0x5b, 0x36, 0x90, 0x09, 0x20, 0x22, 0x08, 0x03, 0x12, 0x90,
    0x12, 0x42, 0x04, 0x20, 0x29, 0x8c, 0x48, 0x6d, 0x20, 0x32,
    0x08, 0x94, 0x88, 0x6c, 0x10, 0x87, 0x21, 0xc1, 0x44, 0x02,
    0x52, 0x40, 0x12, 0xdb, 0xc8, 0x24, 0x14, 0x09, 0x2c, 0x93,
    0x40, 0x09, 0x64, 0xc8, 0x4c, 0x08, 0x48, 0x70, 0xa1, 0x10,
    0x81, 0x4a, 0x80, 0x8c, 0x20, 0x03, 0x31, 0x18, 0xb3, 0x80,
    0xd3, 0x82, 0x25, 0x4c, 0x94, 0x8c, 0x1c, 0x93, 0x89, 0x1a,
    0x91, 0x51, 0xd1, 0xb6, 0x68, 0x43, 0x14, 0x25, 0x84, 0x48,
    0x61, 0x82, 0x40, 0x24, 0xdb, 0x22, 0x4d, 0x63, 0x16, 0x66,
    0x62, 0x90, 0x50, 0xa1, 0x18, 0x86, 0x49, 0x28, 0x25, 0xa0,
    0x10, 0x68, 0x8c, 0x04, 0x00, 0x08, 0x32, 0x4e, 0x22, 0x43,
    0x31, 0x42, 0x96, 0x28, 0x11, 0x23, 0x89, 0xd2, 0xc4, 0x6d,
    0x11, 0x82, 0x8d, 0x8a, 0xa8, 0x90, 0xd2, 0x06, 0x29, 0x80,
    0x82, 0x89, 0x00, 0xa8, 0x41, 0x00, 0x13, 0x6a, 0x12, 0xa8,
    0x04, 0x83, 0xc2, 0x51, 0x13, 0x09, 0x08, 0x62, 0xb4, 0x8d,
    0x94, 0xc2, 0x44, 0x5a, 0xb4, 0x08, 0x0a, 0x10, 0x48, 0xa1,
    0x28, 0x20, 0x1b, 0xb7, 0x64, 0x60, 0x24, 0x25, 0x48, 0xc0,
    0x00, 0x0a, 0x10, 0x09, 0x64, 0xb8, 0x88, 0xcb, 0x44, 0x64,
    0x54, 0x90, 0x05, 0xd2, 0xb8, 0x21, 0x49, 0x28, 0x28, 0x49,
    0x42, 0x0d, 0x63, 0xa0, 0x65, 0xcb, 0x90, 0x30, 0x51, 0x82,
    0x8d, 0x5c, 0xc6, 0x0c, 0x51, 0x06, 0x6a, 0x1a, 0x27, 0x22,
    0x01, 0xa8, 0x24, 0x61, 0xb2, 0x84, 0x23, 0x40, 0x86, 0xa3,
    0xb4, 0x48, 0x19, 0x28, 0x0c, 0x14, 0x06, 0x2e, 0xe2, 0x02,
    0x0d, 0xc4, 0x90, 0x09, 0x08, 0x06, 0x66, 0x9b, 0xc8, 0x10,
    0x5c, 0x46, 0x21, 0xca, 0xa8, 0x30, 0x83, 0x20, 0x89, 0x03,
    0x83, 0x6c, 0xa1, 0x46, 0x8c, 0x90, 0x14, 0x4c, 0x99, 0x02,
    0x81, 0x53, 0x02, 0x10, 0x8b, 0x48, 0x91, 0xe4, 0x40, 0x4a,
    0x22, 0xb1, 0x88, 0xc1, 0x06, 0x0e, 0xc3, 0xa8, 0x08, 0xc8,
    0x46, 0x92, 0x03, 0xb5, 0x4c, 0x23, 0x03, 0x0c, 0xa4, 0x06,
    0x2e, 0xdc, 0x92, 0x81, 0x0c, 0x45, 0x22, 0x40, 0x34, 0x91,
    0x90, 0x96, 0x48, 0x81, 0x82, 0x31, 0xcb, 0x16, 0x72, 0x49,
    0xc8, 0x29, 0x44, 0x86, 0x90, 0x60, 0x22, 0x4e, 0x42, 0x42,
    0x09, 0x4b, 0x82, 0x20, 0x0a, 0xb2, 0x64, 0x20, 0x86, 0x70,
    0x1a, 0xc0, 0x00, 0x1c, 0x41, 0x49, 0x89, 0x84, 0x05, 0x0c,
    0x36, 0x49, 0x19, 0x99, 0x6d, 0x00, 0x08, 0x50, 0x23, 0x96,
    0x6c, 0xe0, 0x44, 0x08, 0x98, 0x24, 0x2c, 0x0a, 0x23, 0x20,
    0x12, 0x04, 0x31, 0xc9, 0x06, 0x32, 0x14, 0x01, 0x41, 0x08,
    0x37, 0x08, 0x58, 0x00, 0x0c, 0x19, 0x04, 0x29, 0x90, 0x18,
    0x05, 0xe1, 0x88, 0x44, 0xc2, 0x20, 0x6c, 0xd1, 0x46, 0x64,
    0xd9, 0x26, 0x62, 0x09, 0x88, 0x68, 0x02, 0x29, 0x29, 0xe1,
    0x18, 0x65, 0x98, 0x04, 0x24, 0xe4, 0x34, 0x0c, 0x12, 0x85,
    0x2d, 0x20, 0x14, 0x06, 0x24, 0x15, 0x82, 0x89, 0x08, 0x91,
    0x60, 0x84, 0x28, 0x24, 0x34, 0x41, 0x1b, 0x49, 0x22, 0xd3,
    0x96, 0x64, 0x1b, 0x86, 0x4c, 0x0c, 0xb9, 0x20, 0x20, 0x39,
    0x04, 0x04, 0x34, 0x6d, 0xc1, 0x28, 0x32, 0x08, 0x14, 0x44,
    0x81, 0x18, 0x2e, 0xda, 0x38, 0x41, 0x63, 0x18, 0x26, 0xd8,
    0x48, 0x26, 0x12, 0x20, 0x21, 0x09, 0xc5, 0x25, 0x92, 0x42,
    0x0c, 0x88, 0x04, 0x64, 0x11, 0x43, 0x8a, 0x19, 0x92, 0x60,
    0x5c, 0xc6, 0x31, 0xa1, 0x24, 0x6a, 0xd8, 0xb6, 0x49, 0x1b,
    0x81, 0x90, 0xe2, 0x32, 0x4e, 0x62, 0x44, 0x21, 0x80, 0xb8,
    0x10, 0x4b, 0x90, 0x49, 0x5c, 0x06, 0x09, 0x48, 0x20, 0x49,
    0xa2, 0x92, 0x71, 0x5c, 0x48, 0x02, 0xc8, 0x08, 0x81, 0xa4,
    0x32, 0x66, 0xc9, 0x30, 0x11, 0xca, 0x92, 0x91, 0xc0, 0x00,
    0x41, 0x44, 0x98, 0x4d, 0x98, 0x12, 0x4e, 0x92, 0x46, 0x8e,
    0x49, 0xb8, 0x64, 0xdc, 0x18, 0x50, 0x51, 0xb4, 0x48, 0x08,
    0x47, 0x24, 0x08, 0x46, 0x32, 0x1b, 0x23, 0x00, 0x09, 0xb8,
    0x04, 0x0a, 0x44, 0x0c, 0x0b, 0xc7, 0x8d, 0x19, 0xa4, 0x09,
    0x11, 0x30, 0x41, 0xe3, 0x24, 0x45, 0x89, 0x1f, 0x65, 0x54,
    0xf6, 0x38, 0x04, 0x37, 0xcc, 0x89, 0xc3, 0xc5, 0xdc, 0x43,
    0xd9, 0x13, 0x56, 0x06, 0x05, 0x50, 0x29, 0x4e, 0x0f, 0xa5,
    0x5c, 0x5d, 0xd7, 0x82, 0xa1, 0x63, 0x59, 0x0d, 0x3e, 0x5b,
    0x00, 0xe6, 0x0e, 0xd8, 0x1c, 0xc7, 0xaf, 0xc0, 0x48, 0xb6,
    0x07, 0x5c, 0x65, 0x00, 0x89, 0xb3, 0x09, 0xbc, 0x4a, 0xaa,
    0xa6, 0x72, 0xbe, 0x6b, 0x9a, 0xb3, 0x5b, 0x27, 0x82, 0x65,
    0x9b, 0xc9, 0x6f, 0x19, 0x88, 0x94, 0x0b, 0x37, 0x44, 0x2f,
    0xe3, 0x9a, 0x02, 0xda, 0xff, 0x11, 0xb0, 0x48, 0x89, 0x70,
    0x8c, 0x84, 0xc2, 0xc0, 0x31, 0x4a, 0xad, 0x70, 0xe1, 0xa7,
    0x15, 0xfd, 0xb2, 0x6d, 0x93, 0xda, 0x17, 0x68, 0xc4, 0xe3,
    0xfd, 0x2c, 0x08, 0x15, 0xb9, 0xa4, 0xc5, 0x1b, 0x97, 0xc9,
    0xa3, 0xaf, 0x0d, 0x21, 0x06, 0x3d, 0xf1, 0x05, 0xd4, 0x35,
    0x80, 0x2e, 0x23, 0x99, 0xbd, 0x3a, 0x1a, 0x6c, 0xad, 0xbf,
    0x56, 0xb5, 0xd3, 0x95, 0x1b, 0x30, 0x4d, 0x56, 0xc1, 0x77,
    0xe6, 0xd6, 0xab, 0x94, 0x46, 0x68, 0xd7, 0xb8, 0xe4, 0x9d,
    0xb2, 0x8d, 0xc4, 0xd1, 0xc8, 0x92, 0xbe, 0x5d, 0x1f, 0x58,
    0x55, 0x7f, 0x11, 0x55, 0xc5, 0x2e, 0xc3, 0x9e, 0x2a, 0x29,
    0x51, 0xe8, 0x75, 0x49, 0xa7, 0xa3, 0xda, 0x0b, 0xcf, 0xf8,
    0x3f, 0x78, 0xac, 0x4c, 0x4e, 0x78, 0x6f, 0x0e, 0x67, 0xad,
    0x94, 0x59, 0x20, 0x5e, 0x37, 0x18, 0xb9, 0x09, 0x87, 0xdb,
    0xdd, 0xf0, 0xc2, 0x4d, 0x03, 0xcc, 0x98, 0x22, 0x4b, 0xe5,
    0x7d, 0x8e, 0x74, 0x7e, 0xa9, 0x1b, 0xeb, 0x7a, 0xae, 0xaf,
    0x2e, 0x7c, 0x3c, 0xc0, 0x1a, 0x30, 0x40, 0x0d, 0x79, 0x86,
    0x53, 0xcc, 0x0b, 0x2b, 0xbe, 0xa5, 0x72, 0x3b, 0xbb, 0x53,
    0x9e, 0xd5, 0xc2, 0x23, 0x1d, 0x35, 0xcd, 0x22, 0x12, 0xed,
    0x9a, 0xee, 0xc8, 0xf9, 0x05, 0x27, 0xdb, 0x46, 0x56, 0xcc,
    0x24, 0x4d, 0xee, 0xaf, 0xab, 0xa9, 0x78, 0x75, 0x75, 0xb9,
    0xd1, 0xfd, 0x39, 0x3a, 0xb2, 0xa2, 0xeb, 0x87, 0x76, 0xb2,
    0x19, 0x47, 0x88, 0xab, 0x42, 0x85, 0x4b, 0xd9, 0x76, 0x22,
    0x68, 0x4b, 0xc9, 0x88, 0x38, 0x28, 0x0a, 0x34, 0x5d, 0x12,
    0x4f, 0xf5, 0x43, 0x64, 0x44, 0x8c, 0x3c, 0xc2, 0x99, 0x91,
    0x4e, 0xfd, 0xfd, 0x9c, 0x73, 0xbf, 0x85, 0xf9, 0x9f, 0xe1,
    0x53, 0x19, 0xc8, 0x19, 0xcb, 0x7c, 0xdb, 0x9a, 0x3a, 0x2c,
    0x34, 0x55, 0x8c, 0x64, 0x6f, 0xc5, 0xb7, 0x93, 0x53, 0xb4,
    0x97, 0x7e, 0xc2, 0xf8, 0x7e, 0x8d, 0x44, 0x10, 0xca, 0x49,
    0xf5, 0x5c, 0xe8, 0xce, 0xc4, 0xcc, 0x42, 0xf0, 0x85, 0xf1,
    0xf2, 0x10, 0xa7, 0x0b, 0x37, 0x6a, 0x8e, 0x50, 0x96, 0x96,
    0x9d, 0xd9, 0x8f, 0x54, 0x45, 0x56, 0xf8, 0x64, 0x88, 0xab,
    0x51, 0x4f, 0x9f, 0x61, 0xd9, 0x12, 0x87, 0xac, 0x1d, 0xc1,
    0x23, 0xea, 0xb3, 0x5d, 0xa4, 0x6d, 0xfa, 0x58, 0x92, 0x8f,
    0x77, 0x78, 0x61, 0xe5, 0xe4, 0x33, 0xdb, 0x10, 0x2d, 0xdd,
    0xb6, 0xd7, 0xb4, 0xd0, 0x8d, 0xd1, 0xa8, 0x0b, 0x94, 0xdf,
    0xcf, 0xd7, 0xac, 0xdf, 0x47, 0x0b, 0x38, 0xe0, 0xa5, 0xf8,
    0xc3, 0xd2, 0xc3, 0xfb, 0x0f, 0x98, 0x00, 0x2b, 0x17, 0x3c,
    0x44, 0x70, 0x36, 0x47, 0x27, 0x89, 0x41, 0xcb, 0x87, 0x5a,
    0xa4, 0x2c, 0x57, 0x6d, 0x8c, 0xcb, 0xc0, 0x7d, 0x6b, 0xf5,
    0xa1, 0x17, 0x39, 0x4a, 0xb5, 0xac, 0xc6, 0x41, 0x90, 0x66,
    0x85, 0xc4, 0x4b, 0x18, 0xc6, 0xe6, 0x09, 0x6d, 0x6e, 0xbb,
    0x7f, 0x72, 0x96, 0xd3, 0x21, 0x5a, 0x96, 0xaf, 0x9e, 0xb6,
    0x0b, 0x3f, 0xe8, 0x83, 0xe5, 0x53, 0x11, 0x81, 0xc6, 0xab,
    0x40, 0xa9, 0x09, 0xb6, 0x74, 0x5e, 0xe1, 0xc3, 0x82, 0x1e,
    0xda, 0x2f, 0x24, 0xe0, 0x94, 0x8f, 0x07, 0xb7, 0x9b, 0xc6,
    0x50, 0xef, 0x3a, 0x79, 0x89, 0x4d, 0x6f, 0x16, 0x33, 0x04,
    0x24, 0x7e, 0x4a, 0xab, 0x5d, 0x03, 0x29, 0xad, 0xba, 0xa3,
    0x6c, 0xe2, 0x05, 0xab, 0x4d, 0x69, 0xb6, 0x61, 0x39, 0x9d,
    0xc3, 0x53, 0x11, 0xc0, 0xe3, 0xaa, 0x2e, 0xdc, 0x74, 0x09,
    0xbd, 0x19, 0xb5, 0xbb, 0x51, 0x1e, 0x77, 0x3e, 0xce, 0x64,
    0x13, 0xeb, 0x74, 0x03, 0xb7, 0x49, 0x99, 0xb0, 0x71, 0x99,
    0xe6, 0x17, 0x3c, 0x80, 0xe6, 0xb5, 0x51, 0xe9, 0xb3, 0xe4,
    0x2b, 0xaa, 0x52, 0x15, 0x99, 0x4e, 0x46, 0x6d, 0x67, 0x8e,
    0x79, 0xc4, 0x3c, 0xa6, 0xdc, 0x8f, 0xed, 0x87, 0xb9, 0x68,
    0x6d, 0xdc, 0x19, 0xa1, 0x52, 0x37, 0x06, 0x76, 0xad, 0xe9,
    0x61, 0x5c, 0x82, 0x16, 0x81, 0xaf, 0x3a, 0x89, 0xbf, 0x72,
    0xb0, 0xc7, 0x88, 0x3c, 0x58, 0xfe, 0xe4, 0xa5, 0x41, 0x50,
    0xfc, 0x8a, 0x15, 0xb0, 0x78, 0xd4, 0x77, 0x06, 0x4b, 0xc4,
    0x21, 0x7f, 0xaa, 0x2b, 0x88, 0x7f, 0x8c, 0x3b, 0x9b, 0xbb,
    0x2e, 0x41, 0xcf, 0x9b, 0x06, 0xd3, 0x4d, 0xcf, 0xb2, 0x9c,
    0x91, 0x46, 0x35, 0x3a, 0x5a, 0x0b, 0xe4, 0xac, 0x96, 0x7c,
    0xe0, 0xd4, 0x34, 0xe5, 0xab, 0xae, 0xa7, 0x67, 0xbf, 0x4d,
    0xab, 0x48, 0xfd, 0xcb, 0x3f, 0x5c, 0xde, 0x3f, 0x83, 0xcc,
    0x52, 0x0f, 0xdd, 0x7f, 0x20, 0x25, 0xed, 0xee, 0xd0, 0x14,
    0x38, 0xf7, 0x33, 0x4c, 0x3c, 0x5e, 0x23, 0x80, 0xa3, 0x0a,
    0xe8, 0xb0, 0xef, 0x5b, 0xca, 0xc9, 0x97, 0x13, 0x98, 0xfe,
    0x91, 0x62, 0x14, 0xa8, 0x64, 0xf6, 0x20, 0xc9, 0xc9, 0x6f,
    0x8b, 0xc0, 0xec, 0x39, 0x15, 0xa7, 0x59, 0x62, 0x68, 0x21,
    0xe1, 0x5f, 0xf6, 0xa1, 0x76, 0xb0, 0xca, 0x1b, 0x2a, 0x71,
    0xe3, 0x1a, 0x24, 0x91, 0x1f, 0x3a, 0xbb, 0xf1, 0xc9, 0x09,
    0x42, 0x48, 0x7e, 0x19, 0x1b, 0xf1, 0xf0, 0x13, 0x33, 0xf1,
    0x62, 0x31, 0x00, 0x97, 0x73, 0x9b, 0x3c, 0x26, 0xf8, 0x42,
    0xd0, 0xd4, 0x41, 0x1b, 0x9f, 0x7e, 0x43, 0x4b, 0x0b, 0x08,
    0xd7, 0xa0, 0xa8, 0x32, 0x34, 0x0a, 0xc9, 0xef, 0xb8, 0xeb,
    0xe7, 0x64, 0x3b, 0x40, 0x88, 0xe0, 0x60, 0x59, 0x07, 0xef,
    0xb9, 0x5f, 0x71, 0x92, 0x90, 0xa4, 0x5f, 0x34, 0x38, 0x93,
    0x92, 0x43, 0x87, 0xaf, 0xdd, 0x87, 0x63, 0x8c, 0x1d, 0xe5,
    0x86, 0x9e, 0xe6, 0xde, 0x94, 0xdd, 0x33, 0x5d, 0x95, 0x64,
    0xd8, 0xc4, 0x8a, 0x3c, 0xe7, 0x4b, 0xd6, 0x3f, 0xc5, 0x69,
    0x6a, 0xa8, 0x7f, 0x0f, 0x93, 0x77, 0x02, 0x46, 0x66, 0xa5,
    0xa0, 0x60, 0x8b, 0xec, 0xb1, 0xa2, 0xfc, 0x2a, 0x09, 0xb8,
    0x08, 0x1c, 0x05, 0x6b, 0x78, 0xb7, 0x7a, 0xe5, 0x60, 0xa4,
    0xaf, 0x3a, 0x9d, 0xaa, 0xf5, 0x22, 0x9b, 0x5e, 0xef, 0xc3,
    0x46, 0xed, 0x67, 0xd0, 0x8b, 0xda, 0xb4, 0xa3, 0x34, 0x32,
    0x20, 0x9d, 0x88, 0x7e, 0x43, 0x42, 0x6f, 0x02, 0xf8, 0x48,
    0x9b, 0xc5, 0x02, 0xad, 0xaa, 0xa9, 0xee, 0x19, 0x1b, 0xde,
    0x02, 0x83, 0x81, 0x10, 0xa6, 0x79, 0x4e, 0xad, 0x15, 0xf7,
    0x3e, 0x4e, 0x1e, 0x72, 0xfe, 0x52, 0x49, 0x24, 0xce, 0x82,
    0x31, 0x59, 0x72, 0xae, 0xd5, 0x34, 0x50, 0x87, 0x8b, 0xe3,
    0x8e, 0xec, 0x61, 0x35, 0x13, 0x57, 0xb1, 0xe6, 0xac, 0xfb,
    0x16, 0xc3, 0x1a, 0x98, 0x92, 0xcb, 0xcd, 0xc9, 0xf7, 0x10,
    0x6a, 0x43, 0x96, 0x33, 0x2d, 0x6f, 0x6c, 0x76, 0xb0, 0xf6,
    0x48, 0x4c, 0xae, 0x13, 0x67, 0x5d, 0x42, 0x01, 0x8e, 0x54,
    0x51, 0xcc, 0x65, 0xf1, 0x95, 0x11, 0x3c, 0x96, 0x2a, 0x5a,
    0x42, 0x3d, 0x9b, 0xbb, 0xb7, 0x7b, 0x28, 0x96, 0x09, 0xbb,
    0xed, 0x2d, 0xbc, 0xb7, 0x90, 0x62, 0xd3, 0xbe, 0xbd, 0xae,
    0x50, 0x15, 0x96, 0xc1, 0x03, 0x91, 0x14, 0x34, 0x4f, 0x21,
    0xa5, 0x6e, 0x78, 0x4a, 0x5d, 0x8b, 0xcf, 0x5b, 0x1a, 0x8a,
    0x57, 0x43, 0xb8, 0x25, 0xd3, 0xa2, 0xcd, 0x78, 0xb4, 0x93,
    0x07, 0x7a, 0x14, 0xc1, 0x0c, 0x6f, 0x5f, 0x5e, 0xcb, 0x11,
    0x17, 0x81, 0x0d, 0x7d, 0x0f, 0xda, 0xd1, 0x92, 0x43, 0x56,
    0xaf, 0x75, 0x53, 0x44, 0x1f, 0xc7, 0x9c, 0xd3, 0xc5, 0x47,
    0xe0, 0xac, 0x4a, 0x11, 0xe4, 0xfe, 0x6c, 0x80, 0x79, 0xcc,
    0x60, 0x7a, 0xd9, 0x56, 0x65, 0x83, 0x5e, 0xcf, 0x37, 0x27,
    0x55, 0xe2, 0x4d, 0xf9, 0xd6, 0x09, 0x2d, 0xee, 0xda, 0x10,
    0x6b, 0xdc, 0xd2, 0x70, 0x46, 0x94, 0xaa, 0xf5, 0x21, 0xc5,
    0xf0, 0x79, 0xdb, 0x9b, 0x8e, 0x9a, 0xdb, 0x5a, 0x56, 0x41,
    0x43, 0xe7, 0x1f, 0x8d, 0xfd, 0xda, 0x12, 0x5f, 0xf7, 0x9e,
    0x47, 0x1a, 0xf7, 0x73, 0x40, 0x67, 0xc2, 0x61, 0x07, 0x33,
    0x16, 0x78, 0x60, 0x05, 0x85, 0x5c, 0x2f, 0x2b, 0xbf, 0x2c,
    0x7a, 0x39, 0xc6, 0xed, 0xcb, 0x43, 0x66, 0x27, 0x93, 0xcd,
    0x92, 0x8d, 0x62, 0x8c, 0xaa, 0x61, 0x1c, 0x9c, 0x4c, 0x90,
    0xba, 0xba, 0x4b, 0xc1, 0xf1, 0x22, 0xde, 0xe0, 0xf9, 0x3e,
    0x04, 0xb9, 0x56, 0xa3, 0x1c, 0xe8, 0xda, 0xd6, 0x09, 0x4a,
    0x7d, 0x89, 0xbc, 0xf4, 0xe8, 0x4d, 0xa1, 0xe8, 0x34, 0x90,
    0xa5, 0x31, 0x3a, 0xec, 0x56, 0xc5, 0xd2, 0x92, 0x0b, 0xe9,
    0x58, 0xbb, 0xb2, 0x84, 0x9b, 0xa9, 0x1d, 0x19, 0xdb, 0x7a,
    0x02, 0x75, 0x79, 0x16, 0x35, 0xee, 0x3a, 0x3f, 0x4e, 0x5e,
    0x11, 0x90, 0x04, 0x03, 0xce, 0x8b, 0xa0, 0xd8, 0xc1, 0xee,
    0x52, 0x33, 0x6e, 0xd2, 0x6e, 0x06, 0x5c, 0x99, 0x24, 0x6f,
    0x16, 0xd9, 0x90, 0x28, 0xe5, 0x2d, 0x91, 0x6f, 0x1a, 0x57,
    0xf0, 0x4c, 0x7c, 0x3f, 0x7b, 0xd7, 0x30, 0xed, 0x6d, 0x21,
    0xb7, 0xf8, 0xed, 0xf3, 0x34, 0x89, 0xfa, 0xf0, 0x51, 0x6f,
    0x99, 0xa0, 0x5e, 0xf8, 0x74, 0xc7, 0x4f, 0xb5, 0x59, 0x52,
    0xbe, 0x45, 0xac, 0x3f, 0x34, 0x51, 0x87, 0x6e, 0x84, 0xea,
    0xb0, 0x40, 0xe1, 0x84, 0x16, 0x66, 0x30, 0xf1, 0x5c, 0xb2,
    0x74, 0x25, 0x03, 0xe3, 0x2e, 0x82, 0xc5, 0x60, 0x9d, 0xe4,
    0xca, 0xec, 0x49, 0x6b, 0x4e, 0x5a, 0x09, 0xa8, 0xfe, 0xff,
    0x1d, 0xa1, 0xe8, 0xec, 0x9a, 0x22, 0x3b, 0xd6, 0x72, 0x93,
    0x6f, 0x6b, 0x5a, 0xfb, 0x2d, 0x5a, 0xde, 0x01, 0x3e, 0xf6,
    0xdc, 0x77, 0x55, 0x1e, 0x32, 0x19, 0xc8, 0xa1, 0xbb, 0xcf,
    0xcb, 0x41, 0x54, 0xa2, 0xcb, 0xe6, 0x61, 0xca, 0x43, 0x63,
    0xd2, 0x2c, 0xae, 0xf4, 0xd9, 0x49, 0xb1, 0x75, 0x1a, 0x06,
    0x92, 0x13, 0x90, 0x57, 0x89, 0x8e, 0x9f, 0x26, 0xc5, 0x14,
    0xd8, 0xc7, 0x93, 0xb2, 0xaa, 0x3a, 0x9c, 0x10, 0xd5, 0x68,
    0x52, 0x28, 0x39, 0xee, 0x30, 0xdc, 0x00, 0x4b, 0x65, 0x72,
    0x59, 0x98, 0xad, 0x2e, 0x8c, 0xaf, 0x4e, 0x79, 0x0a, 0x8c,
    0x0c, 0x9d, 0xb6, 0x43, 0x26, 0x83, 0x71, 0x7b, 0x1e, 0x86,
    0x4d, 0x33, 0xd7, 0x20, 0x29, 0x6a, 0xbf, 0x2f, 0x8e, 0x4b,
    0x13, 0x35, 0x65, 0xc8, 0xec, 0xe3, 0x2c, 0xde, 0xfb, 0x30,
    0x57, 0xa9, 0x92, 0x22, 0x5d, 0x79, 0x16, 0x07, 0x73, 0x9b,
    0xe2, 0x6e, 0xd4, 0x99, 0xb4, 0x35, 0xfd, 0xa2, 0xb5, 0xd9,
    0xe5, 0x74, 0xd1, 0xb2, 0xcf, 0x32, 0xf1, 0x19, 0x69, 0xcf,
    0x1e, 0x10, 0xcc, 0x3c, 0xaf, 0xbe, 0xa4, 0x33, 0x11, 0x83,
    0x64, 0xc0, 0x39, 0xe5, 0xb0, 0x8f, 0x32, 0xf4, 0x01, 0x6a,
    0x2a, 0x11, 0x8e, 0xdd, 0x03, 0x81, 0x39, 0xe7, 0x70, 0x16,
    0x2f, 0x0e, 0x24, 0xa9, 0x12, 0x0b, 0xdb, 0xa8, 0x6c, 0xb3,
    0xf3, 0x74, 0x95, 0xca, 0x64, 0x1d, 0xee, 0x25, 0xc5, 0x27,
    0xed, 0x0f, 0x82, 0xb5, 0x7a, 0x62, 0x27, 0xb2, 0x87, 0x53,
    0x11, 0x39, 0x5e, 0xb8, 0x11, 0xca, 0x25, 0xe8, 0x17, 0x46,
    0xd3, 0x0f, 0x5d, 0x70, 0x68, 0xe1, 0x5f, 0xd1, 0xab, 0x65,
    0xe5, 0x42, 0x87, 0x1e, 0x96, 0xaf, 0x13, 0x0c, 0x9b, 0x15,
    0x75, 0x14, 0x31, 0x75, 0xcc, 0x15, 0xbf, 0x2c, 0x74, 0xab,
    0xc9, 0x9c, 0xda, 0x62, 0x1d, 0xeb, 0x19, 0x81, 0x67, 0x5e,
    0xcd, 0x54, 0x87, 0x07, 0x67, 0xba, 0xe3, 0xf6, 0x03, 0xbe,
    0x6d, 0x64, 0x2d, 0xbc, 0xec, 0x54, 0x13, 0x12, 0x5b, 0x44,
    0x90, 0x95, 0x86, 0x77, 0x8c, 0x59, 0xbd, 0x8e, 0xba, 0xb1,
    0x12, 0xea, 0xc1, 0x94, 0x37, 0xa0, 0x11, 0xff, 0xb2, 0xa4,
    0xc3, 0x61, 0xf2, 0xa3, 0x49, 0xbe, 0xe7, 0xb6, 0x96, 0x2f,
};
static const int sizeof_bench_dilithium_level2_key = sizeof(bench_dilithium_level2_key);

#endif /* !WOLFSSL_DILITHIUM_NO_SIGN */

#ifndef WOLFSSL_DILITHIUM_NO_VERIFY

/* raw public key without ASN1 syntax from
 * ./certs/dilithium/bench_dilithium_level2_key.der */
static const unsigned char bench_dilithium_level2_pubkey[] = {
    0xea, 0x05, 0x24, 0x0d, 0x80, 0x72, 0x25, 0x55, 0xf4, 0x5b,
    0xc2, 0x13, 0x8b, 0x87, 0x5d, 0x31, 0x99, 0x2f, 0x1d, 0xa9,
    0x41, 0x09, 0x05, 0x76, 0xa7, 0xb7, 0x5e, 0x8c, 0x44, 0xe2,
    0x64, 0x79, 0xa0, 0xec, 0x1f, 0x24, 0xb6, 0xc8, 0x05, 0x5b,
    0xc1, 0x18, 0xb0, 0xb7, 0xcf, 0x8c, 0x60, 0x67, 0x6b, 0x81,
    0x44, 0x27, 0xb6, 0x0e, 0xfd, 0x9b, 0xc3, 0xcb, 0x52, 0x31,
    0xfa, 0xc9, 0x34, 0x8d, 0x22, 0x1e, 0x07, 0x9d, 0x96, 0x6a,
    0x63, 0x83, 0x5c, 0xd7, 0x83, 0x2d, 0x7f, 0x48, 0x64, 0x79,
    0xca, 0xb4, 0x9f, 0xa2, 0x02, 0xb7, 0x86, 0x1d, 0x0e, 0xc7,
    0xf9, 0x6c, 0x07, 0xc0, 0x35, 0x6a, 0x34, 0x79, 0x7c, 0xb8,
    0x0f, 0xed, 0x98, 0x50, 0xfb, 0x51, 0xe0, 0x36, 0x44, 0x4c,
    0xc6, 0x35, 0xa2, 0xbb, 0x55, 0xb0, 0x5c, 0x39, 0x08, 0x02,
    0x20, 0x35, 0x5c, 0x56, 0x6d, 0x2e, 0xb9, 0xef, 0x21, 0x26,
    0x87, 0x87, 0x85, 0x8a, 0x32, 0xb5, 0xa7, 0x68, 0x70, 0x3a,
    0xfd, 0x0d, 0x21, 0x48, 0x91, 0xa3, 0x29, 0xc1, 0x2a, 0x38,
    0xe5, 0x26, 0x31, 0x1f, 0x42, 0xde, 0x0b, 0x25, 0xff, 0x1d,
    0x6b, 0xb4, 0xe0, 0x5d, 0x2d, 0xcf, 0x44, 0xd5, 0x7d, 0xc4,
    0xf6, 0x95, 0xf2, 0x06, 0x4f, 0x83, 0x88, 0x9d, 0x1e, 0xeb,
    0x1c, 0x09, 0x45, 0x62, 0x67, 0x3d, 0xff, 0x51, 0x47, 0xe8,
    0xbc, 0x9b, 0x03, 0x1f, 0xc7, 0x72, 0x65, 0xce, 0xa8, 0x8c,
    0xc2, 0xa0, 0xc2, 0xbd, 0x5b, 0x7c, 0x17, 0x16, 0x8b, 0x72,
    0xfa, 0xb1, 0xbd, 0xdf, 0x49, 0xd6, 0xa1, 0x00, 0x65, 0xbe,
    0x82, 0xe7, 0x68, 0xc7, 0xe7, 0xbc, 0xc2, 0xa4, 0xdb, 0xaa,
    0xcc, 0xea, 0x41, 0x52, 0x7f, 0x56, 0xb4, 0x68, 0x1f, 0x92,
    0x96, 0x0f, 0xce, 0xd4, 0xd0, 0x87, 0x4c, 0x4a, 0x73, 0xb5,
    0x6c, 0xd4, 0x69, 0x55, 0x15, 0x47, 0xdc, 0x94, 0x7f, 0xd2,
    0x54, 0x5e, 0xb2, 0x90, 0xc2, 0x47, 0xe4, 0xf5, 0xde, 0x8b,
    0x9b, 0xc6, 0x5d, 0x50, 0x95, 0x60, 0xe0, 0xf0, 0xa7, 0x4e,
    0xe0, 0xcd, 0x41, 0x09, 0xef, 0xb3, 0x3d, 0x90, 0x5c, 0x77,
    0x54, 0xec, 0x9e, 0x5d, 0x8a, 0xe7, 0x09, 0x5c, 0xc9, 0x58,
    0x0c, 0xd0, 0x42, 0x35, 0xd2, 0x14, 0x59, 0x38, 0x69, 0xad,
    0xf9, 0xb5, 0xbf, 0x8a, 0x8e, 0x33, 0xd8, 0x5e, 0x7a, 0x55,
    0xd0, 0x53, 0x15, 0x40, 0x4e, 0xc5, 0x86, 0xd7, 0x8f, 0x5f,
    0x2f, 0x55, 0x82, 0xc2, 0x4f, 0x16, 0xe5, 0xea, 0x1c, 0xbc,
    0xff, 0x5e, 0x1f, 0x39, 0x46, 0x70, 0x54, 0x7a, 0x3a, 0x27,
    0x16, 0x1a, 0x2b, 0x6c, 0xd2, 0xb7, 0x80, 0xd3, 0xd1, 0x9d,
    0x25, 0x59, 0xed, 0xe6, 0x51, 0xb1, 0xf2, 0xad, 0x7e, 0x51,
    0x78, 0x14, 0x2b, 0x19, 0xae, 0x64, 0x72, 0x0f, 0xd8, 0x18,
    0x79, 0x8e, 0x66, 0x88, 0xd3, 0xa4, 0xa3, 0xc3, 0x76, 0x21,
    0xcb, 0xe4, 0x79, 0x5e, 0x95, 0x74, 0xe3, 0x31, 0x18, 0x79,
    0xed, 0xc7, 0xe7, 0xfb, 0x86, 0x48, 0x1b, 0x7b, 0x75, 0x5b,
    0x7f, 0x7c, 0x82, 0xc5, 0xab, 0x11, 0xb4, 0x5d, 0x59, 0x6f,
    0x78, 0xb2, 0xa5, 0x39, 0xc6, 0x63, 0x38, 0x6c, 0xeb, 0x50,
    0x06, 0x14, 0x76, 0xf0, 0xe8, 0xfb, 0x11, 0x95, 0x1f, 0x9d,
    0x9c, 0xa6, 0xe1, 0xe2, 0x0d, 0xa3, 0x66, 0xfc, 0x20, 0x83,
    0x50, 0x0e, 0x53, 0x75, 0xb5, 0x12, 0xf4, 0xdf, 0x31, 0x46,
    0x83, 0xac, 0x5b, 0xf3, 0x99, 0xa6, 0xd1, 0x7b, 0x2b, 0xc5,
    0xdc, 0x71, 0x07, 0x27, 0x33, 0x35, 0x34, 0xf5, 0x30, 0x19,
    0xc1, 0x3b, 0xba, 0x8a, 0xaf, 0x7e, 0x49, 0x93, 0x48, 0x5b,
    0x38, 0xc0, 0xbc, 0x2e, 0xc7, 0x59, 0x1b, 0xd9, 0xf5, 0xcc,
    0x86, 0xf5, 0x7b, 0x4d, 0xd7, 0x39, 0xa7, 0xa2, 0x56, 0x20,
    0x48, 0x98, 0x7d, 0x4f, 0x75, 0x56, 0x9b, 0xb8, 0x95, 0x45,
    0x17, 0xf3, 0x86, 0x3d, 0x97, 0x0a, 0x49, 0x1b, 0xca, 0xff,
    0x20, 0xc0, 0x24, 0x2c, 0x51, 0xc2, 0x0a, 0x3c, 0xbf, 0x07,
    0x60, 0x1c, 0x88, 0x85, 0x9b, 0x85, 0x2d, 0x4a, 0xfe, 0x5a,
    0x1c, 0x90, 0xf5, 0x90, 0x12, 0xd3, 0x03, 0x3c, 0x8c, 0x2e,
    0x95, 0x4a, 0x47, 0x76, 0x0f, 0x1f, 0x5d, 0x9e, 0xed, 0xc5,
    0x64, 0xc4, 0x9b, 0xbf, 0x86, 0xc5, 0x63, 0x84, 0x33, 0x00,
    0xf1, 0x26, 0x18, 0x21, 0xf3, 0x88, 0x1a, 0x08, 0x18, 0x6d,
    0x2f, 0xef, 0xd5, 0xeb, 0x2f, 0x69, 0xc8, 0x6e, 0x92, 0x34,
    0xfc, 0x72, 0x3d, 0x9a, 0xa7, 0x9e, 0x51, 0xfb, 0x56, 0xe3,
    0xdc, 0xf4, 0x8f, 0x9b, 0x6d, 0x0d, 0x2a, 0xec, 0x66, 0x12,
    0x26, 0x35, 0xbd, 0x61, 0xc2, 0x67, 0x19, 0xf5, 0x7e, 0xa1,
    0x67, 0xa2, 0x9c, 0x3b, 0x67, 0xb0, 0xc2, 0x51, 0x6a, 0x37,
    0x7c, 0x48, 0xe9, 0x4b, 0xb9, 0xa3, 0x38, 0x2f, 0xfc, 0xde,
    0xb4, 0x7c, 0xda, 0x52, 0x84, 0x0b, 0xb0, 0xd9, 0x08, 0xe9,
    0x7a, 0x4a, 0x6f, 0x79, 0x29, 0x3d, 0xc4, 0x5c, 0x78, 0xee,
    0x63, 0xb6, 0x96, 0x68, 0xd9, 0x82, 0x4e, 0xc1, 0x1b, 0x6f,
    0x52, 0xf5, 0xb3, 0xfb, 0xe8, 0xc4, 0x2a, 0x07, 0xc6, 0x3b,
    0x85, 0x0d, 0xf4, 0xbf, 0xb0, 0x6b, 0xfb, 0xce, 0x1d, 0xb4,
    0xbf, 0x63, 0x0b, 0x91, 0x67, 0xc4, 0xa3, 0x06, 0xa4, 0xaf,
    0x6c, 0xd3, 0xe5, 0x8b, 0x87, 0x4e, 0x64, 0x9c, 0xb1, 0xf3,
    0x70, 0x7c, 0x68, 0x43, 0x46, 0x13, 0x46, 0xee, 0x27, 0x75,
    0x12, 0x45, 0x42, 0xde, 0xa5, 0x8d, 0xcf, 0xf7, 0x09, 0x87,
    0xa8, 0x80, 0x3d, 0xb6, 0x45, 0xee, 0x41, 0x2d, 0x7c, 0x45,
    0x01, 0x9d, 0xaa, 0x78, 0xa8, 0x10, 0xa4, 0xfd, 0xb5, 0x5f,
    0xee, 0x0f, 0x77, 0xba, 0x73, 0xff, 0x49, 0xdc, 0xfa, 0x39,
    0xd6, 0xa3, 0x6f, 0x25, 0xb9, 0x63, 0x2c, 0x92, 0xc5, 0xdf,
    0xfb, 0xba, 0x89, 0xf9, 0xfa, 0x94, 0x5b, 0x6f, 0x5a, 0x4d,
    0x1c, 0xe4, 0xc9, 0x10, 0xf9, 0xa0, 0xe8, 0xc4, 0xcb, 0x55,
    0x1a, 0xdb, 0x56, 0x5f, 0x8e, 0x91, 0x03, 0x23, 0xca, 0xb0,
    0x1f, 0xef, 0xb8, 0x6c, 0x13, 0x5a, 0x99, 0x25, 0xf0, 0x49,
    0xa9, 0x5a, 0x45, 0xf7, 0xfd, 0x1a, 0xc2, 0x71, 0x06, 0xe3,
    0x2d, 0x25, 0x64, 0xb0, 0x52, 0x12, 0x03, 0x62, 0xc7, 0xb6,
    0xf9, 0xdc, 0x1f, 0x78, 0xff, 0x8b, 0xfa, 0xde, 0x7f, 0x71,
    0xa6, 0x35, 0x3e, 0xac, 0x20, 0x54, 0x94, 0xa7, 0x2e, 0x9d,
    0x47, 0x17, 0x4b, 0xad, 0x92, 0xb3, 0x14, 0x26, 0x8c, 0x5a,
    0xd0, 0x16, 0x4b, 0x22, 0xe9, 0x0c, 0x79, 0x6b, 0x8e, 0xac,
    0x0d, 0x12, 0xf5, 0x66, 0x8e, 0x82, 0x1a, 0x44, 0xf3, 0xe9,
    0x56, 0x5a, 0xcd, 0x1c, 0x1b, 0x81, 0x7b, 0x63, 0x59, 0xfe,
    0xc8, 0xc0, 0xe3, 0xda, 0x16, 0x6b, 0x6f, 0x0d, 0xba, 0x0e,
    0x47, 0x12, 0x86, 0x9e, 0xf0, 0x3b, 0x4d, 0x87, 0x3b, 0xf2,
    0x75, 0x73, 0x2d, 0xdf, 0xca, 0x76, 0x0b, 0xbd, 0xe7, 0xb7,
    0x74, 0x24, 0xf3, 0xc6, 0xe6, 0x75, 0x3f, 0x8b, 0x6a, 0xd9,
    0xad, 0xed, 0xc0, 0x70, 0x04, 0x1e, 0x0b, 0x8e, 0x8b, 0x7f,
    0xea, 0xbc, 0x39, 0x6b, 0x8a, 0x44, 0xa6, 0x9a, 0x2d, 0x0d,
    0x8c, 0x21, 0x60, 0x09, 0xd2, 0x4a, 0xe0, 0x62, 0xcf, 0xfa,
    0xe8, 0x9b, 0x35, 0x6f, 0x23, 0x2f, 0xb5, 0x65, 0x08, 0x60,
    0x92, 0x15, 0xd0, 0x5b, 0x63, 0xcc, 0x65, 0x05, 0xd1, 0xef,
    0x0f, 0x7e, 0x1b, 0xb3, 0x8e, 0xc6, 0x12, 0x85, 0xc9, 0x82,
    0x53, 0x79, 0x2e, 0x80, 0x5f, 0x0c, 0x7b, 0xc7, 0x1c, 0x83,
    0x41, 0x06, 0xd8, 0x41, 0xc9, 0xe7, 0xb9, 0x4b, 0xa1, 0x61,
    0xc6, 0x86, 0x67, 0xf5, 0x10, 0xf7, 0x34, 0x0d, 0x39, 0x9e,
    0x2b, 0x5f, 0x19, 0x06, 0x02, 0xa5, 0x02, 0x23, 0x71, 0xc2,
    0x12, 0x65, 0xcc, 0x81, 0x06, 0xfd, 0x8d, 0x09, 0x68, 0x37,
    0x06, 0x3b, 0xff, 0xc4, 0x24, 0xb3, 0x1f, 0xd6, 0xe6, 0x8f,
    0x9c, 0x74, 0x2c, 0x5e, 0xc5, 0xf4, 0xe9, 0xeb, 0xca, 0xd3,
    0x04, 0x5b, 0x92, 0x9e, 0x5c, 0x1a, 0x1d, 0xa1, 0xa7, 0x34,
    0xd2, 0x05, 0xae, 0xdb, 0x3d, 0x71, 0x10, 0x6e, 0x30, 0xd9,
    0xa3, 0x44, 0xa0, 0xbd, 0x9e, 0x7b, 0xb5, 0x12, 0x8a, 0x12,
    0x07, 0x60, 0xd7, 0x1f, 0x92, 0xe6, 0xfe, 0x04, 0xa9, 0x3e,
    0x62, 0x64, 0x00, 0x5f, 0x7c, 0x7b, 0x34, 0x09, 0xeb, 0x4a,
    0x18, 0x9e, 0x77, 0x72, 0x3a, 0x31, 0x1a, 0x62, 0x2a, 0xb5,
    0xcb, 0x4e, 0x53, 0xce, 0xad, 0x8b, 0x5a, 0x20, 0x4f, 0xd7,
    0x3e, 0x16, 0xf8, 0x10, 0xe2, 0xae, 0xbd, 0x3f, 0x02, 0xa9,
    0x18, 0xa0, 0x01, 0x18, 0x84, 0x95, 0x22, 0x2e, 0x93, 0x76,
    0x44, 0x4e, 0x11, 0x7b, 0x03, 0x51, 0x50, 0x19, 0x79, 0xe7,
    0xbb, 0x5c, 0x7b, 0xca, 0x74, 0xb4, 0x25, 0x26, 0xdb, 0x66,
    0xaa, 0x0b, 0x21, 0x07, 0xfb, 0x7a, 0x96, 0x10, 0x7d, 0x99,
    0xa9, 0x16, 0xcb, 0x0e, 0xba, 0x63, 0xab, 0x95, 0xfc, 0x5a,
    0xbe, 0xa6, 0x7f, 0xd8, 0xb4, 0xcd, 0x7c, 0xc5, 0xd0, 0xb1,
    0x1b, 0x48, 0x40, 0xfb, 0xe6, 0x2f, 0x2b, 0x94, 0xfe, 0x68,
    0xa2, 0xc4, 0x36, 0xd9, 0xcd, 0xc1, 0x93, 0x6d, 0xef, 0x39,
    0x5e, 0x43, 0x30, 0x5a, 0x2e, 0x66, 0xb6, 0xf2, 0xed, 0x9a,
    0x8d, 0x12, 0xdf, 0x5c, 0xae, 0xad, 0x16, 0x12, 0x7e, 0x81,
    0x82, 0x91, 0x7d, 0x2b, 0x12, 0xe9, 0x96, 0xb8, 0xb7, 0x42,
    0xcb, 0x1f, 0xf8, 0xd1, 0xfd, 0x83, 0x7a, 0xe4, 0x36, 0x1d,
    0x04, 0x27, 0x4c, 0xe5, 0xbd, 0x75, 0x24, 0xf7, 0xbd, 0xb6,
    0x6a, 0x68, 0x4e, 0x2c, 0x1b, 0x56, 0x3e, 0x60, 0xa4, 0x42,
    0xca, 0x7a, 0x54, 0xe5, 0x06, 0xe3, 0xda, 0x05, 0xf7, 0x77,
    0x36, 0x8b, 0x81, 0x26, 0x99, 0x92, 0x42, 0xda, 0x45, 0xb1,
    0xfe, 0x4b,
};
static const int sizeof_bench_dilithium_level2_pubkey =
    sizeof(bench_dilithium_level2_pubkey);

#endif /* !WOLFSSL_DILITHIUM_NO_VERIFY */

#ifndef WOLFSSL_DILITHIUM_NO_SIGN

/* raw private key without ASN1 syntax from
 * ./certs/dilithium/bench_dilithium_level3_key.der */
static const unsigned char bench_dilithium_level3_key[] = {
    0x15, 0xc9, 0xe5, 0x53, 0x2f, 0xd8, 0x1f, 0xb4, 0xa3, 0x9f,
    0xae, 0xad, 0xb3, 0x10, 0xd0, 0x72, 0x69, 0xd3, 0x02, 0xf3,
    0xdf, 0x67, 0x5a, 0x31, 0x52, 0x19, 0xca, 0x39, 0x27, 0x77,
    0x61, 0x6d, 0x72, 0xdd, 0x85, 0x06, 0xf6, 0x94, 0x0a, 0x57,
    0x52, 0xcd, 0xac, 0x83, 0x4a, 0xe5, 0xbe, 0xa4, 0x30, 0x79,
    0x9e, 0xc6, 0xd6, 0x04, 0xc8, 0x73, 0xdc, 0x5e, 0x41, 0x75,
    0x2f, 0xac, 0x76, 0x57, 0x03, 0x08, 0x46, 0xcb, 0xaf, 0x4c,
    0x6a, 0x4f, 0x20, 0x18, 0xb3, 0x2e, 0x11, 0x54, 0xb5, 0x94,
    0xe6, 0x6f, 0x76, 0xf6, 0xb9, 0x73, 0x9a, 0x07, 0x73, 0xe8,
    0x90, 0xd1, 0x04, 0xda, 0xc5, 0x97, 0xb9, 0x52, 0x51, 0xc8,
    0xc9, 0xcc, 0x87, 0x29, 0xa1, 0xde, 0x79, 0x9b, 0xf8, 0x7f,
    0x80, 0x3f, 0xfd, 0xb3, 0x24, 0xa5, 0xba, 0xf5, 0xd6, 0xd4,
    0x07, 0xbd, 0xa7, 0x1b, 0xd0, 0xe1, 0xd0, 0x43, 0x14, 0x52,
    0x27, 0x03, 0x33, 0x76, 0x00, 0x67, 0x30, 0x23, 0x76, 0x34,
    0x72, 0x02, 0x41, 0x62, 0x12, 0x43, 0x86, 0x30, 0x18, 0x28,
    0x46, 0x27, 0x45, 0x20, 0x88, 0x33, 0x54, 0x10, 0x03, 0x81,
    0x44, 0x50, 0x06, 0x44, 0x56, 0x30, 0x37, 0x38, 0x38, 0x46,
    0x03, 0x85, 0x01, 0x86, 0x43, 0x80, 0x78, 0x28, 0x83, 0x55,
    0x37, 0x44, 0x80, 0x12, 0x17, 0x51, 0x78, 0x46, 0x22, 0x01,
    0x53, 0x54, 0x63, 0x87, 0x77, 0x38, 0x11, 0x81, 0x43, 0x30,
    0x15, 0x47, 0x66, 0x11, 0x40, 0x65, 0x70, 0x56, 0x62, 0x28,
    0x21, 0x65, 0x30, 0x45, 0x63, 0x53, 0x31, 0x80, 0x81, 0x71,
    0x23, 0x62, 0x85, 0x03, 0x07, 0x56, 0x16, 0x28, 0x18, 0x35,
    0x07, 0x38, 0x60, 0x68, 0x17, 0x30, 0x15, 0x20, 0x04, 0x13,
    0x13, 0x61, 0x51, 0x58, 0x00, 0x37, 0x51, 0x58, 0x14, 0x06,
    0x12, 0x55, 0x13, 0x46, 0x76, 0x05, 0x51, 0x87, 0x32, 0x62,
    0x50, 0x41, 0x88, 0x24, 0x50, 0x31, 0x65, 0x36, 0x31, 0x02,
    0x75, 0x35, 0x78, 0x27, 0x36, 0x08, 0x01, 0x77, 0x22, 0x77,
    0x30, 0x80, 0x11, 0x21, 0x28, 0x26, 0x68, 0x27, 0x13, 0x70,
    0x50, 0x44, 0x88, 0x20, 0x50, 0x67, 0x65, 0x74, 0x17, 0x46,
    0x50, 0x16, 0x42, 0x75, 0x35, 0x12, 0x60, 0x12, 0x17, 0x13,
    0x36, 0x72, 0x04, 0x77, 0x07, 0x55, 0x20, 0x27, 0x15, 0x02,
    0x25, 0x12, 0x57, 0x71, 0x37, 0x45, 0x43, 0x34, 0x40, 0x31,
    0x78, 0x50, 0x31, 0x28, 0x17, 0x84, 0x87, 0x43, 0x25, 0x75,
    0x58, 0x05, 0x61, 0x56, 0x41, 0x44, 0x57, 0x67, 0x85, 0x54,
    0x00, 0x88, 0x88, 0x50, 0x68, 0x11, 0x14, 0x42, 0x08, 0x74,
    0x73, 0x00, 0x38, 0x08, 0x45, 0x28, 0x62, 0x43, 0x36, 0x20,
    0x30, 0x10, 0x87, 0x83, 0x67, 0x62, 0x02, 0x48, 0x46, 0x50,
    0x08, 0x08, 0x41, 0x43, 0x78, 0x22, 0x65, 0x87, 0x43, 0x84,
    0x25, 0x36, 0x58, 0x64, 0x30, 0x10, 0x20, 0x68, 0x82, 0x47,
    0x60, 0x31, 0x76, 0x68, 0x74, 0x68, 0x75, 0x61, 0x16, 0x26,
    0x82, 0x50, 0x32, 0x61, 0x41, 0x22, 0x38, 0x20, 0x86, 0x75,
    0x74, 0x00, 0x77, 0x12, 0x81, 0x35, 0x51, 0x78, 0x88, 0x64,
    0x82, 0x00, 0x41, 0x55, 0x62, 0x87, 0x51, 0x41, 0x74, 0x51,
    0x53, 0x27, 0x33, 0x84, 0x68, 0x86, 0x57, 0x60, 0x44, 0x30,
    0x22, 0x32, 0x10, 0x52, 0x22, 0x83, 0x48, 0x53, 0x66, 0x74,
    0x14, 0x52, 0x32, 0x71, 0x41, 0x08, 0x83, 0x67, 0x41, 0x38,
    0x46, 0x80, 0x88, 0x14, 0x84, 0x30, 0x85, 0x35, 0x46, 0x20,
    0x54, 0x84, 0x56, 0x84, 0x54, 0x82, 0x14, 0x11, 0x52, 0x07,
    0x86, 0x46, 0x05, 0x82, 0x26, 0x85, 0x75, 0x07, 0x88, 0x75,
    0x51, 0x17, 0x54, 0x32, 0x68, 0x66, 0x08, 0x23, 0x66, 0x06,
    0x42, 0x28, 0x00, 0x84, 0x27, 0x27, 0x43, 0x47, 0x12, 0x27,
    0x13, 0x15, 0x17, 0x74, 0x85, 0x14, 0x12, 0x62, 0x06, 0x47,
    0x17, 0x60, 0x00, 0x10, 0x85, 0x16, 0x55, 0x64, 0x46, 0x62,
    0x77, 0x05, 0x51, 0x23, 0x52, 0x37, 0x51, 0x78, 0x35, 0x66,
    0x14, 0x15, 0x78, 0x40, 0x16, 0x54, 0x67, 0x30, 0x61, 0x24,
    0x26, 0x86, 0x56, 0x83, 0x62, 0x78, 0x88, 0x83, 0x50, 0x06,
    0x13, 0x21, 0x33, 0x73, 0x16, 0x44, 0x86, 0x77, 0x65, 0x28,
    0x12, 0x40, 0x62, 0x54, 0x55, 0x84, 0x00, 0x11, 0x77, 0x38,
    0x71, 0x51, 0x38, 0x32, 0x33, 0x67, 0x15, 0x77, 0x24, 0x33,
    0x44, 0x11, 0x05, 0x65, 0x13, 0x03, 0x72, 0x63, 0x81, 0x58,
    0x08, 0x03, 0x34, 0x23, 0x61, 0x00, 0x02, 0x63, 0x86, 0x40,
    0x03, 0x71, 0x34, 0x27, 0x45, 0x10, 0x34, 0x26, 0x83, 0x28,
    0x31, 0x35, 0x26, 0x05, 0x58, 0x41, 0x11, 0x10, 0x65, 0x35,
    0x22, 0x42, 0x28, 0x88, 0x46, 0x06, 0x57, 0x33, 0x88, 0x46,
    0x04, 0x86, 0x88, 0x88, 0x51, 0x74, 0x82, 0x27, 0x58, 0x14,
    0x11, 0x08, 0x13, 0x16, 0x61, 0x16, 0x14, 0x44, 0x83, 0x85,
    0x71, 0x44, 0x55, 0x82, 0x16, 0x62, 0x85, 0x05, 0x43, 0x41,
    0x73, 0x53, 0x60, 0x01, 0x80, 0x68, 0x33, 0x13, 0x43, 0x44,
    0x73, 0x36, 0x65, 0x35, 0x22, 0x26, 0x13, 0x31, 0x36, 0x83,
    0x30, 0x27, 0x15, 0x11, 0x54, 0x53, 0x24, 0x84, 0x75, 0x24,
    0x72, 0x78, 0x34, 0x24, 0x35, 0x80, 0x06, 0x38, 0x88, 0x11,
    0x41, 0x01, 0x34, 0x87, 0x77, 0x20, 0x14, 0x50, 0x55, 0x12,
    0x17, 0x48, 0x87, 0x74, 0x58, 0x42, 0x31, 0x46, 0x36, 0x37,
    0x26, 0x50, 0x04, 0x75, 0x77, 0x15, 0x41, 0x53, 0x04, 0x04,
    0x26, 0x61, 0x65, 0x87, 0x55, 0x56, 0x07, 0x81, 0x28, 0x21,
    0x41, 0x61, 0x41, 0x50, 0x17, 0x47, 0x25, 0x50, 0x20, 0x83,
    0x46, 0x87, 0x18, 0x45, 0x40, 0x21, 0x06, 0x08, 0x12, 0x25,
    0x71, 0x13, 0x35, 0x55, 0x54, 0x61, 0x00, 0x52, 0x74, 0x78,
    0x13, 0x84, 0x55, 0x40, 0x14, 0x40, 0x78, 0x12, 0x88, 0x43,
    0x33, 0x24, 0x66, 0x88, 0x22, 0x44, 0x15, 0x37, 0x81, 0x27,
    0x84, 0x18, 0x28, 0x11, 0x58, 0x51, 0x71, 0x21, 0x02, 0x83,
    0x70, 0x48, 0x32, 0x46, 0x00, 0x70, 0x17, 0x30, 0x63, 0x21,
    0x46, 0x60, 0x50, 0x72, 0x77, 0x45, 0x83, 0x75, 0x26, 0x31,
    0x47, 0x34, 0x47, 0x84, 0x87, 0x63, 0x22, 0x83, 0x21, 0x10,
    0x21, 0x51, 0x47, 0x46, 0x31, 0x06, 0x57, 0x82, 0x65, 0x24,
    0x61, 0x66, 0x24, 0x68, 0x14, 0x03, 0x43, 0x41, 0x04, 0x14,
    0x47, 0x61, 0x57, 0x87, 0x43, 0x83, 0x43, 0x25, 0x87, 0x36,
    0x72, 0x51, 0x38, 0x51, 0x54, 0x54, 0x84, 0x40, 0x15, 0x30,
    0x35, 0x34, 0x43, 0x61, 0x63, 0x42, 0x77, 0x31, 0x42, 0x06,
    0x61, 0x03, 0x01, 0x41, 0x08, 0x84, 0x02, 0x65, 0x04, 0x72,
    0x32, 0x00, 0x21, 0x10, 0x54, 0x73, 0x04, 0x42, 0x48, 0x11,
    0x74, 0x18, 0x63, 0x73, 0x28, 0x61, 0x36, 0x80, 0x20, 0x86,
    0x24, 0x42, 0x16, 0x11, 0x71, 0x83, 0x78, 0x38, 0x82, 0x47,
    0x67, 0x18, 0x56, 0x86, 0x85, 0x66, 0x18, 0x24, 0x50, 0x74,
    0x72, 0x02, 0x66, 0x83, 0x63, 0x08, 0x25, 0x32, 0x15, 0x78,
    0x33, 0x08, 0x34, 0x44, 0x08, 0x28, 0x10, 0x25, 0x40, 0x11,
    0x04, 0x76, 0x60, 0x16, 0x65, 0x16, 0x13, 0x30, 0x53, 0x14,
    0x77, 0x06, 0x06, 0x88, 0x64, 0x47, 0x08, 0x23, 0x11, 0x56,
    0x46, 0x61, 0x48, 0x64, 0x73, 0x66, 0x07, 0x65, 0x41, 0x24,
    0x67, 0x45, 0x42, 0x18, 0x62, 0x01, 0x70, 0x88, 0x03, 0x77,
    0x22, 0x85, 0x77, 0x02, 0x85, 0x03, 0x65, 0x15, 0x57, 0x51,
    0x28, 0x72, 0x53, 0x32, 0x05, 0x58, 0x84, 0x54, 0x03, 0x81,
    0x63, 0x23, 0x38, 0x27, 0x01, 0x85, 0x61, 0x12, 0x28, 0x62,
    0x22, 0x67, 0x56, 0x66, 0x63, 0x08, 0x74, 0x63, 0x21, 0x01,
    0x46, 0x10, 0x08, 0x18, 0x07, 0x86, 0x47, 0x70, 0x50, 0x25,
    0x45, 0x06, 0x55, 0x88, 0x46, 0x11, 0x23, 0x84, 0x70, 0x02,
    0x24, 0x88, 0x52, 0x60, 0x12, 0x72, 0x63, 0x05, 0x81, 0x21,
    0x26, 0x07, 0x64, 0x03, 0x56, 0x48, 0x27, 0x04, 0x38, 0x86,
    0x25, 0x65, 0x21, 0x25, 0x77, 0x21, 0x62, 0x28, 0x82, 0x71,
    0x85, 0x73, 0x78, 0x24, 0x78, 0x51, 0x61, 0x02, 0x81, 0x14,
    0x67, 0x61, 0x08, 0x88, 0x31, 0x77, 0x06, 0x24, 0x45, 0x13,
    0x67, 0x67, 0x54, 0x67, 0x00, 0x12, 0x62, 0x54, 0x11, 0x27,
    0x51, 0x48, 0x07, 0x33, 0x01, 0x24, 0x04, 0x64, 0x11, 0x83,
    0x18, 0x52, 0x55, 0x23, 0x24, 0x58, 0x53, 0x78, 0x30, 0x43,
    0x31, 0x76, 0x62, 0x01, 0x08, 0x73, 0x21, 0x32, 0x12, 0x78,
    0x22, 0x68, 0x33, 0x45, 0x33, 0x73, 0x02, 0x74, 0x21, 0x81,
    0x02, 0x16, 0x54, 0x31, 0x55, 0x76, 0x25, 0x76, 0x41, 0x36,
    0x75, 0x22, 0x78, 0x16, 0x60, 0x48, 0x58, 0x28, 0x83, 0x50,
    0x88, 0x66, 0x72, 0x70, 0x21, 0x21, 0x24, 0x16, 0x62, 0x57,
    0x20, 0x13, 0x80, 0x61, 0x15, 0x45, 0x42, 0x86, 0x00, 0x25,
    0x77, 0x58, 0x84, 0x01, 0x66, 0x16, 0x46, 0x56, 0x68, 0x57,
    0x12, 0x20, 0x75, 0x60, 0x41, 0x85, 0x02, 0x88, 0x12, 0x68,
    0x20, 0x02, 0x41, 0x18, 0x87, 0x13, 0x17, 0x33, 0x74, 0x11,
    0x08, 0x37, 0x47, 0x08, 0x31, 0x67, 0x08, 0x50, 0x61, 0x54,
    0x56, 0x71, 0x63, 0x26, 0x85, 0x22, 0x07, 0x87, 0x71, 0x28,
    0x20, 0x47, 0x48, 0x66, 0x54, 0x38, 0x03, 0x41, 0x38, 0x21,
    0x70, 0x50, 0x66, 0x53, 0x56, 0x70, 0x74, 0x55, 0x70, 0x28,
    0x52, 0x01, 0x42, 0x65, 0x53, 0x73, 0x32, 0x33, 0x67, 0x42,
    0x67, 0x85, 0x18, 0x45, 0x12, 0x37, 0x58, 0x82, 0x13, 0x73,
    0x78, 0x77, 0x03, 0x42, 0x04, 0x65, 0x55, 0x66, 0x07, 0x25,
    0x07, 0x37, 0x40, 0x78, 0x66, 0x71, 0x11, 0x21, 0x43, 0x25,
    0x87, 0x40, 0x58, 0x63, 0x33, 0x43, 0x52, 0x10, 0x31, 0x53,
    0x56, 0x48, 0x05, 0x55, 0x77, 0x77, 0x26, 0x87, 0x28, 0x43,
    0x61, 0x46, 0x11, 0x76, 0x82, 0x50, 0x42, 0x04, 0x32, 0x88,
    0x18, 0x66, 0x16, 0x36, 0x64, 0x41, 0x38, 0x17, 0x55, 0x43,
    0x06, 0x25, 0x80, 0x27, 0x21, 0x16, 0x81, 0x22, 0x64, 0x60,
    0x38, 0x16, 0x82, 0x40, 0x72, 0x34, 0x73, 0x52, 0x61, 0x85,
    0x11, 0x16, 0x00, 0x25, 0x03, 0x30, 0x06, 0x80, 0x21, 0x56,
    0x64, 0x52, 0x23, 0x26, 0x37, 0x75, 0x73, 0x65, 0x53, 0x27,
    0x37, 0x47, 0x56, 0x76, 0x80, 0x38, 0x53, 0x62, 0x14, 0x24,
    0x64, 0x03, 0x66, 0x21, 0x72, 0x16, 0x36, 0x34, 0x11, 0x65,
    0x61, 0x62, 0x86, 0x02, 0x83, 0x27, 0x80, 0x82, 0x70, 0x72,
    0x52, 0x60, 0x20, 0x87, 0x58, 0x58, 0x14, 0x38, 0x47, 0x03,
    0x10, 0x72, 0x60, 0x48, 0x02, 0x01, 0x17, 0x21, 0x61, 0x62,
    0x38, 0x64, 0x27, 0x53, 0x57, 0x13, 0x68, 0x18, 0x26, 0x62,
    0x43, 0x42, 0x21, 0x85, 0x70, 0x23, 0x58, 0x13, 0x72, 0x04,
    0x04, 0x08, 0x05, 0x82, 0x26, 0x18, 0x82, 0x47, 0x87, 0x71,
    0x32, 0x28, 0x68, 0x25, 0x87, 0x24, 0x06, 0x74, 0x41, 0x44,
    0x08, 0x64, 0x68, 0x30, 0x24, 0x44, 0x21, 0x73, 0x03, 0x45,
    0x70, 0x41, 0x06, 0x78, 0x38, 0x33, 0x88, 0x13, 0x31, 0x14,
    0x18, 0x17, 0x45, 0x06, 0x26, 0x67, 0x66, 0x73, 0x82, 0x56,
    0x66, 0x88, 0x70, 0x22, 0x55, 0x47, 0x27, 0x50, 0x86, 0x55,
    0x53, 0x00, 0x28, 0x55, 0x40, 0x62, 0xe9, 0x37, 0x65, 0xe1,
    0x30, 0x48, 0x6b, 0x35, 0x76, 0x96, 0x05, 0x21, 0xce, 0xed,
    0x46, 0xae, 0x7e, 0x6d, 0xc9, 0xf1, 0xc9, 0xb3, 0x7a, 0xa7,
    0xde, 0xa7, 0x62, 0x18, 0x11, 0xc0, 0xd8, 0xd0, 0x17, 0x0f,
    0x38, 0xaf, 0x0e, 0x3d, 0xaf, 0xe6, 0x63, 0xb0, 0xc4, 0x68,
    0x4e, 0x29, 0xa4, 0xf4, 0x20, 0x22, 0xbc, 0x82, 0x15, 0x1d,
    0x08, 0x39, 0x18, 0xfe, 0x69, 0x55, 0x06, 0x3d, 0xf4, 0xa3,
    0xe7, 0x29, 0x23, 0xa4, 0xd9, 0xa4, 0x22, 0x06, 0x2d, 0x5f,
    0x22, 0xb3, 0x9b, 0x1c, 0xb6, 0x3e, 0xf3, 0xf4, 0x8a, 0xb3,
    0x35, 0x18, 0x4c, 0x1f, 0xaf, 0xd4, 0xcf, 0x5b, 0x9b, 0xa7,
    0xf8, 0xd2, 0x86, 0x71, 0x8e, 0x64, 0x96, 0xd1, 0x6e, 0xad,
    0xd2, 0x7e, 0x16, 0x5b, 0x38, 0x91, 0x0e, 0x40, 0xaa, 0x07,
    0x6a, 0x63, 0x2a, 0xc0, 0x5b, 0x14, 0x79, 0x52, 0xcb, 0x23,
    0x6e, 0x76, 0x95, 0xd0, 0x90, 0x6c, 0x18, 0xe7, 0x89, 0xee,
    0xb9, 0x7f, 0x33, 0x08, 0x35, 0x8f, 0xa3, 0xaa, 0xaa, 0x10,
    0x2f, 0x8b, 0xc9, 0x6c, 0x1d, 0x95, 0xb5, 0xb8, 0x54, 0x0d,
    0x67, 0x86, 0xd4, 0x5d, 0xae, 0x8f, 0x33, 0x20, 0xe2, 0x35,
    0xda, 0x71, 0x53, 0x24, 0xad, 0x16, 0x84, 0x2e, 0x98, 0xcd,
    0x00, 0xa2, 0x69, 0x6a, 0x12, 0x9a, 0x86, 0xf3, 0x9f, 0x18,
    0x6c, 0x9f, 0x24, 0xbe, 0xb3, 0xf4, 0x90, 0xb3, 0xc4, 0xa4,
    0x8b, 0xce, 0x88, 0x60, 0xa0, 0x91, 0xb8, 0x9a, 0x52, 0xe5,
    0xfe, 0x16, 0x6d, 0xff, 0xb3, 0xdc, 0x50, 0x79, 0xfe, 0x31,
    0x24, 0xd4, 0x59, 0x5f, 0xf9, 0xb4, 0x70, 0x0b, 0x15, 0x93,
    0xd9, 0xe9, 0x92, 0xb6, 0xf5, 0x80, 0x34, 0x63, 0x66, 0x78,
    0xcf, 0xa9, 0xce, 0x48, 0xbf, 0xbe, 0x9e, 0xfa, 0xdd, 0x7d,
    0xf4, 0x16, 0xe2, 0xd2, 0x98, 0x13, 0xe2, 0x76, 0xdd, 0x0a,
    0xc7, 0x2d, 0xe8, 0x88, 0x8e, 0x1a, 0xc0, 0xfc, 0xe8, 0x35,
    0xaf, 0x5d, 0xe2, 0x4c, 0x96, 0x82, 0x4c, 0xe5, 0x89, 0x14,
    0xb8, 0x27, 0x39, 0xb5, 0x55, 0xc5, 0xa5, 0x8a, 0x01, 0xcc,
    0xfd, 0xbd, 0xa9, 0xec, 0xae, 0xc0, 0xe7, 0xd7, 0xf8, 0x11,
    0x84, 0x35, 0x99, 0x26, 0xb6, 0xc6, 0xf7, 0x35, 0xe0, 0x93,
    0xd8, 0xd7, 0xbf, 0xc0, 0xc8, 0x44, 0xfd, 0x46, 0xf5, 0xb7,
    0xc5, 0x5a, 0x75, 0xd3, 0xc7, 0xfa, 0xf4, 0xe1, 0xc0, 0x84,
    0x5e, 0x31, 0xfe, 0x69, 0x80, 0x5a, 0xe5, 0x4b, 0x9b, 0x5b,
    0xa4, 0x5c, 0x23, 0xaa, 0x85, 0xc9, 0x9a, 0xbd, 0x71, 0x49,
    0x11, 0x30, 0x8b, 0x81, 0xa1, 0xdd, 0xf8, 0xb8, 0x74, 0x91,
    0xe7, 0xf7, 0x82, 0x42, 0x70, 0x22, 0x95, 0xf0, 0xcc, 0x9f,
    0x02, 0x33, 0x0f, 0x08, 0x3b, 0x04, 0x31, 0xd7, 0x4f, 0x86,
    0x78, 0x49, 0xb9, 0x90, 0xf5, 0x8f, 0xec, 0x12, 0x84, 0x52,
    0x03, 0x1f, 0x64, 0x5e, 0xf0, 0x2a, 0xeb, 0x87, 0xa5, 0xec,
    0x95, 0x25, 0x64, 0x25, 0x49, 0x3b, 0x3c, 0x30, 0xed, 0x3b,
    0xe9, 0x36, 0xfd, 0xae, 0xa6, 0x26, 0xd3, 0x45, 0xbc, 0x1b,
    0x78, 0x5f, 0xce, 0x27, 0x45, 0x1c, 0xd5, 0xf9, 0xa7, 0xda,
    0x62, 0xe6, 0x7e, 0xd3, 0xbb, 0xd8, 0x0a, 0xfd, 0xf5, 0xa5,
    0x31, 0x09, 0x6e, 0x40, 0xe8, 0xcf, 0xc1, 0x42, 0x8e, 0x2e,
    0x75, 0x65, 0xaa, 0x91, 0x6f, 0xc7, 0x75, 0x3a, 0x1e, 0x40,
    0x99, 0x71, 0x5e, 0x00, 0xae, 0x07, 0xad, 0x43, 0x49, 0xdd,
    0x6d, 0x36, 0xe3, 0xa8, 0xdf, 0x2c, 0x39, 0xa2, 0x57, 0xd7,
    0x93, 0xa1, 0x16, 0x80, 0x89, 0xa6, 0x56, 0x69, 0x75, 0xea,
    0xb8, 0xb2, 0x43, 0x0c, 0xdf, 0x46, 0x05, 0x9a, 0x39, 0x08,
    0x3b, 0xb6, 0x76, 0xe3, 0x5b, 0x98, 0x5b, 0x48, 0xc0, 0x11,
    0x14, 0x6f, 0xcd, 0xb7, 0xaa, 0x08, 0x1e, 0x53, 0x9b, 0x94,
    0x9d, 0xa2, 0xe6, 0x99, 0xcb, 0x1c, 0xb4, 0xbf, 0x55, 0x84,
    0x12, 0xc9, 0xf1, 0xf0, 0x94, 0xd9, 0x7d, 0x61, 0xa9, 0xe7,
    0xe6, 0xc1, 0xe2, 0xca, 0x6b, 0x36, 0x80, 0x72, 0x31, 0x79,
    0xbf, 0xe7, 0x3e, 0x99, 0x9e, 0xd5, 0x59, 0xd4, 0x97, 0x14,
    0xd5, 0xfa, 0x93, 0x37, 0x8a, 0x65, 0xa5, 0xb6, 0x4e, 0xba,
    0xb3, 0x84, 0xf2, 0xc1, 0x55, 0xb6, 0x94, 0x31, 0x30, 0xe7,
    0xb2, 0x71, 0x4e, 0xc6, 0x21, 0x50, 0xf3, 0xcf, 0x7c, 0xbc,
    0x26, 0xb7, 0x20, 0xcb, 0x2d, 0x9e, 0x55, 0x23, 0x7c, 0xf0,
    0x97, 0x16, 0x57, 0x5b, 0xcc, 0xc5, 0x48, 0xc9, 0xc8, 0xee,
    0x1e, 0x11, 0x6b, 0x72, 0x3b, 0x29, 0x71, 0xa4, 0xed, 0x08,
    0x6c, 0x38, 0xc6, 0x2e, 0x64, 0x3b, 0x16, 0xd8, 0x4d, 0x19,
    0xe8, 0x94, 0xd3, 0xd5, 0xb4, 0x18, 0xb4, 0x03, 0x24, 0x62,
    0xe7, 0x44, 0x5e, 0x09, 0x60, 0xc6, 0xa9, 0xa6, 0xca, 0xbe,
    0x83, 0xe5, 0xf1, 0xbd, 0x04, 0x22, 0x4b, 0x1b, 0x08, 0x0b,
    0xa6, 0x20, 0x95, 0xf2, 0x78, 0x8c, 0x3e, 0x73, 0x03, 0x7b,
    0x75, 0x2c, 0xe5, 0x72, 0xec, 0xc9, 0x25, 0x06, 0x6b, 0x3a,
    0x5e, 0x0e, 0x96, 0xd0, 0xe3, 0x85, 0xb0, 0xb5, 0x6a, 0x83,
    0x40, 0x41, 0x94, 0xce, 0xa1, 0x07, 0x79, 0x07, 0xe2, 0x50,
    0xa4, 0xde, 0x7d, 0x64, 0x2f, 0x7e, 0x43, 0xd5, 0x72, 0xd1,
    0xa7, 0xb9, 0x76, 0xa3, 0xfc, 0x25, 0x33, 0xd7, 0x95, 0xb5,
    0xd9, 0x94, 0x93, 0x55, 0xaf, 0x04, 0x86, 0x4a, 0xfc, 0x2f,
    0x5f, 0x3d, 0x34, 0x86, 0xf2, 0x9a, 0x31, 0x4c, 0xc9, 0xad,
    0x08, 0xa5, 0x03, 0x91, 0x8a, 0x7e, 0x46, 0xc9, 0x44, 0x61,
    0x11, 0x59, 0x4f, 0xbb, 0x70, 0xf9, 0x9d, 0x3e, 0x6d, 0x53,
    0xb4, 0x16, 0x28, 0xd3, 0x67, 0x52, 0x14, 0xad, 0xba, 0xb1,
    0x21, 0xaf, 0x84, 0x18, 0xc9, 0x37, 0x78, 0xb3, 0x78, 0x92,
    0x95, 0xad, 0x1b, 0xc0, 0x70, 0xe7, 0xe9, 0x06, 0x02, 0xed,
    0x6c, 0x99, 0x4e, 0x43, 0xc0, 0xa4, 0x6f, 0x23, 0xa8, 0x02,
    0xc4, 0xbd, 0xc0, 0x16, 0xc4, 0xed, 0xe0, 0xe1, 0x56, 0x06,
    0x3f, 0xf4, 0x77, 0x12, 0x72, 0x52, 0x04, 0xe8, 0xe4, 0x26,
    0xe5, 0x01, 0x47, 0x5b, 0x8a, 0xca, 0x07, 0x3b, 0xc9, 0xb1,
    0x42, 0x8f, 0x7d, 0x64, 0x7d, 0x5d, 0x6a, 0x95, 0xde, 0x4d,
    0x4b, 0xd3, 0xfa, 0xcf, 0xf0, 0x25, 0x27, 0x96, 0x48, 0xb6,
    0xcc, 0x68, 0x29, 0x37, 0x95, 0xcd, 0x36, 0xb7, 0xb0, 0xd6,
    0xf1, 0xfc, 0x4f, 0xe9, 0xa8, 0x6b, 0x9d, 0x75, 0xc7, 0x9b,
    0x19, 0xaf, 0xbb, 0x8a, 0xaf, 0x4b, 0xb8, 0xe2, 0xeb, 0x8d,
    0xd9, 0xf5, 0x75, 0xc5, 0xc8, 0x0b, 0xf2, 0x1c, 0xf9, 0x9e,
    0xc7, 0x4d, 0x7c, 0x71, 0x47, 0xbd, 0x57, 0x7e, 0xe6, 0x59,
    0xca, 0x8c, 0xf2, 0x0c, 0x47, 0x4a, 0x90, 0xa7, 0xf5, 0xb8,
    0xb2, 0x43, 0x97, 0xdb, 0xbe, 0x76, 0x37, 0x29, 0x36, 0x40,
    0xaa, 0x7a, 0x81, 0xf0, 0xa0, 0xd0, 0x81, 0x39, 0x88, 0xf0,
    0x23, 0xb0, 0xa4, 0xbe, 0x5e, 0xd8, 0x33, 0x98, 0x5d, 0x9d,
    0xb5, 0xd4, 0x1c, 0x00, 0xe2, 0x30, 0xb8, 0x68, 0x58, 0x65,
    0x30, 0x94, 0x3d, 0xf2, 0x75, 0x0c, 0x8e, 0x3b, 0xee, 0x9b,
    0xce, 0x6c, 0x67, 0x68, 0x54, 0x86, 0x7d, 0x27, 0x2a, 0x2f,
    0xf7, 0x25, 0xff, 0x22, 0x1e, 0x74, 0xbd, 0x72, 0x11, 0xf4,
    0x47, 0x8e, 0x2f, 0x0d, 0xb9, 0x31, 0xac, 0x5c, 0x1d, 0xa0,
    0x11, 0xea, 0x16, 0x24, 0x86, 0x76, 0xbd, 0xa3, 0x41, 0x7f,
    0x00, 0xe6, 0xe2, 0x86, 0x93, 0xff, 0x02, 0x07, 0xce, 0x49,
    0xe4, 0xaf, 0x00, 0x9b, 0x15, 0xa6, 0x05, 0xf7, 0x54, 0xd1,
    0xbb, 0xa7, 0x09, 0x67, 0xe6, 0x99, 0xf9, 0x23, 0xe6, 0xaa,
    0x6f, 0xcb, 0xe1, 0xc1, 0xac, 0x7b, 0x98, 0xa9, 0x14, 0x43,
    0x55, 0x22, 0x2c, 0x7a, 0x4a, 0x4a, 0x63, 0xc1, 0xfe, 0x5c,
    0xca, 0xf4, 0x91, 0x3b, 0x6f, 0xf8, 0x7e, 0x2a, 0xa1, 0x4a,
    0xc3, 0x16, 0x1c, 0x1d, 0x53, 0x7d, 0x0e, 0x77, 0x0d, 0x72,
    0x07, 0x78, 0xea, 0xce, 0xe4, 0x0c, 0xf7, 0xce, 0xa0, 0xef,
    0xa1, 0xdb, 0x6b, 0x5f, 0xfd, 0xeb, 0x68, 0xc7, 0x76, 0xfd,
    0x35, 0xd2, 0xcb, 0xa4, 0xf6, 0xe6, 0x6b, 0xdb, 0xe9, 0xd5,
    0x1e, 0x05, 0x8a, 0xba, 0xed, 0x77, 0x94, 0x36, 0x6c, 0x3c,
    0xe2, 0x23, 0xf8, 0x84, 0xa1, 0xe3, 0xcd, 0xfa, 0x1d, 0x31,
    0x52, 0x4d, 0xbc, 0x16, 0x31, 0x92, 0xd7, 0xbe, 0x2e, 0xd6,
    0x6d, 0x1d, 0x58, 0x4e, 0xd8, 0x06, 0x8f, 0xb3, 0xe6, 0x79,
    0x60, 0x92, 0x71, 0x1f, 0x72, 0x84, 0x55, 0x7b, 0xfa, 0xc8,
    0xcf, 0x20, 0x16, 0x2f, 0xc7, 0x13, 0x17, 0xd1, 0x2d, 0xd1,
    0x0d, 0x84, 0x48, 0x08, 0x69, 0xd1, 0x55, 0xb1, 0x08, 0xb6,
    0x17, 0x8c, 0x38, 0x31, 0xa4, 0x77, 0x73, 0xc0, 0xe9, 0xfc,
    0x5f, 0x8e, 0xb3, 0x74, 0x1f, 0xab, 0xcf, 0xf5, 0x26, 0x26,
    0x20, 0x80, 0xd8, 0x13, 0x42, 0xcf, 0xc7, 0x9d, 0xd6, 0x5b,
    0x1a, 0xfd, 0x46, 0x83, 0xba, 0xc1, 0xe5, 0x92, 0xe9, 0x27,
    0xa8, 0xa0, 0x36, 0xd5, 0x31, 0x75, 0x7b, 0x8f, 0x53, 0xf6,
    0xbd, 0x08, 0x1a, 0x86, 0x81, 0x83, 0x85, 0x07, 0x44, 0x3e,
    0xf9, 0x72, 0x47, 0xe0, 0xf1, 0xbe, 0x43, 0x6a, 0xc3, 0x00,
    0x94, 0xd3, 0x19, 0x81, 0xde, 0xf3, 0xfd, 0x57, 0x98, 0xdc,
    0x57, 0xfe, 0x9f, 0x4b, 0x38, 0x23, 0xad, 0xa8, 0xd4, 0x07,
    0x07, 0x5c, 0xca, 0x25, 0xb8, 0x77, 0x7e, 0x45, 0x01, 0x9b,
    0xd4, 0x45, 0x5b, 0x94, 0x47, 0x18, 0x35, 0x66, 0xad, 0x0a,
    0x97, 0x06, 0xc6, 0xa7, 0xaa, 0x50, 0xbf, 0x07, 0x90, 0xfe,
    0x50, 0x8d, 0xd9, 0x1f, 0xdd, 0x33, 0xa4, 0xa7, 0x23, 0x48,
    0xa3, 0xd6, 0x5d, 0xb8, 0x9e, 0x97, 0x22, 0x32, 0xd3, 0x8a,
    0xb0, 0x5e, 0xb3, 0xc9, 0x0b, 0x24, 0x09, 0x66, 0x2e, 0xea,
    0x94, 0x9c, 0x90, 0x4f, 0x3e, 0x93, 0xcf, 0x30, 0x3f, 0xb4,
    0xbe, 0x5e, 0x6c, 0xaf, 0x1a, 0xff, 0x00, 0xc7, 0x74, 0x2e,
    0x8b, 0x08, 0xe9, 0x22, 0x61, 0xc5, 0xd1, 0x21, 0x15, 0xa1,
    0xba, 0x37, 0xd2, 0x24, 0xfd, 0xa5, 0x63, 0x9a, 0x97, 0xfa,
    0xfe, 0xb2, 0xa5, 0x1b, 0x3b, 0xbd, 0xb7, 0xb3, 0x2f, 0x3d,
    0xf1, 0x5a, 0xf2, 0xf6, 0xe4, 0x12, 0xe4, 0x3a, 0x26, 0x3c,
    0x21, 0x5c, 0xd6, 0x83, 0x65, 0x26, 0x86, 0xcc, 0x47, 0x84,
    0xd7, 0x26, 0x31, 0x31, 0xcf, 0x1d, 0xd6, 0xc4, 0xa4, 0xf2,
    0xd4, 0x25, 0x54, 0x2b, 0x81, 0x00, 0x1d, 0xd8, 0xdf, 0x04,
    0xb8, 0x4b, 0xcf, 0xe5, 0x16, 0xf4, 0x4a, 0x17, 0xc5, 0xd8,
    0xd3, 0xdf, 0xe4, 0xb7, 0xd3, 0x98, 0xb6, 0x73, 0xa0, 0x37,
    0x67, 0xbb, 0x8b, 0xc3, 0xfc, 0xac, 0x6e, 0x6c, 0x0e, 0x5d,
    0x44, 0xb0, 0x9d, 0xf8, 0xae, 0x17, 0x9b, 0xf9, 0xcb, 0xe8,
    0xfe, 0xc1, 0x7b, 0x78, 0x16, 0xf6, 0x74, 0x04, 0x7d, 0x38,
    0x17, 0x36, 0x09, 0xe3, 0x73, 0xa1, 0x76, 0x78, 0x7c, 0x14,
    0xb3, 0x83, 0x91, 0x59, 0x27, 0xea, 0x8c, 0x69, 0xe6, 0xa5,
    0x21, 0xcd, 0x78, 0xc7, 0x26, 0xa2, 0xfb, 0xd4, 0xf3, 0xaf,
    0x3f, 0xcf, 0x51, 0x10, 0xcc, 0x4b, 0xdd, 0x14, 0xf4, 0xf3,
    0xb8, 0xea, 0x07, 0xa7, 0x76, 0xe7, 0xbe, 0xec, 0x01, 0xb5,
    0x1e, 0xdc, 0xc3, 0x55, 0x19, 0xb1, 0x16, 0x3f, 0xfe, 0xd4,
    0x15, 0x49, 0xaf, 0x04, 0x9d, 0x38, 0xdd, 0x86, 0x53, 0x2a,
    0x80, 0x62, 0x42, 0xb7, 0x98, 0x42, 0x38, 0xaf, 0x9d, 0x87,
    0xe2, 0x3f, 0xea, 0x7e, 0x0a, 0x35, 0xb8, 0xee, 0xa5, 0x48,
    0x09, 0x08, 0xc5, 0x0d, 0xae, 0x01, 0xd5, 0xec, 0x43, 0x29,
    0x3b, 0xfb, 0x78, 0xc4, 0x96, 0x01, 0x1c, 0x21, 0xf2, 0xc9,
    0x44, 0x68, 0x24, 0x66, 0x86, 0x96, 0xb8, 0xc8, 0xe9, 0xd0,
    0x38, 0x0e, 0x96, 0x4d, 0xcc, 0x45, 0xab, 0xe1, 0xca, 0x50,
    0x10, 0x20, 0x01, 0xbe, 0x89, 0xc0, 0x43, 0x84, 0xd8, 0x38,
    0x52, 0xc0, 0xaf, 0x4d, 0x6b, 0x99, 0x0b, 0xc0, 0xc2, 0x99,
    0x07, 0xc6, 0x78, 0xa8, 0xf7, 0x32, 0x84, 0x86, 0xc5, 0x1a,
    0x95, 0x81, 0xa6, 0x6a, 0x05, 0xa7, 0x9d, 0x81, 0x0e, 0x32,
    0x18, 0x11, 0x4a, 0x0f, 0xfc, 0x17, 0x9e, 0xf7, 0xbf, 0x54,
    0x82, 0xed, 0xba, 0x6f, 0xbd, 0x41, 0xc1, 0xca, 0x55, 0x6c,
    0xff, 0x32, 0x6b, 0xa2, 0x59, 0xae, 0xae, 0x92, 0xc1, 0xb5,
    0xa6, 0xfc, 0xaf, 0x09, 0x48, 0x57, 0xd6, 0xee, 0x38, 0x99,
    0xb4, 0xe3, 0x8f, 0xb7, 0xfc, 0x6a, 0x0a, 0x3b, 0x08, 0xe1,
    0x81, 0x46, 0x11, 0xeb, 0x4a, 0x98, 0x43, 0x16, 0x16, 0x1f,
    0x68, 0xdb, 0xb9, 0x71, 0x19, 0xfe, 0x8b, 0xe6, 0xb7, 0x8b,
    0xc1, 0x3b, 0x90, 0xc5, 0x89, 0x1d, 0xca, 0xd9, 0x19, 0x6c,
    0xe8, 0x01, 0xf4, 0x19, 0x50, 0x3e, 0x93, 0x84, 0xbf, 0xaa,
    0x9a, 0x3d, 0x20, 0x4c, 0x4e, 0x79, 0x83, 0xec, 0x46, 0x83,
    0x09, 0x00, 0xc3, 0x8a, 0xad, 0xd5, 0x2b, 0x08, 0xd1, 0x47,
    0xac, 0x96, 0x0e, 0x34, 0xf0, 0x89, 0x1a, 0x0f, 0xf2, 0x51,
    0x8d, 0x2c, 0xb5, 0xf2, 0xfe, 0x8c, 0xdc, 0xed, 0x41, 0x51,
    0x8c, 0x71, 0x12, 0x05, 0xec, 0x68, 0x21, 0x86, 0x94, 0xf4,
    0xfb, 0xfc, 0xaa, 0xc7, 0xc7, 0xbb, 0x74, 0xa2, 0x8b, 0x76,
    0x62, 0x1c, 0x64, 0x11, 0xa0, 0xd0, 0x5f, 0x46, 0x64, 0xd4,
    0x47, 0xbc, 0x8a, 0x5b, 0x2b, 0xc2, 0xc1, 0x88, 0xb2, 0x30,
    0xbd, 0x02, 0x17, 0x18, 0x0a, 0xd7, 0x9b, 0x3d, 0x91, 0xb9,
    0x2c, 0x83, 0x24, 0xb4, 0x8b, 0x9d, 0x02, 0xaf, 0xb2, 0x4e,
    0x57, 0xe1, 0xb0, 0xa2, 0xf3, 0x7c, 0xde, 0x15, 0xba, 0x60,
    0xbd, 0x80, 0xbe, 0x6d, 0x6f, 0x16, 0xb3, 0xb9, 0xb8, 0x6a,
    0x55, 0xb4, 0xad, 0xf1, 0x01, 0x63, 0x40, 0x01, 0xba, 0x5b,
    0x5d, 0x9a, 0xbc, 0xf0, 0x58, 0xa8, 0xf7, 0xbb, 0x8e, 0x91,
    0xa0, 0xfd, 0x8c, 0x49, 0x8f, 0x1a, 0xbb, 0x2a, 0x28, 0x0d,
    0x7a, 0xa6, 0xc2, 0xd7, 0x41, 0x16, 0xed, 0x61, 0x5d, 0xc4,
    0xe7, 0xcf, 0x2b, 0xb4, 0xb9, 0x10, 0x6f, 0x38, 0x42, 0x88,
    0x94, 0x6e, 0x75, 0x2c, 0x89, 0xac, 0xa0, 0xe9, 0x81, 0xec,
    0x2d, 0x62, 0xa3, 0xba, 0x3c, 0x40, 0xdb, 0x65, 0x56, 0x8e,
    0xc7, 0xd8, 0xb0, 0xd4, 0xf9, 0x04, 0x2b, 0x4c, 0x83, 0x20,
    0xbe, 0xad, 0xb8, 0x66, 0x1c, 0x20, 0x32, 0xb3, 0xf6, 0xf1,
    0xac, 0xa5, 0x8a, 0x72, 0x9a, 0x41, 0x1d, 0x6e, 0xa0, 0x16,
    0xe0, 0x0c, 0x39, 0xb6, 0x06, 0x96, 0x55, 0xb7, 0xda, 0x1c,
    0x54, 0x08, 0xf6, 0x30, 0x1b, 0xb6, 0x57, 0xca, 0x7d, 0xb0,
    0xdc, 0x9e, 0xfa, 0x5c, 0x38, 0x7f, 0xac, 0x37, 0x80, 0x26,
    0xba, 0xdc, 0x7a, 0x95, 0xe5, 0x7b, 0x90, 0xf3, 0x1a, 0xc7,
    0x31, 0x8e, 0x97, 0x07, 0x9a, 0xb8, 0xbe, 0xae, 0x16, 0x11,
    0x44, 0xb0, 0x01, 0xf5, 0xe8, 0x37, 0x1a, 0x67, 0xfe, 0x00,
    0x8f, 0xa1, 0xf5, 0x03, 0x7c, 0xed, 0xbf, 0x42, 0xf4, 0x78,
    0x2b, 0xfb, 0x9f, 0x8c, 0xb3, 0x63, 0x0b, 0x42, 0xbf, 0xae,
    0x8e, 0xf7, 0x6f, 0xb4, 0xb1, 0xe8, 0x75, 0x8c, 0xdf, 0x69,
    0xc6, 0xe1, 0x3a, 0x26, 0x05, 0x47, 0x03, 0x61, 0xfc, 0xc5,
    0xa9, 0xc1, 0x4f, 0x70, 0xce, 0x18, 0xbb, 0x01, 0xe6, 0x11,
    0xc9, 0xa7, 0x7e, 0x65, 0xb8, 0xdc, 0x61, 0x3d, 0x9b, 0x47,
    0x2e, 0x34, 0x16, 0xa1, 0x73, 0x61, 0x91, 0xed, 0x45, 0xe3,
    0x01, 0x26, 0xee, 0x16, 0x76, 0x0e, 0xb7, 0xa1, 0xc0, 0xb3,
    0xac, 0xf0, 0xa5, 0x3b, 0xf6, 0x64, 0x1b, 0x93, 0x94, 0x5c,
    0x8f, 0x4c, 0x25, 0x89, 0xa1, 0x92, 0x32, 0x50, 0x28, 0x03,
    0x8b, 0xff, 0xc4, 0xf6, 0x2a, 0xe8, 0xda, 0x8d, 0xfe, 0x49,
    0xb5, 0x33, 0x01, 0xca, 0x2d, 0x2d, 0x60, 0x33, 0xd6, 0x30,
    0x38, 0x8a, 0x1e, 0x38, 0x3d, 0x78, 0x11, 0xff, 0xef, 0x1c,
    0x82, 0x33, 0xbb, 0xfc, 0x95, 0xef, 0x79, 0xb0, 0x59, 0xbd,
    0x2c, 0xfd, 0x1c, 0x3f, 0x42, 0xda, 0xdf, 0xbd, 0x56, 0xf2,
    0xd6, 0xae, 0x2d, 0x23, 0x36, 0xed, 0xb1, 0x8d, 0x62, 0x58,
    0x71, 0x66, 0x21, 0xe0, 0x4d, 0xee, 0xf4, 0x16, 0x48, 0xa6,
    0xcf, 0x1a, 0x8a, 0xf0, 0x8a, 0xd1, 0x53, 0xf6, 0xe5, 0x4e,
    0x98, 0x9d, 0x7d, 0x6c, 0xd2, 0xdf, 0xb8, 0x2d, 0xa6, 0xe5,
    0x8a, 0xd6, 0xb5, 0xae, 0x61, 0x96, 0xfa, 0x6b, 0xca, 0x7f,
    0x08, 0xc2, 0x2b, 0x67, 0x30, 0x5e, 0x21, 0x3b, 0xa4, 0x84,
    0x95, 0xc6, 0x2f, 0x2c, 0x1f, 0xe2, 0x0e, 0x1a, 0xc3, 0x89,
    0x6a, 0x6a, 0xe7, 0x08, 0xf9, 0x74, 0xee, 0x4f, 0xcd, 0x5e,
    0xe8, 0xce, 0x55, 0x4d, 0x38, 0xed, 0x62, 0x35, 0xee, 0xfc,
    0x14, 0x56, 0xb9, 0xf0, 0xce, 0x29, 0x1c, 0x21, 0x40, 0x51,
    0xe4, 0x76, 0xe3, 0xa6, 0xd8, 0x3d, 0x54, 0x58, 0x51, 0xe5,
    0xf0, 0xdc, 0x50, 0x39, 0x43, 0x67, 0x44, 0x14, 0xcc, 0x6e,
    0x5a, 0xb1, 0x15, 0xec, 0xb4, 0x3e, 0x0e, 0xef, 0x8e, 0x72,
    0x6a, 0xdf, 0xba, 0x37, 0x27, 0x15, 0x62, 0xc3, 0xbd, 0xee,
    0x1d, 0xb1, 0x24, 0x2f, 0x57, 0x51, 0xf1, 0x8f, 0xfb, 0xd1,
    0x10, 0x6f, 0x11, 0xb9, 0x94, 0x5c, 0x9c, 0x12, 0x26, 0x46,
    0x46, 0x7b, 0x31, 0x0e, 0xad, 0x93, 0xe4, 0x4f, 0x09, 0xe3,
    0xbf, 0xc5, 0xe3, 0x11, 0xa4, 0x25, 0x8d, 0x9b, 0x8e, 0x26,
    0x02, 0xaa, 0x72, 0x18, 0xce, 0x89, 0x67, 0xfc, 0x1c, 0x28,
    0xab, 0x11, 0x5a, 0x84, 0x23, 0x7c, 0x91, 0xac, 0x6b, 0x48,
    0x9c, 0x39, 0x14, 0xa3, 0xac, 0xc6, 0x30, 0xbc, 0x1e, 0x0c,
    0xd3, 0x34, 0x19, 0xa9, 0x2b, 0xe7, 0xa4, 0xf8, 0xc1, 0xf0,
    0x3c, 0x60, 0xa2, 0xf7, 0x51, 0x86, 0xcf, 0x42, 0xad, 0x34,
    0x81, 0xa6, 0x93, 0x0b, 0x88, 0x4c, 0xbf, 0xd2, 0x4f, 0xe0,
    0xdb, 0xb2, 0x1d, 0x6d, 0xb2, 0x5c, 0xac, 0xd8, 0x64, 0x85,
    0xc3, 0x35, 0x6e, 0x5d, 0xaf, 0x63, 0x3e, 0x47, 0xb7, 0x5d,
    0x39, 0x21, 0x36, 0xa6, 0xd4, 0xef, 0x9e, 0x1c, 0x1f, 0xd6,
    0xa4, 0xe0, 0xe4, 0x22, 0x75, 0x1e, 0xeb, 0x15, 0xb4, 0xee,
    0x43, 0x37, 0x06, 0xf9, 0x77, 0xbf, 0x68, 0x9b, 0x9a, 0x7f,
    0x38, 0x30, 0x87, 0xde, 0x0c, 0x6a, 0x39, 0x41, 0xe1, 0xed,
    0xf4, 0x18, 0x6e, 0x29, 0x44, 0xf0, 0xfc, 0xb6, 0x09, 0x5b,
    0xb3, 0x30, 0xc9, 0x0a, 0x8c, 0x41, 0x6f, 0x1e, 0x95, 0xbe,
    0x93, 0x3c, 0x11, 0x9b, 0x24, 0xf7, 0x57, 0xb8, 0xc5, 0x9b,
    0x08, 0xaa, 0xcd, 0x24, 0x86, 0x98, 0x59, 0x0f, 0xc6, 0x0e,
    0xd2, 0x71, 0xb2, 0x5e, 0xae, 0x72, 0xc9, 0x69, 0x3b, 0x80,
    0xc2, 0x27,
};
static const int sizeof_bench_dilithium_level3_key = sizeof(bench_dilithium_level3_key);

#endif /* !WOLFSSL_DILITHIUM_NO_SIGN */

#ifndef WOLFSSL_DILITHIUM_NO_VERIFY

/* raw public key without ASN1 syntax from
 * ./certs/dilithium/bench_dilithium_level3_key.der */
static const unsigned char bench_dilithium_level3_pubkey[] = {
    0x15, 0xc9, 0xe5, 0x53, 0x2f, 0xd8, 0x1f, 0xb4, 0xa3, 0x9f,
    0xae, 0xad, 0xb3, 0x10, 0xd0, 0x72, 0x69, 0xd3, 0x02, 0xf3,
    0xdf, 0x67, 0x5a, 0x31, 0x52, 0x19, 0xca, 0x39, 0x27, 0x77,
    0x61, 0x6d, 0x0f, 0xc1, 0x33, 0x26, 0x09, 0xf0, 0xf9, 0x4d,
    0x12, 0x7a, 0xef, 0xf7, 0x21, 0x26, 0x2c, 0xe0, 0xe2, 0x92,
    0x1f, 0x9d, 0xd1, 0xaa, 0xaf, 0x08, 0x14, 0xf2, 0xaa, 0x24,
    0x99, 0x0f, 0x20, 0x57, 0x35, 0x04, 0x32, 0x96, 0x8e, 0x6e,
    0x10, 0x64, 0xe3, 0xe3, 0x57, 0x26, 0x33, 0x32, 0x7b, 0xe4,
    0x18, 0x41, 0x77, 0xd3, 0x24, 0x63, 0x3d, 0x11, 0xea, 0xdc,
    0xbe, 0x59, 0xff, 0x8d, 0xc2, 0xe4, 0xc7, 0x04, 0xf3, 0xd4,
    0xe0, 0x1d, 0x5e, 0x09, 0x46, 0xbf, 0x02, 0x05, 0xc7, 0xa6,
    0xb7, 0x82, 0x40, 0x1f, 0x55, 0xe9, 0x77, 0x82, 0xc0, 0xcc,
    0x86, 0x99, 0x19, 0x99, 0xa2, 0xc9, 0x1b, 0x4f, 0xdd, 0x49,
    0x4c, 0x78, 0x0a, 0x58, 0xb8, 0xf0, 0x23, 0xac, 0x1a, 0x71,
    0x57, 0x6d, 0xd6, 0x3a, 0x3a, 0x6f, 0x93, 0xb3, 0x2b, 0x09,
    0xbe, 0xec, 0x7b, 0x5b, 0xf7, 0x3a, 0xed, 0xf9, 0xd0, 0xb1,
    0xfe, 0x9f, 0x9b, 0xec, 0x11, 0xb6, 0x6b, 0xd1, 0xb6, 0x00,
    0x72, 0x7f, 0x68, 0x9a, 0x61, 0xa5, 0xf5, 0x6e, 0xe9, 0x46,
    0xa4, 0x82, 0x08, 0x9f, 0x50, 0x4c, 0x75, 0xc3, 0x48, 0x85,
    0x76, 0x39, 0xea, 0x0c, 0xf2, 0xe8, 0x7e, 0x48, 0x69, 0xd9,
    0x6f, 0x9a, 0x89, 0x7d, 0x98, 0xc1, 0x16, 0xdc, 0x2f, 0xc7,
    0x0a, 0x11, 0xa8, 0xbb, 0xe7, 0x91, 0xb1, 0x0f, 0x0e, 0xf0,
    0xb4, 0xc8, 0x41, 0x7e, 0x62, 0x9e, 0x3c, 0x30, 0x4c, 0xbc,
    0x4c, 0xeb, 0x37, 0xaf, 0x48, 0x72, 0x59, 0x64, 0x8e, 0xfb,
    0x77, 0x11, 0x28, 0xdd, 0x30, 0x52, 0x8e, 0x69, 0x8c, 0x9f,
    0x3d, 0xec, 0xdf, 0xa7, 0x5f, 0x42, 0x18, 0xda, 0xba, 0x1a,
    0x96, 0x91, 0x7d, 0x62, 0xd5, 0x52, 0xff, 0x44, 0xc9, 0x1d,
    0x29, 0xa6, 0xb9, 0x03, 0x9a, 0x26, 0x26, 0xcf, 0x57, 0x40,
    0x70, 0x7e, 0x2b, 0xbd, 0xf0, 0x81, 0x71, 0x0f, 0x0b, 0x2e,
    0x9b, 0x03, 0xba, 0x31, 0x41, 0x68, 0x37, 0xc8, 0xff, 0xea,
    0xc4, 0x73, 0xa5, 0xf9, 0xc2, 0x92, 0x78, 0x0c, 0xe7, 0xfd,
    0x5d, 0xb2, 0x01, 0xb5, 0x8d, 0xeb, 0x64, 0xd4, 0x14, 0xea,
    0x7a, 0xd1, 0x42, 0xc8, 0x99, 0xe4, 0x7d, 0x5b, 0x7e, 0x3b,
    0x8f, 0xab, 0x82, 0x12, 0xdf, 0xbb, 0xa1, 0x45, 0x30, 0xc9,
    0x0f, 0xb9, 0xe5, 0xba, 0xe6, 0x8a, 0xf3, 0x78, 0x61, 0xcc,
    0x9f, 0xe1, 0x46, 0x2a, 0x9a, 0x18, 0x0e, 0x2a, 0x57, 0xf3,
    0xe5, 0x56, 0xd1, 0x42, 0x48, 0xe1, 0x5a, 0x8e, 0x33, 0xce,
    0x19, 0xe5, 0x3e, 0x7f, 0x00, 0x70, 0x9c, 0x4c, 0xd3, 0xe1,
    0x0c, 0xa1, 0x7e, 0xd4, 0xa9, 0x9e, 0x8b, 0xe2, 0xf0, 0xac,
    0xdb, 0xa6, 0x72, 0x75, 0x67, 0xa6, 0x57, 0xed, 0x79, 0x2e,
    0xca, 0x8d, 0xeb, 0x9b, 0x9e, 0xb7, 0xbf, 0x30, 0x02, 0x2b,
    0xb3, 0x43, 0x89, 0x9b, 0xa8, 0x88, 0xa5, 0xbb, 0x33, 0xd9,
    0x99, 0x30, 0x7c, 0xc7, 0xd4, 0x28, 0x5e, 0x5e, 0x3f, 0x9d,
    0x6d, 0x35, 0x75, 0x33, 0x8e, 0xff, 0x84, 0x2e, 0x2d, 0xda,
    0xf0, 0xff, 0x70, 0xe5, 0xb5, 0x62, 0x96, 0x33, 0x3a, 0xd9,
    0xb5, 0x82, 0x25, 0x81, 0x81, 0x40, 0x5d, 0x4f, 0x11, 0x86,
    0x63, 0x1a, 0x06, 0xc1, 0x67, 0xc7, 0x49, 0x03, 0xc7, 0xe4,
    0x6f, 0xb4, 0x13, 0x3e, 0x57, 0x62, 0xfd, 0x8a, 0xc6, 0x2b,
    0x65, 0x5b, 0xa4, 0x29, 0x57, 0x8d, 0xde, 0xa5, 0xee, 0x32,
    0xc2, 0x76, 0x03, 0xca, 0xce, 0xc1, 0x48, 0xec, 0x45, 0xcf,
    0x30, 0x21, 0x28, 0x7f, 0x10, 0x47, 0xd2, 0xdb, 0xee, 0xca,
    0x5b, 0x0f, 0xd5, 0x39, 0x3a, 0xc3, 0xa6, 0x78, 0xb2, 0x15,
    0xaf, 0x82, 0x3c, 0x2f, 0xc4, 0x51, 0x5c, 0x52, 0xad, 0xf2,
    0x89, 0x92, 0x8e, 0xf3, 0x50, 0x38, 0xed, 0xf8, 0xc9, 0x14,
    0x4c, 0xe4, 0xa3, 0x9a, 0xaf, 0xc4, 0x5c, 0xf3, 0x9f, 0xc3,
    0xa3, 0xc0, 0xbe, 0x45, 0x1b, 0x21, 0x63, 0xfa, 0xe0, 0xe0,
    0x91, 0x2b, 0x42, 0xca, 0x91, 0xfb, 0x5e, 0x97, 0x9a, 0x0a,
    0xd4, 0x88, 0xba, 0xb8, 0x22, 0xc6, 0xbf, 0x56, 0x58, 0x1e,
    0x92, 0xa9, 0x9d, 0xa7, 0xed, 0xc9, 0xab, 0x54, 0x4f, 0x75,
    0x8d, 0x42, 0xc1, 0xe1, 0x61, 0xd0, 0x91, 0x9a, 0x3a, 0x40,
    0x9a, 0xa3, 0xfb, 0x7b, 0x4e, 0xf0, 0x85, 0xf0, 0xdc, 0x40,
    0x72, 0x9f, 0x05, 0xa8, 0xbe, 0x95, 0x5a, 0x7f, 0xba, 0x75,
    0x00, 0x6e, 0x95, 0x76, 0xbd, 0xb2, 0x40, 0xf5, 0xb0, 0x64,
    0x0a, 0x2f, 0x06, 0x3d, 0x9f, 0xac, 0x6a, 0xa5, 0x46, 0x5a,
    0x85, 0xa4, 0x6f, 0xee, 0x27, 0xa0, 0xeb, 0x5f, 0x1f, 0x91,
    0xbd, 0x2b, 0x02, 0x16, 0xdf, 0x74, 0x97, 0x2c, 0xd0, 0xa8,
    0x9f, 0x3a, 0x7b, 0xdf, 0x3e, 0x98, 0x4a, 0x91, 0xdc, 0x19,
    0x96, 0x88, 0x75, 0x21, 0x1a, 0x6a, 0xa8, 0x4b, 0x1f, 0x35,
    0xd1, 0x92, 0xf5, 0x76, 0xf4, 0x72, 0x55, 0x13, 0xdb, 0x5d,
    0x07, 0x8d, 0xd9, 0x72, 0xe4, 0x75, 0xde, 0x80, 0xbc, 0xe9,
    0x9c, 0xf0, 0x5c, 0x6a, 0x8a, 0x0e, 0x34, 0xf6, 0x3f, 0x5c,
    0xef, 0x0e, 0xcc, 0x52, 0x38, 0x2d, 0x7b, 0xc2, 0x1b, 0x69,
    0x9f, 0xe5, 0xed, 0x14, 0xb0, 0x91, 0x0b, 0xe9, 0x4d, 0x34,
    0xd5, 0xaa, 0xd4, 0xd2, 0x46, 0x39, 0x45, 0x7e, 0x85, 0x2f,
    0xdb, 0x89, 0xf4, 0xff, 0x05, 0x74, 0x51, 0xba, 0xdd, 0xee,
    0xf6, 0xc2, 0xc1, 0x0a, 0x8f, 0xd9, 0xeb, 0xc7, 0x61, 0x30,
    0x8f, 0x86, 0x8b, 0x1f, 0x82, 0xc1, 0x22, 0xfd, 0x83, 0xf4,
    0x5d, 0xc5, 0x94, 0xf5, 0xd7, 0x17, 0xc7, 0x7b, 0x71, 0xf5,
    0x5e, 0x15, 0x49, 0x70, 0xb2, 0x57, 0xa0, 0xc0, 0x57, 0x63,
    0x53, 0x35, 0xb6, 0x52, 0x20, 0x7b, 0x83, 0xd4, 0x57, 0x63,
    0x25, 0x8e, 0x83, 0xb3, 0x8e, 0x26, 0x1f, 0x09, 0xde, 0x14,
    0xd6, 0xa6, 0xfc, 0xe5, 0x93, 0x3c, 0x88, 0x8e, 0xf5, 0x10,
    0x57, 0xb9, 0xc9, 0x9b, 0xff, 0x72, 0x9d, 0x3d, 0x3f, 0x97,
    0xd9, 0x3c, 0x20, 0xe2, 0x57, 0xfd, 0x2a, 0x5c, 0x17, 0x12,
    0xe6, 0x08, 0xaf, 0xe4, 0x26, 0x96, 0xb9, 0x6d, 0xc3, 0xac,
    0x22, 0xf3, 0x8b, 0x89, 0xde, 0xc7, 0x8a, 0x93, 0x06, 0xf7,
    0x1d, 0x08, 0x21, 0x36, 0x16, 0x74, 0x2b, 0x97, 0x23, 0xe4,
    0x79, 0x31, 0x08, 0x23, 0x62, 0x30, 0x67, 0xe2, 0xed, 0x30,
    0x9b, 0x0c, 0xf9, 0x08, 0x7a, 0x29, 0x73, 0xc6, 0x77, 0x8a,
    0xbb, 0x2a, 0x1c, 0x66, 0xd0, 0xdd, 0x9e, 0xa3, 0xe9, 0x62,
    0xcc, 0xb7, 0x88, 0x25, 0x4a, 0x5f, 0xbc, 0xaa, 0xe3, 0xe4,
    0x4f, 0xec, 0xa6, 0x8e, 0xa6, 0xa4, 0x1b, 0x22, 0x2b, 0x2c,
    0x8f, 0x57, 0x7f, 0xb7, 0x33, 0xfe, 0x16, 0x43, 0x85, 0xc5,
    0xd2, 0x95, 0xe6, 0xb9, 0x21, 0x68, 0x88, 0x98, 0x33, 0x8c,
    0x1d, 0x15, 0x9c, 0x4d, 0x62, 0x1f, 0x6b, 0xe8, 0x7a, 0x2d,
    0x6b, 0x0e, 0xc3, 0xde, 0x1a, 0xa8, 0xed, 0x67, 0xb3, 0xb3,
    0x36, 0x5b, 0x4b, 0xcb, 0xe8, 0xa8, 0x5c, 0x0b, 0x2f, 0xca,
    0xd7, 0x71, 0xe8, 0x85, 0xe7, 0x4d, 0xe5, 0x7b, 0x45, 0xed,
    0xb2, 0x4c, 0x69, 0x04, 0x7e, 0x4f, 0xc0, 0xef, 0x1a, 0xca,
    0x0d, 0xa6, 0xc4, 0x79, 0x15, 0x78, 0x9c, 0xd2, 0x91, 0x3c,
    0x32, 0x55, 0x40, 0xe7, 0xcb, 0x7e, 0xde, 0x07, 0xa6, 0x97,
    0x00, 0x2d, 0x70, 0xf6, 0x3d, 0x15, 0xdf, 0x29, 0x8e, 0xa3,
    0x96, 0x6d, 0xf2, 0xbb, 0xa5, 0x1b, 0x7b, 0x58, 0x30, 0xf6,
    0x17, 0xbd, 0xda, 0x13, 0xf7, 0x33, 0xc2, 0x62, 0x32, 0xd4,
    0x1c, 0x2e, 0x31, 0x74, 0x92, 0xad, 0x99, 0x8c, 0x0e, 0x7c,
    0x50, 0x21, 0xcd, 0xff, 0x41, 0xeb, 0xd1, 0xca, 0x14, 0xb7,
    0xb2, 0x31, 0x2f, 0xbe, 0x16, 0xce, 0x4f, 0x26, 0x16, 0x04,
    0xc2, 0xaf, 0xbe, 0x0d, 0x24, 0xab, 0x9a, 0x21, 0x37, 0x06,
    0xac, 0x50, 0x23, 0xf1, 0xbe, 0x5c, 0xbb, 0x64, 0xf3, 0xd3,
    0x66, 0xa3, 0xb8, 0xbe, 0x8b, 0x49, 0x8d, 0xf6, 0xc7, 0xb9,
    0x8f, 0x4e, 0x31, 0x06, 0x51, 0xe5, 0xf3, 0x0e, 0x56, 0xc4,
    0x24, 0x30, 0xf5, 0xe9, 0x36, 0x71, 0xbc, 0xc9, 0x70, 0x2c,
    0x6c, 0x4c, 0x15, 0x43, 0x44, 0xa4, 0xfc, 0xf1, 0xd2, 0x71,
    0x6c, 0x4c, 0xce, 0x30, 0x6c, 0x05, 0x7d, 0x2e, 0xb7, 0xbc,
    0xe4, 0x65, 0x76, 0x24, 0x75, 0x36, 0xdf, 0x28, 0xfc, 0xcd,
    0x9a, 0xba, 0xc2, 0xcd, 0xb0, 0x30, 0xdb, 0xe7, 0x2e, 0x3c,
    0x92, 0x63, 0x1d, 0x30, 0x23, 0x74, 0xb1, 0xb8, 0xcc, 0xd7,
    0xb6, 0x90, 0x65, 0x73, 0xa2, 0x2a, 0x6e, 0x49, 0x95, 0x0d,
    0xab, 0x24, 0xdf, 0x2d, 0xbf, 0x76, 0x46, 0x01, 0x44, 0xe4,
    0x18, 0x8e, 0xd5, 0x9a, 0x76, 0xc9, 0xc6, 0xbc, 0xdb, 0x7f,
    0x80, 0x52, 0xc6, 0x40, 0x41, 0x12, 0x36, 0x7c, 0x80, 0x69,
    0xce, 0x7b, 0xe1, 0xa0, 0x53, 0xa2, 0xd6, 0x8f, 0x3f, 0xf7,
    0xd7, 0x61, 0x09, 0x70, 0xa2, 0xa0, 0xc6, 0xaf, 0xa0, 0xd0,
    0xfa, 0x13, 0xbf, 0xc0, 0x69, 0x15, 0xce, 0x15, 0xec, 0x24,
    0x4b, 0x6b, 0xdc, 0x93, 0x51, 0xc6, 0x82, 0x19, 0x92, 0x84,
    0x5d, 0x99, 0xb0, 0x90, 0x2c, 0xcc, 0x2a, 0x81, 0x6b, 0x22,
    0x64, 0x0a, 0xcb, 0x51, 0x25, 0x82, 0x50, 0x02, 0x2d, 0x3e,
    0xd4, 0x72, 0xb3, 0x0c, 0x15, 0x77, 0xd2, 0xca, 0x98, 0x2f,
    0x41, 0x93, 0x14, 0xb2, 0x7f, 0xa1, 0x97, 0xa3, 0xb8, 0x8a,
    0x56, 0x24, 0x38, 0xa7, 0x36, 0xc5, 0x01, 0xc0, 0x9f, 0x3f,
    0x3e, 0x9a, 0xf6, 0xe9, 0x16, 0x82, 0x01, 0x58, 0x70, 0x0e,
    0x0d, 0xbc, 0xfa, 0x03, 0x57, 0x65, 0xa8, 0x5a, 0x3d, 0x57,
    0x81, 0x23, 0xbe, 0x6e, 0xa9, 0xe8, 0x22, 0xdf, 0x2f, 0x70,
    0xeb, 0x0a, 0x03, 0x96, 0x6b, 0xef, 0x20, 0x9f, 0xf2, 0x62,
    0xe7, 0xb2, 0x6e, 0x3a, 0x1e, 0x40, 0x1f, 0xd2, 0x97, 0x48,
    0xd1, 0x18, 0xf0, 0xeb, 0x52, 0x58, 0x02, 0x26, 0xce, 0x75,
    0xb1, 0x3a, 0x9d, 0x5b, 0x52, 0x94, 0xb2, 0x6e, 0x0e, 0x3f,
    0x39, 0xb6, 0xd9, 0x8a, 0x9d, 0xe8, 0x7c, 0x83, 0x32, 0xcc,
    0x43, 0x35, 0x9b, 0x7a, 0xed, 0xb2, 0x1e, 0x51, 0x37, 0x6c,
    0x14, 0xd8, 0xb8, 0x55, 0xb3, 0x91, 0xef, 0x0c, 0x3a, 0xe5,
    0x77, 0xd0, 0xbd, 0xb0, 0x7d, 0x38, 0x84, 0x2a, 0x47, 0xb2,
    0xb6, 0xda, 0xd7, 0x75, 0xd6, 0x2e, 0x60, 0xc7, 0x10, 0x52,
    0xf7, 0xdd, 0x09, 0x15, 0x6f, 0x04, 0x31, 0xc3, 0x5a, 0x6b,
    0x0c, 0x60, 0x10, 0xa8, 0x6e, 0x20, 0xa9, 0xdd, 0xb7, 0x72,
    0xc3, 0x9e, 0x85, 0xd2, 0x8f, 0x16, 0x7e, 0x3d, 0xe0, 0x63,
    0x81, 0x32, 0xfd, 0xca, 0xbc, 0x0f, 0xef, 0x3e, 0x74, 0x6a,
    0xb1, 0x60, 0xc1, 0x10, 0x50, 0x7c, 0x67, 0xa4, 0x19, 0xa7,
    0xb8, 0xed, 0xe6, 0xf5, 0x4e, 0x41, 0x53, 0xa6, 0x72, 0x1b,
    0x2c, 0x33, 0x6a, 0x37, 0xf1, 0xb5, 0x1c, 0x01, 0x7d, 0xa2,
    0x1f, 0x2c, 0x4e, 0x0a, 0xbf, 0xd4, 0x2c, 0x24, 0x91, 0x58,
    0x62, 0xfb, 0xf8, 0x63, 0xd9, 0xf8, 0x78, 0xf5, 0xc7, 0x78,
    0x32, 0xda, 0x99, 0xeb, 0x58, 0x20, 0x25, 0x19, 0xb1, 0x06,
    0x7f, 0x6a, 0x29, 0x20, 0xdb, 0xc8, 0x22, 0x48, 0xa9, 0x7f,
    0x24, 0x54, 0x8d, 0x7d, 0x8d, 0xb1, 0x69, 0xb2, 0xa3, 0x98,
    0x14, 0x0f, 0xba, 0xfa, 0xb6, 0x15, 0xe8, 0x28, 0x99, 0x3f,
    0x30, 0x04, 0x50, 0xab, 0x5a, 0x3c, 0xf1, 0x97, 0xe1, 0xc8,
    0x0f, 0x0e, 0xb4, 0x11, 0x63, 0x5a, 0x79, 0x08, 0x48, 0x75,
    0xaf, 0x9b, 0xca, 0xd9, 0x13, 0x18, 0xcc, 0xb1, 0xb3, 0xee,
    0xdd, 0x63, 0xdd, 0xf4, 0x21, 0x98, 0x76, 0xe2, 0x3e, 0xd5,
    0x86, 0x23, 0x33, 0x7e, 0xc7, 0xb4, 0x35, 0x4b, 0xc2, 0x2d,
    0xe1, 0xe2, 0xb0, 0x6c, 0x8b, 0x9b, 0x20, 0x3d, 0x48, 0x24,
    0x7c, 0xea, 0xa1, 0x75, 0x27, 0xe5, 0xf4, 0x70, 0xeb, 0x3b,
    0xc7, 0x26, 0x37, 0x04, 0xff, 0x8a, 0x7a, 0xd0, 0xc2, 0xb7,
    0x84, 0xb7, 0x29, 0xfb, 0x0e, 0xa3, 0xa8, 0x71, 0xcd, 0x58,
    0x06, 0x36, 0xe2, 0xf2, 0x77, 0xcc, 0x0f, 0x78, 0x08, 0x2b,
    0xbb, 0xe3, 0x53, 0x05, 0x71, 0xdc, 0x6c, 0x37, 0x32, 0x91,
    0x46, 0x42, 0x4f, 0x21, 0xe0, 0x34, 0xad, 0x3f, 0x30, 0x5a,
    0xc7, 0x0d, 0x17, 0x19, 0x39, 0x31, 0x58, 0x69, 0x3c, 0x8c,
    0xbe, 0xe7, 0xa6, 0x3b, 0xad, 0xfb, 0x46, 0x89, 0x06, 0xc1,
    0x8c, 0x16, 0x9a, 0x06, 0x3a, 0xd0, 0x7e, 0xd6, 0xb0, 0x7b,
    0x7d, 0xf8, 0x91, 0x7c, 0xfa, 0xd9, 0x66, 0x39, 0xfa, 0xbc,
    0x57, 0xa7, 0x78, 0x8b, 0x36, 0x78, 0xc0, 0x1c, 0x0e, 0x23,
    0x05, 0x0e, 0x04, 0x61, 0x16, 0x34, 0xf9, 0xc6, 0x63, 0x58,
    0xdf, 0xf4, 0x52, 0xce, 0xd0, 0x0f, 0x0c, 0xec, 0xb1, 0x82,
    0xf4, 0x72, 0x73, 0x72, 0x3f, 0x02, 0xbe, 0xe3, 0x9c, 0x63,
    0x73, 0xc8, 0x21, 0x65, 0xba, 0x57, 0x52, 0xa9, 0x19, 0xac,
    0x68, 0x50, 0xbd, 0x2d, 0x72, 0x5b, 0x93, 0x0f, 0x1c, 0x81,
    0x77, 0xd7, 0x2e, 0xc3, 0x93, 0x52, 0x6e, 0xdc, 0x79, 0x52,
    0x9f, 0xe3, 0xde, 0xe1, 0xba, 0x58, 0x55, 0xab, 0x8a, 0xf2,
    0x35, 0x6a, 0xcf, 0x94, 0x1f, 0x17, 0xa4, 0x23, 0x2e, 0x8e,
    0x18, 0x21, 0xbe, 0x14, 0xfa, 0xe7, 0x59, 0xc5, 0x44, 0x34,
    0xce, 0x03, 0xf4, 0xb7, 0x75, 0xd3, 0x51, 0x55, 0xdf, 0xff,
    0xcf, 0x4f, 0x44, 0xee, 0x13, 0x9b, 0xcb, 0x12, 0xae, 0xe5,
    0x5b, 0x44, 0x65, 0x28, 0xcb, 0x6a, 0x9c, 0x24, 0x1d, 0xea,
    0x2d, 0x5e, 0xa5, 0xc3, 0x78, 0xad, 0xed, 0x0c, 0x05, 0xa6,
    0xaf, 0x95, 0x04, 0xd2, 0xb5, 0x91, 0x0e, 0xa0, 0x06, 0x77,
    0xc5, 0x82, 0xf6, 0xdd, 0x72, 0x83, 0x04, 0xcc, 0xb0, 0xab,
    0x7a, 0xf0, 0xb4, 0x4d, 0x36, 0x71, 0x72, 0x1a, 0x9a, 0x0d,
    0xcd, 0xa3, 0x11, 0xa8, 0x0d, 0x7d, 0x49, 0xce, 0x9c, 0x09,
    0x1d, 0x08, 0xa4, 0x39, 0x2e, 0x03, 0xdf, 0x3a, 0xc8, 0xfe,
    0x6a, 0x2b, 0x0b, 0x07, 0x80, 0x55, 0x8a, 0xa8, 0xe6, 0x0e,
    0xc9, 0x7e, 0x83, 0xce, 0x3a, 0x98, 0x98, 0x4e, 0x3e, 0x08,
    0x20, 0x8f, 0x10, 0xfc, 0xc1, 0xc4, 0xcf, 0x37, 0x8d, 0x69,
    0xd8, 0x57, 0x9d, 0x48, 0x80, 0x6a, 0xef, 0x0c, 0xdd, 0x27,
    0x99, 0xf9, 0xe7, 0xd0, 0xd2, 0x36, 0xd8, 0xed, 0x41, 0x14,
    0x1b, 0x10,
};
static const int sizeof_bench_dilithium_level3_pubkey =
    sizeof(bench_dilithium_level3_pubkey);

#endif /* !WOLFSSL_DILITHIUM_NO_VERIFY */

#ifndef WOLFSSL_DILITHIUM_NO_SIGN

/* raw private key without ASN1 syntax from
 * ./certs/dilithium/bench_dilithium_level5_key.der */
static const unsigned char bench_dilithium_level5_key[] = {
    0xef, 0x49, 0x79, 0x47, 0x15, 0xc4, 0x8a, 0xa9, 0x74, 0x2a,
    0xf0, 0x36, 0x94, 0x5c, 0x91, 0x1c, 0x5d, 0xff, 0x2c, 0x83,
    0xf2, 0x8b, 0x04, 0xfc, 0x5d, 0x64, 0xbd, 0x49, 0x73, 0xcd,
    0xcc, 0x99, 0xfd, 0x0f, 0x8f, 0x6e, 0xad, 0x75, 0x9b, 0xc9,
    0xb1, 0xb9, 0x90, 0x93, 0xbf, 0xce, 0x02, 0x2d, 0x12, 0x0c,
    0x54, 0x2e, 0xe2, 0x3e, 0x52, 0xff, 0xe0, 0x7a, 0xca, 0x2d,
    0x81, 0x84, 0xea, 0x16, 0x1f, 0x10, 0xc4, 0xc9, 0xde, 0xcd,
    0xf6, 0xbd, 0x60, 0xc9, 0xb3, 0xd0, 0x0f, 0x57, 0xeb, 0x71,
    0x78, 0x9b, 0xb5, 0x72, 0x2a, 0x65, 0x11, 0x14, 0xff, 0x63,
    0x8d, 0x38, 0xcf, 0xa4, 0xf4, 0xad, 0xd0, 0x68, 0x84, 0x97,
    0xfe, 0xd3, 0x91, 0xa0, 0xe4, 0xc3, 0x74, 0xcf, 0x20, 0x87,
    0x89, 0x84, 0x1f, 0x75, 0x91, 0xe3, 0xb3, 0x47, 0x8b, 0xfe,
    0x76, 0xb7, 0x2d, 0x30, 0x89, 0x02, 0x04, 0xc9, 0x93, 0xa8,
    0x31, 0xd3, 0x84, 0x2d, 0xe4, 0x26, 0x12, 0xdb, 0x94, 0x08,
    0x12, 0x45, 0x45, 0xca, 0x44, 0x89, 0x52, 0xc4, 0x28, 0x41,
    0x46, 0x01, 0x1c, 0x93, 0x20, 0x8b, 0x40, 0x6d, 0x09, 0x36,
    0x65, 0x4c, 0xa2, 0x40, 0x62, 0xb8, 0x2c, 0x1b, 0x00, 0x20,
    0x61, 0x42, 0x8c, 0x24, 0xa7, 0x10, 0x19, 0x27, 0x25, 0x22,
    0x14, 0x31, 0x13, 0x33, 0x46, 0x0c, 0x22, 0x22, 0x18, 0xa7,
    0x91, 0x0c, 0x24, 0x61, 0xd9, 0x32, 0x46, 0xc8, 0x96, 0x49,
    0x5c, 0x90, 0x89, 0x9b, 0x84, 0x01, 0x5c, 0x08, 0x42, 0x64,
    0x84, 0x85, 0x0c, 0x42, 0x21, 0x20, 0x48, 0x21, 0x92, 0x00,
    0x28, 0x83, 0x20, 0x4c, 0x08, 0xc7, 0x51, 0x99, 0x06, 0x66,
    0x01, 0x18, 0x51, 0x13, 0x48, 0x0a, 0x0b, 0x42, 0x90, 0x4c,
    0x14, 0x08, 0x83, 0x14, 0x6d, 0x10, 0x10, 0x91, 0xe2, 0xc4,
    0x8d, 0xe1, 0x12, 0x11, 0x10, 0x40, 0x29, 0x99, 0x92, 0x30,
    0x12, 0x39, 0x6c, 0x91, 0x86, 0x68, 0x08, 0x83, 0x0c, 0x54,
    0x80, 0x80, 0xa2, 0x08, 0x52, 0x09, 0x30, 0x71, 0x0c, 0x10,
    0x04, 0x53, 0x00, 0x65, 0x91, 0x12, 0x2d, 0x0c, 0xa2, 0x8c,
    0x18, 0x14, 0x45, 0xd8, 0x14, 0x06, 0xe4, 0x36, 0x72, 0x93,
    0x10, 0x68, 0x09, 0xc2, 0x08, 0x51, 0x14, 0x8c, 0x13, 0x39,
    0x11, 0xd8, 0x44, 0x02, 0x18, 0x39, 0x29, 0x98, 0x16, 0x71,
    0x82, 0x40, 0x70, 0x01, 0x10, 0x8c, 0x1a, 0x30, 0x08, 0x02,
    0x03, 0x41, 0x5a, 0x00, 0x40, 0xa4, 0x16, 0x90, 0x20, 0x26,
    0x32, 0x00, 0x49, 0x61, 0x20, 0x20, 0x0c, 0x1a, 0xb0, 0x10,
    0x63, 0x10, 0x11, 0x58, 0x30, 0x0d, 0x59, 0x80, 0x68, 0x90,
    0x46, 0x2a, 0x91, 0xa8, 0x71, 0x98, 0x20, 0x40, 0x21, 0x83,
    0x6c, 0xc0, 0x48, 0x0d, 0x8b, 0x90, 0x11, 0x08, 0x09, 0x31,
    0x8c, 0x00, 0x12, 0x10, 0x14, 0x6e, 0xc2, 0x06, 0x32, 0x1a,
    0x26, 0x10, 0x0a, 0x91, 0x44, 0x08, 0x99, 0x8d, 0x60, 0x86,
    0x28, 0x11, 0x20, 0x6d, 0xa3, 0x12, 0x81, 0x8b, 0xc6, 0x51,
    0xcb, 0xa0, 0x61, 0x09, 0x97, 0x61, 0x48, 0xb6, 0x0d, 0x21,
    0x49, 0x51, 0x08, 0x13, 0x0c, 0x0a, 0x34, 0x86, 0x49, 0x80,
    0x65, 0x14, 0x39, 0x04, 0x21, 0x01, 0x81, 0x9a, 0xb8, 0x4d,
    0x04, 0x41, 0x48, 0x03, 0x92, 0x81, 0x62, 0x14, 0x6c, 0x10,
    0x16, 0x11, 0xe2, 0xa2, 0x49, 0xe3, 0x30, 0x65, 0x04, 0x93,
    0x8d, 0x1c, 0x33, 0x70, 0x1b, 0x15, 0x50, 0xe4, 0x38, 0x80,
    0x21, 0x37, 0x06, 0x20, 0xc6, 0x24, 0xc8, 0x22, 0x88, 0x4a,
    0x44, 0x80, 0x14, 0x43, 0x88, 0x54, 0x44, 0x42, 0x11, 0x49,
    0x41, 0x19, 0xb9, 0x2d, 0xcc, 0x04, 0x0d, 0x19, 0xc1, 0x65,
    0x5b, 0xa0, 0x11, 0x94, 0x00, 0x84, 0xe4, 0xb6, 0x41, 0xc2,
    0x18, 0x72, 0x5c, 0x02, 0x69, 0x11, 0x85, 0x24, 0x13, 0x35,
    0x00, 0x62, 0x34, 0x04, 0x58, 0x40, 0x21, 0x00, 0xc4, 0x28,
    0x0c, 0x17, 0x30, 0x10, 0x47, 0x60, 0x4b, 0xc2, 0x61, 0x9c,
    0x80, 0x2c, 0x20, 0x94, 0x31, 0x58, 0x92, 0x09, 0xcc, 0x00,
    0x02, 0x42, 0x94, 0x69, 0x99, 0x28, 0x06, 0x98, 0x02, 0x52,
    0x90, 0x32, 0x6e, 0x8a, 0x18, 0x2e, 0x54, 0x94, 0x81, 0x03,
    0xc6, 0x89, 0x03, 0xa1, 0x84, 0x48, 0x82, 0x48, 0x52, 0xc4,
    0x00, 0x91, 0x30, 0x24, 0x20, 0x12, 0x0d, 0x83, 0x80, 0x05,
    0x92, 0x48, 0x61, 0x98, 0x46, 0x92, 0xe1, 0xa6, 0x25, 0x20,
    0x93, 0x4d, 0x1c, 0x37, 0x2c, 0x9b, 0x94, 0x8d, 0xc8, 0x88,
    0x80, 0xa2, 0x18, 0x72, 0x0c, 0x09, 0x70, 0x81, 0x36, 0x90,
    0x24, 0x45, 0x69, 0x53, 0x36, 0x6c, 0xd2, 0x20, 0x51, 0x23,
    0xc1, 0x8c, 0x62, 0xb0, 0x70, 0x11, 0xb2, 0x70, 0xcb, 0x84,
    0x69, 0x4b, 0x32, 0x89, 0x01, 0x21, 0x81, 0x02, 0x38, 0x66,
    0xa3, 0x26, 0x12, 0x24, 0xa3, 0x30, 0x22, 0x24, 0x84, 0x18,
    0xb9, 0x84, 0x40, 0x16, 0x50, 0x22, 0x44, 0x31, 0x1b, 0x13,
    0x8d, 0x53, 0x02, 0x89, 0x4a, 0x22, 0x10, 0x53, 0x18, 0x01,
    0x58, 0x30, 0x2d, 0x00, 0x05, 0x08, 0x13, 0x80, 0x84, 0xc2,
    0x22, 0x0e, 0x88, 0x26, 0x2a, 0x04, 0xc4, 0x4c, 0x19, 0x43,
    0x01, 0xc8, 0x38, 0x4c, 0xd1, 0xb2, 0x90, 0x13, 0x29, 0x10,
    0x12, 0x48, 0x22, 0x01, 0xa8, 0x51, 0xd1, 0x92, 0x40, 0x11,
    0x27, 0x62, 0x10, 0x01, 0x0c, 0x0c, 0xc6, 0x28, 0xe3, 0x46,
    0x60, 0x24, 0x01, 0x8d, 0x14, 0xb6, 0x10, 0x50, 0xb6, 0x25,
    0x44, 0x38, 0x40, 0x44, 0xc2, 0x0c, 0x19, 0xc0, 0x64, 0x9c,
    0x44, 0x02, 0x21, 0x25, 0x65, 0x02, 0x23, 0x86, 0x1a, 0x12,
    0x70, 0x51, 0x24, 0x91, 0x09, 0x08, 0x44, 0x09, 0x35, 0x66,
    0x91, 0x04, 0x12, 0x43, 0x42, 0x8d, 0x22, 0xa0, 0x70, 0x14,
    0x91, 0x25, 0xa0, 0x00, 0x80, 0xe4, 0x00, 0x90, 0x44, 0xb2,
    0x61, 0x14, 0x20, 0x6e, 0xca, 0x14, 0x0d, 0x23, 0x85, 0x68,
    0xda, 0x40, 0x92, 0x0b, 0xb1, 0x20, 0x92, 0x04, 0x46, 0xc0,
    0x08, 0x8a, 0x40, 0xc4, 0x4d, 0x0c, 0x17, 0x45, 0xd3, 0x18,
    0x52, 0x1b, 0x46, 0x24, 0xc2, 0x24, 0x71, 0x83, 0x10, 0x80,
    0xc8, 0x82, 0x68, 0xc2, 0x96, 0x81, 0x0a, 0x01, 0x92, 0x60,
    0xb4, 0x84, 0x09, 0xc6, 0x00, 0x04, 0x37, 0x90, 0x0b, 0xa0,
    0x28, 0x12, 0x27, 0x09, 0x94, 0x80, 0x50, 0xd8, 0x04, 0x86,
    0x08, 0x13, 0x8a, 0x4a, 0x06, 0x89, 0x9b, 0xc4, 0x60, 0xe3,
    0xa2, 0x20, 0xe0, 0x38, 0x21, 0x22, 0xb4, 0x68, 0x0a, 0xa1,
    0x0c, 0x01, 0x24, 0x32, 0x4c, 0x48, 0x30, 0xa2, 0x80, 0x8d,
    0x58, 0x44, 0x10, 0xc8, 0x94, 0x6d, 0x21, 0xc3, 0x61, 0xcb,
    0x98, 0x24, 0xdc, 0x38, 0x11, 0xc9, 0x18, 0x11, 0x20, 0x01,
    0x50, 0x1c, 0x34, 0x8d, 0x02, 0x03, 0x09, 0x0a, 0x40, 0x61,
    0xd4, 0xb8, 0x84, 0x9c, 0xc2, 0x09, 0x04, 0xb1, 0x89, 0x83,
    0x86, 0x84, 0x19, 0x83, 0x0c, 0x5a, 0x86, 0x89, 0x10, 0x21,
    0x0d, 0xd1, 0xc2, 0x80, 0x18, 0x29, 0x2a, 0x0c, 0x01, 0x50,
    0x89, 0x88, 0x48, 0x03, 0xa7, 0x85, 0x21, 0x92, 0x64, 0xc4,
    0x16, 0x81, 0x94, 0x06, 0x6c, 0x53, 0x26, 0x12, 0x90, 0xb6,
    0x21, 0x0b, 0xa8, 0x64, 0x43, 0x96, 0x84, 0x41, 0x88, 0x70,
    0xe3, 0xa6, 0x44, 0x12, 0xc0, 0x09, 0x01, 0xc7, 0x60, 0xc3,
    0x20, 0x42, 0xc3, 0x40, 0x68, 0x10, 0xa6, 0x51, 0xa4, 0xa0,
    0x71, 0x54, 0x98, 0x04, 0x88, 0xb2, 0x00, 0x54, 0x18, 0x6a,
    0x48, 0x98, 0x20, 0x21, 0xb2, 0x8d, 0x82, 0x20, 0x81, 0x99,
    0x16, 0x81, 0x0a, 0xc5, 0x88, 0x0a, 0x23, 0x11, 0x8a, 0x16,
    0x44, 0x24, 0xc9, 0x29, 0x59, 0x08, 0x91, 0x1c, 0x29, 0x05,
    0x14, 0xc9, 0x44, 0xe3, 0x20, 0x10, 0x1b, 0xa1, 0x64, 0x82,
    0xa2, 0x90, 0x00, 0x00, 0x82, 0x98, 0xb2, 0x85, 0xc8, 0x04,
    0x28, 0xc8, 0xb2, 0x65, 0xc9, 0xc6, 0x88, 0xcc, 0x08, 0x91,
    0x84, 0x08, 0x30, 0x94, 0x94, 0x8d, 0xc0, 0x18, 0x46, 0x82,
    0x36, 0x4c, 0x83, 0x10, 0x72, 0x23, 0xb1, 0x88, 0x81, 0x20,
    0x8e, 0x19, 0x03, 0x8a, 0x94, 0x46, 0x22, 0x21, 0x35, 0x8e,
    0x04, 0xc0, 0x88, 0x5b, 0xb6, 0x09, 0x0a, 0x18, 0x44, 0x21,
    0x90, 0x65, 0x03, 0xb2, 0x21, 0xc4, 0x10, 0x50, 0xc1, 0x80,
    0x0c, 0x09, 0x40, 0x49, 0xe4, 0xa8, 0x8c, 0xa4, 0x36, 0x61,
    0x59, 0x12, 0x86, 0x20, 0x08, 0x2d, 0x10, 0x19, 0x85, 0xe4,
    0x34, 0x60, 0xc4, 0xb6, 0x60, 0x00, 0x18, 0x06, 0x8c, 0xb8,
    0x45, 0x19, 0x13, 0x4a, 0x53, 0xc4, 0x40, 0xc9, 0x38, 0x71,
    0xd9, 0x48, 0x10, 0x59, 0x08, 0x02, 0x02, 0x10, 0x69, 0x53,
    0x28, 0x80, 0x22, 0x81, 0x4c, 0xc9, 0x16, 0x26, 0xa1, 0x48,
    0x64, 0x19, 0x21, 0x11, 0x1c, 0x37, 0x88, 0x4b, 0x94, 0x2c,
    0x48, 0xc8, 0x6c, 0x63, 0x88, 0x65, 0x81, 0x40, 0x61, 0xa1,
    0x44, 0x31, 0x82, 0x18, 0x08, 0x80, 0x00, 0x26, 0x50, 0x14,
    0x49, 0xa1, 0x32, 0x50, 0x02, 0xc8, 0x45, 0x0c, 0x07, 0x24,
    0x13, 0x01, 0x6d, 0x0a, 0xb3, 0x90, 0x64, 0x30, 0x85, 0x21,
    0x09, 0x61, 0x44, 0x44, 0x72, 0x08, 0x32, 0x06, 0xe1, 0xa2,
    0x21, 0xdb, 0xa4, 0x09, 0x5a, 0xb4, 0x71, 0x43, 0xb2, 0x09,
    0x82, 0xc4, 0x64, 0x88, 0xa0, 0x91, 0xca, 0x14, 0x90, 0xa4,
    0xa8, 0x41, 0xc1, 0x38, 0x85, 0x12, 0x32, 0x60, 0x1a, 0x11,
    0x72, 0x53, 0x32, 0x2c, 0xe3, 0x08, 0x4d, 0x24, 0xc6, 0x28,
    0x0a, 0x03, 0x8c, 0x88, 0x06, 0x05, 0xa0, 0xa8, 0x05, 0x84,
    0xa2, 0x4c, 0x80, 0x40, 0x62, 0xda, 0x24, 0x81, 0x9a, 0x16,
    0x91, 0x24, 0x81, 0x04, 0xa4, 0x46, 0x51, 0xc2, 0xa8, 0x25,
    0x20, 0x28, 0x42, 0x13, 0x46, 0x2c, 0x63, 0x42, 0x72, 0x03,
    0x88, 0x28, 0xa3, 0x22, 0x24, 0x1a, 0x02, 0x26, 0x42, 0xa2,
    0x11, 0x11, 0xb0, 0x51, 0x92, 0xb4, 0x6c, 0xe2, 0x32, 0x85,
    0x10, 0xc2, 0x41, 0xc1, 0x40, 0x46, 0x4c, 0x26, 0x01, 0x1c,
    0x35, 0x02, 0x0c, 0x14, 0x0c, 0x18, 0x81, 0x00, 0x10, 0x26,
    0x02, 0xc8, 0x32, 0x8c, 0xe4, 0x02, 0x68, 0xcc, 0x14, 0x2e,
    0x89, 0x38, 0x60, 0x10, 0x12, 0x24, 0x93, 0x42, 0x65, 0xe3,
    0x24, 0x29, 0x08, 0x80, 0x41, 0x09, 0x29, 0x46, 0x5b, 0x26,
    0x49, 0x5b, 0x30, 0x80, 0x03, 0xc1, 0x2c, 0x04, 0x09, 0x82,
    0x4c, 0x48, 0x2d, 0x1c, 0x36, 0x4d, 0xdb, 0x02, 0x86, 0x21,
    0xb5, 0x51, 0x81, 0x80, 0x2d, 0xcb, 0x20, 0x81, 0x5b, 0x34,
    0x41, 0x89, 0x36, 0x48, 0x44, 0xa0, 0x05, 0x59, 0xb6, 0x64,
    0x12, 0x45, 0x21, 0x20, 0x31, 0x51, 0x0a, 0xc3, 0x8c, 0x14,
    0x48, 0x71, 0x18, 0x35, 0x24, 0x20, 0x45, 0x05, 0x88, 0x20,
    0x09, 0x08, 0xb1, 0x29, 0x18, 0xa0, 0x09, 0x4a, 0x00, 0x8a,
    0xe2, 0xb8, 0x45, 0x02, 0x27, 0x89, 0xd8, 0x10, 0x25, 0x51,
    0x82, 0x8c, 0x13, 0x92, 0x30, 0x1c, 0x24, 0x8e, 0x1c, 0x93,
    0x4d, 0xa3, 0x48, 0x51, 0x93, 0xa8, 0x69, 0xe2, 0x04, 0x89,
    0x13, 0x13, 0x61, 0xcb, 0x98, 0x8c, 0x09, 0x21, 0x62, 0x4b,
    0x14, 0x4e, 0x11, 0xa3, 0x09, 0x98, 0x40, 0x42, 0x91, 0x12,
    0x08, 0x80, 0x84, 0x2d, 0xc0, 0x12, 0x60, 0x03, 0xa4, 0x29,
    0x18, 0x80, 0x01, 0x94, 0x44, 0x8a, 0x12, 0x11, 0x72, 0xc4,
    0x22, 0x32, 0x9a, 0x46, 0x88, 0x1b, 0x16, 0x4d, 0x4b, 0x08,
    0x11, 0x02, 0x48, 0x45, 0x81, 0xa4, 0x64, 0xe1, 0x88, 0x0c,
    0x63, 0x10, 0x70, 0x48, 0x98, 0x05, 0x9b, 0xb8, 0x84, 0x03,
    0x14, 0x05, 0x44, 0x86, 0x0c, 0x20, 0x11, 0x68, 0xbe, 0x71,
    0x83, 0xc2, 0x69, 0xde, 0x49, 0xad, 0xb4, 0xdb, 0x93, 0xcb,
    0x20, 0x2b, 0xbd, 0x95, 0x97, 0x57, 0x7e, 0xcb, 0xbc, 0x73,
    0xb6, 0x3d, 0x16, 0x4a, 0x0e, 0xe4, 0x9c, 0x81, 0xb1, 0x5d,
    0x27, 0x64, 0xa2, 0x14, 0x12, 0x1b, 0x8e, 0xd0, 0xd8, 0x38,
    0xf6, 0xc7, 0xbb, 0x9f, 0x77, 0x3c, 0x62, 0x04, 0x92, 0xe1,
    0x97, 0xaf, 0x24, 0xa7, 0xf9, 0xf0, 0x8d, 0x3a, 0xbf, 0x5d,
    0xab, 0x5c, 0x97, 0x0f, 0xfc, 0x35, 0xbc, 0x62, 0xd8, 0x42,
    0xfd, 0xc7, 0x8b, 0xf7, 0x80, 0xd1, 0x38, 0x68, 0x14, 0x5e,
    0x4f, 0x99, 0x31, 0xc7, 0xaf, 0xbd, 0x27, 0xce, 0x1c, 0x5b,
    0x09, 0x1b, 0xcf, 0xbb, 0xfb, 0xf9, 0xf4, 0x90, 0x4c, 0xc1,
    0xa2, 0x12, 0xf9, 0xd0, 0xa5, 0x2c, 0xfd, 0x7b, 0x55, 0xb0,
    0xb1, 0xc6, 0x42, 0xe6, 0xeb, 0x10, 0x5e, 0xe9, 0x00, 0xe8,
    0x46, 0xe4, 0xe0, 0x8b, 0x21, 0xbc, 0xb1, 0xa9, 0x9e, 0x75,
    0x66, 0xf0, 0xb8, 0x87, 0xb9, 0x11, 0x7e, 0x28, 0x6c, 0x4d,
    0x58, 0xcd, 0x54, 0x71, 0x0c, 0x6a, 0xcc, 0xfb, 0x52, 0xc2,
    0x5b, 0xcc, 0x19, 0x67, 0x4f, 0xc2, 0x2f, 0x09, 0x62, 0x51,
    0x82, 0xeb, 0x9b, 0x94, 0x11, 0xb4, 0x5a, 0x67, 0x7f, 0x58,
    0x18, 0xb2, 0x3f, 0x37, 0x1f, 0x94, 0x44, 0x73, 0x6a, 0x02,
    0xf5, 0xfb, 0x5b, 0x03, 0xac, 0x5d, 0xc6, 0xa9, 0x79, 0x8f,
    0x0f, 0x50, 0xa0, 0x57, 0x46, 0x05, 0x6d, 0x58, 0xde, 0x6e,
    0x8d, 0x9c, 0x0e, 0x6a, 0xb5, 0x9b, 0x1b, 0x22, 0x74, 0xad,
    0x00, 0x55, 0x27, 0x46, 0xce, 0xbb, 0x82, 0x77, 0x4e, 0x6e,
    0x59, 0x38, 0x26, 0xb3, 0xc7, 0xbc, 0x97, 0x54, 0x83, 0x69,
    0x1f, 0x3e, 0xbd, 0x0f, 0xff, 0x2f, 0xca, 0xb9, 0xea, 0x91,
    0x26, 0x8e, 0x0a, 0x78, 0x25, 0xf6, 0x6b, 0x11, 0x30, 0xd7,
    0xe2, 0xf4, 0x2b, 0xda, 0xcf, 0xe1, 0x4a, 0x47, 0xab, 0x5f,
    0x54, 0x34, 0x38, 0xac, 0xd1, 0xbf, 0x45, 0xad, 0x4b, 0x52,
    0x0f, 0x4c, 0xa2, 0xac, 0x22, 0x7c, 0xb6, 0xed, 0x7f, 0xd5,
    0x63, 0x3b, 0x1a, 0x3b, 0xf2, 0x3d, 0x9b, 0x96, 0x92, 0x08,
    0xb9, 0x95, 0x13, 0xaf, 0x20, 0x26, 0x8b, 0x15, 0x97, 0x89,
    0xa5, 0x88, 0x8f, 0x78, 0xb4, 0x57, 0x9d, 0x51, 0x96, 0x9c,
    0x98, 0x93, 0xd5, 0x83, 0xf9, 0xff, 0x94, 0x29, 0x1e, 0xa5,
    0x28, 0xa4, 0x0c, 0x22, 0xab, 0xbc, 0x70, 0x48, 0xa2, 0x16,
    0x1c, 0xa4, 0xba, 0x8b, 0xfe, 0xb2, 0xa9, 0x03, 0x96, 0x5f,
    0xb4, 0x84, 0x8e, 0xb4, 0xbb, 0x7b, 0x11, 0xc5, 0xc2, 0xdb,
    0xe3, 0x88, 0xb5, 0xd3, 0xac, 0x07, 0x33, 0x53, 0xe8, 0x10,
    0x9e, 0xc5, 0x81, 0xb0, 0x77, 0x2f, 0x4f, 0x6d, 0x0d, 0x89,
    0xb4, 0x04, 0x98, 0x05, 0xe6, 0xd3, 0x36, 0x97, 0xcd, 0x3e,
    0x4d, 0xc6, 0x21, 0xe4, 0x0b, 0xcf, 0xed, 0xa7, 0x4d, 0xd9,
    0xd3, 0x25, 0xec, 0xec, 0x47, 0xfd, 0x06, 0x92, 0x77, 0x25,
    0x3c, 0x44, 0xe6, 0x5d, 0xb4, 0x35, 0x2b, 0x5d, 0x05, 0x65,
    0x63, 0x0b, 0xd9, 0xb8, 0x28, 0xdf, 0xdd, 0xfd, 0x64, 0x18,
    0x42, 0x19, 0x7f, 0x12, 0x78, 0xdd, 0xf0, 0x64, 0xd6, 0x99,
    0xb8, 0x74, 0x81, 0xe2, 0xb9, 0xc8, 0x67, 0x6d, 0x31, 0x22,
    0xa5, 0x68, 0xa1, 0x8d, 0x3e, 0x49, 0xbe, 0x10, 0x68, 0xa8,
    0x74, 0x1d, 0x18, 0xcf, 0x00, 0xe1, 0x4f, 0x77, 0xd8, 0xc6,
    0xe3, 0x08, 0xbb, 0x4c, 0xed, 0xff, 0xd9, 0x9b, 0xb0, 0xd1,
    0x50, 0xbb, 0x8b, 0x91, 0xcd, 0x5f, 0x2a, 0xfb, 0x8f, 0x4d,
    0x3c, 0x98, 0xba, 0xd7, 0x98, 0x99, 0xa7, 0x22, 0x14, 0xd7,
    0x94, 0xb5, 0xb8, 0xa4, 0x52, 0x31, 0xa7, 0xa1, 0xa4, 0x28,
    0xee, 0x31, 0xb5, 0xd0, 0xc1, 0x07, 0x05, 0x16, 0x1d, 0x53,
    0x45, 0x62, 0x23, 0x05, 0x44, 0xb6, 0x4f, 0x92, 0x03, 0x53,
    0x9a, 0x71, 0x56, 0xae, 0x16, 0x81, 0xb4, 0xc9, 0x98, 0xf4,
    0x7f, 0x11, 0x37, 0xc2, 0xc8, 0xf2, 0xe4, 0x48, 0xe3, 0xcc,
    0xf1, 0xe3, 0x3d, 0x8e, 0x13, 0x5b, 0x25, 0xad, 0xce, 0x6f,
    0xed, 0x60, 0x4f, 0x7d, 0x51, 0xe1, 0xd0, 0x74, 0xf4, 0xed,
    0xf3, 0x84, 0xa6, 0x0e, 0xba, 0xb4, 0x8e, 0x5a, 0xb9, 0x12,
    0x70, 0x43, 0x4c, 0xb5, 0xa5, 0x1e, 0x86, 0xa5, 0xe3, 0x4d,
    0x76, 0x95, 0xce, 0x2c, 0x53, 0x3a, 0x4e, 0x3f, 0x47, 0x73,
    0x85, 0x88, 0xd9, 0x39, 0x21, 0x83, 0x24, 0x68, 0x6a, 0x1e,
    0x77, 0xdf, 0x59, 0xc5, 0x1b, 0xe2, 0xb1, 0x47, 0x9d, 0xee,
    0x45, 0x1e, 0xc6, 0xd4, 0x43, 0xe2, 0xc7, 0x1c, 0x98, 0x84,
    0xe0, 0x39, 0xe9, 0x9f, 0xa0, 0xa2, 0x24, 0x4a, 0x88, 0x46,
    0xf3, 0x50, 0x52, 0xb5, 0xae, 0x37, 0x5c, 0xa1, 0x7d, 0xad,
    0x7c, 0x30, 0x3e, 0xcd, 0x80, 0x1c, 0xac, 0xf4, 0xe6, 0xb5,
    0x9f, 0x22, 0xb6, 0xfb, 0x0e, 0x6d, 0x80, 0x10, 0xf7, 0x3f,
    0xdd, 0x5b, 0xd9, 0xd4, 0x03, 0x14, 0x41, 0x90, 0x88, 0xa8,
    0xcf, 0x50, 0xa2, 0xf2, 0x7e, 0xf0, 0x0a, 0x7f, 0xed, 0x77,
    0x09, 0x48, 0x32, 0x55, 0xe9, 0x93, 0xe7, 0x27, 0x18, 0x46,
    0x17, 0x03, 0x25, 0x8e, 0x17, 0x5d, 0xe8, 0x9e, 0xb1, 0xb4,
    0x9d, 0x1a, 0x5e, 0xbe, 0xa8, 0xb8, 0x45, 0x30, 0xc6, 0xa5,
    0xb4, 0xaf, 0xf3, 0x0d, 0x91, 0x9c, 0xa9, 0x5b, 0x4c, 0xbb,
    0x19, 0x19, 0x39, 0x51, 0x36, 0x80, 0xf7, 0x10, 0xf7, 0x73,
    0x49, 0x17, 0xec, 0xbc, 0x92, 0x08, 0x21, 0xb1, 0x0c, 0x23,
    0xc4, 0xd6, 0xd2, 0xb3, 0xfd, 0xae, 0xe7, 0x71, 0xf3, 0x50,
    0x11, 0x27, 0x1a, 0x85, 0xf0, 0xab, 0xd8, 0x16, 0x64, 0xcb,
    0xad, 0xbb, 0xae, 0x54, 0x37, 0xa3, 0xa8, 0xf4, 0x09, 0x67,
    0x54, 0x61, 0x86, 0x0f, 0x0e, 0x25, 0x0d, 0xda, 0x4a, 0xc7,
    0xe7, 0x02, 0x80, 0x6b, 0x59, 0xd2, 0xc8, 0x88, 0x4d, 0x7d,
    0xfd, 0x3d, 0x48, 0x04, 0x6d, 0x95, 0xdf, 0xc2, 0x8b, 0x23,
    0x70, 0x4a, 0xf5, 0xdc, 0xc9, 0x24, 0x8d, 0x7e, 0x52, 0x22,
    0x7e, 0x9c, 0x5c, 0x32, 0xa5, 0xd5, 0xf2, 0x11, 0x08, 0xa0,
    0xd4, 0xa2, 0xd8, 0xdb, 0x1d, 0x9f, 0x1b, 0x54, 0x8f, 0xb5,
    0xf6, 0x71, 0x71, 0x49, 0xbc, 0x38, 0x09, 0xb6, 0x24, 0x94,
    0x80, 0x1f, 0x2d, 0x0c, 0xc7, 0xe4, 0xd6, 0xcd, 0xab, 0x53,
    0x79, 0x28, 0xed, 0x48, 0x23, 0x14, 0x2f, 0x0b, 0x3a, 0xd0,
    0xa7, 0x08, 0xe1, 0xfd, 0x1e, 0xb6, 0xdd, 0x12, 0x93, 0x2d,
    0x95, 0x06, 0xba, 0x95, 0xcb, 0x1a, 0xed, 0xfb, 0x60, 0xe7,
    0xf1, 0x1c, 0xad, 0xc3, 0xea, 0x8d, 0x3c, 0x53, 0x32, 0xb5,
    0x38, 0x26, 0xdd, 0x39, 0xf0, 0x39, 0x4e, 0x6f, 0x3e, 0xa9,
    0xea, 0x25, 0x29, 0xb8, 0x6c, 0x7d, 0x0a, 0x91, 0xd4, 0xb9,
    0x7b, 0x67, 0xe4, 0xe5, 0x63, 0xd7, 0x6b, 0x03, 0xa5, 0xd7,
    0xe8, 0xd2, 0xc0, 0x34, 0x53, 0xa6, 0x16, 0x21, 0x2a, 0x2a,
    0x09, 0xd3, 0xad, 0xa1, 0x2c, 0x6a, 0x88, 0x2d, 0x90, 0x06,
    0xba, 0x0b, 0xaa, 0xd1, 0xdb, 0xa4, 0xd0, 0x49, 0x0f, 0x42,
    0xe1, 0xca, 0xf0, 0x69, 0x15, 0x63, 0xcb, 0x0b, 0x4c, 0x2e,
    0x99, 0x20, 0x44, 0xe3, 0x6e, 0x32, 0x8a, 0xa1, 0x5c, 0x5b,
    0x03, 0xeb, 0xb5, 0x05, 0xff, 0x1a, 0x76, 0x38, 0x1c, 0xb0,
    0x74, 0xf1, 0x5a, 0x0d, 0x8a, 0xd2, 0x4e, 0x38, 0x11, 0x86,
    0xb0, 0x2d, 0xd3, 0x88, 0xe2, 0x0f, 0x51, 0x68, 0xb9, 0x79,
    0x96, 0x50, 0x95, 0xdc, 0x69, 0xcb, 0xa6, 0x25, 0x4a, 0xdf,
    0xa1, 0x39, 0x13, 0x47, 0x0a, 0xf0, 0xeb, 0xcb, 0x14, 0x01,
    0x28, 0x9c, 0x0f, 0xe2, 0x62, 0xca, 0xb5, 0x40, 0x51, 0x45,
    0x8e, 0x18, 0x88, 0xc9, 0x58, 0xaf, 0xb3, 0x48, 0xd5, 0x20,
    0xe8, 0xd8, 0x5b, 0xa2, 0x98, 0x74, 0x25, 0xfa, 0x25, 0x19,
    0x82, 0x22, 0xfa, 0x82, 0x7c, 0x38, 0x8d, 0x62, 0x86, 0x01,
    0x63, 0x20, 0x36, 0x8e, 0xaf, 0x15, 0x8a, 0x74, 0x1e, 0xfd,
    0x7f, 0xbe, 0x60, 0xc3, 0x65, 0x31, 0xce, 0xdb, 0x92, 0xb9,
    0x13, 0x2a, 0x78, 0xa9, 0xfc, 0x6a, 0x7b, 0x18, 0xec, 0x0c,
    0x7b, 0x4c, 0x86, 0xaf, 0xea, 0x6d, 0x52, 0x09, 0x76, 0x52,
    0x87, 0x8a, 0x0b, 0x2a, 0xf3, 0x93, 0x35, 0x92, 0x8b, 0x60,
    0x42, 0x2e, 0x12, 0xa9, 0xf7, 0x7c, 0x61, 0x5c, 0x8f, 0xc0,
    0xaa, 0x6e, 0x6a, 0xf6, 0x48, 0x48, 0xc6, 0x3e, 0xe0, 0x1d,
    0xb4, 0xfb, 0xc4, 0xd8, 0x01, 0xb8, 0xf2, 0xf4, 0xdf, 0xc1,
    0xba, 0xb5, 0xf2, 0x27, 0x3f, 0xdb, 0x78, 0x62, 0x1c, 0x0a,
    0xbe, 0xdb, 0xdd, 0x3c, 0x0c, 0x29, 0x85, 0xf1, 0x44, 0x5f,
    0x2b, 0x43, 0x80, 0x57, 0xa7, 0x5a, 0x4d, 0x1b, 0xbe, 0x03,
    0xe7, 0x55, 0x7b, 0x91, 0x9d, 0x4c, 0x8b, 0xd7, 0xfd, 0xde,
    0x65, 0x7e, 0xa8, 0x48, 0xbb, 0xa9, 0x96, 0x06, 0x7f, 0xc0,
    0x6c, 0xed, 0x87, 0x53, 0x77, 0xb4, 0x5a, 0x7c, 0xbb, 0xce,
    0xcf, 0x01, 0x08, 0x45, 0x61, 0xc1, 0x28, 0xb6, 0xf2, 0xb4,
    0x5b, 0x6b, 0x84, 0xfe, 0x18, 0x09, 0x39, 0xc1, 0xc8, 0x96,
    0x36, 0x6e, 0xba, 0x7e, 0x48, 0x12, 0xe6, 0xdc, 0x22, 0x48,
    0x17, 0x0b, 0xbd, 0x92, 0x64, 0xfa, 0xc9, 0x9b, 0x07, 0xda,
    0xed, 0x04, 0x68, 0x42, 0x15, 0x8c, 0xf9, 0xd8, 0xc3, 0x0d,
    0x21, 0x9d, 0x96, 0xbc, 0xc3, 0x07, 0x1a, 0x2c, 0x59, 0x3f,
    0x1a, 0x83, 0x43, 0xf0, 0xe0, 0xde, 0xe3, 0x40, 0x8e, 0x04,
    0x66, 0x3c, 0x87, 0x1e, 0xfa, 0x7b, 0x8a, 0x7b, 0xd2, 0x9e,
    0x15, 0xf5, 0xec, 0x3c, 0x72, 0x7e, 0x2d, 0x19, 0xf8, 0xfd,
    0xf0, 0x28, 0x71, 0x8a, 0xf5, 0xcb, 0x4c, 0x61, 0x5f, 0x85,
    0xe0, 0x6f, 0xb8, 0xf3, 0x17, 0x10, 0xcb, 0x44, 0x45, 0x8c,
    0x96, 0x08, 0xa1, 0xf1, 0x48, 0xa4, 0x1d, 0xea, 0x35, 0x2f,
    0x82, 0x2b, 0xc2, 0x0b, 0xef, 0x73, 0xe1, 0xc2, 0x35, 0xdb,
    0xe7, 0x68, 0xfd, 0xb0, 0xe8, 0x7b, 0x2d, 0x0f, 0xfd, 0x53,
    0x1b, 0xb8, 0x36, 0x54, 0xd6, 0x43, 0x30, 0xcf, 0x83, 0xb0,
    0x18, 0xda, 0x9b, 0x86, 0x82, 0xfa, 0xe6, 0x37, 0x5b, 0x9e,
    0xa4, 0xdb, 0x7c, 0x59, 0x25, 0x59, 0xc6, 0x46, 0x36, 0x72,
    0xc5, 0x72, 0xd8, 0x2f, 0x26, 0xe2, 0xee, 0xe3, 0xcb, 0xe5,
    0x33, 0x1f, 0x18, 0x2e, 0x16, 0xce, 0xd2, 0x9c, 0x89, 0x6e,
    0xd5, 0x21, 0xfa, 0x58, 0x83, 0xa9, 0x4c, 0x69, 0x97, 0x7d,
    0xae, 0x1f, 0x65, 0xd5, 0xdb, 0xf0, 0xfe, 0xd5, 0x32, 0xb1,
    0x50, 0x72, 0xdf, 0x2b, 0xe2, 0xc1, 0xe6, 0x2e, 0x8b, 0x87,
    0xa8, 0x4e, 0x84, 0xbe, 0xc9, 0x27, 0xb5, 0x74, 0x7e, 0x13,
    0x17, 0x57, 0x9c, 0xc6, 0xd3, 0x9f, 0xcd, 0x86, 0x50, 0x4b,
    0x6c, 0x50, 0xa2, 0xba, 0xfe, 0xf6, 0xd5, 0x85, 0x68, 0x31,
    0x89, 0xfb, 0xeb, 0xfe, 0x92, 0xb0, 0xd0, 0x4c, 0xbc, 0x65,
    0x4b, 0x62, 0xe2, 0xdf, 0x88, 0x7e, 0x90, 0xe0, 0xb3, 0xec,
    0x13, 0x69, 0x33, 0xea, 0x53, 0x69, 0x9a, 0x0b, 0x27, 0xfb,
    0xca, 0x9f, 0x9e, 0x1f, 0xcf, 0xb1, 0xeb, 0xf4, 0x8f, 0xe2,
    0x53, 0xc8, 0xe6, 0x51, 0x75, 0xee, 0xb1, 0x34, 0x3e, 0x37,
    0xdd, 0x2d, 0x3a, 0x72, 0x76, 0x33, 0xc1, 0x27, 0xe7, 0xbd,
    0xc1, 0x7f, 0xcb, 0x53, 0x5d, 0xdf, 0xc4, 0x1f, 0x36, 0xdb,
    0x6a, 0x91, 0x1f, 0x6a, 0xa5, 0xc6, 0xe2, 0x37, 0x68, 0x1a,
    0x7d, 0xf7, 0xed, 0x2a, 0xc7, 0x99, 0x5e, 0xbd, 0x59, 0x57,
    0x09, 0x22, 0x7e, 0x9c, 0xbd, 0x8e, 0xad, 0xbe, 0xee, 0xa5,
    0x2a, 0xe3, 0x9f, 0xff, 0x14, 0xda, 0xba, 0x90, 0x37, 0xba,
    0x3a, 0x42, 0xcd, 0x4a, 0x28, 0x47, 0x27, 0x58, 0x7a, 0x33,
    0x93, 0x77, 0x83, 0x29, 0xab, 0x47, 0x19, 0x43, 0x00, 0x6f,
    0xe7, 0x77, 0xc1, 0xaa, 0xd6, 0xbc, 0xc0, 0x1b, 0xd0, 0xdf,
    0xf9, 0x40, 0x4d, 0xb2, 0x60, 0xce, 0x59, 0x17, 0x0a, 0xa9,
    0x14, 0x4e, 0x6a, 0x30, 0x1b, 0x26, 0x68, 0x55, 0x12, 0x19,
    0x62, 0x85, 0x5d, 0xa6, 0xb4, 0x48, 0x4a, 0xe9, 0xe1, 0x57,
    0xb1, 0x48, 0xf3, 0x86, 0xd1, 0x50, 0x2e, 0x1d, 0x57, 0xbe,
    0x09, 0xf8, 0x53, 0x40, 0xd9, 0x55, 0xd9, 0x71, 0x4c, 0xa7,
    0xdb, 0x61, 0x82, 0x4e, 0x00, 0x58, 0xe4, 0x89, 0xae, 0xa6,
    0x1a, 0x4b, 0xe3, 0x9d, 0xec, 0x65, 0xee, 0xe1, 0x7b, 0xdb,
    0x4f, 0x8d, 0xf3, 0xd9, 0x89, 0xaa, 0xd1, 0x31, 0x30, 0xde,
    0xc3, 0x5c, 0xbc, 0xb9, 0x60, 0x0a, 0xe0, 0x13, 0x14, 0x85,
    0x08, 0x60, 0xc5, 0x1c, 0xc2, 0x9d, 0x8b, 0x6e, 0xb8, 0x94,
    0x11, 0x6f, 0xd3, 0xee, 0xfb, 0xf8, 0x15, 0xd8, 0xa4, 0x0b,
    0x92, 0xdf, 0x7c, 0x9a, 0xa2, 0xec, 0xa3, 0x3d, 0xbc, 0xcd,
    0xe8, 0xb5, 0xb3, 0xf5, 0xe8, 0xee, 0x2a, 0x57, 0xf7, 0x58,
    0xc4, 0xaa, 0xeb, 0x33, 0x44, 0x5f, 0x62, 0xbe, 0x90, 0x48,
    0xe5, 0xcb, 0x6a, 0xcb, 0x55, 0x94, 0x6d, 0xe6, 0x22, 0x03,
    0xeb, 0xcb, 0x05, 0xb8, 0xb4, 0xa5, 0xbe, 0xec, 0x79, 0x21,
    0x0d, 0xb3, 0x5c, 0x74, 0x11, 0xcb, 0xb3, 0xa6, 0x06, 0x2f,
    0x73, 0xd1, 0x14, 0xd9, 0x70, 0x4e, 0xc5, 0xf5, 0xff, 0xfd,
    0x49, 0x3b, 0xa9, 0x22, 0x80, 0x2a, 0x5e, 0xf9, 0xae, 0xa5,
    0xd4, 0x3c, 0x74, 0xd7, 0x5a, 0x5d, 0x88, 0x6f, 0x99, 0xe2,
    0x4c, 0xa3, 0x9b, 0x15, 0xb8, 0xfd, 0x0b, 0x0d, 0x57, 0x03,
    0xe8, 0xda, 0x78, 0xc4, 0x63, 0x49, 0x48, 0x7a, 0x39, 0xcd,
    0xfa, 0xad, 0x92, 0x55, 0x4a, 0x0e, 0x68, 0x08, 0xb9, 0x34,
    0xe0, 0x14, 0x6e, 0x19, 0xed, 0x69, 0x14, 0x7f, 0xc1, 0x7d,
    0x12, 0xac, 0x5d, 0xf7, 0x62, 0x6f, 0x77, 0x65, 0xa3, 0xc2,
    0xf9, 0xda, 0x43, 0x9e, 0x6b, 0x82, 0xd9, 0x14, 0x57, 0x02,
    0x09, 0x9f, 0xa7, 0x15, 0x27, 0xe8, 0xad, 0xa1, 0x73, 0xc7,
    0xb6, 0x11, 0x4c, 0x5e, 0xf4, 0x1a, 0x0a, 0x97, 0x98, 0x5e,
    0x29, 0x8a, 0x8b, 0xa5, 0xbd, 0x86, 0x7f, 0x6d, 0x31, 0x72,
    0x6d, 0xe5, 0xcf, 0x13, 0xff, 0xb9, 0x4e, 0x69, 0x66, 0x37,
    0x1b, 0xfb, 0xe8, 0xb7, 0x60, 0xfe, 0xbf, 0xaa, 0x06, 0x88,
    0xa4, 0xa2, 0x0b, 0x33, 0x55, 0xac, 0x61, 0x77, 0x0a, 0x6f,
    0x1f, 0xaf, 0xd8, 0x9b, 0xc7, 0x26, 0x13, 0xf6, 0xc4, 0xef,
    0xce, 0x0f, 0x16, 0x86, 0x64, 0x1b, 0xc0, 0x71, 0x35, 0xf9,
    0x1f, 0xaf, 0xc4, 0x7a, 0xa3, 0x3b, 0x89, 0x40, 0xcb, 0x09,
    0x11, 0x7b, 0x01, 0x54, 0xd5, 0xd2, 0x2a, 0xc8, 0xfe, 0x0e,
    0xef, 0x8c, 0xfb, 0x2b, 0x08, 0x12, 0x6d, 0xbb, 0xa8, 0x2e,
    0x7a, 0x2b, 0xc2, 0x91, 0x2a, 0x76, 0x0b, 0x31, 0x30, 0x4a,
    0x5b, 0xca, 0x96, 0xc9, 0x89, 0xa0, 0x12, 0x40, 0x76, 0xbe,
    0xcd, 0x59, 0x5f, 0xc2, 0x7b, 0xaf, 0xf6, 0x29, 0xde, 0xe9,
    0x24, 0x61, 0x3f, 0x46, 0x78, 0xa7, 0xda, 0x65, 0xb0, 0xb3,
    0xae, 0xf3, 0x72, 0x6e, 0x37, 0x6e, 0xae, 0xb1, 0x3b, 0xf6,
    0x60, 0xa1, 0x92, 0x86, 0x9e, 0x97, 0x4f, 0x5e, 0x86, 0x88,
    0x32, 0x06, 0x7c, 0xe3, 0x37, 0x7e, 0xb1, 0x83, 0xf5, 0x83,
    0x05, 0x43, 0xb3, 0xe3, 0xa1, 0x68, 0xe5, 0x4c, 0x92, 0x9c,
    0x61, 0xa3, 0x5d, 0xcf, 0x23, 0xe7, 0xce, 0xf5, 0x7f, 0xbb,
    0xf7, 0x89, 0x5e, 0xa8, 0xf0, 0xa1, 0xff, 0x1a, 0xaf, 0x15,
    0xc8, 0x3d, 0x8b, 0xce, 0x06, 0xa4, 0x60, 0xd6, 0x40, 0x19,
    0x48, 0x33, 0x53, 0x34, 0x9e, 0xd8, 0x75, 0xfc, 0x45, 0x73,
    0x35, 0x8f, 0x70, 0x04, 0x80, 0xa1, 0xe5, 0xfc, 0x98, 0xb0,
    0x52, 0x63, 0x41, 0x84, 0x57, 0xa2, 0x85, 0x4e, 0x68, 0x13,
    0x2d, 0x3e, 0x4b, 0x68, 0x7f, 0x43, 0x04, 0x05, 0x02, 0x5a,
    0x16, 0x67, 0x5a, 0xc5, 0xea, 0xac, 0x25, 0x61, 0xd4, 0xa4,
    0xe7, 0xbe, 0x13, 0x95, 0xbd, 0x03, 0xb4, 0x26, 0xe3, 0xbf,
    0x7e, 0xe5, 0x0b, 0x34, 0xeb, 0x59, 0x5d, 0xd7, 0xdb, 0x1e,
    0x07, 0xfc, 0x63, 0xab, 0xbb, 0xc6, 0x7a, 0x51, 0x50, 0x59,
    0x13, 0x4b, 0x27, 0x88, 0x98, 0xdc, 0x01, 0x37, 0xeb, 0x58,
    0x75, 0xde, 0x5a, 0xa4, 0x6b, 0xdd, 0xba, 0x01, 0x40, 0xf7,
    0x1c, 0x0a, 0xf3, 0x02, 0x3d, 0x54, 0x64, 0xf2, 0x85, 0x43,
    0x90, 0xc0, 0x69, 0x18, 0x94, 0x95, 0x6e, 0x57, 0x14, 0xda,
    0x27, 0x0a, 0x42, 0xb2, 0x5a, 0x78, 0xe4, 0xf1, 0x45, 0x85,
    0x54, 0xec, 0x44, 0xa0, 0xcb, 0xf4, 0xd1, 0x3a, 0x85, 0x74,
    0x0f, 0x04, 0x67, 0xf4, 0x42, 0x01, 0xc4, 0x04, 0x66, 0x48,
    0x6c, 0xbe, 0x84, 0x38, 0x6e, 0xda, 0x23, 0xd0, 0xd1, 0x26,
    0x94, 0x11, 0x65, 0x2e, 0xc6, 0xd8, 0x6e, 0x25, 0x17, 0x43,
    0x9f, 0x55, 0x2d, 0x1d, 0x55, 0xa9, 0xdd, 0x3b, 0xc7, 0x09,
    0xde, 0x26, 0x64, 0xd4, 0x85, 0x21, 0x15, 0x0d, 0x4a, 0x45,
    0x4d, 0xba, 0x13, 0x9e, 0x3b, 0x5e, 0xc2, 0xf7, 0xc1, 0x34,
    0xc5, 0x74, 0xd4, 0x95, 0x19, 0x3d, 0x69, 0x9c, 0xae, 0xef,
    0x13, 0x95, 0x2c, 0x77, 0xdd, 0x64, 0x2c, 0x12, 0x31, 0x7d,
    0xb5, 0x55, 0xde, 0x69, 0x35, 0x3f, 0x77, 0x72, 0xc6, 0x21,
    0x22, 0x23, 0x7a, 0x05, 0xbf, 0x92, 0xae, 0x49, 0x7f, 0x74,
    0x17, 0x97, 0x5f, 0x5b, 0x4d, 0x7d, 0x86, 0x23, 0x04, 0xe0,
    0xff, 0x10, 0x06, 0xc3, 0xd3, 0x05, 0xde, 0xc4, 0xae, 0xaf,
    0x3d, 0x2d, 0xaf, 0x3c, 0xaf, 0xd3, 0xd5, 0xfd, 0x84, 0xd8,
    0x3b, 0x6c, 0x8e, 0x8b, 0x23, 0x8b, 0x16, 0xaa, 0x67, 0xf1,
    0xde, 0xa4, 0x4b, 0x5a, 0x39, 0x60, 0x73, 0xd2, 0x9f, 0x1f,
    0x8c, 0xcf, 0xbc, 0xaa, 0x74, 0x9e, 0x8d, 0xfd, 0xc3, 0xb7,
    0x86, 0xe5, 0xbb, 0x5a, 0x4d, 0x3d, 0xe2, 0xc3, 0x28, 0x78,
    0x26, 0xd4, 0xb3, 0x45, 0x94, 0xd3, 0x2d, 0xbf, 0x8c, 0x92,
    0x56, 0x3c, 0x6e, 0xea, 0x53, 0x38, 0x7f, 0x22, 0x67, 0xc9,
    0xa7, 0x14, 0x20, 0xb9, 0x13, 0xc4, 0xa0, 0x44, 0x83, 0xc4,
    0x19, 0xca, 0x98, 0x71, 0xc7, 0x13, 0x70, 0x3a, 0xa7, 0xfb,
    0x9e, 0xc4, 0x94, 0x8c, 0xfd, 0x21, 0x36, 0x88, 0xea, 0x23,
    0xc7, 0x43, 0x52, 0x9f, 0xf4, 0x9e, 0xb1, 0xb4, 0xd3, 0x20,
    0x65, 0xd8, 0x18, 0x25, 0x80, 0xb7, 0xe4, 0x5c, 0x96, 0x3a,
    0xa3, 0xb5, 0x40, 0x63, 0xac, 0x02, 0x34, 0x51, 0xf7, 0x12,
    0xea, 0x97, 0x9d, 0x3e, 0xe7, 0xcb, 0x88, 0x15, 0xaa, 0xe3,
    0xfe, 0xe5, 0x42, 0xe5, 0x48, 0xcf, 0xc6, 0x8e, 0x0e, 0xc6,
    0x48, 0xdb, 0xe5, 0x1e, 0x79, 0x99, 0xed, 0x78, 0xa6, 0x37,
    0xdd, 0xe3, 0x7b, 0x01, 0xdd, 0x20, 0x63, 0x45, 0x57, 0xd1,
    0x0f, 0x05, 0x5d, 0x29, 0xad, 0x99, 0x6c, 0x27, 0xa3, 0x0c,
    0x72, 0x81, 0xb1, 0x26, 0x16, 0xaf, 0x11, 0x65, 0xba, 0x79,
    0xbc, 0xb8, 0xfe, 0xe7, 0xc5, 0xe6, 0x4c, 0xfa, 0x37, 0xc5,
    0xe0, 0x2e, 0x4e, 0xef, 0x75, 0xe4, 0x04, 0xaf, 0xfa, 0x41,
    0x7f, 0x58, 0x2e, 0x8f, 0x95, 0x5f, 0x15, 0x5c, 0x15, 0x23,
    0x81, 0xb7, 0x2c, 0x81, 0x70, 0xf5, 0xcc, 0x60, 0x09, 0x7e,
    0xf1, 0x0d, 0x9c, 0x9d, 0xcc, 0xa0, 0x30, 0xa8, 0x82, 0x23,
    0x5f, 0x94, 0xcb, 0x18, 0xc4, 0x32, 0xe6, 0xab, 0xcd, 0x96,
    0x9e, 0xab, 0xcd, 0x68, 0x6f, 0x88, 0xb7, 0x72, 0x65, 0xbc,
    0x1e, 0x05, 0x60, 0xfe, 0x6b, 0x77, 0x2a, 0x11, 0x63, 0x59,
    0x29, 0xdb, 0xba, 0xe0, 0x50, 0xd5, 0x51, 0x77, 0x16, 0xb8,
    0xb7, 0xf4, 0xa9, 0xbe, 0xf0, 0xa5, 0xaa, 0x20, 0x50, 0x2e,
    0x73, 0x21, 0xee, 0x77, 0xa3, 0xc8, 0xbc, 0x0c, 0x16, 0x0f,
    0x83, 0x7b, 0xaf, 0xbb, 0x91, 0x95, 0xd3, 0x6e, 0xe7, 0x28,
    0x77, 0x00, 0xbc, 0x83, 0x46, 0xa5, 0x0a, 0x19, 0xe8, 0x10,
    0xfb, 0x24, 0xeb, 0x27, 0xc2, 0xa3, 0xdd, 0xb8, 0x5b, 0x27,
    0xb9, 0xbb, 0x49, 0xd9, 0xd0, 0x32, 0x94, 0x48, 0x1b, 0xb8,
    0xf8, 0xb2, 0x30, 0xf4, 0x1f, 0x3d, 0xbf, 0xe6, 0xf3, 0x34,
    0xd3, 0x32, 0x85, 0x67, 0x85, 0x13, 0x3e, 0x20, 0xb7, 0xfa,
    0x74, 0x27, 0x74, 0x8f, 0x55, 0x47, 0x15, 0x91, 0x0b, 0x3f,
    0xb1, 0x18, 0xe7, 0x11, 0x1e, 0x52, 0xd8, 0xd1, 0x3f, 0xb9,
    0x5d, 0x4f, 0x88, 0xb9, 0x1e, 0x5a, 0xb6, 0x90, 0x64, 0xad,
    0x6f, 0x8d, 0x33, 0xb3, 0x57, 0xde, 0x3e, 0x13, 0xb3, 0x9f,
    0x2d, 0x00, 0xb1, 0x79, 0x84, 0x60, 0x6d, 0x3c, 0x5f, 0xc0,
    0x34, 0x08, 0x4b, 0x58, 0x33, 0x59, 0xfe, 0xe5, 0xed, 0xd3,
    0x10, 0xd8, 0xd8, 0x85, 0xc3, 0xc9, 0x71, 0xcf, 0x40, 0x96,
    0xc0, 0xd5, 0x5e, 0x62, 0xe7, 0xcb, 0x33, 0xee, 0x72, 0xb5,
    0xb8, 0x6e, 0xea, 0x13, 0xde, 0xeb, 0x82, 0x03, 0x8e, 0x6c,
    0xb3, 0x67, 0xb1, 0x5f, 0xd4, 0xe1, 0xd9, 0xc2, 0x7a, 0x97,
    0xbb, 0xd4, 0x5e, 0x0b, 0xfe, 0xc1, 0xb3, 0x1f, 0x2b, 0x1a,
    0x37, 0x98, 0x26, 0x27, 0xb1, 0xaf, 0x4c, 0x55, 0xe1, 0xae,
    0x4c, 0x86, 0x80, 0x4b, 0xc5, 0xf2, 0x35, 0x48, 0x81, 0xf7,
    0x83, 0x75, 0x63, 0x08, 0x0d, 0x77, 0x41, 0x14, 0xbc, 0xf3,
    0x6e, 0x46, 0xbd, 0x9c, 0x5a, 0x4f, 0x5c, 0x89, 0x26, 0xb6,
    0x6c, 0xde, 0x0d, 0x15, 0x31, 0xec, 0x7e, 0x13, 0xf2, 0x99,
    0x74, 0x40, 0x3c, 0xe1, 0xea, 0xa0, 0xc9, 0x99, 0x0a, 0x4b,
    0x17, 0x74, 0xff, 0x47, 0x15, 0x76, 0x5e, 0x44, 0xa2, 0x1c,
    0x93, 0xd3, 0xe6, 0xa2, 0x82, 0x0f, 0x7f, 0x55, 0xa8, 0xf3,
    0x79, 0xc3, 0xa8, 0x9f, 0x37, 0x2b, 0x97, 0x7e, 0x90, 0x71,
    0xfc, 0xa7, 0xff, 0xc6, 0xc7, 0x93, 0x5c, 0xc9, 0xed, 0x20,
    0x60, 0xbd, 0x5c, 0x36, 0x05, 0x55, 0x51, 0x55, 0x51, 0x15,
    0x36, 0x01, 0x17, 0xa9, 0x56, 0x27, 0x44, 0x66, 0xc9, 0x3a,
    0xb9, 0xbb, 0xee, 0x04, 0xb6, 0x2a, 0xfd, 0x10, 0x9a, 0x46,
    0xdd, 0x5d, 0x6d, 0xad, 0x21, 0x86, 0x6d, 0x62, 0x8a, 0x4a,
    0xbc, 0x73, 0xf0, 0x9d, 0x93, 0x0d, 0xf1, 0x62, 0xfa, 0x58,
    0x64, 0x37, 0x4f, 0x0b, 0xa3, 0xa1, 0x52, 0xce, 0x03, 0xce,
    0x0f, 0x77, 0x29, 0xad, 0x47, 0x38, 0xca, 0xbc, 0x61, 0xe6,
    0xad, 0xe4, 0x8b, 0xf1, 0x82, 0xa8, 0xd5, 0xe3, 0x8c, 0xd3,
    0xa0, 0xc4, 0xc0, 0x5e, 0x3b, 0xa1, 0x66, 0x2a, 0x6e, 0x88,
    0x24, 0x56, 0xe4, 0x84, 0x0a, 0x36, 0x72, 0xf3, 0x5c, 0x11,
    0xd9, 0x66, 0xd8, 0x45, 0x5c, 0x83, 0x9e, 0x1c, 0x8c, 0xc6,
    0xf6, 0x6e, 0x6a, 0xb1, 0x52, 0xed, 0x6c, 0x6a, 0x6d, 0x23,
    0xb9, 0x0b, 0x66, 0x26, 0x5a, 0x16, 0x16, 0x90, 0x43, 0xb9,
    0xc3, 0x02, 0xc1, 0x43, 0x93, 0x13, 0x94, 0xfe, 0xc3, 0x59,
    0x49, 0xbe, 0x1e, 0x26, 0x1b, 0x9d, 0x8e, 0xba, 0xc4, 0x29,
    0x51, 0x05, 0x28, 0x1f, 0x55, 0x59, 0x1c, 0x3e, 0x25, 0x86,
    0xcc, 0xc7, 0xd9, 0xd3, 0xa8, 0xe7, 0x10, 0xa0, 0xb6, 0x23,
    0xb9, 0xaf, 0x00, 0x8b, 0x7d, 0xf1, 0x5b, 0xd6, 0xb7, 0x56,
    0x44, 0x9b, 0x0a, 0xec, 0xa6, 0x2b, 0xb4, 0x4e, 0x1d, 0x4f,
    0xc5, 0x0b, 0x45, 0xd2, 0x3a, 0xc5, 0xc0, 0xbf, 0xb9, 0xdd,
    0x59, 0x21, 0xf2, 0x67, 0x25, 0x88, 0x9b, 0xb6, 0x66, 0x83,
    0xbf, 0x62, 0xfe, 0x7c, 0xfa, 0x9e, 0x50, 0xed, 0x15, 0x93,
    0xb6, 0x7a, 0xb0, 0xc4, 0xbe, 0xcf, 0x2a, 0x70, 0x4e, 0x52,
    0x20, 0xc1, 0x24, 0x08, 0x49, 0xd9, 0x05, 0x04, 0x53, 0x73,
    0xf3, 0xcf, 0x14, 0x70, 0xac, 0x3c, 0x45, 0x0f, 0x08, 0xa3,
    0xae, 0x43, 0xe7, 0x7f, 0x1f, 0xe2, 0x14, 0xf1, 0xbb, 0x25,
    0x20, 0xfd, 0xe4, 0xaf, 0x44, 0x9e, 0x77, 0x88, 0x4d, 0x26,
    0x09, 0xb1, 0xb0, 0x12, 0xf5, 0xdf, 0x3c, 0x53, 0x48, 0x78,
    0xb9, 0x60, 0x41, 0xd3, 0x8f, 0x8d, 0x11, 0x63, 0x60, 0x28,
    0x30, 0x07, 0xa2, 0x14, 0x3b, 0x8c, 0x50, 0xe2, 0xee, 0x73,
    0x39, 0x66, 0xd1, 0x51, 0x87, 0xac, 0x90, 0x9b, 0x2c, 0x6d,
    0x8d, 0xd5, 0x75, 0x3f, 0xc6, 0xf1, 0x8f, 0xdf, 0xdb, 0x45,
    0x38, 0xf8, 0xd6, 0x7e, 0xc7, 0x7c, 0x44, 0x08, 0x4a, 0x14,
    0xa0, 0x84, 0x7c, 0x8b, 0x88, 0x40, 0x93, 0x89, 0xae, 0x2c,
    0x20, 0x07, 0x80, 0xec, 0xce, 0x4c, 0x2c, 0x4e, 0x49, 0x79,
    0x53, 0xe7, 0xde, 0xa2, 0x9e, 0x67, 0x21, 0x53, 0x7c, 0x85,
    0xe7, 0x6f, 0xbd, 0x93, 0xab, 0x63, 0xba, 0xf0, 0xbd, 0xea,
    0x39, 0x16, 0x47, 0xbf, 0xe6, 0x0c, 0xcb, 0x63, 0xc7, 0xc5,
    0xf1, 0xdc, 0x5a, 0x52, 0xcd, 0x4c, 0x53, 0x8b, 0x7e, 0xb1,
    0xc3, 0x4e, 0xe7, 0x61, 0x25, 0x01, 0xec, 0xae, 0x06, 0x74,
    0x9f, 0xbc, 0xbb, 0x2a, 0x47, 0x46, 0xe8, 0xae, 0xf2, 0xab,
    0x15, 0xed, 0xa6, 0x86, 0x8f, 0x2f, 0xe5, 0x67, 0x0f, 0xdd,
    0xbf, 0x70, 0x53, 0xaa, 0x9b, 0x74,
};
static const int sizeof_bench_dilithium_level5_key = sizeof(bench_dilithium_level5_key);

#endif /* !WOLFSSL_DILITHIUM_NO_SIGN */

#ifndef WOLFSSL_DILITHIUM_NO_VERIFY

/* raw public key without ASN1 syntax from
 * ./certs/dilithium/bench_dilithium_level5_key.der */
static const unsigned char bench_dilithium_level5_pubkey[] = {
    0xef, 0x49, 0x79, 0x47, 0x15, 0xc4, 0x8a, 0xa9, 0x74, 0x2a,
    0xf0, 0x36, 0x94, 0x5c, 0x91, 0x1c, 0x5d, 0xff, 0x2c, 0x83,
    0xf2, 0x8b, 0x04, 0xfc, 0x5d, 0x64, 0xbd, 0x49, 0x73, 0xcd,
    0xcc, 0x99, 0x50, 0x5f, 0x2b, 0x16, 0x3a, 0xbb, 0x98, 0xc0,
    0xa7, 0x69, 0x0e, 0x95, 0x99, 0x0b, 0xa2, 0x6c, 0xfe, 0x6c,
    0xdb, 0xc8, 0xa7, 0x09, 0x46, 0x6c, 0x90, 0x50, 0xa4, 0x75,
    0x30, 0xf7, 0x90, 0xac, 0x31, 0xb6, 0xdd, 0x21, 0xaf, 0xc6,
    0xf9, 0xfe, 0xee, 0xc6, 0x5b, 0xa8, 0x8f, 0x0a, 0x2e, 0xd0,
    0x42, 0xab, 0xa8, 0x3c, 0x8d, 0xbf, 0xf7, 0x44, 0xbd, 0x0d,
    0xcf, 0xf4, 0x68, 0xfc, 0x16, 0x67, 0xf7, 0x39, 0x48, 0x5f,
    0x56, 0xd1, 0xe7, 0x1f, 0x49, 0x80, 0x50, 0xbe, 0x54, 0xd1,
    0xb7, 0xc9, 0xd2, 0x32, 0xc7, 0x08, 0x8c, 0xde, 0x2c, 0x31,
    0xf6, 0x1d, 0xc7, 0xac, 0xb3, 0x79, 0xd7, 0x4b, 0x1b, 0x23,
    0x89, 0x0a, 0xdc, 0x8e, 0x44, 0x41, 0x14, 0x28, 0x99, 0x13,
    0xb3, 0x26, 0xa6, 0x0e, 0x83, 0x60, 0xaa, 0x8d, 0x7c, 0x23,
    0x13, 0xba, 0x6c, 0x28, 0x90, 0x56, 0x84, 0xa1, 0x23, 0x8b,
    0x81, 0x20, 0x97, 0x7c, 0x66, 0x3f, 0xed, 0x5d, 0xd0, 0xe4,
    0x5d, 0xee, 0x46, 0xbc, 0x4b, 0x3c, 0x03, 0xb5, 0xbc, 0x4d,
    0x8d, 0x37, 0xa3, 0x56, 0x4b, 0x33, 0xad, 0xef, 0xd4, 0xb6,
    0xec, 0xdb, 0x04, 0x9a, 0x19, 0x58, 0x57, 0xd8, 0x00, 0x3a,
    0x92, 0x61, 0x0c, 0x0b, 0xc8, 0x52, 0xe5, 0x04, 0x02, 0x9a,
    0x00, 0x7e, 0xec, 0x7e, 0x94, 0xaa, 0xef, 0x2d, 0x7f, 0xb6,
    0x2e, 0x7c, 0xb0, 0x73, 0xa2, 0x20, 0xc0, 0x07, 0x30, 0x41,
    0x50, 0x20, 0x14, 0x18, 0x21, 0x5e, 0x2a, 0x6f, 0x70, 0x21,
    0xd6, 0x97, 0x13, 0xb9, 0xc1, 0x9e, 0x90, 0x67, 0xcc, 0x55,
    0x8a, 0xec, 0xec, 0x0a, 0x1e, 0x90, 0xdc, 0x3f, 0xb0, 0x4d,
    0xd1, 0x18, 0xea, 0x4f, 0xcb, 0x5d, 0x15, 0x4c, 0xb8, 0x35,
    0x9b, 0x34, 0x24, 0x30, 0x06, 0x53, 0x17, 0xf0, 0xbe, 0x27,
    0x36, 0xb3, 0x04, 0x6a, 0xbd, 0xbf, 0xa7, 0x39, 0xee, 0xa9,
    0x8f, 0x0e, 0x98, 0xc5, 0xf5, 0x9f, 0x46, 0x25, 0x93, 0xc9,
    0xf2, 0xf6, 0x2b, 0x8e, 0x92, 0x06, 0x01, 0x3d, 0x81, 0x18,
    0xf2, 0xec, 0xf1, 0x05, 0x4c, 0xad, 0x4b, 0xcb, 0x98, 0xa4,
    0xb5, 0x61, 0x20, 0xda, 0x81, 0xa1, 0xfb, 0x92, 0x4c, 0xaf,
    0x87, 0x6f, 0x6e, 0xd2, 0x57, 0xec, 0xcd, 0x94, 0xb3, 0x79,
    0xbf, 0x59, 0x88, 0x17, 0x81, 0xce, 0x8a, 0x57, 0xce, 0x57,
    0xae, 0x3e, 0x82, 0x81, 0x2f, 0x83, 0x61, 0xd8, 0xf9, 0x68,
    0x21, 0xe7, 0x72, 0x5b, 0xd6, 0x80, 0x55, 0x68, 0x5d, 0x67,
    0x15, 0x0c, 0x8b, 0xdc, 0x4f, 0xc3, 0x89, 0x36, 0x3c, 0xac,
    0xaf, 0x16, 0x5e, 0x1c, 0xfa, 0x68, 0x74, 0x6a, 0xab, 0x68,
    0xd8, 0x59, 0x96, 0x2d, 0x33, 0x62, 0xe4, 0xbd, 0xb3, 0xb7,
    0x4d, 0x88, 0x35, 0xb8, 0xed, 0xb2, 0x16, 0x85, 0x97, 0x08,
    0x71, 0x71, 0x39, 0x7e, 0x0c, 0x53, 0x16, 0xda, 0x38, 0xe5,
    0x28, 0x09, 0x9c, 0xd9, 0x46, 0xec, 0x68, 0xda, 0x8d, 0xd0,
    0xad, 0xb2, 0x79, 0x28, 0x3b, 0x1e, 0x12, 0xc9, 0xdf, 0xa9,
    0x6d, 0x3d, 0x29, 0x99, 0x2f, 0x53, 0xc2, 0xd0, 0xf9, 0x88,
    0x26, 0x94, 0x47, 0xaf, 0xf6, 0x96, 0xf3, 0xe1, 0x11, 0xa6,
    0x82, 0x3d, 0x43, 0x3f, 0x1f, 0xbc, 0xf6, 0x98, 0xbe, 0xff,
    0x06, 0x86, 0x61, 0x27, 0xdc, 0x91, 0x54, 0xd4, 0xfc, 0x68,
    0x83, 0xe8, 0x35, 0x3e, 0xee, 0x94, 0x59, 0x28, 0x2f, 0xde,
    0xdd, 0x03, 0x60, 0x66, 0xc1, 0x49, 0x57, 0xdd, 0xbc, 0xd5,
    0x0a, 0x67, 0x34, 0xf1, 0xa6, 0x0a, 0x57, 0x94, 0x65, 0x02,
    0x2c, 0x52, 0x43, 0x70, 0x3b, 0xc1, 0x9a, 0xff, 0xda, 0x6f,
    0xb9, 0x54, 0x47, 0x01, 0xda, 0x27, 0xe4, 0x48, 0x4a, 0x90,
    0x9f, 0xb5, 0xc3, 0xee, 0x0e, 0x09, 0x57, 0xfe, 0x48, 0x51,
    0x08, 0x34, 0x5e, 0x8f, 0x16, 0xc9, 0x0b, 0x74, 0xd9, 0x7d,
    0x22, 0x3f, 0xd6, 0xb7, 0x5d, 0xd6, 0x76, 0x00, 0x8d, 0x4e,
    0x78, 0x73, 0x86, 0xd6, 0xdb, 0x2a, 0x65, 0xab, 0xdf, 0xb0,
    0xea, 0x11, 0xad, 0xdf, 0xba, 0x43, 0xdb, 0xa8, 0x0a, 0xfb,
    0x04, 0x38, 0x81, 0x2b, 0xa3, 0x29, 0xfc, 0x95, 0x73, 0x9a,
    0x0c, 0x6c, 0x9e, 0xcd, 0xdc, 0xcf, 0x0a, 0x0c, 0x18, 0x41,
    0x6f, 0x1d, 0xa3, 0xf6, 0x12, 0x4c, 0x13, 0xf2, 0x02, 0xc6,
    0x50, 0x99, 0x86, 0x73, 0xa7, 0xf9, 0x7e, 0x84, 0x7f, 0x4c,
    0x00, 0xce, 0x2e, 0x21, 0x76, 0x8e, 0x17, 0x7a, 0x87, 0x6f,
    0x81, 0xe6, 0xc0, 0x52, 0xa5, 0xa0, 0x3c, 0x54, 0x3c, 0xec,
    0xb0, 0x9d, 0x1c, 0x3b, 0xec, 0xe5, 0x4e, 0x4a, 0x37, 0xe7,
    0xd5, 0xa9, 0x07, 0x87, 0x23, 0x28, 0x5d, 0x3d, 0x22, 0x02,
    0x79, 0x40, 0x3f, 0x2d, 0x40, 0xc9, 0xe5, 0xa6, 0x9b, 0xa8,
    0xb8, 0x76, 0xf6, 0x77, 0x5b, 0x8d, 0x72, 0x96, 0x3e, 0x13,
    0xbf, 0x76, 0xfa, 0x7b, 0xb7, 0x82, 0x5f, 0xe7, 0x9d, 0x54,
    0x0e, 0x05, 0x1a, 0x9f, 0xa4, 0x42, 0xa5, 0xb4, 0x93, 0x23,
    0x06, 0x59, 0x43, 0xa8, 0xe8, 0x5c, 0xfc, 0x18, 0x97, 0xdb,
    0xad, 0x9a, 0x80, 0x0a, 0xf2, 0x20, 0x50, 0xac, 0xc1, 0x13,
    0x3e, 0x98, 0x09, 0xde, 0xf2, 0x70, 0x9e, 0x14, 0xc2, 0x5c,
    0xec, 0x65, 0x07, 0x0b, 0xfa, 0x02, 0x5c, 0xf8, 0x71, 0xaa,
    0x9b, 0x45, 0x62, 0xe2, 0x27, 0xaf, 0x77, 0xf8, 0xe3, 0xeb,
    0x7b, 0x24, 0x7b, 0x3c, 0x67, 0xc2, 0x6d, 0x6e, 0x17, 0xae,
    0x6e, 0x86, 0x6f, 0x98, 0xc9, 0xac, 0x13, 0x9f, 0x87, 0x64,
    0x3d, 0x4d, 0x6f, 0xa0, 0xb3, 0x39, 0xc6, 0x68, 0x1b, 0xa7,
    0xeb, 0x3e, 0x0f, 0x6b, 0xc7, 0xa4, 0xe2, 0x20, 0x27, 0x75,
    0x3f, 0x09, 0x16, 0xff, 0x1a, 0xcc, 0xa7, 0xc4, 0x6d, 0xc2,
    0xfc, 0xc3, 0x0b, 0x37, 0x63, 0xff, 0x9b, 0x10, 0xe6, 0x00,
    0xf7, 0x18, 0x43, 0x9f, 0x07, 0x50, 0x31, 0x51, 0xd4, 0xfd,
    0xad, 0xa2, 0x0f, 0x77, 0xda, 0x41, 0xc1, 0x0a, 0x6f, 0x86,
    0xd7, 0xdc, 0x8a, 0x52, 0xd6, 0xa1, 0x27, 0xdb, 0x14, 0x67,
    0x26, 0x91, 0xb3, 0xcd, 0x01, 0x5f, 0x60, 0xa1, 0x7f, 0x43,
    0x15, 0x1a, 0x82, 0x0f, 0xd3, 0x66, 0x5f, 0x60, 0x57, 0x2f,
    0xb2, 0x8c, 0x27, 0x2a, 0x9d, 0x1b, 0xf9, 0xf2, 0x59, 0x20,
    0x39, 0xd9, 0xc5, 0xaf, 0xf2, 0x36, 0x8c, 0x58, 0x00, 0x1b,
    0xd0, 0xc5, 0x8e, 0x1a, 0x49, 0xa8, 0x60, 0xbe, 0xd1, 0xd7,
    0x2a, 0xb0, 0xc2, 0xab, 0x58, 0x8a, 0x7a, 0xa9, 0x41, 0x68,
    0x70, 0xbd, 0xea, 0x73, 0xa5, 0x03, 0x11, 0xb2, 0x27, 0xd9,
    0xcd, 0xf5, 0x09, 0xe8, 0x1c, 0xe2, 0x4f, 0x50, 0x6a, 0x84,
    0x34, 0x62, 0x2e, 0x36, 0xaa, 0x4c, 0xc1, 0x83, 0x78, 0x98,
    0x35, 0x7a, 0x27, 0x7e, 0xfe, 0xf1, 0x6f, 0x59, 0x27, 0x35,
    0x73, 0xce, 0x74, 0xaa, 0xb4, 0x72, 0x82, 0xa8, 0xe2, 0x81,
    0x7a, 0x6b, 0xca, 0x33, 0xa5, 0xda, 0xa2, 0x63, 0xca, 0x2e,
    0x90, 0x03, 0x32, 0xec, 0x63, 0xdb, 0x52, 0x7b, 0x16, 0xfc,
    0x01, 0x2d, 0x30, 0x12, 0x1e, 0xf9, 0xa3, 0x72, 0x21, 0x3c,
    0x75, 0x0c, 0x61, 0x9c, 0x7e, 0x73, 0x04, 0x71, 0x41, 0x45,
    0x5d, 0x7f, 0x49, 0x1c, 0x09, 0x08, 0xa4, 0xec, 0x2f, 0xfd,
    0xc4, 0xfb, 0x59, 0x6a, 0x27, 0x7a, 0xd4, 0xfc, 0x5f, 0x20,
    0x04, 0x34, 0x7d, 0x08, 0xed, 0x82, 0x5a, 0x90, 0xe1, 0xab,
    0xfd, 0x35, 0x3a, 0x8d, 0xbb, 0x0a, 0x9d, 0x73, 0xff, 0x69,
    0xe5, 0xe9, 0x09, 0x55, 0x14, 0xd9, 0x7b, 0x6f, 0x0d, 0x99,
    0xd2, 0x7e, 0x71, 0xf8, 0x4f, 0x72, 0x2f, 0xbb, 0xc6, 0xc4,
    0x36, 0xc9, 0x01, 0xd3, 0x9b, 0x94, 0xab, 0x41, 0x0f, 0x4a,
    0x61, 0x5c, 0x68, 0xe5, 0xd7, 0x0d, 0x94, 0xaa, 0xee, 0xba,
    0x95, 0xcb, 0x8c, 0x0e, 0x85, 0x3a, 0x02, 0x6b, 0x95, 0x50,
    0xfd, 0x02, 0xfd, 0xa4, 0x58, 0x29, 0x78, 0x4f, 0xd0, 0xae,
    0x66, 0xd6, 0x5c, 0xe7, 0x45, 0xfe, 0x98, 0xb0, 0xa3, 0xe2,
    0x87, 0xc0, 0xd2, 0x81, 0x08, 0xf1, 0xf1, 0xe7, 0xda, 0x62,
    0x9e, 0xa0, 0x34, 0x86, 0xeb, 0xa1, 0x6e, 0x4a, 0x26, 0x8e,
    0x39, 0x0c, 0x51, 0x10, 0x33, 0x11, 0x87, 0xf8, 0x79, 0x3c,
    0x49, 0x7a, 0x8b, 0xce, 0xc1, 0x0a, 0x0e, 0xe1, 0xd5, 0x2a,
    0xac, 0xf0, 0x3a, 0x1d, 0x6a, 0x6a, 0xe5, 0xe1, 0x81, 0x70,
    0xad, 0xaf, 0x15, 0x4c, 0x2a, 0x70, 0x2a, 0x6b, 0x22, 0x0d,
    0x30, 0xe7, 0x56, 0xed, 0x2d, 0x4b, 0x85, 0x17, 0x49, 0x72,
    0x3a, 0x1b, 0x6f, 0x57, 0x1c, 0xf7, 0x72, 0x9e, 0x20, 0xdb,
    0x57, 0x1c, 0xfb, 0x36, 0x50, 0x52, 0xec, 0x5b, 0xd6, 0x6a,
    0x1b, 0xf8, 0x74, 0xad, 0xe6, 0x00, 0x74, 0x04, 0xc5, 0x99,
    0x83, 0xe4, 0x5a, 0x0c, 0xc3, 0xe8, 0x6d, 0x3a, 0xd7, 0x3c,
    0x3c, 0xc0, 0x1a, 0x28, 0xb3, 0x29, 0x7a, 0x10, 0x9e, 0x39,
    0x66, 0x5b, 0xc1, 0x38, 0xac, 0x21, 0x4e, 0xcd, 0x01, 0xf2,
    0xf6, 0x30, 0x2c, 0x2b, 0xb6, 0xbf, 0xf5, 0xea, 0x61, 0xaf,
    0x0c, 0xa6, 0x01, 0x11, 0x15, 0x19, 0x09, 0x8c, 0x7e, 0x69,
    0xdf, 0x3b, 0xea, 0xd3, 0x0a, 0x3a, 0xd7, 0xbd, 0xe1, 0x17,
    0xaf, 0x92, 0x3c, 0xf5, 0xfe, 0x35, 0xd6, 0xcf, 0x07, 0xa6,
    0xf7, 0xe9, 0xc1, 0x99, 0xed, 0x80, 0xe3, 0x12, 0xd5, 0x4b,
    0xb9, 0xdf, 0xaf, 0x4e, 0x52, 0xad, 0x8e, 0x66, 0x87, 0xe5,
    0x2c, 0xd0, 0x45, 0x70, 0xd9, 0x78, 0x8f, 0x4b, 0xf4, 0xe1,
    0xf1, 0x22, 0xf2, 0xe3, 0xed, 0x1f, 0xeb, 0xe9, 0x70, 0x31,
    0x4c, 0x65, 0x5f, 0x55, 0xee, 0x5d, 0xaa, 0x83, 0x87, 0x76,
    0xbe, 0x11, 0xae, 0xd7, 0xf2, 0xfb, 0x43, 0xe7, 0x17, 0x81,
    0x33, 0x15, 0x47, 0xa0, 0xf3, 0x8e, 0x84, 0x57, 0xff, 0x35,
    0x9e, 0x4a, 0x8a, 0xab, 0x50, 0x3a, 0x45, 0xe0, 0xc3, 0x73,
    0xca, 0x77, 0x61, 0x68, 0x38, 0xd0, 0xa3, 0x5f, 0x03, 0x8d,
    0x41, 0xc2, 0xd3, 0x4a, 0x17, 0xe0, 0xa8, 0xaa, 0x00, 0xf3,
    0xf2, 0x5b, 0xa8, 0xe1, 0x06, 0xa6, 0x2b, 0xdb, 0xe1, 0x74,
    0xbd, 0xc4, 0xd2, 0x2b, 0x55, 0x9a, 0xb0, 0xf8, 0x35, 0xd8,
    0x6b, 0xec, 0xdb, 0xc5, 0xf4, 0x6c, 0x40, 0x90, 0x6a, 0x68,
    0xc9, 0xb5, 0xcb, 0xbb, 0xd0, 0xb0, 0xbc, 0x9f, 0xb9, 0xaa,
    0x50, 0x14, 0x93, 0x3b, 0x9f, 0x25, 0xcb, 0x40, 0xb8, 0x08,
    0xcc, 0x13, 0xe5, 0xdc, 0x3f, 0x84, 0x96, 0xe0, 0x73, 0x7b,
    0x7d, 0x9e, 0x41, 0x92, 0x5d, 0xcc, 0xa4, 0xea, 0x4f, 0x93,
    0x0c, 0x40, 0x2e, 0x42, 0x8a, 0xe9, 0xb9, 0x12, 0x74, 0xbb,
    0x79, 0x7c, 0xb0, 0x37, 0x20, 0xb6, 0xaf, 0x43, 0x3a, 0x88,
    0x59, 0x7c, 0x68, 0x28, 0x5f, 0x98, 0xc2, 0xf0, 0x2a, 0xbc,
    0xa1, 0x61, 0x88, 0x1f, 0x43, 0xbc, 0x42, 0x8f, 0x43, 0xf3,
    0x7e, 0x16, 0x96, 0xfa, 0x92, 0x70, 0xaf, 0x3c, 0x9f, 0x4b,
    0xd9, 0x60, 0xe9, 0xf6, 0x2e, 0x84, 0xda, 0x88, 0x31, 0x34,
    0xa6, 0x85, 0x10, 0x05, 0xef, 0x40, 0xa8, 0xa5, 0x4f, 0x92,
    0x59, 0xf7, 0xe0, 0xc4, 0x2b, 0x12, 0x17, 0x71, 0xbe, 0x8c,
    0x4a, 0x02, 0xfe, 0x12, 0xb6, 0x3b, 0x85, 0x75, 0x37, 0xf3,
    0x73, 0x2d, 0x9c, 0x00, 0x5d, 0x80, 0xad, 0x20, 0x2f, 0x5a,
    0x0b, 0x17, 0x7e, 0x67, 0x72, 0x24, 0x5a, 0xb9, 0xf3, 0xb1,
    0x33, 0xa4, 0x57, 0x1d, 0x49, 0x72, 0x2c, 0x7f, 0x47, 0x15,
    0x07, 0xe0, 0x45, 0x14, 0xdd, 0x77, 0x86, 0x6d, 0x03, 0xbe,
    0x57, 0xd0, 0xaa, 0x18, 0xa6, 0xdd, 0x94, 0x18, 0x3f, 0x8a,
    0xf3, 0xb5, 0xd7, 0x5a, 0xec, 0xc8, 0x79, 0x7f, 0x51, 0x61,
    0x3c, 0x9b, 0xb2, 0x9b, 0xf3, 0xb4, 0x35, 0xd1, 0x38, 0xbf,
    0x37, 0xce, 0x54, 0xd1, 0xf8, 0xb6, 0x45, 0xeb, 0x52, 0x0d,
    0x9a, 0x09, 0x58, 0x0d, 0x2c, 0x0b, 0xb1, 0xf2, 0x30, 0x3a,
    0x95, 0xc1, 0x13, 0x91, 0xd2, 0x9f, 0x8d, 0x8d, 0xd0, 0x38,
    0x3e, 0x4c, 0xae, 0x4a, 0x55, 0xa7, 0x42, 0x11, 0x83, 0xc4,
    0x70, 0xf0, 0x2b, 0x68, 0x9e, 0x07, 0xad, 0xb7, 0x83, 0xc6,
    0x53, 0x3c, 0xfb, 0x0a, 0x5d, 0x24, 0xdc, 0xe1, 0x55, 0x72,
    0xcf, 0xce, 0x3e, 0xc8, 0xd0, 0x57, 0x8a, 0x82, 0x5e, 0x78,
    0x2b, 0x80, 0xc5, 0xb9, 0x09, 0x46, 0xf8, 0x90, 0x39, 0x52,
    0xa9, 0xce, 0x3f, 0x3d, 0x41, 0x3b, 0x28, 0x45, 0xa3, 0xb3,
    0x21, 0xc2, 0xcd, 0x14, 0x49, 0x41, 0x6c, 0x38, 0xda, 0x1b,
    0x5f, 0x16, 0x49, 0xf9, 0x65, 0x00, 0x4e, 0xb4, 0x20, 0x55,
    0x70, 0xe8, 0x58, 0x1a, 0x18, 0xbf, 0x41, 0xef, 0x31, 0xb1,
    0xe7, 0x8d, 0x89, 0xc1, 0x48, 0xe8, 0xf5, 0x57, 0x35, 0xfa,
    0xc1, 0x79, 0xee, 0x2c, 0xe8, 0x7d, 0xb6, 0x03, 0xcc, 0x66,
    0x09, 0x6f, 0x52, 0x84, 0x0a, 0x34, 0x18, 0x2c, 0x01, 0x45,
    0x81, 0x00, 0xe5, 0x5e, 0x8d, 0xae, 0x1c, 0x96, 0x8b, 0x45,
    0x73, 0x00, 0x0a, 0xb5, 0xcf, 0x8d, 0x0e, 0x35, 0x5d, 0x1a,
    0x0e, 0xbf, 0x64, 0x9a, 0x52, 0x20, 0x48, 0xc6, 0xb9, 0x40,
    0xd3, 0x2c, 0x52, 0xca, 0x93, 0xcf, 0xbb, 0x94, 0x06, 0xf3,
    0x97, 0xee, 0xcc, 0x5d, 0xa3, 0xea, 0xf8, 0x5a, 0x39, 0x77,
    0x34, 0xd7, 0xf6, 0x4e, 0xbe, 0x8a, 0x07, 0x5f, 0x51, 0x53,
    0xc5, 0x1b, 0x8c, 0x47, 0x8f, 0x34, 0x0e, 0x60, 0x0a, 0x90,
    0xe2, 0xda, 0x7b, 0xef, 0xd6, 0xf5, 0x5d, 0xe5, 0x32, 0x37,
    0x75, 0x99, 0x81, 0x4a, 0x2a, 0x78, 0x71, 0xdc, 0xf4, 0xe5,
    0xca, 0xd8, 0x6b, 0x3b, 0x90, 0x68, 0x2e, 0x93, 0xc5, 0x10,
    0x42, 0x5d, 0x38, 0x90, 0x32, 0x46, 0xea, 0x87, 0xe0, 0xbc,
    0xb8, 0x9a, 0x18, 0x20, 0x68, 0x85, 0x6d, 0x9b, 0xc9, 0x8f,
    0x9b, 0xd2, 0xbe, 0x15, 0x12, 0x68, 0xd0, 0xb0, 0x16, 0x5f,
    0xe2, 0x69, 0x1d, 0x04, 0x00, 0xfc, 0x63, 0x33, 0xcd, 0x1f,
    0x89, 0xcd, 0x52, 0xff, 0xec, 0x19, 0x69, 0x74, 0xa3, 0xce,
    0x4d, 0xab, 0x93, 0xe4, 0xc6, 0x13, 0x56, 0x27, 0xc9, 0x25,
    0x5a, 0x01, 0xb2, 0x36, 0x8b, 0x61, 0xe5, 0x8b, 0x98, 0xac,
    0xe4, 0x2a, 0xb6, 0x40, 0x9f, 0x42, 0xe4, 0x1b, 0x52, 0xf7,
    0xfd, 0xd8, 0x30, 0x07, 0x33, 0xf9, 0x47, 0xcb, 0x3c, 0xad,
    0x12, 0xc1, 0xcc, 0x29, 0x62, 0x49, 0x04, 0x0c, 0x23, 0x97,
    0x5a, 0xa4, 0x84, 0x67, 0xde, 0x5a, 0xe5, 0x36, 0xd2, 0x88,
    0xf1, 0xd4, 0xeb, 0x13, 0x81, 0x54, 0x51, 0x11, 0xe3, 0xba,
    0xbc, 0xee, 0xdd, 0x6c, 0xcd, 0xe6, 0xb4, 0xa1, 0x8b, 0x0b,
    0x66, 0xfb, 0x8e, 0x50, 0xa0, 0xda, 0x69, 0x8d, 0xcc, 0x2d,
    0xe4, 0x2c, 0xc4, 0x37, 0xdf, 0x61, 0xc0, 0x03, 0xbd, 0x8b,
    0x28, 0xca, 0xd2, 0x8c, 0x1c, 0xf1, 0xa4, 0x26, 0x69, 0xe5,
    0xcf, 0x45, 0xdb, 0x5a, 0x47, 0x79, 0xed, 0x9f, 0xf7, 0xd2,
    0xdb, 0xba, 0x46, 0x53, 0x4f, 0xce, 0xa8, 0xbe, 0x8f, 0x4a,
    0xd6, 0xdf, 0x2e, 0x06, 0xe6, 0x4c, 0x9a, 0xc1, 0xb6, 0x49,
    0xed, 0xc4, 0xeb, 0xaa, 0xa4, 0x29, 0x6d, 0xd4, 0xcc, 0x8c,
    0xb6, 0x40, 0x11, 0x39, 0x69, 0xf7, 0x75, 0xcd, 0xb1, 0x99,
    0x46, 0x4e, 0xde, 0xcb, 0xf6, 0x9d, 0x32, 0xf3, 0xc9, 0x47,
    0x47, 0x7a, 0xcb, 0xfb, 0xa3, 0x0c, 0x3b, 0xdf, 0xb7, 0xde,
    0xec, 0x99, 0xde, 0xb0, 0x26, 0x04, 0x34, 0xae, 0x6b, 0xfc,
    0x99, 0xbc, 0xde, 0xd5, 0xbe, 0xe7, 0xeb, 0xf9, 0xe7, 0xa6,
    0x01, 0x9a, 0x0c, 0x5e, 0x66, 0xe6, 0x53, 0xe4, 0xd1, 0x58,
    0xac, 0xda, 0x69, 0x77, 0x7b, 0x68, 0xd6, 0x30, 0x2a, 0x9c,
    0x6b, 0xbe, 0x9f, 0x3d, 0x71, 0xd6, 0x54, 0xcd, 0x59, 0x4e,
    0x1f, 0xe3, 0x83, 0x4e, 0xd1, 0x8e, 0xaf, 0x97, 0xa8, 0xe5,
    0xb6, 0x59, 0x77, 0xa8, 0x02, 0x20, 0xe4, 0xeb, 0x44, 0x71,
    0xbc, 0x07, 0x14, 0x79, 0x4f, 0x0c, 0x27, 0x06, 0x39, 0xcf,
    0x7c, 0xef, 0x2b, 0x9b, 0x5e, 0xc4, 0x6d, 0x79, 0x13, 0x00,
    0x43, 0x6f, 0x51, 0x77, 0xb5, 0xc3, 0x72, 0xad, 0x13, 0xa9,
    0xe5, 0x9a, 0x5b, 0x1a, 0x99, 0x74, 0xc0, 0x7a, 0xf9, 0xc5,
    0xb0, 0x58, 0x35, 0x1c, 0xa5, 0x51, 0xdb, 0xa1, 0x14, 0xcd,
    0x26, 0x71, 0xb1, 0xe7, 0xaa, 0x14, 0xa7, 0x46, 0x93, 0xd3,
    0x5c, 0x8c, 0x1a, 0x91, 0x77, 0x46, 0x2e, 0x15, 0xaa, 0x9e,
    0xf7, 0x2b, 0x79, 0x41, 0x76, 0xf7, 0x22, 0x53, 0x7d, 0x51,
    0xdb, 0x98, 0x3d, 0x5b, 0x78, 0x5f, 0xc3, 0xc9, 0x29, 0xa3,
    0xff, 0x75, 0x82, 0x06, 0x9a, 0x16, 0x5e, 0xa4, 0x79, 0x0d,
    0xd1, 0x6d, 0x08, 0xff, 0x43, 0xef, 0x9c, 0xf3, 0x1b, 0x7a,
    0x3f, 0x34, 0xbe, 0x19, 0x15, 0x06, 0x33, 0xdb, 0xa5, 0x71,
    0xcb, 0x5f, 0x6b, 0x8d, 0xbd, 0x5b, 0x32, 0x91, 0xb2, 0x37,
    0x3d, 0xb4, 0x40, 0x9e, 0x02, 0x9b, 0xb7, 0x68, 0x20, 0x58,
    0x5c, 0xab, 0xcb, 0xc8, 0x23, 0x2d, 0x77, 0xcc, 0x0b, 0xf6,
    0x78, 0x6b, 0x80, 0x06, 0x91, 0xa9, 0xfd, 0x7e, 0xfa, 0x25,
    0x98, 0x9f, 0xcc, 0x79, 0x0a, 0x1a, 0x54, 0x83, 0xac, 0x64,
    0x16, 0x90, 0xe5, 0xd9, 0xa7, 0xd7, 0x1b, 0x86, 0x0d, 0xe6,
    0xe6, 0x22, 0x2b, 0x1f, 0x44, 0x49, 0x98, 0x9c, 0x51, 0x6f,
    0xcf, 0x58, 0x4a, 0xfa, 0xfa, 0x84, 0x12, 0xa5, 0x10, 0xf4,
    0xca, 0xf0, 0x98, 0x2b, 0xc9, 0x03, 0x71, 0x37, 0xe7, 0xdc,
    0xc2, 0xb1, 0x4e, 0x64, 0xde, 0x4f, 0x46, 0x0d, 0x6b, 0x25,
    0x88, 0x5d, 0xd6, 0xff, 0x23, 0x46, 0x57, 0x36, 0x14, 0x18,
    0xa7, 0xcb, 0xb8, 0xbd, 0xf0, 0xc5, 0x37, 0x36, 0xee, 0xe1,
    0xed, 0x9f, 0x4d, 0xd4, 0x39, 0xe5, 0x92, 0xcf, 0x95, 0x4d,
    0x66, 0x36, 0x5d, 0xd0, 0xcc, 0x07, 0xcf, 0x15, 0x5a, 0xce,
    0x14, 0xb8, 0xda, 0x0d, 0x3d, 0x1b, 0x45, 0xc5, 0x2e, 0x34,
    0x43, 0x25, 0x02, 0x3a, 0xcd, 0x14, 0x45, 0xfb, 0x3e, 0xf9,
    0x88, 0x5d, 0x0d, 0x29, 0x31, 0xb9, 0xa1, 0xe6, 0x31, 0x18,
    0x52, 0x46, 0x3f, 0x22, 0x4f, 0x9f, 0x7a, 0x65, 0x36, 0x88,
    0xa3, 0x1c, 0x3e, 0x6f, 0x50, 0x7a, 0x36, 0xbe, 0x56, 0x7e,
    0x50, 0xcb, 0x7a, 0x10, 0xa0, 0xec, 0xf6, 0x82, 0xd6, 0x30,
    0x1c, 0xe8, 0x4c, 0x50, 0xf9, 0x3e, 0xdb, 0xac, 0xbe, 0x4f,
    0x90, 0xb1, 0xd5, 0x1b, 0x12, 0x95, 0xfb, 0xe8, 0x08, 0x64,
    0x56, 0x7c, 0x96, 0xcc, 0x90, 0xb1, 0xbc, 0xa0, 0xf5, 0x32,
    0x69, 0xb3, 0x5f, 0x27, 0x0f, 0xbe, 0xc9, 0xbd, 0xeb, 0xfa,
    0x4b, 0x5c, 0xc5, 0x99, 0x9e, 0x5a, 0x04, 0xcc, 0xd0, 0x4d,
    0x29, 0xe8, 0x84, 0x55, 0x8c, 0xd7, 0xc4, 0x06, 0x13, 0x4d,
    0x92, 0xe5, 0x98, 0x9c, 0x4c, 0xc1, 0xf7, 0xaf, 0x7b, 0xd5,
    0x2b, 0x92, 0x68, 0x68, 0x19, 0x70, 0x4c, 0x9e, 0x46, 0xb8,
    0x34, 0xeb, 0x01, 0x47, 0xbe, 0x59, 0xab, 0x0b, 0x22, 0x25,
    0xe7, 0x56, 0xa8, 0xb4, 0x93, 0x3c, 0xd5, 0x98, 0x9f, 0x61,
    0x2e, 0xfa, 0xcb, 0x5f, 0x5b, 0xd8, 0x09, 0x83, 0xe9, 0x40,
    0xe9, 0x0e, 0x42, 0xdd, 0x17, 0xd7, 0x6e, 0x19, 0x8d, 0x95,
    0x0a, 0x93,
};
static const int sizeof_bench_dilithium_level5_pubkey =
    sizeof(bench_dilithium_level5_pubkey);

#endif /* !WOLFSSL_DILITHIUM_NO_VERIFY */

#endif /* HAVE_DILITHIUM */

#if defined(HAVE_SPHINCS)

/* certs/sphincs/bench_sphincs_fast_level1_key.der */
static const unsigned char bench_sphincs_fast_level1_key[] =
{
        0x30, 0x71, 0x02, 0x01, 0x00, 0x30, 0x08, 0x06, 0x06, 0x2B,
        0xCE, 0x0F, 0x06, 0x07, 0x0D, 0x04, 0x62, 0x04, 0x60, 0xD8,
        0xC4, 0x6E, 0x8D, 0x3B, 0xB7, 0xE7, 0x48, 0x8D, 0x6F, 0x0C,
        0x3D, 0xDF, 0xAB, 0x79, 0xB6, 0x62, 0xAE, 0x89, 0x19, 0x6F,
        0x5E, 0xF9, 0xD3, 0x3A, 0x69, 0xBA, 0xFF, 0x4C, 0x46, 0xDE,
        0xAA, 0x7C, 0x40, 0x79, 0x8C, 0xE1, 0xE5, 0x30, 0xE6, 0xDF,
        0x4E, 0x23, 0x5E, 0x14, 0xDB, 0x0A, 0x48, 0x4E, 0xF6, 0x57,
        0xCE, 0x45, 0x8F, 0x8B, 0x1D, 0x68, 0x63, 0xAA, 0x24, 0xA4,
        0xE1, 0x0D, 0xFB, 0x7C, 0x40, 0x79, 0x8C, 0xE1, 0xE5, 0x30,
        0xE6, 0xDF, 0x4E, 0x23, 0x5E, 0x14, 0xDB, 0x0A, 0x48, 0x4E,
        0xF6, 0x57, 0xCE, 0x45, 0x8F, 0x8B, 0x1D, 0x68, 0x63, 0xAA,
        0x24, 0xA4, 0xE1, 0x0D, 0xFB
};
static const int sizeof_bench_sphincs_fast_level1_key = sizeof(bench_sphincs_fast_level1_key);

/* certs/sphincs/bench_sphincs_fast_level3_key.der */
static const unsigned char bench_sphincs_fast_level3_key[] =
{
        0x30, 0x81, 0xA3, 0x02, 0x01, 0x00, 0x30, 0x08, 0x06, 0x06,
        0x2B, 0xCE, 0x0F, 0x06, 0x08, 0x0A, 0x04, 0x81, 0x93, 0x04,
        0x81, 0x90, 0xB2, 0x3A, 0x67, 0xA6, 0x4B, 0x8E, 0xB9, 0xEF,
        0xAD, 0x99, 0xE4, 0x3D, 0x65, 0xE8, 0xEE, 0xCF, 0xAC, 0xCF,
        0x2F, 0xDE, 0xBC, 0x11, 0x67, 0x8D, 0x8F, 0x8D, 0x3E, 0x99,
        0x31, 0x67, 0xED, 0x31, 0x6A, 0x05, 0x47, 0xC1, 0xDA, 0xC5,
        0x14, 0x17, 0xA1, 0x93, 0x83, 0x44, 0x58, 0x09, 0x80, 0x3A,
        0x47, 0x67, 0x42, 0x6D, 0x4C, 0xB7, 0xC8, 0x7D, 0x37, 0xF3,
        0x90, 0xF7, 0x46, 0x92, 0xB6, 0x26, 0xF7, 0x4E, 0x0D, 0x8D,
        0xB8, 0xCA, 0x8B, 0xA8, 0x20, 0x5D, 0x67, 0x85, 0xD2, 0x83,
        0x2C, 0x2A, 0x38, 0x1F, 0x57, 0x89, 0x76, 0x8C, 0x6D, 0x88,
        0xCE, 0x18, 0x4F, 0xA7, 0x88, 0x48, 0x7C, 0x0D, 0x47, 0x67,
        0x42, 0x6D, 0x4C, 0xB7, 0xC8, 0x7D, 0x37, 0xF3, 0x90, 0xF7,
        0x46, 0x92, 0xB6, 0x26, 0xF7, 0x4E, 0x0D, 0x8D, 0xB8, 0xCA,
        0x8B, 0xA8, 0x20, 0x5D, 0x67, 0x85, 0xD2, 0x83, 0x2C, 0x2A,
        0x38, 0x1F, 0x57, 0x89, 0x76, 0x8C, 0x6D, 0x88, 0xCE, 0x18,
        0x4F, 0xA7, 0x88, 0x48, 0x7C, 0x0D
};
static const int sizeof_bench_sphincs_fast_level3_key = sizeof(bench_sphincs_fast_level3_key);

/* certs/sphincs/bench_sphincs_fast_level5_key.der */
static const unsigned char bench_sphincs_fast_level5_key[] =
{
        0x30, 0x81, 0xD3, 0x02, 0x01, 0x00, 0x30, 0x08, 0x06, 0x06,
        0x2B, 0xCE, 0x0F, 0x06, 0x09, 0x0A, 0x04, 0x81, 0xC3, 0x04,
        0x81, 0xC0, 0xAB, 0xD3, 0xFD, 0x3B, 0x17, 0x00, 0xCD, 0xD5,
        0xB2, 0xEE, 0xD2, 0x36, 0xE5, 0xF7, 0x1D, 0xDC, 0xC8, 0x42,
        0xDB, 0x53, 0x6A, 0x8A, 0x0D, 0x6D, 0xD2, 0x3C, 0x1C, 0x7C,
        0x98, 0x4D, 0x73, 0xC8, 0xAB, 0x2E, 0xAA, 0x7A, 0xC0, 0x26,
        0xC4, 0x0D, 0x7E, 0xB4, 0xD3, 0xBB, 0x13, 0xF4, 0x6E, 0xFE,
        0x0E, 0xA5, 0xA4, 0x58, 0x57, 0xA2, 0xDD, 0x99, 0x62, 0xB9,
        0xBA, 0xC2, 0x5B, 0x26, 0xED, 0x6E, 0x99, 0xFA, 0x11, 0x0E,
        0xCF, 0x33, 0x54, 0x85, 0x56, 0x0C, 0xEB, 0x2A, 0xB0, 0xAA,
        0xEB, 0x74, 0x14, 0x89, 0x1A, 0xB9, 0x38, 0xF5, 0x29, 0x66,
        0x28, 0x28, 0x17, 0xF5, 0x72, 0x42, 0xEE, 0xC0, 0x14, 0x59,
        0xA0, 0x72, 0x9B, 0x9B, 0x1E, 0x7F, 0x70, 0x70, 0xBB, 0x89,
        0x0C, 0x7E, 0x87, 0x8B, 0x83, 0x80, 0x2B, 0x66, 0x58, 0x64,
        0x1D, 0x94, 0xAF, 0x58, 0xB5, 0x23, 0x2C, 0xA1, 0xE9, 0x95,
        0x99, 0xFA, 0x11, 0x0E, 0xCF, 0x33, 0x54, 0x85, 0x56, 0x0C,
        0xEB, 0x2A, 0xB0, 0xAA, 0xEB, 0x74, 0x14, 0x89, 0x1A, 0xB9,
        0x38, 0xF5, 0x29, 0x66, 0x28, 0x28, 0x17, 0xF5, 0x72, 0x42,
        0xEE, 0xC0, 0x14, 0x59, 0xA0, 0x72, 0x9B, 0x9B, 0x1E, 0x7F,
        0x70, 0x70, 0xBB, 0x89, 0x0C, 0x7E, 0x87, 0x8B, 0x83, 0x80,
        0x2B, 0x66, 0x58, 0x64, 0x1D, 0x94, 0xAF, 0x58, 0xB5, 0x23,
        0x2C, 0xA1, 0xE9, 0x95
};
static const int sizeof_bench_sphincs_fast_level5_key = sizeof(bench_sphincs_fast_level5_key);

/* certs/sphincs/bench_sphincs_small_level1_key.der */
static const unsigned char bench_sphincs_small_level1_key[] =
{
        0x30, 0x71, 0x02, 0x01, 0x00, 0x30, 0x08, 0x06, 0x06, 0x2B,
        0xCE, 0x0F, 0x06, 0x07, 0x10, 0x04, 0x62, 0x04, 0x60, 0xFF,
        0x26, 0x56, 0x65, 0xAC, 0x6C, 0x0B, 0x72, 0x2D, 0x8D, 0xB8,
        0x29, 0x4A, 0x15, 0x7E, 0xEF, 0x55, 0xFD, 0xBE, 0xF4, 0xC0,
        0xE6, 0x6F, 0x2B, 0x7A, 0x97, 0x60, 0x51, 0x1C, 0xCB, 0x82,
        0x43, 0x44, 0xDE, 0x14, 0x3D, 0x4F, 0xE7, 0x3C, 0x1C, 0xB3,
        0xBB, 0x9F, 0xE8, 0x9F, 0x8F, 0xA4, 0xAD, 0xB9, 0x52, 0xC1,
        0x31, 0xF7, 0xC1, 0x86, 0x7E, 0x73, 0xFB, 0x9E, 0x72, 0x57,
        0x8A, 0xD7, 0x44, 0x44, 0xDE, 0x14, 0x3D, 0x4F, 0xE7, 0x3C,
        0x1C, 0xB3, 0xBB, 0x9F, 0xE8, 0x9F, 0x8F, 0xA4, 0xAD, 0xB9,
        0x52, 0xC1, 0x31, 0xF7, 0xC1, 0x86, 0x7E, 0x73, 0xFB, 0x9E,
        0x72, 0x57, 0x8A, 0xD7, 0x44
};
static const int sizeof_bench_sphincs_small_level1_key = sizeof(bench_sphincs_small_level1_key);

/* certs/sphincs/bench_sphincs_small_level3_key.der */
static const unsigned char bench_sphincs_small_level3_key[] =
{
        0x30, 0x81, 0xA3, 0x02, 0x01, 0x00, 0x30, 0x08, 0x06, 0x06,
        0x2B, 0xCE, 0x0F, 0x06, 0x08, 0x0C, 0x04, 0x81, 0x93, 0x04,
        0x81, 0x90, 0x59, 0xC1, 0x44, 0x8A, 0x5F, 0xF3, 0xF1, 0xB3,
        0xB8, 0xFF, 0x98, 0x7F, 0x86, 0x4A, 0x4C, 0x19, 0xFC, 0x51,
        0xB8, 0x12, 0x87, 0x9C, 0x52, 0xD6, 0x7F, 0xD6, 0xB0, 0xA9,
        0xF7, 0xED, 0x44, 0x26, 0xAF, 0xC2, 0xCE, 0x47, 0xD9, 0xE3,
        0x95, 0x1A, 0xE6, 0x11, 0xC1, 0x37, 0x67, 0xA5, 0x89, 0xDD,
        0x37, 0x6A, 0xE9, 0xC3, 0x8C, 0x9B, 0x3E, 0xBA, 0xB1, 0x76,
        0x4A, 0x5A, 0xEE, 0xCD, 0x96, 0x66, 0xF2, 0x53, 0xDA, 0x8C,
        0x89, 0x69, 0xBF, 0xBF, 0xF9, 0xA5, 0xBC, 0x7D, 0x80, 0xA8,
        0x97, 0x63, 0x90, 0x55, 0x58, 0x6C, 0x0A, 0x52, 0x61, 0x0B,
        0xF3, 0xBC, 0xE1, 0x1F, 0xB4, 0xA6, 0x5F, 0x9F, 0x37, 0x6A,
        0xE9, 0xC3, 0x8C, 0x9B, 0x3E, 0xBA, 0xB1, 0x76, 0x4A, 0x5A,
        0xEE, 0xCD, 0x96, 0x66, 0xF2, 0x53, 0xDA, 0x8C, 0x89, 0x69,
        0xBF, 0xBF, 0xF9, 0xA5, 0xBC, 0x7D, 0x80, 0xA8, 0x97, 0x63,
        0x90, 0x55, 0x58, 0x6C, 0x0A, 0x52, 0x61, 0x0B, 0xF3, 0xBC,
        0xE1, 0x1F, 0xB4, 0xA6, 0x5F, 0x9F
};
static const int sizeof_bench_sphincs_small_level3_key = sizeof(bench_sphincs_small_level3_key);

/* certs/sphincs/bench_sphincs_small_level5_key.der */
static const unsigned char bench_sphincs_small_level5_key[] =
{
        0x30, 0x81, 0xD3, 0x02, 0x01, 0x00, 0x30, 0x08, 0x06, 0x06,
        0x2B, 0xCE, 0x0F, 0x06, 0x09, 0x0C, 0x04, 0x81, 0xC3, 0x04,
        0x81, 0xC0, 0x53, 0xE5, 0x25, 0x41, 0x1C, 0xCB, 0x8F, 0xAF,
        0x83, 0xBE, 0x64, 0x43, 0x70, 0x4E, 0x1D, 0x86, 0xF8, 0xFA,
        0xEA, 0x65, 0x9B, 0x45, 0xBC, 0xF1, 0x79, 0x57, 0x87, 0x51,
        0x2F, 0x6D, 0x50, 0xB8, 0x0D, 0x9A, 0x9F, 0x8C, 0xE8, 0x9B,
        0xE8, 0xFA, 0x1E, 0xF0, 0xA1, 0x98, 0xCA, 0x8B, 0x34, 0xD4,
        0x71, 0x53, 0xF0, 0xA7, 0x1D, 0xD6, 0x0D, 0xDF, 0x63, 0x61,
        0xA7, 0x12, 0x80, 0x64, 0xF7, 0x73, 0x14, 0x03, 0xD4, 0x54,
        0x01, 0x9D, 0x9D, 0x5D, 0x42, 0xC1, 0x2B, 0x91, 0xC3, 0xA2,
        0xD3, 0x12, 0x67, 0x35, 0x3B, 0xD7, 0x67, 0x31, 0xD5, 0xDC,
        0xDF, 0x4C, 0x4C, 0xAA, 0x45, 0xA8, 0x5D, 0x1E, 0xFB, 0x9E,
        0x34, 0x5D, 0x4B, 0x83, 0x77, 0xBF, 0x52, 0x8A, 0xDB, 0x67,
        0x7A, 0x52, 0xA4, 0x02, 0x29, 0xEB, 0x34, 0x9A, 0x4E, 0x86,
        0x25, 0x66, 0xFF, 0xA0, 0x79, 0x47, 0xBE, 0x94, 0xC2, 0x69,
        0x14, 0x03, 0xD4, 0x54, 0x01, 0x9D, 0x9D, 0x5D, 0x42, 0xC1,
        0x2B, 0x91, 0xC3, 0xA2, 0xD3, 0x12, 0x67, 0x35, 0x3B, 0xD7,
        0x67, 0x31, 0xD5, 0xDC, 0xDF, 0x4C, 0x4C, 0xAA, 0x45, 0xA8,
        0x5D, 0x1E, 0xFB, 0x9E, 0x34, 0x5D, 0x4B, 0x83, 0x77, 0xBF,
        0x52, 0x8A, 0xDB, 0x67, 0x7A, 0x52, 0xA4, 0x02, 0x29, 0xEB,
        0x34, 0x9A, 0x4E, 0x86, 0x25, 0x66, 0xFF, 0xA0, 0x79, 0x47,
        0xBE, 0x94, 0xC2, 0x69
};
static const int sizeof_bench_sphincs_small_level5_key = sizeof(bench_sphincs_small_level5_key);

#endif /* HAVE_SPHINCS */

#if defined(HAVE_ECC) && defined(USE_CERT_BUFFERS_256)

/* ./certs/ecc-client-key.der, ECC */
static const unsigned char ecc_clikey_der_256[] =
{
        0x30, 0x77, 0x02, 0x01, 0x01, 0x04, 0x20, 0xF8, 0xCF, 0x92,
        0x6B, 0xBD, 0x1E, 0x28, 0xF1, 0xA8, 0xAB, 0xA1, 0x23, 0x4F,
        0x32, 0x74, 0x18, 0x88, 0x50, 0xAD, 0x7E, 0xC7, 0xEC, 0x92,
        0xF8, 0x8F, 0x97, 0x4D, 0xAF, 0x56, 0x89, 0x65, 0xC7, 0xA0,
        0x0A, 0x06, 0x08, 0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x03, 0x01,
        0x07, 0xA1, 0x44, 0x03, 0x42, 0x00, 0x04, 0x55, 0xBF, 0xF4,
        0x0F, 0x44, 0x50, 0x9A, 0x3D, 0xCE, 0x9B, 0xB7, 0xF0, 0xC5,
        0x4D, 0xF5, 0x70, 0x7B, 0xD4, 0xEC, 0x24, 0x8E, 0x19, 0x80,
        0xEC, 0x5A, 0x4C, 0xA2, 0x24, 0x03, 0x62, 0x2C, 0x9B, 0xDA,
        0xEF, 0xA2, 0x35, 0x12, 0x43, 0x84, 0x76, 0x16, 0xC6, 0x56,
        0x95, 0x06, 0xCC, 0x01, 0xA9, 0xBD, 0xF6, 0x75, 0x1A, 0x42,
        0xF7, 0xBD, 0xA9, 0xB2, 0x36, 0x22, 0x5F, 0xC7, 0x5D, 0x7F,
        0xB4
};
static const int sizeof_ecc_clikey_der_256 = sizeof(ecc_clikey_der_256);

/* ./certs/ecc-client-keyPub.der, ECC */
static const unsigned char ecc_clikeypub_der_256[] =
{
        0x30, 0x59, 0x30, 0x13, 0x06, 0x07, 0x2A, 0x86, 0x48, 0xCE,
        0x3D, 0x02, 0x01, 0x06, 0x08, 0x2A, 0x86, 0x48, 0xCE, 0x3D,
        0x03, 0x01, 0x07, 0x03, 0x42, 0x00, 0x04, 0x55, 0xBF, 0xF4,
        0x0F, 0x44, 0x50, 0x9A, 0x3D, 0xCE, 0x9B, 0xB7, 0xF0, 0xC5,
        0x4D, 0xF5, 0x70, 0x7B, 0xD4, 0xEC, 0x24, 0x8E, 0x19, 0x80,
        0xEC, 0x5A, 0x4C, 0xA2, 0x24, 0x03, 0x62, 0x2C, 0x9B, 0xDA,
        0xEF, 0xA2, 0x35, 0x12, 0x43, 0x84, 0x76, 0x16, 0xC6, 0x56,
        0x95, 0x06, 0xCC, 0x01, 0xA9, 0xBD, 0xF6, 0x75, 0x1A, 0x42,
        0xF7, 0xBD, 0xA9, 0xB2, 0x36, 0x22, 0x5F, 0xC7, 0x5D, 0x7F,
        0xB4
};
static const int sizeof_ecc_clikeypub_der_256 = sizeof(ecc_clikeypub_der_256);

/* ./certs/client-ecc-cert.der, ECC */
static const unsigned char cliecc_cert_der_256[] =
{
        0x30, 0x82, 0x03, 0x5E, 0x30, 0x82, 0x03, 0x04, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x14, 0x75, 0x99, 0xDB, 0x38, 0xED,
        0x32, 0xB1, 0xC2, 0xD1, 0x2C, 0x5E, 0x6F, 0x6F, 0x9D, 0x47,
        0x17, 0x58, 0xDD, 0xEE, 0x26, 0x30, 0x0A, 0x06, 0x08, 0x2A,
        0x86, 0x48, 0xCE, 0x3D, 0x04, 0x03, 0x02, 0x30, 0x81, 0x8D,
        0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13,
        0x02, 0x55, 0x53, 0x31, 0x0F, 0x30, 0x0D, 0x06, 0x03, 0x55,
        0x04, 0x08, 0x0C, 0x06, 0x4F, 0x72, 0x65, 0x67, 0x6F, 0x6E,
        0x31, 0x0E, 0x30, 0x0C, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C,
        0x05, 0x53, 0x61, 0x6C, 0x65, 0x6D, 0x31, 0x13, 0x30, 0x11,
        0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x0A, 0x43, 0x6C, 0x69,
        0x65, 0x6E, 0x74, 0x20, 0x45, 0x43, 0x43, 0x31, 0x0D, 0x30,
        0x0B, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x04, 0x46, 0x61,
        0x73, 0x74, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04,
        0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F,
        0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D,
        0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x30, 0x1E, 0x17, 0x0D, 0x32, 0x34, 0x31, 0x32, 0x31,
        0x38, 0x32, 0x31, 0x32, 0x35, 0x33, 0x30, 0x5A, 0x17, 0x0D,
        0x32, 0x37, 0x30, 0x39, 0x31, 0x34, 0x32, 0x31, 0x32, 0x35,
        0x33, 0x30, 0x5A, 0x30, 0x81, 0x8D, 0x31, 0x0B, 0x30, 0x09,
        0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31,
        0x0F, 0x30, 0x0D, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x06,
        0x4F, 0x72, 0x65, 0x67, 0x6F, 0x6E, 0x31, 0x0E, 0x30, 0x0C,
        0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x05, 0x53, 0x61, 0x6C,
        0x65, 0x6D, 0x31, 0x13, 0x30, 0x11, 0x06, 0x03, 0x55, 0x04,
        0x0A, 0x0C, 0x0A, 0x43, 0x6C, 0x69, 0x65, 0x6E, 0x74, 0x20,
        0x45, 0x43, 0x43, 0x31, 0x0D, 0x30, 0x0B, 0x06, 0x03, 0x55,
        0x04, 0x0B, 0x0C, 0x04, 0x46, 0x61, 0x73, 0x74, 0x31, 0x18,
        0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77,
        0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C,
        0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09,
        0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16,
        0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66,
        0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x30, 0x59, 0x30,
        0x13, 0x06, 0x07, 0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x02, 0x01,
        0x06, 0x08, 0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x03, 0x01, 0x07,
        0x03, 0x42, 0x00, 0x04, 0x55, 0xBF, 0xF4, 0x0F, 0x44, 0x50,
        0x9A, 0x3D, 0xCE, 0x9B, 0xB7, 0xF0, 0xC5, 0x4D, 0xF5, 0x70,
        0x7B, 0xD4, 0xEC, 0x24, 0x8E, 0x19, 0x80, 0xEC, 0x5A, 0x4C,
        0xA2, 0x24, 0x03, 0x62, 0x2C, 0x9B, 0xDA, 0xEF, 0xA2, 0x35,
        0x12, 0x43, 0x84, 0x76, 0x16, 0xC6, 0x56, 0x95, 0x06, 0xCC,
        0x01, 0xA9, 0xBD, 0xF6, 0x75, 0x1A, 0x42, 0xF7, 0xBD, 0xA9,
        0xB2, 0x36, 0x22, 0x5F, 0xC7, 0x5D, 0x7F, 0xB4, 0xA3, 0x82,
        0x01, 0x3E, 0x30, 0x82, 0x01, 0x3A, 0x30, 0x1D, 0x06, 0x03,
        0x55, 0x1D, 0x0E, 0x04, 0x16, 0x04, 0x14, 0xEB, 0xD4, 0x4B,
        0x59, 0x6B, 0x95, 0x61, 0x3F, 0x51, 0x57, 0xB6, 0x04, 0x4D,
        0x89, 0x41, 0x88, 0x44, 0x5C, 0xAB, 0xF2, 0x30, 0x81, 0xCD,
        0x06, 0x03, 0x55, 0x1D, 0x23, 0x04, 0x81, 0xC5, 0x30, 0x81,
        0xC2, 0x80, 0x14, 0xEB, 0xD4, 0x4B, 0x59, 0x6B, 0x95, 0x61,
        0x3F, 0x51, 0x57, 0xB6, 0x04, 0x4D, 0x89, 0x41, 0x88, 0x44,
        0x5C, 0xAB, 0xF2, 0xA1, 0x81, 0x93, 0xA4, 0x81, 0x90, 0x30,
        0x81, 0x8D, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04,
        0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x0F, 0x30, 0x0D, 0x06,
        0x03, 0x55, 0x04, 0x08, 0x0C, 0x06, 0x4F, 0x72, 0x65, 0x67,
        0x6F, 0x6E, 0x31, 0x0E, 0x30, 0x0C, 0x06, 0x03, 0x55, 0x04,
        0x07, 0x0C, 0x05, 0x53, 0x61, 0x6C, 0x65, 0x6D, 0x31, 0x13,
        0x30, 0x11, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x0A, 0x43,
        0x6C, 0x69, 0x65, 0x6E, 0x74, 0x20, 0x45, 0x43, 0x43, 0x31,
        0x0D, 0x30, 0x0B, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x04,
        0x46, 0x61, 0x73, 0x74, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03,
        0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77,
        0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D,
        0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86,
        0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66,
        0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E,
        0x63, 0x6F, 0x6D, 0x82, 0x14, 0x75, 0x99, 0xDB, 0x38, 0xED,
        0x32, 0xB1, 0xC2, 0xD1, 0x2C, 0x5E, 0x6F, 0x6F, 0x9D, 0x47,
        0x17, 0x58, 0xDD, 0xEE, 0x26, 0x30, 0x0C, 0x06, 0x03, 0x55,
        0x1D, 0x13, 0x04, 0x05, 0x30, 0x03, 0x01, 0x01, 0xFF, 0x30,
        0x1C, 0x06, 0x03, 0x55, 0x1D, 0x11, 0x04, 0x15, 0x30, 0x13,
        0x82, 0x0B, 0x65, 0x78, 0x61, 0x6D, 0x70, 0x6C, 0x65, 0x2E,
        0x63, 0x6F, 0x6D, 0x87, 0x04, 0x7F, 0x00, 0x00, 0x01, 0x30,
        0x1D, 0x06, 0x03, 0x55, 0x1D, 0x25, 0x04, 0x16, 0x30, 0x14,
        0x06, 0x08, 0x2B, 0x06, 0x01, 0x05, 0x05, 0x07, 0x03, 0x01,
        0x06, 0x08, 0x2B, 0x06, 0x01, 0x05, 0x05, 0x07, 0x03, 0x02,
        0x30, 0x0A, 0x06, 0x08, 0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x04,
        0x03, 0x02, 0x03, 0x48, 0x00, 0x30, 0x45, 0x02, 0x20, 0x03,
        0x69, 0x31, 0x45, 0x6F, 0x01, 0x88, 0x6B, 0x63, 0xC6, 0x1C,
        0xEB, 0x39, 0xE4, 0x9A, 0xA8, 0xE2, 0xE0, 0x34, 0xAC, 0xAC,
        0xE6, 0xA1, 0xD6, 0xFE, 0xCE, 0x85, 0x98, 0x1E, 0xB0, 0x0D,
        0xA9, 0x02, 0x21, 0x00, 0xA3, 0xDD, 0x84, 0x5D, 0x08, 0x28,
        0x4B, 0x8B, 0x58, 0xFB, 0x0D, 0x33, 0xDB, 0x02, 0xEA, 0xC8,
        0x0C, 0xDA, 0x34, 0x0B, 0x4E, 0x83, 0xA2, 0x10, 0x67, 0x99,
        0x19, 0x1C, 0x93, 0x91, 0xC8, 0xC7
};
static const int sizeof_cliecc_cert_der_256 = sizeof(cliecc_cert_der_256);

/* ./certs/ecc-key.der, ECC */
static const unsigned char ecc_key_der_256[] =
{
        0x30, 0x77, 0x02, 0x01, 0x01, 0x04, 0x20, 0x45, 0xB6, 0x69,
        0x02, 0x73, 0x9C, 0x6C, 0x85, 0xA1, 0x38, 0x5B, 0x72, 0xE8,
        0xE8, 0xC7, 0xAC, 0xC4, 0x03, 0x8D, 0x53, 0x35, 0x04, 0xFA,
        0x6C, 0x28, 0xDC, 0x34, 0x8D, 0xE1, 0xA8, 0x09, 0x8C, 0xA0,
        0x0A, 0x06, 0x08, 0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x03, 0x01,
        0x07, 0xA1, 0x44, 0x03, 0x42, 0x00, 0x04, 0xBB, 0x33, 0xAC,
        0x4C, 0x27, 0x50, 0x4A, 0xC6, 0x4A, 0xA5, 0x04, 0xC3, 0x3C,
        0xDE, 0x9F, 0x36, 0xDB, 0x72, 0x2D, 0xCE, 0x94, 0xEA, 0x2B,
        0xFA, 0xCB, 0x20, 0x09, 0x39, 0x2C, 0x16, 0xE8, 0x61, 0x02,
        0xE9, 0xAF, 0x4D, 0xD3, 0x02, 0x93, 0x9A, 0x31, 0x5B, 0x97,
        0x92, 0x21, 0x7F, 0xF0, 0xCF, 0x18, 0xDA, 0x91, 0x11, 0x02,
        0x34, 0x86, 0xE8, 0x20, 0x58, 0x33, 0x0B, 0x80, 0x34, 0x89,
        0xD8
};
static const int sizeof_ecc_key_der_256 = sizeof(ecc_key_der_256);

/* ./certs/ecc-keyPub.der, ECC */
static const unsigned char ecc_key_pub_der_256[] =
{
        0x30, 0x59, 0x30, 0x13, 0x06, 0x07, 0x2A, 0x86, 0x48, 0xCE,
        0x3D, 0x02, 0x01, 0x06, 0x08, 0x2A, 0x86, 0x48, 0xCE, 0x3D,
        0x03, 0x01, 0x07, 0x03, 0x42, 0x00, 0x04, 0xBB, 0x33, 0xAC,
        0x4C, 0x27, 0x50, 0x4A, 0xC6, 0x4A, 0xA5, 0x04, 0xC3, 0x3C,
        0xDE, 0x9F, 0x36, 0xDB, 0x72, 0x2D, 0xCE, 0x94, 0xEA, 0x2B,
        0xFA, 0xCB, 0x20, 0x09, 0x39, 0x2C, 0x16, 0xE8, 0x61, 0x02,
        0xE9, 0xAF, 0x4D, 0xD3, 0x02, 0x93, 0x9A, 0x31, 0x5B, 0x97,
        0x92, 0x21, 0x7F, 0xF0, 0xCF, 0x18, 0xDA, 0x91, 0x11, 0x02,
        0x34, 0x86, 0xE8, 0x20, 0x58, 0x33, 0x0B, 0x80, 0x34, 0x89,
        0xD8
};
static const int sizeof_ecc_key_pub_der_256 = sizeof(ecc_key_pub_der_256);

/* ./certs/statickeys/ecc-secp256r1.der, ECC */
static const unsigned char ecc_secp_r1_statickey_der_256[] =
{
        0x30, 0x77, 0x02, 0x01, 0x01, 0x04, 0x20, 0xD3, 0x6B, 0xC6,
        0x68, 0x76, 0xDE, 0xD8, 0x97, 0x95, 0xF6, 0xD9, 0x8E, 0x2F,
        0x41, 0x73, 0x53, 0xF8, 0x03, 0x57, 0xED, 0x90, 0x80, 0x19,
        0xEB, 0xAA, 0x4A, 0x91, 0x8A, 0x8F, 0x31, 0x63, 0x45, 0xA0,
        0x0A, 0x06, 0x08, 0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x03, 0x01,
        0x07, 0xA1, 0x44, 0x03, 0x42, 0x00, 0x04, 0x58, 0xA0, 0x80,
        0x7C, 0x51, 0xEC, 0xDD, 0x41, 0x5F, 0x93, 0xA8, 0x7A, 0x60,
        0x47, 0x47, 0xE9, 0xCF, 0x5A, 0x40, 0xE4, 0xDD, 0x37, 0xEC,
        0xCA, 0xA7, 0x10, 0x1C, 0x43, 0xDA, 0xE8, 0x73, 0x8C, 0x28,
        0xF5, 0xC9, 0xEC, 0x1A, 0x33, 0x0B, 0x26, 0x2E, 0x97, 0x80,
        0x2E, 0xE0, 0xB8, 0x01, 0x91, 0x16, 0xB4, 0xCC, 0x02, 0x18,
        0xB5, 0x1D, 0xCC, 0xBA, 0x3C, 0xED, 0x04, 0xC9, 0xA8, 0x92,
        0x37
};
static const int sizeof_ecc_secp_r1_statickey_der_256 = sizeof(ecc_secp_r1_statickey_der_256);

/* ./certs/server-ecc-comp.der, ECC */
static const unsigned char serv_ecc_comp_der_256[] =
{
        0x30, 0x82, 0x03, 0x77, 0x30, 0x82, 0x03, 0x1D, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x14, 0x0C, 0x33, 0x75, 0x68, 0xFF,
        0x2E, 0x13, 0x4A, 0x2A, 0x30, 0x56, 0xB4, 0xA8, 0x79, 0x14,
        0xE2, 0xC4, 0xCA, 0x61, 0x54, 0x30, 0x0A, 0x06, 0x08, 0x2A,
        0x86, 0x48, 0xCE, 0x3D, 0x04, 0x03, 0x02, 0x30, 0x81, 0xA0,
        0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13,
        0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55,
        0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E,
        0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x07,
        0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31,
        0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x0F,
        0x45, 0x6C, 0x6C, 0x69, 0x70, 0x74, 0x69, 0x63, 0x20, 0x2D,
        0x20, 0x63, 0x6F, 0x6D, 0x70, 0x31, 0x18, 0x30, 0x16, 0x06,
        0x03, 0x55, 0x04, 0x0B, 0x0C, 0x0F, 0x53, 0x65, 0x72, 0x76,
        0x65, 0x72, 0x20, 0x45, 0x43, 0x43, 0x2D, 0x63, 0x6F, 0x6D,
        0x70, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x03,
        0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66,
        0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30,
        0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01,
        0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77,
        0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D,
        0x30, 0x1E, 0x17, 0x0D, 0x32, 0x34, 0x31, 0x32, 0x31, 0x38,
        0x32, 0x31, 0x32, 0x35, 0x33, 0x30, 0x5A, 0x17, 0x0D, 0x32,
        0x37, 0x30, 0x39, 0x31, 0x34, 0x32, 0x31, 0x32, 0x35, 0x33,
        0x30, 0x5A, 0x30, 0x81, 0xA0, 0x31, 0x0B, 0x30, 0x09, 0x06,
        0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x10,
        0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D,
        0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E,
        0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A,
        0x65, 0x6D, 0x61, 0x6E, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03,
        0x55, 0x04, 0x0A, 0x0C, 0x0F, 0x45, 0x6C, 0x6C, 0x69, 0x70,
        0x74, 0x69, 0x63, 0x20, 0x2D, 0x20, 0x63, 0x6F, 0x6D, 0x70,
        0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C,
        0x0F, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x20, 0x45, 0x43,
        0x43, 0x2D, 0x63, 0x6F, 0x6D, 0x70, 0x31, 0x18, 0x30, 0x16,
        0x06, 0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77,
        0x2E, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63,
        0x6F, 0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86,
        0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69,
        0x6E, 0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73,
        0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x30, 0x39, 0x30, 0x13, 0x06,
        0x07, 0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x02, 0x01, 0x06, 0x08,
        0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x03, 0x01, 0x07, 0x03, 0x22,
        0x00, 0x02, 0xBB, 0x33, 0xAC, 0x4C, 0x27, 0x50, 0x4A, 0xC6,
        0x4A, 0xA5, 0x04, 0xC3, 0x3C, 0xDE, 0x9F, 0x36, 0xDB, 0x72,
        0x2D, 0xCE, 0x94, 0xEA, 0x2B, 0xFA, 0xCB, 0x20, 0x09, 0x39,
        0x2C, 0x16, 0xE8, 0x61, 0xA3, 0x82, 0x01, 0x51, 0x30, 0x82,
        0x01, 0x4D, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D, 0x0E, 0x04,
        0x16, 0x04, 0x14, 0x8C, 0x38, 0x3A, 0x6B, 0xB8, 0x24, 0xB7,
        0xDF, 0x6E, 0xF4, 0x59, 0xAC, 0x56, 0x4E, 0xAA, 0xE2, 0x58,
        0xA6, 0x5A, 0x18, 0x30, 0x81, 0xE0, 0x06, 0x03, 0x55, 0x1D,
        0x23, 0x04, 0x81, 0xD8, 0x30, 0x81, 0xD5, 0x80, 0x14, 0x8C,
        0x38, 0x3A, 0x6B, 0xB8, 0x24, 0xB7, 0xDF, 0x6E, 0xF4, 0x59,
        0xAC, 0x56, 0x4E, 0xAA, 0xE2, 0x58, 0xA6, 0x5A, 0x18, 0xA1,
        0x81, 0xA6, 0xA4, 0x81, 0xA3, 0x30, 0x81, 0xA0, 0x31, 0x0B,
        0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55,
        0x53, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08,
        0x0C, 0x07, 0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31,
        0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07,
        0x42, 0x6F, 0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x18, 0x30,
        0x16, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x0F, 0x45, 0x6C,
        0x6C, 0x69, 0x70, 0x74, 0x69, 0x63, 0x20, 0x2D, 0x20, 0x63,
        0x6F, 0x6D, 0x70, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55,
        0x04, 0x0B, 0x0C, 0x0F, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
        0x20, 0x45, 0x43, 0x43, 0x2D, 0x63, 0x6F, 0x6D, 0x70, 0x31,
        0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F,
        0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73,
        0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06,
        0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01,
        0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x82, 0x14,
        0x0C, 0x33, 0x75, 0x68, 0xFF, 0x2E, 0x13, 0x4A, 0x2A, 0x30,
        0x56, 0xB4, 0xA8, 0x79, 0x14, 0xE2, 0xC4, 0xCA, 0x61, 0x54,
        0x30, 0x0C, 0x06, 0x03, 0x55, 0x1D, 0x13, 0x04, 0x05, 0x30,
        0x03, 0x01, 0x01, 0xFF, 0x30, 0x1C, 0x06, 0x03, 0x55, 0x1D,
        0x11, 0x04, 0x15, 0x30, 0x13, 0x82, 0x0B, 0x65, 0x78, 0x61,
        0x6D, 0x70, 0x6C, 0x65, 0x2E, 0x63, 0x6F, 0x6D, 0x87, 0x04,
        0x7F, 0x00, 0x00, 0x01, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D,
        0x25, 0x04, 0x16, 0x30, 0x14, 0x06, 0x08, 0x2B, 0x06, 0x01,
        0x05, 0x05, 0x07, 0x03, 0x01, 0x06, 0x08, 0x2B, 0x06, 0x01,
        0x05, 0x05, 0x07, 0x03, 0x02, 0x30, 0x0A, 0x06, 0x08, 0x2A,
        0x86, 0x48, 0xCE, 0x3D, 0x04, 0x03, 0x02, 0x03, 0x48, 0x00,
        0x30, 0x45, 0x02, 0x20, 0x23, 0xD1, 0xF6, 0x8F, 0xD4, 0x29,
        0x83, 0x27, 0x8F, 0x4A, 0x8E, 0x49, 0x44, 0x49, 0x32, 0x1C,
        0x12, 0xE4, 0xC1, 0x33, 0xB1, 0x97, 0x2B, 0x31, 0xCD, 0x62,
        0x47, 0xCB, 0xB6, 0xD0, 0xEB, 0x4D, 0x02, 0x21, 0x00, 0xE0,
        0x6E, 0xDC, 0x48, 0x70, 0xAA, 0x10, 0xB2, 0x74, 0xD1, 0x88,
        0xDA, 0xF1, 0x3F, 0xD9, 0xD7, 0xE9, 0xE4, 0x88, 0xE5, 0x91,
        0x00, 0x03, 0xC1, 0x0C, 0x1F, 0x54, 0xA0, 0xCA, 0x4D, 0x99,
        0x6A
};
static const int sizeof_serv_ecc_comp_der_256 = sizeof(serv_ecc_comp_der_256);

/* ./certs/server-ecc-rsa.der, ECC */
static const unsigned char serv_ecc_rsa_der_256[] =
{
        0x30, 0x82, 0x04, 0x2A, 0x30, 0x82, 0x03, 0x12, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x01, 0x01, 0x30, 0x0D, 0x06, 0x09,
        0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B, 0x05,
        0x00, 0x30, 0x81, 0x94, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03,
        0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x10, 0x30,
        0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F,
        0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E, 0x06,
        0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65,
        0x6D, 0x61, 0x6E, 0x31, 0x11, 0x30, 0x0F, 0x06, 0x03, 0x55,
        0x04, 0x0A, 0x0C, 0x08, 0x53, 0x61, 0x77, 0x74, 0x6F, 0x6F,
        0x74, 0x68, 0x31, 0x13, 0x30, 0x11, 0x06, 0x03, 0x55, 0x04,
        0x0B, 0x0C, 0x0A, 0x43, 0x6F, 0x6E, 0x73, 0x75, 0x6C, 0x74,
        0x69, 0x6E, 0x67, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55,
        0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F,
        0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31,
        0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7,
        0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F,
        0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63,
        0x6F, 0x6D, 0x30, 0x1E, 0x17, 0x0D, 0x32, 0x34, 0x31, 0x32,
        0x31, 0x38, 0x32, 0x31, 0x32, 0x35, 0x33, 0x30, 0x5A, 0x17,
        0x0D, 0x32, 0x37, 0x30, 0x39, 0x31, 0x34, 0x32, 0x31, 0x32,
        0x35, 0x33, 0x30, 0x5A, 0x30, 0x81, 0x9D, 0x31, 0x0B, 0x30,
        0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53,
        0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C,
        0x07, 0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10,
        0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42,
        0x6F, 0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x1A, 0x30, 0x18,
        0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x11, 0x45, 0x6C, 0x6C,
        0x69, 0x70, 0x74, 0x69, 0x63, 0x20, 0x2D, 0x20, 0x52, 0x53,
        0x41, 0x73, 0x69, 0x67, 0x31, 0x13, 0x30, 0x11, 0x06, 0x03,
        0x55, 0x04, 0x0B, 0x0C, 0x0A, 0x45, 0x43, 0x43, 0x2D, 0x52,
        0x53, 0x41, 0x73, 0x69, 0x67, 0x31, 0x18, 0x30, 0x16, 0x06,
        0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48,
        0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E,
        0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C,
        0x2E, 0x63, 0x6F, 0x6D, 0x30, 0x59, 0x30, 0x13, 0x06, 0x07,
        0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x02, 0x01, 0x06, 0x08, 0x2A,
        0x86, 0x48, 0xCE, 0x3D, 0x03, 0x01, 0x07, 0x03, 0x42, 0x00,
        0x04, 0xBB, 0x33, 0xAC, 0x4C, 0x27, 0x50, 0x4A, 0xC6, 0x4A,
        0xA5, 0x04, 0xC3, 0x3C, 0xDE, 0x9F, 0x36, 0xDB, 0x72, 0x2D,
        0xCE, 0x94, 0xEA, 0x2B, 0xFA, 0xCB, 0x20, 0x09, 0x39, 0x2C,
        0x16, 0xE8, 0x61, 0x02, 0xE9, 0xAF, 0x4D, 0xD3, 0x02, 0x93,
        0x9A, 0x31, 0x5B, 0x97, 0x92, 0x21, 0x7F, 0xF0, 0xCF, 0x18,
        0xDA, 0x91, 0x11, 0x02, 0x34, 0x86, 0xE8, 0x20, 0x58, 0x33,
        0x0B, 0x80, 0x34, 0x89, 0xD8, 0xA3, 0x82, 0x01, 0x45, 0x30,
        0x82, 0x01, 0x41, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D, 0x0E,
        0x04, 0x16, 0x04, 0x14, 0x5D, 0x5D, 0x26, 0xEF, 0xAC, 0x7E,
        0x36, 0xF9, 0x9B, 0x76, 0x15, 0x2B, 0x4A, 0x25, 0x02, 0x23,
        0xEF, 0xB2, 0x89, 0x30, 0x30, 0x81, 0xD4, 0x06, 0x03, 0x55,
        0x1D, 0x23, 0x04, 0x81, 0xCC, 0x30, 0x81, 0xC9, 0x80, 0x14,
        0x27, 0x8E, 0x67, 0x11, 0x74, 0xC3, 0x26, 0x1D, 0x3F, 0xED,
        0x33, 0x63, 0xB3, 0xA4, 0xD8, 0x1D, 0x30, 0xE5, 0xE8, 0xD5,
        0xA1, 0x81, 0x9A, 0xA4, 0x81, 0x97, 0x30, 0x81, 0x94, 0x31,
        0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02,
        0x55, 0x53, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04,
        0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61,
        0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C,
        0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x11,
        0x30, 0x0F, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x08, 0x53,
        0x61, 0x77, 0x74, 0x6F, 0x6F, 0x74, 0x68, 0x31, 0x13, 0x30,
        0x11, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x0A, 0x43, 0x6F,
        0x6E, 0x73, 0x75, 0x6C, 0x74, 0x69, 0x6E, 0x67, 0x31, 0x18,
        0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77,
        0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C,
        0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09,
        0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16,
        0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66,
        0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x82, 0x14, 0x6B,
        0x9B, 0x70, 0xC6, 0xF1, 0xA3, 0x94, 0x65, 0x19, 0xA1, 0x08,
        0x58, 0xEF, 0xA7, 0x8D, 0x2B, 0x7A, 0x83, 0xC1, 0xDA, 0x30,
        0x0C, 0x06, 0x03, 0x55, 0x1D, 0x13, 0x04, 0x05, 0x30, 0x03,
        0x01, 0x01, 0xFF, 0x30, 0x1C, 0x06, 0x03, 0x55, 0x1D, 0x11,
        0x04, 0x15, 0x30, 0x13, 0x82, 0x0B, 0x65, 0x78, 0x61, 0x6D,
        0x70, 0x6C, 0x65, 0x2E, 0x63, 0x6F, 0x6D, 0x87, 0x04, 0x7F,
        0x00, 0x00, 0x01, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D, 0x25,
        0x04, 0x16, 0x30, 0x14, 0x06, 0x08, 0x2B, 0x06, 0x01, 0x05,
        0x05, 0x07, 0x03, 0x01, 0x06, 0x08, 0x2B, 0x06, 0x01, 0x05,
        0x05, 0x07, 0x03, 0x02, 0x30, 0x0D, 0x06, 0x09, 0x2A, 0x86,
        0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x0B, 0x05, 0x00, 0x03,
        0x82, 0x01, 0x01, 0x00, 0x38, 0xE8, 0x66, 0xC3, 0x74, 0xE0,
        0x5C, 0x59, 0xA9, 0x12, 0x46, 0xAD, 0x84, 0xE3, 0xB3, 0xFA,
        0x3E, 0x68, 0x90, 0xD0, 0x06, 0xA4, 0x2C, 0xAB, 0xF7, 0xB3,
        0xB9, 0x7C, 0x89, 0x62, 0xFB, 0x88, 0xEB, 0x88, 0x04, 0xD9,
        0x4B, 0xAC, 0x7E, 0x4B, 0x4B, 0x7C, 0x7C, 0x0F, 0xE1, 0xDD,
        0x73, 0xEE, 0x88, 0xB3, 0xE7, 0x1E, 0x00, 0x7B, 0xFE, 0xAA,
        0x24, 0x50, 0xD7, 0x3C, 0xC4, 0x03, 0xF2, 0xBA, 0xFD, 0x81,
        0xCE, 0x7A, 0x0C, 0x1C, 0x48, 0x6A, 0x33, 0xAD, 0xA7, 0xF8,
        0xF7, 0xCA, 0xF7, 0x00, 0x47, 0x5C, 0xFC, 0xF8, 0x05, 0x98,
        0x5F, 0xEC, 0xC3, 0xAA, 0x75, 0x93, 0x03, 0xA1, 0x4E, 0x7A,
        0x37, 0xEC, 0x8B, 0xA9, 0x99, 0xFA, 0x76, 0x85, 0xCB, 0xC3,
        0x99, 0x29, 0x70, 0x1E, 0x9C, 0x41, 0xF1, 0x49, 0xFD, 0xE8,
        0xC0, 0x75, 0x0A, 0xDD, 0xA0, 0xE0, 0xD3, 0x6E, 0x7F, 0x93,
        0x7E, 0x4D, 0x2E, 0xEE, 0xA1, 0xC9, 0xDB, 0xFC, 0x98, 0x86,
        0xBB, 0x67, 0x7D, 0x2F, 0x74, 0x00, 0x10, 0x7C, 0x24, 0x5B,
        0x58, 0xF3, 0x5A, 0xED, 0x96, 0x6E, 0x8F, 0x34, 0xEE, 0x47,
        0x46, 0xBE, 0x3E, 0x96, 0x25, 0x2B, 0x7C, 0x90, 0x5B, 0x65,
        0x24, 0x48, 0x66, 0x5A, 0x79, 0xA6, 0x6A, 0xF5, 0xED, 0x31,
        0xCF, 0x0B, 0x29, 0xC3, 0xF1, 0xAB, 0x91, 0x21, 0x9C, 0x79,
        0x99, 0xC9, 0x5C, 0x4C, 0x2B, 0xAC, 0xF1, 0x21, 0x5C, 0x44,
        0x07, 0x14, 0x45, 0xE5, 0xE0, 0x84, 0xCE, 0xA3, 0x49, 0x59,
        0x8D, 0x94, 0x4A, 0x9D, 0x11, 0x20, 0xC3, 0xD3, 0xFC, 0xCE,
        0x8F, 0x2C, 0x38, 0x5B, 0x38, 0xE7, 0xB2, 0xD0, 0x71, 0x9F,
        0x3F, 0xDE, 0x4E, 0x08, 0x03, 0xF9, 0x11, 0x58, 0x7C, 0x46,
        0x04, 0x0A, 0x73, 0x28, 0x68, 0xB8, 0x17, 0x17, 0x02, 0x45,
        0x9C, 0x65, 0x96, 0x1A, 0xB3, 0x98, 0x4D, 0x3B, 0xFB, 0xC7

};
static const int sizeof_serv_ecc_rsa_der_256 = sizeof(serv_ecc_rsa_der_256);

/* ./certs/server-ecc.der, ECC */
static const unsigned char serv_ecc_der_256[] =
{
        0x30, 0x82, 0x02, 0xA2, 0x30, 0x82, 0x02, 0x48, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x01, 0x03, 0x30, 0x0A, 0x06, 0x08,
        0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x04, 0x03, 0x02, 0x30, 0x81,
        0x97, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06,
        0x13, 0x02, 0x55, 0x53, 0x31, 0x13, 0x30, 0x11, 0x06, 0x03,
        0x55, 0x04, 0x08, 0x0C, 0x0A, 0x57, 0x61, 0x73, 0x68, 0x69,
        0x6E, 0x67, 0x74, 0x6F, 0x6E, 0x31, 0x10, 0x30, 0x0E, 0x06,
        0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x53, 0x65, 0x61, 0x74,
        0x74, 0x6C, 0x65, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55,
        0x04, 0x0A, 0x0C, 0x07, 0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53,
        0x4C, 0x31, 0x14, 0x30, 0x12, 0x06, 0x03, 0x55, 0x04, 0x0B,
        0x0C, 0x0B, 0x44, 0x65, 0x76, 0x65, 0x6C, 0x6F, 0x70, 0x6D,
        0x65, 0x6E, 0x74, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55,
        0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F,
        0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31,
        0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7,
        0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F,
        0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63,
        0x6F, 0x6D, 0x30, 0x1E, 0x17, 0x0D, 0x32, 0x34, 0x31, 0x32,
        0x31, 0x38, 0x32, 0x31, 0x32, 0x35, 0x33, 0x30, 0x5A, 0x17,
        0x0D, 0x32, 0x37, 0x30, 0x39, 0x31, 0x34, 0x32, 0x31, 0x32,
        0x35, 0x33, 0x30, 0x5A, 0x30, 0x81, 0x90, 0x31, 0x0B, 0x30,
        0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53,
        0x31, 0x13, 0x30, 0x11, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C,
        0x0A, 0x57, 0x61, 0x73, 0x68, 0x69, 0x6E, 0x67, 0x74, 0x6F,
        0x6E, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x07,
        0x0C, 0x07, 0x53, 0x65, 0x61, 0x74, 0x74, 0x6C, 0x65, 0x31,
        0x11, 0x30, 0x0F, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x08,
        0x45, 0x6C, 0x6C, 0x69, 0x70, 0x74, 0x69, 0x63, 0x31, 0x0C,
        0x30, 0x0A, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x03, 0x45,
        0x43, 0x43, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04,
        0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F,
        0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D,
        0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x30, 0x59, 0x30, 0x13, 0x06, 0x07, 0x2A, 0x86, 0x48,
        0xCE, 0x3D, 0x02, 0x01, 0x06, 0x08, 0x2A, 0x86, 0x48, 0xCE,
        0x3D, 0x03, 0x01, 0x07, 0x03, 0x42, 0x00, 0x04, 0xBB, 0x33,
        0xAC, 0x4C, 0x27, 0x50, 0x4A, 0xC6, 0x4A, 0xA5, 0x04, 0xC3,
        0x3C, 0xDE, 0x9F, 0x36, 0xDB, 0x72, 0x2D, 0xCE, 0x94, 0xEA,
        0x2B, 0xFA, 0xCB, 0x20, 0x09, 0x39, 0x2C, 0x16, 0xE8, 0x61,
        0x02, 0xE9, 0xAF, 0x4D, 0xD3, 0x02, 0x93, 0x9A, 0x31, 0x5B,
        0x97, 0x92, 0x21, 0x7F, 0xF0, 0xCF, 0x18, 0xDA, 0x91, 0x11,
        0x02, 0x34, 0x86, 0xE8, 0x20, 0x58, 0x33, 0x0B, 0x80, 0x34,
        0x89, 0xD8, 0xA3, 0x81, 0x89, 0x30, 0x81, 0x86, 0x30, 0x1D,
        0x06, 0x03, 0x55, 0x1D, 0x0E, 0x04, 0x16, 0x04, 0x14, 0x5D,
        0x5D, 0x26, 0xEF, 0xAC, 0x7E, 0x36, 0xF9, 0x9B, 0x76, 0x15,
        0x2B, 0x4A, 0x25, 0x02, 0x23, 0xEF, 0xB2, 0x89, 0x30, 0x30,
        0x1F, 0x06, 0x03, 0x55, 0x1D, 0x23, 0x04, 0x18, 0x30, 0x16,
        0x80, 0x14, 0x56, 0x8E, 0x9A, 0xC3, 0xF0, 0x42, 0xDE, 0x18,
        0xB9, 0x45, 0x55, 0x6E, 0xF9, 0x93, 0xCF, 0xEA, 0xC3, 0xF3,
        0xA5, 0x21, 0x30, 0x0C, 0x06, 0x03, 0x55, 0x1D, 0x13, 0x01,
        0x01, 0xFF, 0x04, 0x02, 0x30, 0x00, 0x30, 0x0E, 0x06, 0x03,
        0x55, 0x1D, 0x0F, 0x01, 0x01, 0xFF, 0x04, 0x04, 0x03, 0x02,
        0x03, 0xA8, 0x30, 0x13, 0x06, 0x03, 0x55, 0x1D, 0x25, 0x04,
        0x0C, 0x30, 0x0A, 0x06, 0x08, 0x2B, 0x06, 0x01, 0x05, 0x05,
        0x07, 0x03, 0x01, 0x30, 0x11, 0x06, 0x09, 0x60, 0x86, 0x48,
        0x01, 0x86, 0xF8, 0x42, 0x01, 0x01, 0x04, 0x04, 0x03, 0x02,
        0x06, 0x40, 0x30, 0x0A, 0x06, 0x08, 0x2A, 0x86, 0x48, 0xCE,
        0x3D, 0x04, 0x03, 0x02, 0x03, 0x48, 0x00, 0x30, 0x45, 0x02,
        0x21, 0x00, 0x8B, 0x82, 0xA5, 0xD2, 0xF6, 0xCA, 0x84, 0xBA,
        0xAD, 0x2D, 0xDE, 0x36, 0xE9, 0x2A, 0x4D, 0xEE, 0x4B, 0x20,
        0x46, 0xBA, 0xAB, 0x4E, 0xD0, 0x10, 0x6E, 0xEB, 0x30, 0xB6,
        0x7E, 0xD8, 0xAF, 0x8C, 0x02, 0x20, 0x06, 0x74, 0x40, 0x6A,
        0xA9, 0x31, 0x54, 0xFE, 0x20, 0x9D, 0xC6, 0x6D, 0x2B, 0xDF,
        0x1D, 0xAA, 0x63, 0xDA, 0xFC, 0x97, 0x50, 0x87, 0x92, 0x69,
        0xEE, 0x63, 0x57, 0xB6, 0xEC, 0xE2, 0xE9, 0xFA
};
static const int sizeof_serv_ecc_der_256 = sizeof(serv_ecc_der_256);

/* ./certs/ca-ecc-key.der, ECC */
static const unsigned char ca_ecc_key_der_256[] =
{
        0x30, 0x77, 0x02, 0x01, 0x01, 0x04, 0x20, 0x02, 0xE1, 0x33,
        0x98, 0x77, 0x97, 0xAC, 0x4A, 0x59, 0x6D, 0x28, 0x9B, 0x6E,
        0xA0, 0x93, 0x9B, 0x07, 0x71, 0x8B, 0x4D, 0x60, 0x63, 0x85,
        0x99, 0xE6, 0xBB, 0x16, 0x70, 0xE9, 0x0A, 0xF6, 0x80, 0xA0,
        0x0A, 0x06, 0x08, 0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x03, 0x01,
        0x07, 0xA1, 0x44, 0x03, 0x42, 0x00, 0x04, 0x02, 0xD3, 0xD9,
        0x6E, 0xD6, 0x01, 0x8E, 0x45, 0xC8, 0xB9, 0x90, 0x31, 0xE5,
        0xC0, 0x4C, 0xE3, 0x9E, 0xAD, 0x29, 0x38, 0x98, 0xBA, 0x10,
        0xD6, 0xE9, 0x09, 0x2A, 0x80, 0xA9, 0x2E, 0x17, 0x2A, 0xB9,
        0x8A, 0xBF, 0x33, 0x83, 0x46, 0xE3, 0x95, 0x0B, 0xE4, 0x77,
        0x40, 0xB5, 0x3B, 0x43, 0x45, 0x33, 0x0F, 0x61, 0x53, 0x7C,
        0x37, 0x44, 0xC1, 0xCB, 0xFC, 0x80, 0xCA, 0xE8, 0x43, 0xEA,
        0xA7
};
static const int sizeof_ca_ecc_key_der_256 = sizeof(ca_ecc_key_der_256);

/* ./certs/ca-ecc-cert.der, ECC */
static const unsigned char ca_ecc_cert_der_256[] =
{
        0x30, 0x82, 0x02, 0x95, 0x30, 0x82, 0x02, 0x3B, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x14, 0x30, 0xB9, 0x30, 0x50, 0xF8,
        0x1A, 0x0D, 0xFF, 0xAD, 0x68, 0xD1, 0x6D, 0xE8, 0xA3, 0x6B,
        0x58, 0x23, 0x33, 0x7A, 0x84, 0x30, 0x0A, 0x06, 0x08, 0x2A,
        0x86, 0x48, 0xCE, 0x3D, 0x04, 0x03, 0x02, 0x30, 0x81, 0x97,
        0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13,
        0x02, 0x55, 0x53, 0x31, 0x13, 0x30, 0x11, 0x06, 0x03, 0x55,
        0x04, 0x08, 0x0C, 0x0A, 0x57, 0x61, 0x73, 0x68, 0x69, 0x6E,
        0x67, 0x74, 0x6F, 0x6E, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03,
        0x55, 0x04, 0x07, 0x0C, 0x07, 0x53, 0x65, 0x61, 0x74, 0x74,
        0x6C, 0x65, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04,
        0x0A, 0x0C, 0x07, 0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C,
        0x31, 0x14, 0x30, 0x12, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C,
        0x0B, 0x44, 0x65, 0x76, 0x65, 0x6C, 0x6F, 0x70, 0x6D, 0x65,
        0x6E, 0x74, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04,
        0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F,
        0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D,
        0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x30, 0x1E, 0x17, 0x0D, 0x32, 0x34, 0x31, 0x32, 0x31,
        0x38, 0x32, 0x31, 0x32, 0x35, 0x32, 0x39, 0x5A, 0x17, 0x0D,
        0x32, 0x37, 0x30, 0x39, 0x31, 0x34, 0x32, 0x31, 0x32, 0x35,
        0x32, 0x39, 0x5A, 0x30, 0x81, 0x97, 0x31, 0x0B, 0x30, 0x09,
        0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31,
        0x13, 0x30, 0x11, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x0A,
        0x57, 0x61, 0x73, 0x68, 0x69, 0x6E, 0x67, 0x74, 0x6F, 0x6E,
        0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C,
        0x07, 0x53, 0x65, 0x61, 0x74, 0x74, 0x6C, 0x65, 0x31, 0x10,
        0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x07, 0x77,
        0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C, 0x31, 0x14, 0x30, 0x12,
        0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x0B, 0x44, 0x65, 0x76,
        0x65, 0x6C, 0x6F, 0x70, 0x6D, 0x65, 0x6E, 0x74, 0x31, 0x18,
        0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77,
        0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C,
        0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09,
        0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16,
        0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66,
        0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x30, 0x59, 0x30,
        0x13, 0x06, 0x07, 0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x02, 0x01,
        0x06, 0x08, 0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x03, 0x01, 0x07,
        0x03, 0x42, 0x00, 0x04, 0x02, 0xD3, 0xD9, 0x6E, 0xD6, 0x01,
        0x8E, 0x45, 0xC8, 0xB9, 0x90, 0x31, 0xE5, 0xC0, 0x4C, 0xE3,
        0x9E, 0xAD, 0x29, 0x38, 0x98, 0xBA, 0x10, 0xD6, 0xE9, 0x09,
        0x2A, 0x80, 0xA9, 0x2E, 0x17, 0x2A, 0xB9, 0x8A, 0xBF, 0x33,
        0x83, 0x46, 0xE3, 0x95, 0x0B, 0xE4, 0x77, 0x40, 0xB5, 0x3B,
        0x43, 0x45, 0x33, 0x0F, 0x61, 0x53, 0x7C, 0x37, 0x44, 0xC1,
        0xCB, 0xFC, 0x80, 0xCA, 0xE8, 0x43, 0xEA, 0xA7, 0xA3, 0x63,
        0x30, 0x61, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D, 0x0E, 0x04,
        0x16, 0x04, 0x14, 0x56, 0x8E, 0x9A, 0xC3, 0xF0, 0x42, 0xDE,
        0x18, 0xB9, 0x45, 0x55, 0x6E, 0xF9, 0x93, 0xCF, 0xEA, 0xC3,
        0xF3, 0xA5, 0x21, 0x30, 0x1F, 0x06, 0x03, 0x55, 0x1D, 0x23,
        0x04, 0x18, 0x30, 0x16, 0x80, 0x14, 0x56, 0x8E, 0x9A, 0xC3,
        0xF0, 0x42, 0xDE, 0x18, 0xB9, 0x45, 0x55, 0x6E, 0xF9, 0x93,
        0xCF, 0xEA, 0xC3, 0xF3, 0xA5, 0x21, 0x30, 0x0F, 0x06, 0x03,
        0x55, 0x1D, 0x13, 0x01, 0x01, 0xFF, 0x04, 0x05, 0x30, 0x03,
        0x01, 0x01, 0xFF, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x1D, 0x0F,
        0x01, 0x01, 0xFF, 0x04, 0x04, 0x03, 0x02, 0x01, 0x86, 0x30,
        0x0A, 0x06, 0x08, 0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x04, 0x03,
        0x02, 0x03, 0x48, 0x00, 0x30, 0x45, 0x02, 0x21, 0x00, 0x88,
        0xCC, 0x7F, 0x00, 0xF5, 0xA9, 0x4E, 0xC0, 0x69, 0x6E, 0x36,
        0x39, 0x24, 0x8F, 0x83, 0x45, 0x4D, 0xFA, 0xD0, 0x39, 0x14,
        0xB8, 0xC8, 0x7F, 0x95, 0x51, 0xF2, 0xC5, 0x98, 0xC0, 0xB7,
        0xE2, 0x02, 0x20, 0x2A, 0x93, 0x61, 0xB0, 0x06, 0xDE, 0xEB,
        0xDA, 0xFD, 0xAF, 0x6B, 0x39, 0xBF, 0x88, 0x17, 0xF1, 0xBA,
        0x2A, 0x7D, 0x59, 0xA8, 0xDE, 0xE7, 0x0A, 0x11, 0x83, 0x4F,
        0x92, 0x77, 0x8D, 0x92, 0x3B
};
static const int sizeof_ca_ecc_cert_der_256 = sizeof(ca_ecc_cert_der_256);

/* ./certs/ca-ecc384-key.der, ECC */
static const unsigned char ca_ecc_key_der_384[] =
{
        0x30, 0x81, 0xA4, 0x02, 0x01, 0x01, 0x04, 0x30, 0x7B, 0x16,
        0xE3, 0xD6, 0xD2, 0x81, 0x94, 0x6C, 0x8A, 0xDD, 0xA8, 0x78,
        0xEE, 0xC7, 0x7E, 0xB3, 0xC5, 0xD1, 0xDB, 0x2E, 0xF3, 0xED,
        0x0E, 0x48, 0x85, 0xB1, 0xF2, 0xE1, 0x7A, 0x39, 0x56, 0xC0,
        0xF1, 0x62, 0x12, 0x0F, 0x35, 0xB7, 0x39, 0xBC, 0x9C, 0x25,
        0xC0, 0x76, 0xEB, 0xFE, 0x55, 0x70, 0xA0, 0x07, 0x06, 0x05,
        0x2B, 0x81, 0x04, 0x00, 0x22, 0xA1, 0x64, 0x03, 0x62, 0x00,
        0x04, 0xEE, 0x82, 0xD4, 0x39, 0x9A, 0xB1, 0x27, 0x82, 0xF4,
        0xD7, 0xEA, 0xC6, 0xBC, 0x03, 0x1D, 0x4D, 0x83, 0x61, 0xF4,
        0x03, 0xAE, 0x7E, 0xBD, 0xD8, 0x5A, 0xA5, 0xB9, 0xF0, 0x8E,
        0xA2, 0xA5, 0xDA, 0xCE, 0x87, 0x3B, 0x5A, 0xAB, 0x44, 0x16,
        0x9C, 0xF5, 0x9F, 0x62, 0xDD, 0xF6, 0x20, 0xCD, 0x9C, 0x76,
        0x3C, 0x40, 0xB1, 0x3F, 0x97, 0x17, 0xDF, 0x59, 0xF6, 0xCD,
        0xDE, 0xCD, 0x46, 0x35, 0xC0, 0xED, 0x5E, 0x2E, 0x48, 0xB6,
        0x66, 0x91, 0x71, 0x74, 0xB7, 0x0C, 0x3F, 0xB9, 0x9A, 0xB7,
        0x83, 0xBD, 0x93, 0x3F, 0x5F, 0x50, 0x2D, 0x70, 0x3F, 0xDE,
        0x35, 0x25, 0xE1, 0x90, 0x3B, 0x86, 0xE0
};
static const int sizeof_ca_ecc_key_der_384 = sizeof(ca_ecc_key_der_384);

/* ./certs/ca-ecc384-cert.der, ECC */
static const unsigned char ca_ecc_cert_der_384[] =
{
        0x30, 0x82, 0x02, 0xD2, 0x30, 0x82, 0x02, 0x58, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x14, 0x4E, 0x08, 0x67, 0x9D, 0x29,
        0x61, 0x47, 0x3E, 0x2A, 0x23, 0x82, 0xCD, 0xCF, 0xCB, 0x53,
        0x2A, 0xB8, 0x02, 0x22, 0x57, 0x30, 0x0A, 0x06, 0x08, 0x2A,
        0x86, 0x48, 0xCE, 0x3D, 0x04, 0x03, 0x03, 0x30, 0x81, 0x97,
        0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13,
        0x02, 0x55, 0x53, 0x31, 0x13, 0x30, 0x11, 0x06, 0x03, 0x55,
        0x04, 0x08, 0x0C, 0x0A, 0x57, 0x61, 0x73, 0x68, 0x69, 0x6E,
        0x67, 0x74, 0x6F, 0x6E, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03,
        0x55, 0x04, 0x07, 0x0C, 0x07, 0x53, 0x65, 0x61, 0x74, 0x74,
        0x6C, 0x65, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04,
        0x0A, 0x0C, 0x07, 0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C,
        0x31, 0x14, 0x30, 0x12, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C,
        0x0B, 0x44, 0x65, 0x76, 0x65, 0x6C, 0x6F, 0x70, 0x6D, 0x65,
        0x6E, 0x74, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04,
        0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F,
        0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D,
        0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x30, 0x1E, 0x17, 0x0D, 0x32, 0x34, 0x31, 0x32, 0x31,
        0x38, 0x32, 0x31, 0x32, 0x35, 0x32, 0x39, 0x5A, 0x17, 0x0D,
        0x32, 0x37, 0x30, 0x39, 0x31, 0x34, 0x32, 0x31, 0x32, 0x35,
        0x32, 0x39, 0x5A, 0x30, 0x81, 0x97, 0x31, 0x0B, 0x30, 0x09,
        0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31,
        0x13, 0x30, 0x11, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x0A,
        0x57, 0x61, 0x73, 0x68, 0x69, 0x6E, 0x67, 0x74, 0x6F, 0x6E,
        0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C,
        0x07, 0x53, 0x65, 0x61, 0x74, 0x74, 0x6C, 0x65, 0x31, 0x10,
        0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x07, 0x77,
        0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C, 0x31, 0x14, 0x30, 0x12,
        0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x0B, 0x44, 0x65, 0x76,
        0x65, 0x6C, 0x6F, 0x70, 0x6D, 0x65, 0x6E, 0x74, 0x31, 0x18,
        0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77,
        0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C,
        0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09,
        0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16,
        0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66,
        0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x30, 0x76, 0x30,
        0x10, 0x06, 0x07, 0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x02, 0x01,
        0x06, 0x05, 0x2B, 0x81, 0x04, 0x00, 0x22, 0x03, 0x62, 0x00,
        0x04, 0xEE, 0x82, 0xD4, 0x39, 0x9A, 0xB1, 0x27, 0x82, 0xF4,
        0xD7, 0xEA, 0xC6, 0xBC, 0x03, 0x1D, 0x4D, 0x83, 0x61, 0xF4,
        0x03, 0xAE, 0x7E, 0xBD, 0xD8, 0x5A, 0xA5, 0xB9, 0xF0, 0x8E,
        0xA2, 0xA5, 0xDA, 0xCE, 0x87, 0x3B, 0x5A, 0xAB, 0x44, 0x16,
        0x9C, 0xF5, 0x9F, 0x62, 0xDD, 0xF6, 0x20, 0xCD, 0x9C, 0x76,
        0x3C, 0x40, 0xB1, 0x3F, 0x97, 0x17, 0xDF, 0x59, 0xF6, 0xCD,
        0xDE, 0xCD, 0x46, 0x35, 0xC0, 0xED, 0x5E, 0x2E, 0x48, 0xB6,
        0x66, 0x91, 0x71, 0x74, 0xB7, 0x0C, 0x3F, 0xB9, 0x9A, 0xB7,
        0x83, 0xBD, 0x93, 0x3F, 0x5F, 0x50, 0x2D, 0x70, 0x3F, 0xDE,
        0x35, 0x25, 0xE1, 0x90, 0x3B, 0x86, 0xE0, 0xA3, 0x63, 0x30,
        0x61, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D, 0x0E, 0x04, 0x16,
        0x04, 0x14, 0xAB, 0xE0, 0xC3, 0x26, 0x4C, 0x18, 0xD4, 0x72,
        0xBB, 0xD2, 0x84, 0x8C, 0x9C, 0x0A, 0x05, 0x92, 0x80, 0x12,
        0x53, 0x52, 0x30, 0x1F, 0x06, 0x03, 0x55, 0x1D, 0x23, 0x04,
        0x18, 0x30, 0x16, 0x80, 0x14, 0xAB, 0xE0, 0xC3, 0x26, 0x4C,
        0x18, 0xD4, 0x72, 0xBB, 0xD2, 0x84, 0x8C, 0x9C, 0x0A, 0x05,
        0x92, 0x80, 0x12, 0x53, 0x52, 0x30, 0x0F, 0x06, 0x03, 0x55,
        0x1D, 0x13, 0x01, 0x01, 0xFF, 0x04, 0x05, 0x30, 0x03, 0x01,
        0x01, 0xFF, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x1D, 0x0F, 0x01,
        0x01, 0xFF, 0x04, 0x04, 0x03, 0x02, 0x01, 0x86, 0x30, 0x0A,
        0x06, 0x08, 0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x04, 0x03, 0x03,
        0x03, 0x68, 0x00, 0x30, 0x65, 0x02, 0x30, 0x1D, 0x3F, 0x92,
        0x02, 0xB2, 0x46, 0x54, 0xEE, 0x9E, 0x0D, 0x90, 0x03, 0x73,
        0x6A, 0xAB, 0x04, 0x5A, 0x41, 0xFE, 0xF4, 0x1B, 0xFD, 0xD6,
        0x99, 0xCC, 0x7A, 0x6C, 0xFD, 0x52, 0xDA, 0x2E, 0x4E, 0x78,
        0xFE, 0xEF, 0x79, 0x74, 0x12, 0x5E, 0x04, 0x9D, 0x2C, 0xE4,
        0xE7, 0x1A, 0x4D, 0xD3, 0x1E, 0x02, 0x31, 0x00, 0xB7, 0x34,
        0xE8, 0x4C, 0x69, 0x70, 0xDB, 0xFD, 0x1A, 0x48, 0xC5, 0xDC,
        0x8E, 0xEF, 0x15, 0xCA, 0x13, 0xEE, 0xF8, 0x4F, 0x27, 0x5F,
        0xD2, 0x3A, 0x6A, 0x06, 0x7D, 0xF3, 0x32, 0xA7, 0x75, 0x97,
        0x27, 0x6D, 0x60, 0xED, 0xA2, 0x9F, 0x9F, 0x7E, 0x66, 0x43,
        0xF9, 0x15, 0x1D, 0x65, 0x5D, 0x49
};
static const int sizeof_ca_ecc_cert_der_384 = sizeof(ca_ecc_cert_der_384);

#endif /* HAVE_ECC && USE_CERT_BUFFERS_256 */

/* dh1024 p */
static const unsigned char dh_p[] =
{
    0xE6, 0x96, 0x9D, 0x3D, 0x49, 0x5B, 0xE3, 0x2C, 0x7C, 0xF1, 0x80, 0xC3,
    0xBD, 0xD4, 0x79, 0x8E, 0x91, 0xB7, 0x81, 0x82, 0x51, 0xBB, 0x05, 0x5E,
    0x2A, 0x20, 0x64, 0x90, 0x4A, 0x79, 0xA7, 0x70, 0xFA, 0x15, 0xA2, 0x59,
    0xCB, 0xD5, 0x23, 0xA6, 0xA6, 0xEF, 0x09, 0xC4, 0x30, 0x48, 0xD5, 0xA2,
    0x2F, 0x97, 0x1F, 0x3C, 0x20, 0x12, 0x9B, 0x48, 0x00, 0x0E, 0x6E, 0xDD,
    0x06, 0x1C, 0xBC, 0x05, 0x3E, 0x37, 0x1D, 0x79, 0x4E, 0x53, 0x27, 0xDF,
    0x61, 0x1E, 0xBB, 0xBE, 0x1B, 0xAC, 0x9B, 0x5C, 0x60, 0x44, 0xCF, 0x02,
    0x3D, 0x76, 0xE0, 0x5E, 0xEA, 0x9B, 0xAD, 0x99, 0x1B, 0x13, 0xA6, 0x3C,
    0x97, 0x4E, 0x9E, 0xF1, 0x83, 0x9E, 0xB5, 0xDB, 0x12, 0x51, 0x36, 0xF7,
    0x26, 0x2E, 0x56, 0xA8, 0x87, 0x15, 0x38, 0xDF, 0xD8, 0x23, 0xC6, 0x50,
    0x50, 0x85, 0xE2, 0x1F, 0x0D, 0xD5, 0xC8, 0x6B,
};

/* dh1024 g */
static const unsigned char dh_g[] =
{
  0x02,
};

#if defined(NO_ASN) && defined(WOLFSSL_SP_MATH)
/* dh2048 p */
static const unsigned char dh2048_p[] =
{
    0xb0, 0xa1, 0x08, 0x06, 0x9c, 0x08, 0x13, 0xba, 0x59, 0x06, 0x3c, 0xbc,
    0x30, 0xd5, 0xf5, 0x00, 0xc1, 0x4f, 0x44, 0xa7, 0xd6, 0xef, 0x4a, 0xc6,
    0x25, 0x27, 0x1c, 0xe8, 0xd2, 0x96, 0x53, 0x0a, 0x5c, 0x91, 0xdd, 0xa2,
    0xc2, 0x94, 0x84, 0xbf, 0x7d, 0xb2, 0x44, 0x9f, 0x9b, 0xd2, 0xc1, 0x8a,
    0xc5, 0xbe, 0x72, 0x5c, 0xa7, 0xe7, 0x91, 0xe6, 0xd4, 0x9f, 0x73, 0x07,
    0x85, 0x5b, 0x66, 0x48, 0xc7, 0x70, 0xfa, 0xb4, 0xee, 0x02, 0xc9, 0x3d,
    0x9a, 0x4a, 0xda, 0x3d, 0xc1, 0x46, 0x3e, 0x19, 0x69, 0xd1, 0x17, 0x46,
    0x07, 0xa3, 0x4d, 0x9f, 0x2b, 0x96, 0x17, 0x39, 0x6d, 0x30, 0x8d, 0x2a,
    0xf3, 0x94, 0xd3, 0x75, 0xcf, 0xa0, 0x75, 0xe6, 0xf2, 0x92, 0x1f, 0x1a,
    0x70, 0x05, 0xaa, 0x04, 0x83, 0x57, 0x30, 0xfb, 0xda, 0x76, 0x93, 0x38,
    0x50, 0xe8, 0x27, 0xfd, 0x63, 0xee, 0x3c, 0xe5, 0xb7, 0xc8, 0x09, 0xae,
    0x6f, 0x50, 0x35, 0x8e, 0x84, 0xce, 0x4a, 0x00, 0xe9, 0x12, 0x7e, 0x5a,
    0x31, 0xd7, 0x33, 0xfc, 0x21, 0x13, 0x76, 0xcc, 0x16, 0x30, 0xdb, 0x0c,
    0xfc, 0xc5, 0x62, 0xa7, 0x35, 0xb8, 0xef, 0xb7, 0xb0, 0xac, 0xc0, 0x36,
    0xf6, 0xd9, 0xc9, 0x46, 0x48, 0xf9, 0x40, 0x90, 0x00, 0x2b, 0x1b, 0xaa,
    0x6c, 0xe3, 0x1a, 0xc3, 0x0b, 0x03, 0x9e, 0x1b, 0xc2, 0x46, 0xe4, 0x48,
    0x4e, 0x22, 0x73, 0x6f, 0xc3, 0x5f, 0xd4, 0x9a, 0xd6, 0x30, 0x07, 0x48,
    0xd6, 0x8c, 0x90, 0xab, 0xd4, 0xf6, 0xf1, 0xe3, 0x48, 0xd3, 0x58, 0x4b,
    0xa6, 0xb9, 0xcd, 0x29, 0xbf, 0x68, 0x1f, 0x08, 0x4b, 0x63, 0x86, 0x2f,
    0x5c, 0x6b, 0xd6, 0xb6, 0x06, 0x65, 0xf7, 0xa6, 0xdc, 0x00, 0x67, 0x6b,
    0xbb, 0xc3, 0xa9, 0x41, 0x83, 0xfb, 0xc7, 0xfa, 0xc8, 0xe2, 0x1e, 0x7e,
    0xaf, 0x00, 0x3f, 0x93
};

/* dh2048 g */
static const unsigned char dh2048_g[] =
{
  0x02,
};
#endif

#if defined(HAVE_ED25519)

/* ./certs/ed25519/server-ed25519.der, ED25519 */
static const unsigned char server_ed25519_cert[] =
{
        0x30, 0x82, 0x02, 0xA7, 0x30, 0x82, 0x02, 0x59, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x01, 0x01, 0x30, 0x05, 0x06, 0x03,
        0x2B, 0x65, 0x70, 0x30, 0x81, 0xB4, 0x31, 0x0B, 0x30, 0x09,
        0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31,
        0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07,
        0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30,
        0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F,
        0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x18, 0x30, 0x16, 0x06,
        0x03, 0x55, 0x04, 0x0A, 0x0C, 0x0F, 0x77, 0x6F, 0x6C, 0x66,
        0x53, 0x53, 0x4C, 0x5F, 0x65, 0x64, 0x32, 0x35, 0x35, 0x31,
        0x39, 0x31, 0x13, 0x30, 0x11, 0x06, 0x03, 0x55, 0x04, 0x0B,
        0x0C, 0x0A, 0x43, 0x41, 0x2D, 0x65, 0x64, 0x32, 0x35, 0x35,
        0x31, 0x39, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04,
        0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C,
        0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F,
        0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D,
        0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x31, 0x17, 0x30, 0x15, 0x06, 0x0A, 0x09, 0x92, 0x26,
        0x89, 0x93, 0xF2, 0x2C, 0x64, 0x01, 0x01, 0x0C, 0x07, 0x77,
        0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C, 0x30, 0x1E, 0x17, 0x0D,
        0x32, 0x34, 0x31, 0x32, 0x31, 0x38, 0x32, 0x31, 0x32, 0x35,
        0x33, 0x30, 0x5A, 0x17, 0x0D, 0x32, 0x37, 0x30, 0x39, 0x31,
        0x34, 0x32, 0x31, 0x32, 0x35, 0x33, 0x30, 0x5A, 0x30, 0x81,
        0xB8, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06,
        0x13, 0x02, 0x55, 0x53, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03,
        0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F, 0x6E, 0x74, 0x61,
        0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04,
        0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65, 0x6D, 0x61, 0x6E,
        0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C,
        0x0F, 0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C, 0x5F, 0x65,
        0x64, 0x32, 0x35, 0x35, 0x31, 0x39, 0x31, 0x17, 0x30, 0x15,
        0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x0E, 0x53, 0x65, 0x72,
        0x76, 0x65, 0x72, 0x2D, 0x65, 0x64, 0x32, 0x35, 0x35, 0x31,
        0x39, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55, 0x04, 0x03,
        0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77, 0x6F, 0x6C, 0x66,
        0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x1F, 0x30,
        0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01,
        0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66, 0x6F, 0x40, 0x77,
        0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D,
        0x31, 0x17, 0x30, 0x15, 0x06, 0x0A, 0x09, 0x92, 0x26, 0x89,
        0x93, 0xF2, 0x2C, 0x64, 0x01, 0x01, 0x0C, 0x07, 0x77, 0x6F,
        0x6C, 0x66, 0x53, 0x53, 0x4C, 0x30, 0x2A, 0x30, 0x05, 0x06,
        0x03, 0x2B, 0x65, 0x70, 0x03, 0x21, 0x00, 0x23, 0xAA, 0x4D,
        0x60, 0x50, 0xE0, 0x13, 0xD3, 0x3A, 0xED, 0xAB, 0xF6, 0xA9,
        0xCC, 0x4A, 0xFE, 0xD7, 0x4D, 0x2F, 0xD2, 0x5B, 0x1A, 0x10,
        0x05, 0xEF, 0x5A, 0x41, 0x25, 0xCE, 0x1B, 0x53, 0x78, 0xA3,
        0x81, 0x89, 0x30, 0x81, 0x86, 0x30, 0x1D, 0x06, 0x03, 0x55,
        0x1D, 0x0E, 0x04, 0x16, 0x04, 0x14, 0xA3, 0x29, 0x81, 0xE7,
        0x90, 0x6F, 0xB9, 0x60, 0xF8, 0xAF, 0xCC, 0x15, 0x7A, 0xAE,
        0xD7, 0xA1, 0xF4, 0xB4, 0x86, 0xBA, 0x30, 0x1F, 0x06, 0x03,
        0x55, 0x1D, 0x23, 0x04, 0x18, 0x30, 0x16, 0x80, 0x14, 0x74,
        0xD5, 0x38, 0x19, 0x5E, 0x83, 0xB9, 0x03, 0xF8, 0x01, 0x8A,
        0x35, 0x35, 0xBB, 0x89, 0x4C, 0x49, 0xB4, 0x23, 0xE9, 0x30,
        0x0C, 0x06, 0x03, 0x55, 0x1D, 0x13, 0x01, 0x01, 0xFF, 0x04,
        0x02, 0x30, 0x00, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x1D, 0x0F,
        0x01, 0x01, 0xFF, 0x04, 0x04, 0x03, 0x02, 0x03, 0xA8, 0x30,
        0x13, 0x06, 0x03, 0x55, 0x1D, 0x25, 0x04, 0x0C, 0x30, 0x0A,
        0x06, 0x08, 0x2B, 0x06, 0x01, 0x05, 0x05, 0x07, 0x03, 0x01,
        0x30, 0x11, 0x06, 0x09, 0x60, 0x86, 0x48, 0x01, 0x86, 0xF8,
        0x42, 0x01, 0x01, 0x04, 0x04, 0x03, 0x02, 0x06, 0x40, 0x30,
        0x05, 0x06, 0x03, 0x2B, 0x65, 0x70, 0x03, 0x41, 0x00, 0x04,
        0x19, 0x32, 0xE4, 0x24, 0xE5, 0xDF, 0x5A, 0xA4, 0x19, 0xC4,
        0x31, 0x15, 0x81, 0x05, 0x4C, 0x45, 0x0A, 0x40, 0x4A, 0x5D,
        0x6A, 0x8B, 0x0A, 0x77, 0x02, 0xFE, 0x48, 0x82, 0xD2, 0x83,
        0x8D, 0xDE, 0x42, 0xB8, 0xCF, 0x02, 0xDC, 0x64, 0x2C, 0xBD,
        0x8C, 0x9D, 0x22, 0x16, 0xD8, 0x7A, 0x23, 0x65, 0x5D, 0xB0,
        0x25, 0x92, 0xAC, 0xA8, 0x6C, 0xDE, 0xDF, 0x1D, 0xEB, 0x64,
        0xE4, 0x8A, 0x06
};
static const int sizeof_server_ed25519_cert = sizeof(server_ed25519_cert);

/* ./certs/ed25519/server-ed25519-key.der, ED25519 */
static const unsigned char server_ed25519_key[] =
{
        0x30, 0x2A, 0x30, 0x05, 0x06, 0x03, 0x2B, 0x65, 0x70, 0x03,
        0x21, 0x00, 0x23, 0xAA, 0x4D, 0x60, 0x50, 0xE0, 0x13, 0xD3,
        0x3A, 0xED, 0xAB, 0xF6, 0xA9, 0xCC, 0x4A, 0xFE, 0xD7, 0x4D,
        0x2F, 0xD2, 0x5B, 0x1A, 0x10, 0x05, 0xEF, 0x5A, 0x41, 0x25,
        0xCE, 0x1B, 0x53, 0x78
};
static const int sizeof_server_ed25519_key = sizeof(server_ed25519_key);

/* ./certs/ed25519/ca-ed25519.der, ED25519 */
static const unsigned char ca_ed25519_cert[] =
{
        0x30, 0x82, 0x02, 0x65, 0x30, 0x82, 0x02, 0x17, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x01, 0x01, 0x30, 0x05, 0x06, 0x03,
        0x2B, 0x65, 0x70, 0x30, 0x81, 0x9D, 0x31, 0x0B, 0x30, 0x09,
        0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31,
        0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07,
        0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30,
        0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F,
        0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x18, 0x30, 0x16, 0x06,
        0x03, 0x55, 0x04, 0x0A, 0x0C, 0x0F, 0x77, 0x6F, 0x6C, 0x66,
        0x53, 0x53, 0x4C, 0x5F, 0x45, 0x64, 0x32, 0x35, 0x35, 0x31,
        0x39, 0x31, 0x15, 0x30, 0x13, 0x06, 0x03, 0x55, 0x04, 0x0B,
        0x0C, 0x0C, 0x52, 0x6F, 0x6F, 0x74, 0x2D, 0x45, 0x64, 0x32,
        0x35, 0x35, 0x31, 0x39, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03,
        0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77,
        0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D,
        0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86,
        0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66,
        0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E,
        0x63, 0x6F, 0x6D, 0x30, 0x1E, 0x17, 0x0D, 0x32, 0x34, 0x31,
        0x32, 0x31, 0x38, 0x32, 0x31, 0x32, 0x35, 0x33, 0x30, 0x5A,
        0x17, 0x0D, 0x32, 0x37, 0x30, 0x39, 0x31, 0x34, 0x32, 0x31,
        0x32, 0x35, 0x33, 0x30, 0x5A, 0x30, 0x81, 0xB4, 0x31, 0x0B,
        0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55,
        0x53, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08,
        0x0C, 0x07, 0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31,
        0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07,
        0x42, 0x6F, 0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x18, 0x30,
        0x16, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x0C, 0x0F, 0x77, 0x6F,
        0x6C, 0x66, 0x53, 0x53, 0x4C, 0x5F, 0x65, 0x64, 0x32, 0x35,
        0x35, 0x31, 0x39, 0x31, 0x13, 0x30, 0x11, 0x06, 0x03, 0x55,
        0x04, 0x0B, 0x0C, 0x0A, 0x43, 0x41, 0x2D, 0x65, 0x64, 0x32,
        0x35, 0x35, 0x31, 0x39, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03,
        0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77,
        0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D,
        0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86,
        0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66,
        0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E,
        0x63, 0x6F, 0x6D, 0x31, 0x17, 0x30, 0x15, 0x06, 0x0A, 0x09,
        0x92, 0x26, 0x89, 0x93, 0xF2, 0x2C, 0x64, 0x01, 0x01, 0x0C,
        0x07, 0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C, 0x30, 0x2A,
        0x30, 0x05, 0x06, 0x03, 0x2B, 0x65, 0x70, 0x03, 0x21, 0x00,
        0x42, 0x3B, 0x7A, 0xF9, 0x82, 0xCF, 0xF9, 0xDF, 0x19, 0xDD,
        0xF3, 0xF0, 0x32, 0x29, 0x6D, 0xFA, 0xFD, 0x76, 0x4F, 0x68,
        0xC2, 0xC2, 0xE0, 0x6C, 0x47, 0xAE, 0xC2, 0x55, 0x68, 0xAC,
        0x0D, 0x4D, 0xA3, 0x63, 0x30, 0x61, 0x30, 0x1D, 0x06, 0x03,
        0x55, 0x1D, 0x0E, 0x04, 0x16, 0x04, 0x14, 0x74, 0xD5, 0x38,
        0x19, 0x5E, 0x83, 0xB9, 0x03, 0xF8, 0x01, 0x8A, 0x35, 0x35,
        0xBB, 0x89, 0x4C, 0x49, 0xB4, 0x23, 0xE9, 0x30, 0x1F, 0x06,
        0x03, 0x55, 0x1D, 0x23, 0x04, 0x18, 0x30, 0x16, 0x80, 0x14,
        0xFA, 0xBA, 0x5B, 0x76, 0x1D, 0xF1, 0x1D, 0x1D, 0x4D, 0x74,
        0x48, 0xD8, 0x98, 0x3B, 0x56, 0xEF, 0xB3, 0x14, 0xF3, 0xDE,
        0x30, 0x0F, 0x06, 0x03, 0x55, 0x1D, 0x13, 0x01, 0x01, 0xFF,
        0x04, 0x05, 0x30, 0x03, 0x01, 0x01, 0xFF, 0x30, 0x0E, 0x06,
        0x03, 0x55, 0x1D, 0x0F, 0x01, 0x01, 0xFF, 0x04, 0x04, 0x03,
        0x02, 0x01, 0x86, 0x30, 0x05, 0x06, 0x03, 0x2B, 0x65, 0x70,
        0x03, 0x41, 0x00, 0x44, 0xEB, 0x38, 0xC6, 0x27, 0xD4, 0x70,
        0x42, 0x3F, 0x9B, 0xA0, 0xD7, 0x90, 0x96, 0xD6, 0x6E, 0x42,
        0x38, 0x5B, 0x38, 0x38, 0x9F, 0x21, 0xCA, 0xB0, 0xFA, 0x5E,
        0x7C, 0x17, 0xB4, 0x32, 0x5C, 0xB3, 0x08, 0xA2, 0x65, 0x50,
        0xD7, 0x65, 0x6B, 0xF8, 0xA9, 0xEF, 0x0D, 0xD1, 0x54, 0x2D,
        0x4D, 0xB6, 0x0F, 0x42, 0x9E, 0x51, 0xF7, 0xDB, 0xA7, 0xBF,
        0x16, 0x23, 0xC4, 0xBD, 0x7D, 0xC9, 0x03
};
static const int sizeof_ca_ed25519_cert = sizeof(ca_ed25519_cert);

/* ./certs/ed25519/client-ed25519.der, ED25519 */
static const unsigned char client_ed25519_cert[] =
{
        0x30, 0x82, 0x03, 0x9F, 0x30, 0x82, 0x03, 0x51, 0xA0, 0x03,
        0x02, 0x01, 0x02, 0x02, 0x14, 0x33, 0x8B, 0x57, 0xD5, 0x8E,
        0x84, 0x67, 0x6A, 0xE1, 0xED, 0xF2, 0xB9, 0x11, 0x16, 0x5E,
        0x12, 0xE5, 0x0C, 0x78, 0x8A, 0x30, 0x05, 0x06, 0x03, 0x2B,
        0x65, 0x70, 0x30, 0x81, 0xB8, 0x31, 0x0B, 0x30, 0x09, 0x06,
        0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x10,
        0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D,
        0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E,
        0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A,
        0x65, 0x6D, 0x61, 0x6E, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03,
        0x55, 0x04, 0x0A, 0x0C, 0x0F, 0x77, 0x6F, 0x6C, 0x66, 0x53,
        0x53, 0x4C, 0x5F, 0x65, 0x64, 0x32, 0x35, 0x35, 0x31, 0x39,
        0x31, 0x17, 0x30, 0x15, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C,
        0x0E, 0x43, 0x6C, 0x69, 0x65, 0x6E, 0x74, 0x2D, 0x65, 0x64,
        0x32, 0x35, 0x35, 0x31, 0x39, 0x31, 0x18, 0x30, 0x16, 0x06,
        0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E,
        0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F,
        0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48,
        0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E,
        0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C,
        0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x17, 0x30, 0x15, 0x06, 0x0A,
        0x09, 0x92, 0x26, 0x89, 0x93, 0xF2, 0x2C, 0x64, 0x01, 0x01,
        0x0C, 0x07, 0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C, 0x30,
        0x1E, 0x17, 0x0D, 0x32, 0x34, 0x31, 0x32, 0x31, 0x38, 0x32,
        0x31, 0x32, 0x35, 0x33, 0x30, 0x5A, 0x17, 0x0D, 0x32, 0x37,
        0x30, 0x39, 0x31, 0x34, 0x32, 0x31, 0x32, 0x35, 0x33, 0x30,
        0x5A, 0x30, 0x81, 0xB8, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03,
        0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31, 0x10, 0x30,
        0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07, 0x4D, 0x6F,
        0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30, 0x0E, 0x06,
        0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F, 0x7A, 0x65,
        0x6D, 0x61, 0x6E, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03, 0x55,
        0x04, 0x0A, 0x0C, 0x0F, 0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53,
        0x4C, 0x5F, 0x65, 0x64, 0x32, 0x35, 0x35, 0x31, 0x39, 0x31,
        0x17, 0x30, 0x15, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x0C, 0x0E,
        0x43, 0x6C, 0x69, 0x65, 0x6E, 0x74, 0x2D, 0x65, 0x64, 0x32,
        0x35, 0x35, 0x31, 0x39, 0x31, 0x18, 0x30, 0x16, 0x06, 0x03,
        0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77, 0x2E, 0x77,
        0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63, 0x6F, 0x6D,
        0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86,
        0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69, 0x6E, 0x66,
        0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E,
        0x63, 0x6F, 0x6D, 0x31, 0x17, 0x30, 0x15, 0x06, 0x0A, 0x09,
        0x92, 0x26, 0x89, 0x93, 0xF2, 0x2C, 0x64, 0x01, 0x01, 0x0C,
        0x07, 0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C, 0x30, 0x2A,
        0x30, 0x05, 0x06, 0x03, 0x2B, 0x65, 0x70, 0x03, 0x21, 0x00,
        0xE6, 0x57, 0x5B, 0x13, 0x1B, 0xC7, 0x51, 0x14, 0x6B, 0xED,
        0x3B, 0xF5, 0xD1, 0xFA, 0xAB, 0x9E, 0x6C, 0xB6, 0xEB, 0x02,
        0x09, 0xA3, 0x99, 0xF5, 0x6E, 0xBF, 0x9D, 0x3C, 0xFE, 0x54,
        0x39, 0xE6, 0xA3, 0x82, 0x01, 0x69, 0x30, 0x82, 0x01, 0x65,
        0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D, 0x0E, 0x04, 0x16, 0x04,
        0x14, 0xFE, 0x41, 0x5E, 0x3E, 0x81, 0xE2, 0x2E, 0x46, 0xB3,
        0x3E, 0x47, 0x89, 0x90, 0xD4, 0xC2, 0xB4, 0x8E, 0x11, 0xD6,
        0x8A, 0x30, 0x81, 0xF8, 0x06, 0x03, 0x55, 0x1D, 0x23, 0x04,
        0x81, 0xF0, 0x30, 0x81, 0xED, 0x80, 0x14, 0xFE, 0x41, 0x5E,
        0x3E, 0x81, 0xE2, 0x2E, 0x46, 0xB3, 0x3E, 0x47, 0x89, 0x90,
        0xD4, 0xC2, 0xB4, 0x8E, 0x11, 0xD6, 0x8A, 0xA1, 0x81, 0xBE,
        0xA4, 0x81, 0xBB, 0x30, 0x81, 0xB8, 0x31, 0x0B, 0x30, 0x09,
        0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x55, 0x53, 0x31,
        0x10, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x08, 0x0C, 0x07,
        0x4D, 0x6F, 0x6E, 0x74, 0x61, 0x6E, 0x61, 0x31, 0x10, 0x30,
        0x0E, 0x06, 0x03, 0x55, 0x04, 0x07, 0x0C, 0x07, 0x42, 0x6F,
        0x7A, 0x65, 0x6D, 0x61, 0x6E, 0x31, 0x18, 0x30, 0x16, 0x06,
        0x03, 0x55, 0x04, 0x0A, 0x0C, 0x0F, 0x77, 0x6F, 0x6C, 0x66,
        0x53, 0x53, 0x4C, 0x5F, 0x65, 0x64, 0x32, 0x35, 0x35, 0x31,
        0x39, 0x31, 0x17, 0x30, 0x15, 0x06, 0x03, 0x55, 0x04, 0x0B,
        0x0C, 0x0E, 0x43, 0x6C, 0x69, 0x65, 0x6E, 0x74, 0x2D, 0x65,
        0x64, 0x32, 0x35, 0x35, 0x31, 0x39, 0x31, 0x18, 0x30, 0x16,
        0x06, 0x03, 0x55, 0x04, 0x03, 0x0C, 0x0F, 0x77, 0x77, 0x77,
        0x2E, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73, 0x6C, 0x2E, 0x63,
        0x6F, 0x6D, 0x31, 0x1F, 0x30, 0x1D, 0x06, 0x09, 0x2A, 0x86,
        0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x10, 0x69,
        0x6E, 0x66, 0x6F, 0x40, 0x77, 0x6F, 0x6C, 0x66, 0x73, 0x73,
        0x6C, 0x2E, 0x63, 0x6F, 0x6D, 0x31, 0x17, 0x30, 0x15, 0x06,
        0x0A, 0x09, 0x92, 0x26, 0x89, 0x93, 0xF2, 0x2C, 0x64, 0x01,
        0x01, 0x0C, 0x07, 0x77, 0x6F, 0x6C, 0x66, 0x53, 0x53, 0x4C,
        0x82, 0x14, 0x33, 0x8B, 0x57, 0xD5, 0x8E, 0x84, 0x67, 0x6A,
        0xE1, 0xED, 0xF2, 0xB9, 0x11, 0x16, 0x5E, 0x12, 0xE5, 0x0C,
        0x78, 0x8A, 0x30, 0x0C, 0x06, 0x03, 0x55, 0x1D, 0x13, 0x04,
        0x05, 0x30, 0x03, 0x01, 0x01, 0xFF, 0x30, 0x1C, 0x06, 0x03,
        0x55, 0x1D, 0x11, 0x04, 0x15, 0x30, 0x13, 0x82, 0x0B, 0x65,
        0x78, 0x61, 0x6D, 0x70, 0x6C, 0x65, 0x2E, 0x63, 0x6F, 0x6D,
        0x87, 0x04, 0x7F, 0x00, 0x00, 0x01, 0x30, 0x1D, 0x06, 0x03,
        0x55, 0x1D, 0x25, 0x04, 0x16, 0x30, 0x14, 0x06, 0x08, 0x2B,
        0x06, 0x01, 0x05, 0x05, 0x07, 0x03, 0x01, 0x06, 0x08, 0x2B,
        0x06, 0x01, 0x05, 0x05, 0x07, 0x03, 0x02, 0x30, 0x05, 0x06,
        0x03, 0x2B, 0x65, 0x70, 0x03, 0x41, 0x00, 0xAA, 0xC7, 0xBD,
        0x8E, 0x56, 0x40, 0xAB, 0x7D, 0x9C, 0x55, 0xF0, 0x4D, 0x1D,
        0x97, 0xE9, 0x03, 0x62, 0x11, 0xCA, 0x51, 0xAD, 0x80, 0xCF,
        0x1A, 0x2C, 0x2C, 0x5B, 0x2D, 0x71, 0xFE, 0xDB, 0x1D, 0x4B,
        0xCD, 0x4B, 0x8B, 0x2D, 0x12, 0xF7, 0x01, 0xEE, 0xFB, 0x7D,
        0x2E, 0x21, 0xFC, 0x81, 0xDE, 0x84, 0x59, 0xC8, 0xA5, 0x1E,
        0x92, 0xE3, 0x21, 0x58, 0xD1, 0x3E, 0x8A, 0x71, 0x91, 0x2D,
        0x0E
};
static const int sizeof_client_ed25519_cert = sizeof(client_ed25519_cert);

/* ./certs/ed25519/client-ed25519-key.der, ED25519 */
static const unsigned char client_ed25519_key[] =
{
        0x30, 0x2A, 0x30, 0x05, 0x06, 0x03, 0x2B, 0x65, 0x70, 0x03,
        0x21, 0x00, 0xE6, 0x57, 0x5B, 0x13, 0x1B, 0xC7, 0x51, 0x14,
        0x6B, 0xED, 0x3B, 0xF5, 0xD1, 0xFA, 0xAB, 0x9E, 0x6C, 0xB6,
        0xEB, 0x02, 0x09, 0xA3, 0x99, 0xF5, 0x6E, 0xBF, 0x9D, 0x3C,
        0xFE, 0x54, 0x39, 0xE6
};
static const int sizeof_client_ed25519_key = sizeof(client_ed25519_key);

#endif /* HAVE_ED25519 */

#if defined(USE_CERT_BUFFERS_25519)

/* ./certs/statickeys/x25519.der, CURVE25519 */
static const unsigned char x25519_statickey_der[] =
{
        0x30, 0x2E, 0x02, 0x01, 0x00, 0x30, 0x05, 0x06, 0x03, 0x2B,
        0x65, 0x6E, 0x04, 0x22, 0x04, 0x20, 0x78, 0x8E, 0x31, 0x5C,
        0x33, 0xA9, 0x19, 0xC0, 0x5E, 0x36, 0x70, 0x1B, 0xA4, 0xE8,
        0xEF, 0xC1, 0x89, 0x8C, 0xB3, 0x15, 0xC6, 0x79, 0xD3, 0xAC,
        0x22, 0x00, 0xAE, 0xFA, 0xB3, 0xB7, 0x0F, 0x78
};
static const int sizeof_x25519_statickey_der = sizeof(x25519_statickey_der);

/* ./certs/statickeys/x25519-pub.der, CURVE25519 */
static const unsigned char x25519_pub_statickey_der[] =
{
        0x30, 0x2A, 0x30, 0x05, 0x06, 0x03, 0x2B, 0x65, 0x6E, 0x03,
        0x21, 0x00, 0x09, 0xBC, 0x8C, 0xC7, 0x45, 0x0D, 0xC1, 0xC2,
        0x02, 0x57, 0x9A, 0x68, 0x3A, 0xFD, 0x7A, 0xA8, 0xA5, 0x2F,
        0xF0, 0x99, 0x39, 0x98, 0xEA, 0x26, 0xA2, 0x5B, 0x38, 0xFD,
        0x96, 0xDB, 0x2A, 0x26
};
static const int sizeof_x25519_pub_statickey_der = sizeof(x25519_pub_statickey_der);

#endif /* USE_CERT_BUFFERS_25519 */

#endif /* WOLFSSL_CERTS_TEST_H */

