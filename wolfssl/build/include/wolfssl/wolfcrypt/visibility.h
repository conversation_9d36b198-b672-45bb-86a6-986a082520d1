/* visibility.h
 *
 * Copyright (C) 2006-2025 wolfSSL Inc.
 *
 * This file is part of wolfSSL.
 *
 * wolfSSL is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * wolfSSL is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1335, USA
 */


/* Visibility control macros */

#ifndef WOLF_CRYPT_VISIBILITY_H
#define WOLF_CRYPT_VISIBILITY_H

/* WOLFSSL_API is used for the public API symbols.
        It either imports or exports (or does nothing for static builds)

   WOLFSSL_LOCAL is used for non-API symbols (private).
*/

#if defined(BUILDING_WOLFSSL)
    #if defined(_MSC_VER) || defined(__MINGW32__) || defined(__CYGWIN__) || \
        defined(_WIN32_WCE) || defined(__WATCOMC__)
        #if defined(WOLFSSL_DLL)
            #define WOLFSSL_API __declspec(dllexport)
        #else
            #define WOLFSSL_API
        #endif
        #define WOLFSSL_LOCAL
    #elif defined(HAVE_VISIBILITY) && HAVE_VISIBILITY
        #define WOLFSSL_API   __attribute__ ((visibility("default")))
        #define WOLFSSL_LOCAL __attribute__ ((visibility("hidden")))
    #elif defined(__SUNPRO_C) && (__SUNPRO_C >= 0x550)
        #define WOLFSSL_API   __global
        #define WOLFSSL_LOCAL __hidden
    #else
        #define WOLFSSL_API
        #define WOLFSSL_LOCAL
    #endif /* HAVE_VISIBILITY */

    #ifdef WOLFSSL_PRIVATE_TEST_VIS
        #define WOLFSSL_TEST_VIS WOLFSSL_LOCAL
    #else
        #define WOLFSSL_TEST_VIS WOLFSSL_API
    #endif
#else /* !BUILDING_WOLFSSL */
    #if defined(__WATCOMC__)
        #if defined(WOLFSSL_DLL) && defined(__NT__)
            #define WOLFSSL_API __declspec(dllimport)
        #else
            #define WOLFSSL_API
        #endif
        #define WOLFSSL_LOCAL
    #elif defined(_MSC_VER) || defined(__MINGW32__) || defined(__CYGWIN__) || \
        defined(_WIN32_WCE)
        #if defined(WOLFSSL_DLL)
            #define WOLFSSL_API __declspec(dllimport)
        #else
            #define WOLFSSL_API
        #endif
        #define WOLFSSL_LOCAL
    #else
        #define WOLFSSL_API
        #define WOLFSSL_LOCAL
    #endif

    #if defined(WOLFSSL_VIS_FOR_TESTS)
        #ifdef WOLFSSL_PRIVATE_TEST_VIS
            #error WOLFSSL_VIS_FOR_TESTS is unavailable in WOLFSSL_PRIVATE_TEST_VIS builds.
        #endif
        #define WOLFSSL_TEST_VIS WOLFSSL_API
    #else
        #define WOLFSSL_TEST_VIS WOLFSSL_API WC_DEPRECATED("internal use only")
    #endif

#endif /* !BUILDING_WOLFSSL */

/* WOLFSSL_ABI is used for public API symbols that must not change
 * their signature. This tag is used for all APIs that are a
 * part of the fixed ABI.
 */
#define WOLFSSL_ABI

#endif /* WOLF_CRYPT_VISIBILITY_H */
