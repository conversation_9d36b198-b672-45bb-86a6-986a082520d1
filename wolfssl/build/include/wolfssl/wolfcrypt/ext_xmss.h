/* ext_xmss.h
 *
 * Copyright (C) 2006-2025 wolfSSL Inc.
 *
 * This file is part of wolfSSL.
 *
 * wolfSSL is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * wolfSSL is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1335, USA
 */

#ifndef EXT_XMSS_H
#define EXT_XMSS_H

#if defined(WOLFSSL_HAVE_XMSS) && defined(HAVE_LIBXMSS)

#include <wolfssl/wolfcrypt/xmss.h>

#include <xmss.h>
#include <params.h>

#if defined(WOLFSSL_WC_XMSS)
    #error "This code is incompatible with wolfCrypt's implementation of XMSS."
#endif

struct XmssKey {
    unsigned char        pk[XMSS_SHA256_PUBLEN];
    uint32_t             oid;
    int                  is_xmssmt;
    xmss_params          params;
#ifndef WOLFSSL_XMSS_VERIFY_ONLY
    /* The secret key length is a function of xmss_params. */
    unsigned char *      sk;
    word32               sk_len;
    wc_xmss_write_private_key_cb write_private_key; /* Callback to write/update key. */
    wc_xmss_read_private_key_cb  read_private_key;  /* Callback to read key. */
    void *               context;           /* Context arg passed to callbacks. */
#endif /* ifndef WOLFSSL_XMSS_VERIFY_ONLY */
    enum wc_XmssState    state;
};

#endif /* WOLFSSL_HAVE_XMSS */
#endif /* EXT_XMSS_H */
