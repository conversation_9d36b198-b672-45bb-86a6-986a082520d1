name: docker-build

on:
  push:
    paths:
    - docker/Dockerfile
    branches:
    - '**'

permissions: read-all

jobs:
  build:
    runs-on: ubuntu-24.04

    steps:
    - uses: actions/checkout@v4
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    - name: Build
      uses: docker/build-push-action@v6
      with:
        context: docker
        build-args: NGTCP2_BRANCH=${{ github.ref_name }}
