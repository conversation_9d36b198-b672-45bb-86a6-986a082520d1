name: interop

on:
  push:
    branches:
    - main

permissions: read-all

env:
  REGISTRY_IMAGE: ghcr.io/ngtcp2/ngtcp2-interop

jobs:
  build:
    permissions:
      packages: write

    strategy:
      matrix:
        include:
        - os: ubuntu-24.04
          arch: amd64
        - os: ubuntu-24.04-arm
          arch: arm64

    runs-on: ${{ matrix.os }}

    steps:
    - uses: actions/checkout@v4
    - name: Docker meta
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY_IMAGE }}
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    - name: Login to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    - name: Build and publish interop docker image
      id: build
      uses: docker/build-push-action@v6
      with:
        context: interop
        platforms: linux/${{ matrix.arch }}
        labels: ${{ steps.meta.outputs.labels }}
        outputs: type=image,name=${{ env.REGISTRY_IMAGE }},push-by-digest=true,name-canonical=true,push=true
        provenance: false
    - name: Export digest
      run: |
        mkdir -p /tmp/digests
        digest="${{ steps.build.outputs.digest }}"
        touch "/tmp/digests/${digest#sha256:}"
    - name: Upload digest
      uses: actions/upload-artifact@v4
      with:
        name: digests-linux-${{ matrix.arch }}
        path: /tmp/digests/*
        if-no-files-found: error
        retention-days: 1

  merge:
    permissions:
      packages: write

    runs-on: ubuntu-24.04

    needs:
    - build

    steps:
    - name: Download digests
      uses: actions/download-artifact@v4
      with:
        path: /tmp/digests
        pattern: digests-*
        merge-multiple: true
    - name: Docker meta
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY_IMAGE }}
        tags: type=raw,value=latest,enable={{is_default_branch}}
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    - name: Login to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    - name: Create manifest list and push
      working-directory: /tmp/digests
      run: |
        docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
        $(printf '${{ env.REGISTRY_IMAGE }}@sha256:%s ' *)
    - name: Inspect image
      run: |
        docker buildx imagetools inspect ${{ env.REGISTRY_IMAGE }}:${{ steps.meta.outputs.version }}
