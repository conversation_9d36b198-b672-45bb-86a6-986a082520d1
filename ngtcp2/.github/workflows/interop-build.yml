name: interop-build

on:
  push:
    branches-ignore:
    - main
    paths:
    - interop/Dockerfile
    - .github/workflows/interop-build.yml

permissions: read-all

jobs:
  build:
    strategy:
      matrix:
        include:
        - os: ubuntu-24.04
          arch: amd64
        - os: ubuntu-24.04-arm
          arch: arm64

    runs-on: ${{ matrix.os }}

    steps:
    - uses: actions/checkout@v4
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    - name: Build
      uses: docker/build-push-action@v6
      with:
        context: interop
        platforms: linux/${{ matrix.arch }}
        build-args: NGTCP2_BRANCH=${{ github.ref_name }}
