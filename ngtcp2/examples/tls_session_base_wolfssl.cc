/*
 * ngtcp2
 *
 * Copyright (c) 2020 ngtcp2 contributors
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
#include "tls_session_base_wolfssl.h"

#include <array>

#include "util.h"

using namespace ngtcp2;

TLSSessionBase::TLSSessionBase() : ssl_{nullptr} {}

TLSSessionBase::~TLSSessionBase() {
  if (ssl_) {
    wolfSSL_free(ssl_);
  }
}

WOLFSSL *TLSSessionBase::get_native_handle() const { return ssl_; }

std::string TLSSessionBase::get_cipher_name() const {
  return wolfSSL_get_cipher_name(ssl_);
}

std::string TLSSessionBase::get_selected_alpn() const {
  char *alpn = nullptr;
  unsigned short alpnlen;

  wolfSSL_ALPN_GetProtocol(ssl_, &alpn, &alpnlen);

  return std::string{alpn, alpn + alpnlen};
}
