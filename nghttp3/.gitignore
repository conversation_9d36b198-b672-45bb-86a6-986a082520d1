# emacs backup file
*~

# autotools
*.la
*.lo
*.m4
*.o
*.pyc
.dirstamp
.deps/
.libs/
INSTALL
Makefile
Makefile.in
autom4te.cache/
compile
config.guess
config.h
config.h.in
config.log
config.status
config.sub
configure
depcomp
install-sh
libtool
ltmain.sh
missing
stamp-h1
test-driver

# test logs generated by `make check`
*.log
*.trs

# autotools derivatives
/lib/includes/nghttp3/version.h
/lib/libnghttp3.pc
