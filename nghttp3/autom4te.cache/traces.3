m4trace:aclocal.m4:975: -1- AC_SUBST([am__quote])
m4trace:aclocal.m4:975: -1- AC_SUBST_TRACE([am__quote])
m4trace:aclocal.m4:975: -1- m4_pattern_allow([^am__quote$])
m4trace:aclocal.m4:1427: -1- m4_include([m4/ax_check_compile_flag.m4])
m4trace:aclocal.m4:1428: -1- m4_include([m4/ax_cxx_compile_stdcxx.m4])
m4trace:aclocal.m4:1429: -1- m4_include([m4/libtool.m4])
m4trace:aclocal.m4:1430: -1- m4_include([m4/ltoptions.m4])
m4trace:aclocal.m4:1431: -1- m4_include([m4/ltsugar.m4])
m4trace:aclocal.m4:1432: -1- m4_include([m4/ltversion.m4])
m4trace:aclocal.m4:1433: -1- m4_include([m4/lt~obsolete.m4])
m4trace:configure.ac:26: -1- AC_INIT([nghttp3], [1.11.0-DEV], [<EMAIL>])
m4trace:configure.ac:26: -1- m4_pattern_forbid([^_?A[CHUM]_])
m4trace:configure.ac:26: -1- m4_pattern_forbid([_AC_])
m4trace:configure.ac:26: -1- m4_pattern_forbid([^LIBOBJS$], [do not use LIBOBJS directly, use AC_LIBOBJ (see section `AC_LIBOBJ vs LIBOBJS'])
m4trace:configure.ac:26: -1- m4_pattern_allow([^AS_FLAGS$])
m4trace:configure.ac:26: -1- m4_pattern_forbid([^_?m4_])
m4trace:configure.ac:26: -1- m4_pattern_forbid([^dnl$])
m4trace:configure.ac:26: -1- m4_pattern_forbid([^_?AS_])
m4trace:configure.ac:26: -1- AC_SUBST([SHELL])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([SHELL])
m4trace:configure.ac:26: -1- m4_pattern_allow([^SHELL$])
m4trace:configure.ac:26: -1- AC_SUBST([PATH_SEPARATOR])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([PATH_SEPARATOR])
m4trace:configure.ac:26: -1- m4_pattern_allow([^PATH_SEPARATOR$])
m4trace:configure.ac:26: -1- AC_SUBST([PACKAGE_NAME], [m4_ifdef([AC_PACKAGE_NAME],      ['AC_PACKAGE_NAME'])])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([PACKAGE_NAME])
m4trace:configure.ac:26: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.ac:26: -1- AC_SUBST([PACKAGE_TARNAME], [m4_ifdef([AC_PACKAGE_TARNAME],   ['AC_PACKAGE_TARNAME'])])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([PACKAGE_TARNAME])
m4trace:configure.ac:26: -1- m4_pattern_allow([^PACKAGE_TARNAME$])
m4trace:configure.ac:26: -1- AC_SUBST([PACKAGE_VERSION], [m4_ifdef([AC_PACKAGE_VERSION],   ['AC_PACKAGE_VERSION'])])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([PACKAGE_VERSION])
m4trace:configure.ac:26: -1- m4_pattern_allow([^PACKAGE_VERSION$])
m4trace:configure.ac:26: -1- AC_SUBST([PACKAGE_STRING], [m4_ifdef([AC_PACKAGE_STRING],    ['AC_PACKAGE_STRING'])])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([PACKAGE_STRING])
m4trace:configure.ac:26: -1- m4_pattern_allow([^PACKAGE_STRING$])
m4trace:configure.ac:26: -1- AC_SUBST([PACKAGE_BUGREPORT], [m4_ifdef([AC_PACKAGE_BUGREPORT], ['AC_PACKAGE_BUGREPORT'])])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([PACKAGE_BUGREPORT])
m4trace:configure.ac:26: -1- m4_pattern_allow([^PACKAGE_BUGREPORT$])
m4trace:configure.ac:26: -1- AC_SUBST([PACKAGE_URL], [m4_ifdef([AC_PACKAGE_URL],       ['AC_PACKAGE_URL'])])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([PACKAGE_URL])
m4trace:configure.ac:26: -1- m4_pattern_allow([^PACKAGE_URL$])
m4trace:configure.ac:26: -1- AC_SUBST([exec_prefix], [NONE])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([exec_prefix])
m4trace:configure.ac:26: -1- m4_pattern_allow([^exec_prefix$])
m4trace:configure.ac:26: -1- AC_SUBST([prefix], [NONE])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([prefix])
m4trace:configure.ac:26: -1- m4_pattern_allow([^prefix$])
m4trace:configure.ac:26: -1- AC_SUBST([program_transform_name], [s,x,x,])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([program_transform_name])
m4trace:configure.ac:26: -1- m4_pattern_allow([^program_transform_name$])
m4trace:configure.ac:26: -1- AC_SUBST([bindir], ['${exec_prefix}/bin'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([bindir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^bindir$])
m4trace:configure.ac:26: -1- AC_SUBST([sbindir], ['${exec_prefix}/sbin'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([sbindir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^sbindir$])
m4trace:configure.ac:26: -1- AC_SUBST([libexecdir], ['${exec_prefix}/libexec'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([libexecdir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^libexecdir$])
m4trace:configure.ac:26: -1- AC_SUBST([datarootdir], ['${prefix}/share'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([datarootdir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^datarootdir$])
m4trace:configure.ac:26: -1- AC_SUBST([datadir], ['${datarootdir}'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([datadir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^datadir$])
m4trace:configure.ac:26: -1- AC_SUBST([sysconfdir], ['${prefix}/etc'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([sysconfdir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^sysconfdir$])
m4trace:configure.ac:26: -1- AC_SUBST([sharedstatedir], ['${prefix}/com'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([sharedstatedir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^sharedstatedir$])
m4trace:configure.ac:26: -1- AC_SUBST([localstatedir], ['${prefix}/var'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([localstatedir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^localstatedir$])
m4trace:configure.ac:26: -1- AC_SUBST([runstatedir], ['${localstatedir}/run'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([runstatedir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^runstatedir$])
m4trace:configure.ac:26: -1- AC_SUBST([includedir], ['${prefix}/include'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([includedir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^includedir$])
m4trace:configure.ac:26: -1- AC_SUBST([oldincludedir], ['/usr/include'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([oldincludedir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^oldincludedir$])
m4trace:configure.ac:26: -1- AC_SUBST([docdir], [m4_ifset([AC_PACKAGE_TARNAME],
				     ['${datarootdir}/doc/${PACKAGE_TARNAME}'],
				     ['${datarootdir}/doc/${PACKAGE}'])])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([docdir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^docdir$])
m4trace:configure.ac:26: -1- AC_SUBST([infodir], ['${datarootdir}/info'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([infodir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^infodir$])
m4trace:configure.ac:26: -1- AC_SUBST([htmldir], ['${docdir}'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([htmldir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^htmldir$])
m4trace:configure.ac:26: -1- AC_SUBST([dvidir], ['${docdir}'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([dvidir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^dvidir$])
m4trace:configure.ac:26: -1- AC_SUBST([pdfdir], ['${docdir}'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([pdfdir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^pdfdir$])
m4trace:configure.ac:26: -1- AC_SUBST([psdir], ['${docdir}'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([psdir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^psdir$])
m4trace:configure.ac:26: -1- AC_SUBST([libdir], ['${exec_prefix}/lib'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([libdir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^libdir$])
m4trace:configure.ac:26: -1- AC_SUBST([localedir], ['${datarootdir}/locale'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([localedir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^localedir$])
m4trace:configure.ac:26: -1- AC_SUBST([mandir], ['${datarootdir}/man'])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([mandir])
m4trace:configure.ac:26: -1- m4_pattern_allow([^mandir$])
m4trace:configure.ac:26: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_NAME])
m4trace:configure.ac:26: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.ac:26: -1- AH_OUTPUT([PACKAGE_NAME], [/* Define to the full name of this package. */
@%:@undef PACKAGE_NAME])
m4trace:configure.ac:26: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_TARNAME])
m4trace:configure.ac:26: -1- m4_pattern_allow([^PACKAGE_TARNAME$])
m4trace:configure.ac:26: -1- AH_OUTPUT([PACKAGE_TARNAME], [/* Define to the one symbol short name of this package. */
@%:@undef PACKAGE_TARNAME])
m4trace:configure.ac:26: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_VERSION])
m4trace:configure.ac:26: -1- m4_pattern_allow([^PACKAGE_VERSION$])
m4trace:configure.ac:26: -1- AH_OUTPUT([PACKAGE_VERSION], [/* Define to the version of this package. */
@%:@undef PACKAGE_VERSION])
m4trace:configure.ac:26: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_STRING])
m4trace:configure.ac:26: -1- m4_pattern_allow([^PACKAGE_STRING$])
m4trace:configure.ac:26: -1- AH_OUTPUT([PACKAGE_STRING], [/* Define to the full name and version of this package. */
@%:@undef PACKAGE_STRING])
m4trace:configure.ac:26: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_BUGREPORT])
m4trace:configure.ac:26: -1- m4_pattern_allow([^PACKAGE_BUGREPORT$])
m4trace:configure.ac:26: -1- AH_OUTPUT([PACKAGE_BUGREPORT], [/* Define to the address where bug reports for this package should be sent. */
@%:@undef PACKAGE_BUGREPORT])
m4trace:configure.ac:26: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_URL])
m4trace:configure.ac:26: -1- m4_pattern_allow([^PACKAGE_URL$])
m4trace:configure.ac:26: -1- AH_OUTPUT([PACKAGE_URL], [/* Define to the home page for this package. */
@%:@undef PACKAGE_URL])
m4trace:configure.ac:26: -1- AC_SUBST([DEFS])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([DEFS])
m4trace:configure.ac:26: -1- m4_pattern_allow([^DEFS$])
m4trace:configure.ac:26: -1- AC_SUBST([ECHO_C])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([ECHO_C])
m4trace:configure.ac:26: -1- m4_pattern_allow([^ECHO_C$])
m4trace:configure.ac:26: -1- AC_SUBST([ECHO_N])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([ECHO_N])
m4trace:configure.ac:26: -1- m4_pattern_allow([^ECHO_N$])
m4trace:configure.ac:26: -1- AC_SUBST([ECHO_T])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([ECHO_T])
m4trace:configure.ac:26: -1- m4_pattern_allow([^ECHO_T$])
m4trace:configure.ac:26: -1- AC_SUBST([LIBS])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:26: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:26: -1- AC_SUBST([build_alias])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([build_alias])
m4trace:configure.ac:26: -1- m4_pattern_allow([^build_alias$])
m4trace:configure.ac:26: -1- AC_SUBST([host_alias])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([host_alias])
m4trace:configure.ac:26: -1- m4_pattern_allow([^host_alias$])
m4trace:configure.ac:26: -1- AC_SUBST([target_alias])
m4trace:configure.ac:26: -1- AC_SUBST_TRACE([target_alias])
m4trace:configure.ac:26: -1- m4_pattern_allow([^target_alias$])
m4trace:configure.ac:27: -1- AC_CONFIG_AUX_DIR([.])
m4trace:configure.ac:28: -1- AC_CONFIG_MACRO_DIR_TRACE([m4])
m4trace:configure.ac:29: -1- AC_CONFIG_HEADERS([config.h])
m4trace:configure.ac:30: -1- AH_OUTPUT([USE_SYSTEM_EXTENSIONS], [/* Enable extensions on AIX 3, Interix.  */
#ifndef _ALL_SOURCE
# undef _ALL_SOURCE
#endif
/* Enable general extensions on macOS.  */
#ifndef _DARWIN_C_SOURCE
# undef _DARWIN_C_SOURCE
#endif
/* Enable general extensions on Solaris.  */
#ifndef __EXTENSIONS__
# undef __EXTENSIONS__
#endif
/* Enable GNU extensions on systems that have them.  */
#ifndef _GNU_SOURCE
# undef _GNU_SOURCE
#endif
/* Enable X/Open compliant socket functions that do not require linking
   with -lxnet on HP-UX 11.11.  */
#ifndef _HPUX_ALT_XOPEN_SOCKET_API
# undef _HPUX_ALT_XOPEN_SOCKET_API
#endif
/* Identify the host operating system as Minix.
   This macro does not affect the system headers\' behavior.
   A future release of Autoconf may stop defining this macro.  */
#ifndef _MINIX
# undef _MINIX
#endif
/* Enable general extensions on NetBSD.
   Enable NetBSD compatibility extensions on Minix.  */
#ifndef _NETBSD_SOURCE
# undef _NETBSD_SOURCE
#endif
/* Enable OpenBSD compatibility extensions on NetBSD.
   Oddly enough, this does nothing on OpenBSD.  */
#ifndef _OPENBSD_SOURCE
# undef _OPENBSD_SOURCE
#endif
/* Define to 1 if needed for POSIX-compatible behavior.  */
#ifndef _POSIX_SOURCE
# undef _POSIX_SOURCE
#endif
/* Define to 2 if needed for POSIX-compatible behavior.  */
#ifndef _POSIX_1_SOURCE
# undef _POSIX_1_SOURCE
#endif
/* Enable POSIX-compatible threading on Solaris.  */
#ifndef _POSIX_PTHREAD_SEMANTICS
# undef _POSIX_PTHREAD_SEMANTICS
#endif
/* Enable extensions specified by ISO/IEC TS 18661-5:2014.  */
#ifndef __STDC_WANT_IEC_60559_ATTRIBS_EXT__
# undef __STDC_WANT_IEC_60559_ATTRIBS_EXT__
#endif
/* Enable extensions specified by ISO/IEC TS 18661-1:2014.  */
#ifndef __STDC_WANT_IEC_60559_BFP_EXT__
# undef __STDC_WANT_IEC_60559_BFP_EXT__
#endif
/* Enable extensions specified by ISO/IEC TS 18661-2:2015.  */
#ifndef __STDC_WANT_IEC_60559_DFP_EXT__
# undef __STDC_WANT_IEC_60559_DFP_EXT__
#endif
/* Enable extensions specified by ISO/IEC TS 18661-4:2015.  */
#ifndef __STDC_WANT_IEC_60559_FUNCS_EXT__
# undef __STDC_WANT_IEC_60559_FUNCS_EXT__
#endif
/* Enable extensions specified by ISO/IEC TS 18661-3:2015.  */
#ifndef __STDC_WANT_IEC_60559_TYPES_EXT__
# undef __STDC_WANT_IEC_60559_TYPES_EXT__
#endif
/* Enable extensions specified by ISO/IEC TR 24731-2:2010.  */
#ifndef __STDC_WANT_LIB_EXT2__
# undef __STDC_WANT_LIB_EXT2__
#endif
/* Enable extensions specified by ISO/IEC 24747:2009.  */
#ifndef __STDC_WANT_MATH_SPEC_FUNCS__
# undef __STDC_WANT_MATH_SPEC_FUNCS__
#endif
/* Enable extensions on HP NonStop.  */
#ifndef _TANDEM_SOURCE
# undef _TANDEM_SOURCE
#endif
/* Enable X/Open extensions.  Define to 500 only if necessary
   to make mbstate_t available.  */
#ifndef _XOPEN_SOURCE
# undef _XOPEN_SOURCE
#endif
])
m4trace:configure.ac:30: -1- AH_OUTPUT([HAVE_STDIO_H], [/* Define to 1 if you have the <stdio.h> header file. */
@%:@undef HAVE_STDIO_H])
m4trace:configure.ac:30: -1- AC_SUBST([CC])
m4trace:configure.ac:30: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:30: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:30: -1- AC_SUBST([CFLAGS])
m4trace:configure.ac:30: -1- AC_SUBST_TRACE([CFLAGS])
m4trace:configure.ac:30: -1- m4_pattern_allow([^CFLAGS$])
m4trace:configure.ac:30: -1- AC_SUBST([LDFLAGS])
m4trace:configure.ac:30: -1- AC_SUBST_TRACE([LDFLAGS])
m4trace:configure.ac:30: -1- m4_pattern_allow([^LDFLAGS$])
m4trace:configure.ac:30: -1- AC_SUBST([LIBS])
m4trace:configure.ac:30: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:30: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:30: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.ac:30: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.ac:30: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:30: -1- AC_SUBST([CC])
m4trace:configure.ac:30: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:30: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:30: -1- AC_SUBST([CC])
m4trace:configure.ac:30: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:30: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:30: -1- AC_SUBST([CC])
m4trace:configure.ac:30: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:30: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:30: -1- AC_SUBST([CC])
m4trace:configure.ac:30: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:30: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:30: -1- AC_SUBST([ac_ct_CC])
m4trace:configure.ac:30: -1- AC_SUBST_TRACE([ac_ct_CC])
m4trace:configure.ac:30: -1- m4_pattern_allow([^ac_ct_CC$])
m4trace:configure.ac:30: -1- AC_SUBST([CC])
m4trace:configure.ac:30: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:30: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:30: -1- AC_SUBST([EXEEXT], [$ac_cv_exeext])
m4trace:configure.ac:30: -1- AC_SUBST_TRACE([EXEEXT])
m4trace:configure.ac:30: -1- m4_pattern_allow([^EXEEXT$])
m4trace:configure.ac:30: -1- AC_SUBST([OBJEXT], [$ac_cv_objext])
m4trace:configure.ac:30: -1- AC_SUBST_TRACE([OBJEXT])
m4trace:configure.ac:30: -1- m4_pattern_allow([^OBJEXT$])
m4trace:configure.ac:30: -1- AC_REQUIRE_AUX_FILE([compile])
m4trace:configure.ac:30: -1- AH_OUTPUT([HAVE_STDLIB_H], [/* Define to 1 if you have the <stdlib.h> header file. */
@%:@undef HAVE_STDLIB_H])
m4trace:configure.ac:30: -1- AH_OUTPUT([HAVE_STRING_H], [/* Define to 1 if you have the <string.h> header file. */
@%:@undef HAVE_STRING_H])
m4trace:configure.ac:30: -1- AH_OUTPUT([HAVE_INTTYPES_H], [/* Define to 1 if you have the <inttypes.h> header file. */
@%:@undef HAVE_INTTYPES_H])
m4trace:configure.ac:30: -1- AH_OUTPUT([HAVE_STDINT_H], [/* Define to 1 if you have the <stdint.h> header file. */
@%:@undef HAVE_STDINT_H])
m4trace:configure.ac:30: -1- AH_OUTPUT([HAVE_STRINGS_H], [/* Define to 1 if you have the <strings.h> header file. */
@%:@undef HAVE_STRINGS_H])
m4trace:configure.ac:30: -1- AH_OUTPUT([HAVE_SYS_STAT_H], [/* Define to 1 if you have the <sys/stat.h> header file. */
@%:@undef HAVE_SYS_STAT_H])
m4trace:configure.ac:30: -1- AH_OUTPUT([HAVE_SYS_TYPES_H], [/* Define to 1 if you have the <sys/types.h> header file. */
@%:@undef HAVE_SYS_TYPES_H])
m4trace:configure.ac:30: -1- AH_OUTPUT([HAVE_UNISTD_H], [/* Define to 1 if you have the <unistd.h> header file. */
@%:@undef HAVE_UNISTD_H])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([STDC_HEADERS])
m4trace:configure.ac:30: -1- m4_pattern_allow([^STDC_HEADERS$])
m4trace:configure.ac:30: -1- AH_OUTPUT([STDC_HEADERS], [/* Define to 1 if all of the C90 standard headers exist (not just the ones
   required in a freestanding environment). This macro is provided for
   backward compatibility; new code need not use it. */
@%:@undef STDC_HEADERS])
m4trace:configure.ac:30: -1- AH_OUTPUT([HAVE_WCHAR_H], [/* Define to 1 if you have the <wchar.h> header file. */
@%:@undef HAVE_WCHAR_H])
m4trace:configure.ac:30: -1- AH_OUTPUT([HAVE_MINIX_CONFIG_H], [/* Define to 1 if you have the <minix/config.h> header file. */
@%:@undef HAVE_MINIX_CONFIG_H])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([_ALL_SOURCE])
m4trace:configure.ac:30: -1- m4_pattern_allow([^_ALL_SOURCE$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([_DARWIN_C_SOURCE])
m4trace:configure.ac:30: -1- m4_pattern_allow([^_DARWIN_C_SOURCE$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([_GNU_SOURCE])
m4trace:configure.ac:30: -1- m4_pattern_allow([^_GNU_SOURCE$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([_HPUX_ALT_XOPEN_SOCKET_API])
m4trace:configure.ac:30: -1- m4_pattern_allow([^_HPUX_ALT_XOPEN_SOCKET_API$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([_NETBSD_SOURCE])
m4trace:configure.ac:30: -1- m4_pattern_allow([^_NETBSD_SOURCE$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([_OPENBSD_SOURCE])
m4trace:configure.ac:30: -1- m4_pattern_allow([^_OPENBSD_SOURCE$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([_POSIX_PTHREAD_SEMANTICS])
m4trace:configure.ac:30: -1- m4_pattern_allow([^_POSIX_PTHREAD_SEMANTICS$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([__STDC_WANT_IEC_60559_ATTRIBS_EXT__])
m4trace:configure.ac:30: -1- m4_pattern_allow([^__STDC_WANT_IEC_60559_ATTRIBS_EXT__$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([__STDC_WANT_IEC_60559_BFP_EXT__])
m4trace:configure.ac:30: -1- m4_pattern_allow([^__STDC_WANT_IEC_60559_BFP_EXT__$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([__STDC_WANT_IEC_60559_DFP_EXT__])
m4trace:configure.ac:30: -1- m4_pattern_allow([^__STDC_WANT_IEC_60559_DFP_EXT__$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([__STDC_WANT_IEC_60559_FUNCS_EXT__])
m4trace:configure.ac:30: -1- m4_pattern_allow([^__STDC_WANT_IEC_60559_FUNCS_EXT__$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([__STDC_WANT_IEC_60559_TYPES_EXT__])
m4trace:configure.ac:30: -1- m4_pattern_allow([^__STDC_WANT_IEC_60559_TYPES_EXT__$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([__STDC_WANT_LIB_EXT2__])
m4trace:configure.ac:30: -1- m4_pattern_allow([^__STDC_WANT_LIB_EXT2__$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([__STDC_WANT_MATH_SPEC_FUNCS__])
m4trace:configure.ac:30: -1- m4_pattern_allow([^__STDC_WANT_MATH_SPEC_FUNCS__$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([_TANDEM_SOURCE])
m4trace:configure.ac:30: -1- m4_pattern_allow([^_TANDEM_SOURCE$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([_MINIX])
m4trace:configure.ac:30: -1- m4_pattern_allow([^_MINIX$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([_POSIX_SOURCE])
m4trace:configure.ac:30: -1- m4_pattern_allow([^_POSIX_SOURCE$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([_POSIX_1_SOURCE])
m4trace:configure.ac:30: -1- m4_pattern_allow([^_POSIX_1_SOURCE$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([__EXTENSIONS__])
m4trace:configure.ac:30: -1- m4_pattern_allow([^__EXTENSIONS__$])
m4trace:configure.ac:30: -1- AC_DEFINE_TRACE_LITERAL([_XOPEN_SOURCE])
m4trace:configure.ac:30: -1- m4_pattern_allow([^_XOPEN_SOURCE$])
m4trace:configure.ac:33: -1- LT_INIT([])
m4trace:configure.ac:33: -1- m4_pattern_forbid([^_?LT_[A-Z_]+$])
m4trace:configure.ac:33: -1- m4_pattern_allow([^(_LT_EOF|LT_DLGLOBAL|LT_DLLAZY_OR_NOW|LT_MULTI_MODULE)$])
m4trace:configure.ac:33: -1- AC_REQUIRE_AUX_FILE([ltmain.sh])
m4trace:configure.ac:33: -1- AC_SUBST([LIBTOOL])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([LIBTOOL])
m4trace:configure.ac:33: -1- m4_pattern_allow([^LIBTOOL$])
m4trace:configure.ac:33: -1- AC_CANONICAL_HOST
m4trace:configure.ac:33: -1- AC_CANONICAL_BUILD
m4trace:configure.ac:33: -1- AC_REQUIRE_AUX_FILE([config.sub])
m4trace:configure.ac:33: -1- AC_REQUIRE_AUX_FILE([config.guess])
m4trace:configure.ac:33: -1- AC_SUBST([build], [$ac_cv_build])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([build])
m4trace:configure.ac:33: -1- m4_pattern_allow([^build$])
m4trace:configure.ac:33: -1- AC_SUBST([build_cpu], [$[1]])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([build_cpu])
m4trace:configure.ac:33: -1- m4_pattern_allow([^build_cpu$])
m4trace:configure.ac:33: -1- AC_SUBST([build_vendor], [$[2]])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([build_vendor])
m4trace:configure.ac:33: -1- m4_pattern_allow([^build_vendor$])
m4trace:configure.ac:33: -1- AC_SUBST([build_os])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([build_os])
m4trace:configure.ac:33: -1- m4_pattern_allow([^build_os$])
m4trace:configure.ac:33: -1- AC_SUBST([host], [$ac_cv_host])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([host])
m4trace:configure.ac:33: -1- m4_pattern_allow([^host$])
m4trace:configure.ac:33: -1- AC_SUBST([host_cpu], [$[1]])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([host_cpu])
m4trace:configure.ac:33: -1- m4_pattern_allow([^host_cpu$])
m4trace:configure.ac:33: -1- AC_SUBST([host_vendor], [$[2]])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([host_vendor])
m4trace:configure.ac:33: -1- m4_pattern_allow([^host_vendor$])
m4trace:configure.ac:33: -1- AC_SUBST([host_os])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([host_os])
m4trace:configure.ac:33: -1- m4_pattern_allow([^host_os$])
m4trace:configure.ac:33: -1- AC_SUBST([SED])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([SED])
m4trace:configure.ac:33: -1- m4_pattern_allow([^SED$])
m4trace:configure.ac:33: -1- AC_SUBST([GREP])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([GREP])
m4trace:configure.ac:33: -1- m4_pattern_allow([^GREP$])
m4trace:configure.ac:33: -1- AC_SUBST([EGREP])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([EGREP])
m4trace:configure.ac:33: -1- m4_pattern_allow([^EGREP$])
m4trace:configure.ac:33: -1- AC_SUBST([FGREP])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([FGREP])
m4trace:configure.ac:33: -1- m4_pattern_allow([^FGREP$])
m4trace:configure.ac:33: -1- AC_SUBST([GREP])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([GREP])
m4trace:configure.ac:33: -1- m4_pattern_allow([^GREP$])
m4trace:configure.ac:33: -1- AC_SUBST([LD])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([LD])
m4trace:configure.ac:33: -1- m4_pattern_allow([^LD$])
m4trace:configure.ac:33: -1- AC_SUBST([DUMPBIN])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([DUMPBIN])
m4trace:configure.ac:33: -1- m4_pattern_allow([^DUMPBIN$])
m4trace:configure.ac:33: -1- AC_SUBST([ac_ct_DUMPBIN])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([ac_ct_DUMPBIN])
m4trace:configure.ac:33: -1- m4_pattern_allow([^ac_ct_DUMPBIN$])
m4trace:configure.ac:33: -1- AC_SUBST([DUMPBIN])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([DUMPBIN])
m4trace:configure.ac:33: -1- m4_pattern_allow([^DUMPBIN$])
m4trace:configure.ac:33: -1- AC_SUBST([NM])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([NM])
m4trace:configure.ac:33: -1- m4_pattern_allow([^NM$])
m4trace:configure.ac:33: -1- AC_SUBST([LN_S], [$as_ln_s])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([LN_S])
m4trace:configure.ac:33: -1- m4_pattern_allow([^LN_S$])
m4trace:configure.ac:33: -1- AC_SUBST([OBJDUMP])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([OBJDUMP])
m4trace:configure.ac:33: -1- m4_pattern_allow([^OBJDUMP$])
m4trace:configure.ac:33: -1- AC_SUBST([OBJDUMP])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([OBJDUMP])
m4trace:configure.ac:33: -1- m4_pattern_allow([^OBJDUMP$])
m4trace:configure.ac:33: -1- AC_SUBST([DLLTOOL])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([DLLTOOL])
m4trace:configure.ac:33: -1- m4_pattern_allow([^DLLTOOL$])
m4trace:configure.ac:33: -1- AC_SUBST([DLLTOOL])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([DLLTOOL])
m4trace:configure.ac:33: -1- m4_pattern_allow([^DLLTOOL$])
m4trace:configure.ac:33: -1- AC_SUBST([AR])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([AR])
m4trace:configure.ac:33: -1- m4_pattern_allow([^AR$])
m4trace:configure.ac:33: -1- AC_SUBST([ac_ct_AR])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([ac_ct_AR])
m4trace:configure.ac:33: -1- m4_pattern_allow([^ac_ct_AR$])
m4trace:configure.ac:33: -1- AC_SUBST([STRIP])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([STRIP])
m4trace:configure.ac:33: -1- m4_pattern_allow([^STRIP$])
m4trace:configure.ac:33: -1- AC_SUBST([RANLIB])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([RANLIB])
m4trace:configure.ac:33: -1- m4_pattern_allow([^RANLIB$])
m4trace:configure.ac:33: -1- AC_SUBST([AWK])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([AWK])
m4trace:configure.ac:33: -1- m4_pattern_allow([^AWK$])
m4trace:configure.ac:33: -1- m4_pattern_allow([LT_OBJDIR])
m4trace:configure.ac:33: -1- AC_DEFINE_TRACE_LITERAL([LT_OBJDIR])
m4trace:configure.ac:33: -1- m4_pattern_allow([^LT_OBJDIR$])
m4trace:configure.ac:33: -1- AH_OUTPUT([LT_OBJDIR], [/* Define to the sub-directory where libtool stores uninstalled libraries. */
@%:@undef LT_OBJDIR])
m4trace:configure.ac:33: -1- LT_SUPPORTED_TAG([CC])
m4trace:configure.ac:33: -1- AC_SUBST([MANIFEST_TOOL])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([MANIFEST_TOOL])
m4trace:configure.ac:33: -1- m4_pattern_allow([^MANIFEST_TOOL$])
m4trace:configure.ac:33: -1- AC_SUBST([DSYMUTIL])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([DSYMUTIL])
m4trace:configure.ac:33: -1- m4_pattern_allow([^DSYMUTIL$])
m4trace:configure.ac:33: -1- AC_SUBST([NMEDIT])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([NMEDIT])
m4trace:configure.ac:33: -1- m4_pattern_allow([^NMEDIT$])
m4trace:configure.ac:33: -1- AC_SUBST([LIPO])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([LIPO])
m4trace:configure.ac:33: -1- m4_pattern_allow([^LIPO$])
m4trace:configure.ac:33: -1- AC_SUBST([OTOOL])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([OTOOL])
m4trace:configure.ac:33: -1- m4_pattern_allow([^OTOOL$])
m4trace:configure.ac:33: -1- AC_SUBST([OTOOL64])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([OTOOL64])
m4trace:configure.ac:33: -1- m4_pattern_allow([^OTOOL64$])
m4trace:configure.ac:33: -1- AC_SUBST([LT_SYS_LIBRARY_PATH])
m4trace:configure.ac:33: -1- AC_SUBST_TRACE([LT_SYS_LIBRARY_PATH])
m4trace:configure.ac:33: -1- m4_pattern_allow([^LT_SYS_LIBRARY_PATH$])
m4trace:configure.ac:33: -1- AH_OUTPUT([HAVE_DLFCN_H], [/* Define to 1 if you have the <dlfcn.h> header file. */
@%:@undef HAVE_DLFCN_H])
m4trace:configure.ac:33: -1- AC_DEFINE_TRACE_LITERAL([HAVE_DLFCN_H])
m4trace:configure.ac:33: -1- m4_pattern_allow([^HAVE_DLFCN_H$])
m4trace:configure.ac:35: -1- AC_CANONICAL_BUILD
m4trace:configure.ac:36: -1- AC_CANONICAL_HOST
m4trace:configure.ac:37: -1- AC_CANONICAL_TARGET
m4trace:configure.ac:37: -1- AC_SUBST([target], [$ac_cv_target])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([target])
m4trace:configure.ac:37: -1- m4_pattern_allow([^target$])
m4trace:configure.ac:37: -1- AC_SUBST([target_cpu], [$[1]])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([target_cpu])
m4trace:configure.ac:37: -1- m4_pattern_allow([^target_cpu$])
m4trace:configure.ac:37: -1- AC_SUBST([target_vendor], [$[2]])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([target_vendor])
m4trace:configure.ac:37: -1- m4_pattern_allow([^target_vendor$])
m4trace:configure.ac:37: -1- AC_SUBST([target_os])
m4trace:configure.ac:37: -1- AC_SUBST_TRACE([target_os])
m4trace:configure.ac:37: -1- m4_pattern_allow([^target_os$])
m4trace:configure.ac:39: -1- AM_INIT_AUTOMAKE([subdir-objects tar-pax])
m4trace:configure.ac:39: -1- m4_pattern_allow([^AM_[A-Z]+FLAGS$])
m4trace:configure.ac:39: -1- AM_AUTOMAKE_VERSION([1.16.5])
m4trace:configure.ac:39: -1- AC_REQUIRE_AUX_FILE([install-sh])
m4trace:configure.ac:39: -1- AC_SUBST([INSTALL_PROGRAM])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([INSTALL_PROGRAM])
m4trace:configure.ac:39: -1- m4_pattern_allow([^INSTALL_PROGRAM$])
m4trace:configure.ac:39: -1- AC_SUBST([INSTALL_SCRIPT])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([INSTALL_SCRIPT])
m4trace:configure.ac:39: -1- m4_pattern_allow([^INSTALL_SCRIPT$])
m4trace:configure.ac:39: -1- AC_SUBST([INSTALL_DATA])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([INSTALL_DATA])
m4trace:configure.ac:39: -1- m4_pattern_allow([^INSTALL_DATA$])
m4trace:configure.ac:39: -1- AC_SUBST([am__isrc], [' -I$(srcdir)'])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([am__isrc])
m4trace:configure.ac:39: -1- m4_pattern_allow([^am__isrc$])
m4trace:configure.ac:39: -1- _AM_SUBST_NOTMAKE([am__isrc])
m4trace:configure.ac:39: -1- AC_SUBST([CYGPATH_W])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([CYGPATH_W])
m4trace:configure.ac:39: -1- m4_pattern_allow([^CYGPATH_W$])
m4trace:configure.ac:39: -1- AC_SUBST([PACKAGE], ['AC_PACKAGE_TARNAME'])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([PACKAGE])
m4trace:configure.ac:39: -1- m4_pattern_allow([^PACKAGE$])
m4trace:configure.ac:39: -1- AC_SUBST([VERSION], ['AC_PACKAGE_VERSION'])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([VERSION])
m4trace:configure.ac:39: -1- m4_pattern_allow([^VERSION$])
m4trace:configure.ac:39: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE])
m4trace:configure.ac:39: -1- m4_pattern_allow([^PACKAGE$])
m4trace:configure.ac:39: -1- AH_OUTPUT([PACKAGE], [/* Name of package */
@%:@undef PACKAGE])
m4trace:configure.ac:39: -1- AC_DEFINE_TRACE_LITERAL([VERSION])
m4trace:configure.ac:39: -1- m4_pattern_allow([^VERSION$])
m4trace:configure.ac:39: -1- AH_OUTPUT([VERSION], [/* Version number of package */
@%:@undef VERSION])
m4trace:configure.ac:39: -1- AC_REQUIRE_AUX_FILE([missing])
m4trace:configure.ac:39: -1- AC_SUBST([ACLOCAL])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([ACLOCAL])
m4trace:configure.ac:39: -1- m4_pattern_allow([^ACLOCAL$])
m4trace:configure.ac:39: -1- AC_SUBST([AUTOCONF])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([AUTOCONF])
m4trace:configure.ac:39: -1- m4_pattern_allow([^AUTOCONF$])
m4trace:configure.ac:39: -1- AC_SUBST([AUTOMAKE])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([AUTOMAKE])
m4trace:configure.ac:39: -1- m4_pattern_allow([^AUTOMAKE$])
m4trace:configure.ac:39: -1- AC_SUBST([AUTOHEADER])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([AUTOHEADER])
m4trace:configure.ac:39: -1- m4_pattern_allow([^AUTOHEADER$])
m4trace:configure.ac:39: -1- AC_SUBST([MAKEINFO])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([MAKEINFO])
m4trace:configure.ac:39: -1- m4_pattern_allow([^MAKEINFO$])
m4trace:configure.ac:39: -1- AC_SUBST([install_sh])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([install_sh])
m4trace:configure.ac:39: -1- m4_pattern_allow([^install_sh$])
m4trace:configure.ac:39: -1- AC_SUBST([STRIP])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([STRIP])
m4trace:configure.ac:39: -1- m4_pattern_allow([^STRIP$])
m4trace:configure.ac:39: -1- AC_SUBST([INSTALL_STRIP_PROGRAM])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([INSTALL_STRIP_PROGRAM])
m4trace:configure.ac:39: -1- m4_pattern_allow([^INSTALL_STRIP_PROGRAM$])
m4trace:configure.ac:39: -1- AC_REQUIRE_AUX_FILE([install-sh])
m4trace:configure.ac:39: -1- AC_SUBST([MKDIR_P])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([MKDIR_P])
m4trace:configure.ac:39: -1- m4_pattern_allow([^MKDIR_P$])
m4trace:configure.ac:39: -1- AC_SUBST([mkdir_p], ['$(MKDIR_P)'])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([mkdir_p])
m4trace:configure.ac:39: -1- m4_pattern_allow([^mkdir_p$])
m4trace:configure.ac:39: -1- AC_SUBST([SET_MAKE])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([SET_MAKE])
m4trace:configure.ac:39: -1- m4_pattern_allow([^SET_MAKE$])
m4trace:configure.ac:39: -1- AC_SUBST([am__leading_dot])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([am__leading_dot])
m4trace:configure.ac:39: -1- m4_pattern_allow([^am__leading_dot$])
m4trace:configure.ac:39: -1- AC_SUBST([AMTAR], ['$${TAR-tar}'])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([AMTAR])
m4trace:configure.ac:39: -1- m4_pattern_allow([^AMTAR$])
m4trace:configure.ac:39: -1- AC_SUBST([am__tar])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([am__tar])
m4trace:configure.ac:39: -1- m4_pattern_allow([^am__tar$])
m4trace:configure.ac:39: -1- AC_SUBST([am__untar])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([am__untar])
m4trace:configure.ac:39: -1- m4_pattern_allow([^am__untar$])
m4trace:configure.ac:39: -1- AC_SUBST([DEPDIR], ["${am__leading_dot}deps"])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([DEPDIR])
m4trace:configure.ac:39: -1- m4_pattern_allow([^DEPDIR$])
m4trace:configure.ac:39: -1- AC_SUBST([am__include])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([am__include])
m4trace:configure.ac:39: -1- m4_pattern_allow([^am__include$])
m4trace:configure.ac:39: -1- AM_CONDITIONAL([AMDEP], [test "x$enable_dependency_tracking" != xno])
m4trace:configure.ac:39: -1- AC_SUBST([AMDEP_TRUE])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([AMDEP_TRUE])
m4trace:configure.ac:39: -1- m4_pattern_allow([^AMDEP_TRUE$])
m4trace:configure.ac:39: -1- AC_SUBST([AMDEP_FALSE])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([AMDEP_FALSE])
m4trace:configure.ac:39: -1- m4_pattern_allow([^AMDEP_FALSE$])
m4trace:configure.ac:39: -1- _AM_SUBST_NOTMAKE([AMDEP_TRUE])
m4trace:configure.ac:39: -1- _AM_SUBST_NOTMAKE([AMDEP_FALSE])
m4trace:configure.ac:39: -1- AC_SUBST([AMDEPBACKSLASH])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([AMDEPBACKSLASH])
m4trace:configure.ac:39: -1- m4_pattern_allow([^AMDEPBACKSLASH$])
m4trace:configure.ac:39: -1- _AM_SUBST_NOTMAKE([AMDEPBACKSLASH])
m4trace:configure.ac:39: -1- AC_SUBST([am__nodep])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([am__nodep])
m4trace:configure.ac:39: -1- m4_pattern_allow([^am__nodep$])
m4trace:configure.ac:39: -1- _AM_SUBST_NOTMAKE([am__nodep])
m4trace:configure.ac:39: -1- AC_SUBST([CCDEPMODE], [depmode=$am_cv_CC_dependencies_compiler_type])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([CCDEPMODE])
m4trace:configure.ac:39: -1- m4_pattern_allow([^CCDEPMODE$])
m4trace:configure.ac:39: -1- AM_CONDITIONAL([am__fastdepCC], [
  test "x$enable_dependency_tracking" != xno \
  && test "$am_cv_CC_dependencies_compiler_type" = gcc3])
m4trace:configure.ac:39: -1- AC_SUBST([am__fastdepCC_TRUE])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([am__fastdepCC_TRUE])
m4trace:configure.ac:39: -1- m4_pattern_allow([^am__fastdepCC_TRUE$])
m4trace:configure.ac:39: -1- AC_SUBST([am__fastdepCC_FALSE])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([am__fastdepCC_FALSE])
m4trace:configure.ac:39: -1- m4_pattern_allow([^am__fastdepCC_FALSE$])
m4trace:configure.ac:39: -1- _AM_SUBST_NOTMAKE([am__fastdepCC_TRUE])
m4trace:configure.ac:39: -1- _AM_SUBST_NOTMAKE([am__fastdepCC_FALSE])
m4trace:configure.ac:39: -1- AC_SUBST([CTAGS])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([CTAGS])
m4trace:configure.ac:39: -1- m4_pattern_allow([^CTAGS$])
m4trace:configure.ac:39: -1- AC_SUBST([ETAGS])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([ETAGS])
m4trace:configure.ac:39: -1- m4_pattern_allow([^ETAGS$])
m4trace:configure.ac:39: -1- AC_SUBST([CSCOPE])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([CSCOPE])
m4trace:configure.ac:39: -1- m4_pattern_allow([^CSCOPE$])
m4trace:configure.ac:39: -1- AM_SILENT_RULES
m4trace:configure.ac:39: -1- AC_SUBST([AM_V])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([AM_V])
m4trace:configure.ac:39: -1- m4_pattern_allow([^AM_V$])
m4trace:configure.ac:39: -1- _AM_SUBST_NOTMAKE([AM_V])
m4trace:configure.ac:39: -1- AC_SUBST([AM_DEFAULT_V])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([AM_DEFAULT_V])
m4trace:configure.ac:39: -1- m4_pattern_allow([^AM_DEFAULT_V$])
m4trace:configure.ac:39: -1- _AM_SUBST_NOTMAKE([AM_DEFAULT_V])
m4trace:configure.ac:39: -1- AC_SUBST([AM_DEFAULT_VERBOSITY])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([AM_DEFAULT_VERBOSITY])
m4trace:configure.ac:39: -1- m4_pattern_allow([^AM_DEFAULT_VERBOSITY$])
m4trace:configure.ac:39: -1- AC_SUBST([AM_BACKSLASH])
m4trace:configure.ac:39: -1- AC_SUBST_TRACE([AM_BACKSLASH])
m4trace:configure.ac:39: -1- m4_pattern_allow([^AM_BACKSLASH$])
m4trace:configure.ac:39: -1- _AM_SUBST_NOTMAKE([AM_BACKSLASH])
m4trace:configure.ac:41: -1- AM_SILENT_RULES([yes])
m4trace:configure.ac:41: -1- AC_SUBST([AM_V])
m4trace:configure.ac:41: -1- AC_SUBST_TRACE([AM_V])
m4trace:configure.ac:41: -1- m4_pattern_allow([^AM_V$])
m4trace:configure.ac:41: -1- _AM_SUBST_NOTMAKE([AM_V])
m4trace:configure.ac:41: -1- AC_SUBST([AM_DEFAULT_V])
m4trace:configure.ac:41: -1- AC_SUBST_TRACE([AM_DEFAULT_V])
m4trace:configure.ac:41: -1- m4_pattern_allow([^AM_DEFAULT_V$])
m4trace:configure.ac:41: -1- _AM_SUBST_NOTMAKE([AM_DEFAULT_V])
m4trace:configure.ac:41: -1- AC_SUBST([AM_DEFAULT_VERBOSITY])
m4trace:configure.ac:41: -1- AC_SUBST_TRACE([AM_DEFAULT_VERBOSITY])
m4trace:configure.ac:41: -1- m4_pattern_allow([^AM_DEFAULT_VERBOSITY$])
m4trace:configure.ac:41: -1- AC_SUBST([AM_BACKSLASH])
m4trace:configure.ac:41: -1- AC_SUBST_TRACE([AM_BACKSLASH])
m4trace:configure.ac:41: -1- m4_pattern_allow([^AM_BACKSLASH$])
m4trace:configure.ac:41: -1- _AM_SUBST_NOTMAKE([AM_BACKSLASH])
m4trace:configure.ac:45: -1- AC_SUBST([LT_CURRENT], [11])
m4trace:configure.ac:45: -1- AC_SUBST_TRACE([LT_CURRENT])
m4trace:configure.ac:45: -1- m4_pattern_allow([^LT_CURRENT$])
m4trace:configure.ac:46: -1- AC_SUBST([LT_REVISION], [9])
m4trace:configure.ac:46: -1- AC_SUBST_TRACE([LT_REVISION])
m4trace:configure.ac:46: -1- m4_pattern_allow([^LT_REVISION$])
m4trace:configure.ac:47: -1- AC_SUBST([LT_AGE], [2])
m4trace:configure.ac:47: -1- AC_SUBST_TRACE([LT_AGE])
m4trace:configure.ac:47: -1- m4_pattern_allow([^LT_AGE$])
m4trace:configure.ac:56: -1- AC_SUBST([PACKAGE_VERSION_NUM])
m4trace:configure.ac:56: -1- AC_SUBST_TRACE([PACKAGE_VERSION_NUM])
m4trace:configure.ac:56: -1- m4_pattern_allow([^PACKAGE_VERSION_NUM$])
m4trace:configure.ac:71: -1- AC_SUBST([DEBUGCFLAGS])
m4trace:configure.ac:71: -1- AC_SUBST_TRACE([DEBUGCFLAGS])
m4trace:configure.ac:71: -1- m4_pattern_allow([^DEBUGCFLAGS$])
m4trace:configure.ac:72: -1- AC_DEFINE_TRACE_LITERAL([DEBUGBUILD])
m4trace:configure.ac:72: -1- m4_pattern_allow([^DEBUGBUILD$])
m4trace:configure.ac:72: -1- AH_OUTPUT([DEBUGBUILD], [/* Define to 1 to enable debug output. */
@%:@undef DEBUGBUILD])
m4trace:configure.ac:91: -1- AC_SUBST([CC])
m4trace:configure.ac:91: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:91: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:91: -1- AC_SUBST([CFLAGS])
m4trace:configure.ac:91: -1- AC_SUBST_TRACE([CFLAGS])
m4trace:configure.ac:91: -1- m4_pattern_allow([^CFLAGS$])
m4trace:configure.ac:91: -1- AC_SUBST([LDFLAGS])
m4trace:configure.ac:91: -1- AC_SUBST_TRACE([LDFLAGS])
m4trace:configure.ac:91: -1- m4_pattern_allow([^LDFLAGS$])
m4trace:configure.ac:91: -1- AC_SUBST([LIBS])
m4trace:configure.ac:91: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:91: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:91: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.ac:91: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.ac:91: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:91: -1- AC_SUBST([CC])
m4trace:configure.ac:91: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:91: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:91: -1- AC_SUBST([CC])
m4trace:configure.ac:91: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:91: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:91: -1- AC_SUBST([CC])
m4trace:configure.ac:91: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:91: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:91: -1- AC_SUBST([CC])
m4trace:configure.ac:91: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:91: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:91: -1- AC_SUBST([ac_ct_CC])
m4trace:configure.ac:91: -1- AC_SUBST_TRACE([ac_ct_CC])
m4trace:configure.ac:91: -1- m4_pattern_allow([^ac_ct_CC$])
m4trace:configure.ac:91: -1- AC_SUBST([CC])
m4trace:configure.ac:91: -1- AC_SUBST_TRACE([CC])
m4trace:configure.ac:91: -1- m4_pattern_allow([^CC$])
m4trace:configure.ac:91: -1- AC_REQUIRE_AUX_FILE([compile])
m4trace:configure.ac:92: -1- AC_SUBST([CXX])
m4trace:configure.ac:92: -1- AC_SUBST_TRACE([CXX])
m4trace:configure.ac:92: -1- m4_pattern_allow([^CXX$])
m4trace:configure.ac:92: -1- AC_SUBST([CXXFLAGS])
m4trace:configure.ac:92: -1- AC_SUBST_TRACE([CXXFLAGS])
m4trace:configure.ac:92: -1- m4_pattern_allow([^CXXFLAGS$])
m4trace:configure.ac:92: -1- AC_SUBST([LDFLAGS])
m4trace:configure.ac:92: -1- AC_SUBST_TRACE([LDFLAGS])
m4trace:configure.ac:92: -1- m4_pattern_allow([^LDFLAGS$])
m4trace:configure.ac:92: -1- AC_SUBST([LIBS])
m4trace:configure.ac:92: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.ac:92: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.ac:92: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.ac:92: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.ac:92: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:92: -1- AC_SUBST([CXX])
m4trace:configure.ac:92: -1- AC_SUBST_TRACE([CXX])
m4trace:configure.ac:92: -1- m4_pattern_allow([^CXX$])
m4trace:configure.ac:92: -1- AC_SUBST([ac_ct_CXX])
m4trace:configure.ac:92: -1- AC_SUBST_TRACE([ac_ct_CXX])
m4trace:configure.ac:92: -1- m4_pattern_allow([^ac_ct_CXX$])
m4trace:configure.ac:92: -1- LT_SUPPORTED_TAG([CXX])
m4trace:configure.ac:92: -1- AC_SUBST([CXXCPP])
m4trace:configure.ac:92: -1- AC_SUBST_TRACE([CXXCPP])
m4trace:configure.ac:92: -1- m4_pattern_allow([^CXXCPP$])
m4trace:configure.ac:92: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.ac:92: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.ac:92: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:92: -1- AC_SUBST([CXXCPP])
m4trace:configure.ac:92: -1- AC_SUBST_TRACE([CXXCPP])
m4trace:configure.ac:92: -1- m4_pattern_allow([^CXXCPP$])
m4trace:configure.ac:92: -1- AC_SUBST([LD])
m4trace:configure.ac:92: -1- AC_SUBST_TRACE([LD])
m4trace:configure.ac:92: -1- m4_pattern_allow([^LD$])
m4trace:configure.ac:92: -1- AC_SUBST([LT_SYS_LIBRARY_PATH])
m4trace:configure.ac:92: -1- AC_SUBST_TRACE([LT_SYS_LIBRARY_PATH])
m4trace:configure.ac:92: -1- m4_pattern_allow([^LT_SYS_LIBRARY_PATH$])
m4trace:configure.ac:92: -1- AC_SUBST([CXXDEPMODE], [depmode=$am_cv_CXX_dependencies_compiler_type])
m4trace:configure.ac:92: -1- AC_SUBST_TRACE([CXXDEPMODE])
m4trace:configure.ac:92: -1- m4_pattern_allow([^CXXDEPMODE$])
m4trace:configure.ac:92: -1- AM_CONDITIONAL([am__fastdepCXX], [
  test "x$enable_dependency_tracking" != xno \
  && test "$am_cv_CXX_dependencies_compiler_type" = gcc3])
m4trace:configure.ac:92: -1- AC_SUBST([am__fastdepCXX_TRUE])
m4trace:configure.ac:92: -1- AC_SUBST_TRACE([am__fastdepCXX_TRUE])
m4trace:configure.ac:92: -1- m4_pattern_allow([^am__fastdepCXX_TRUE$])
m4trace:configure.ac:92: -1- AC_SUBST([am__fastdepCXX_FALSE])
m4trace:configure.ac:92: -1- AC_SUBST_TRACE([am__fastdepCXX_FALSE])
m4trace:configure.ac:92: -1- m4_pattern_allow([^am__fastdepCXX_FALSE$])
m4trace:configure.ac:92: -1- _AM_SUBST_NOTMAKE([am__fastdepCXX_TRUE])
m4trace:configure.ac:92: -1- _AM_SUBST_NOTMAKE([am__fastdepCXX_FALSE])
m4trace:configure.ac:93: -1- AC_SUBST([CPP])
m4trace:configure.ac:93: -1- AC_SUBST_TRACE([CPP])
m4trace:configure.ac:93: -1- m4_pattern_allow([^CPP$])
m4trace:configure.ac:93: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.ac:93: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.ac:93: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.ac:93: -1- AC_SUBST([CPP])
m4trace:configure.ac:93: -1- AC_SUBST_TRACE([CPP])
m4trace:configure.ac:93: -1- m4_pattern_allow([^CPP$])
m4trace:configure.ac:95: -1- AC_SUBST([LN_S], [$as_ln_s])
m4trace:configure.ac:95: -1- AC_SUBST_TRACE([LN_S])
m4trace:configure.ac:95: -1- m4_pattern_allow([^LN_S$])
m4trace:configure.ac:96: -1- AC_SUBST([SET_MAKE])
m4trace:configure.ac:96: -1- AC_SUBST_TRACE([SET_MAKE])
m4trace:configure.ac:96: -1- m4_pattern_allow([^SET_MAKE$])
m4trace:configure.ac:99: -1- m4_pattern_forbid([^_?PKG_[A-Z_]+$])
m4trace:configure.ac:99: -1- m4_pattern_allow([^PKG_CONFIG(_(PATH|LIBDIR|SYSROOT_DIR|ALLOW_SYSTEM_(CFLAGS|LIBS)))?$])
m4trace:configure.ac:99: -1- m4_pattern_allow([^PKG_CONFIG_(DISABLE_UNINSTALLED|TOP_BUILD_DIR|DEBUG_SPEW)$])
m4trace:configure.ac:99: -1- AC_SUBST([PKG_CONFIG])
m4trace:configure.ac:99: -1- AC_SUBST_TRACE([PKG_CONFIG])
m4trace:configure.ac:99: -1- m4_pattern_allow([^PKG_CONFIG$])
m4trace:configure.ac:99: -1- AC_SUBST([PKG_CONFIG_PATH])
m4trace:configure.ac:99: -1- AC_SUBST_TRACE([PKG_CONFIG_PATH])
m4trace:configure.ac:99: -1- m4_pattern_allow([^PKG_CONFIG_PATH$])
m4trace:configure.ac:99: -1- AC_SUBST([PKG_CONFIG_LIBDIR])
m4trace:configure.ac:99: -1- AC_SUBST_TRACE([PKG_CONFIG_LIBDIR])
m4trace:configure.ac:99: -1- m4_pattern_allow([^PKG_CONFIG_LIBDIR$])
m4trace:configure.ac:99: -1- AC_SUBST([PKG_CONFIG])
m4trace:configure.ac:99: -1- AC_SUBST_TRACE([PKG_CONFIG])
m4trace:configure.ac:99: -1- m4_pattern_allow([^PKG_CONFIG$])
m4trace:configure.ac:101: -1- AC_DEFINE_TRACE_LITERAL([HAVE_CXX17])
m4trace:configure.ac:101: -1- m4_pattern_allow([^HAVE_CXX17$])
m4trace:configure.ac:101: -1- AH_OUTPUT([HAVE_CXX17], [/* define if the compiler supports basic C++17 syntax */
@%:@undef HAVE_CXX17])
m4trace:configure.ac:101: -1- AC_SUBST([HAVE_CXX17])
m4trace:configure.ac:101: -1- AC_SUBST_TRACE([HAVE_CXX17])
m4trace:configure.ac:101: -1- m4_pattern_allow([^HAVE_CXX17$])
m4trace:configure.ac:110: -1- AM_CONDITIONAL([ENABLE_EXAMPLES], [ test "x${enable_examples}" = "xyes" ])
m4trace:configure.ac:110: -1- AC_SUBST([ENABLE_EXAMPLES_TRUE])
m4trace:configure.ac:110: -1- AC_SUBST_TRACE([ENABLE_EXAMPLES_TRUE])
m4trace:configure.ac:110: -1- m4_pattern_allow([^ENABLE_EXAMPLES_TRUE$])
m4trace:configure.ac:110: -1- AC_SUBST([ENABLE_EXAMPLES_FALSE])
m4trace:configure.ac:110: -1- AC_SUBST_TRACE([ENABLE_EXAMPLES_FALSE])
m4trace:configure.ac:110: -1- m4_pattern_allow([^ENABLE_EXAMPLES_FALSE$])
m4trace:configure.ac:110: -1- _AM_SUBST_NOTMAKE([ENABLE_EXAMPLES_TRUE])
m4trace:configure.ac:110: -1- _AM_SUBST_NOTMAKE([ENABLE_EXAMPLES_FALSE])
m4trace:configure.ac:113: -1- AH_OUTPUT([HAVE_ARPA_INET_H], [/* Define to 1 if you have the <arpa/inet.h> header file. */
@%:@undef HAVE_ARPA_INET_H])
m4trace:configure.ac:113: -1- AC_DEFINE_TRACE_LITERAL([HAVE_ARPA_INET_H])
m4trace:configure.ac:113: -1- m4_pattern_allow([^HAVE_ARPA_INET_H$])
m4trace:configure.ac:113: -1- AH_OUTPUT([HAVE_STDDEF_H], [/* Define to 1 if you have the <stddef.h> header file. */
@%:@undef HAVE_STDDEF_H])
m4trace:configure.ac:113: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STDDEF_H])
m4trace:configure.ac:113: -1- m4_pattern_allow([^HAVE_STDDEF_H$])
m4trace:configure.ac:113: -1- AH_OUTPUT([HAVE_STDINT_H], [/* Define to 1 if you have the <stdint.h> header file. */
@%:@undef HAVE_STDINT_H])
m4trace:configure.ac:113: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STDINT_H])
m4trace:configure.ac:113: -1- m4_pattern_allow([^HAVE_STDINT_H$])
m4trace:configure.ac:113: -1- AH_OUTPUT([HAVE_STDLIB_H], [/* Define to 1 if you have the <stdlib.h> header file. */
@%:@undef HAVE_STDLIB_H])
m4trace:configure.ac:113: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STDLIB_H])
m4trace:configure.ac:113: -1- m4_pattern_allow([^HAVE_STDLIB_H$])
m4trace:configure.ac:113: -1- AH_OUTPUT([HAVE_STRING_H], [/* Define to 1 if you have the <string.h> header file. */
@%:@undef HAVE_STRING_H])
m4trace:configure.ac:113: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRING_H])
m4trace:configure.ac:113: -1- m4_pattern_allow([^HAVE_STRING_H$])
m4trace:configure.ac:113: -1- AH_OUTPUT([HAVE_UNISTD_H], [/* Define to 1 if you have the <unistd.h> header file. */
@%:@undef HAVE_UNISTD_H])
m4trace:configure.ac:113: -1- AC_DEFINE_TRACE_LITERAL([HAVE_UNISTD_H])
m4trace:configure.ac:113: -1- m4_pattern_allow([^HAVE_UNISTD_H$])
m4trace:configure.ac:113: -1- AH_OUTPUT([HAVE_SYS_ENDIAN_H], [/* Define to 1 if you have the <sys/endian.h> header file. */
@%:@undef HAVE_SYS_ENDIAN_H])
m4trace:configure.ac:113: -1- AC_DEFINE_TRACE_LITERAL([HAVE_SYS_ENDIAN_H])
m4trace:configure.ac:113: -1- m4_pattern_allow([^HAVE_SYS_ENDIAN_H$])
m4trace:configure.ac:113: -1- AH_OUTPUT([HAVE_ENDIAN_H], [/* Define to 1 if you have the <endian.h> header file. */
@%:@undef HAVE_ENDIAN_H])
m4trace:configure.ac:113: -1- AC_DEFINE_TRACE_LITERAL([HAVE_ENDIAN_H])
m4trace:configure.ac:113: -1- m4_pattern_allow([^HAVE_ENDIAN_H$])
m4trace:configure.ac:113: -1- AH_OUTPUT([HAVE_BYTESWAP_H], [/* Define to 1 if you have the <byteswap.h> header file. */
@%:@undef HAVE_BYTESWAP_H])
m4trace:configure.ac:113: -1- AC_DEFINE_TRACE_LITERAL([HAVE_BYTESWAP_H])
m4trace:configure.ac:113: -1- m4_pattern_allow([^HAVE_BYTESWAP_H$])
m4trace:configure.ac:126: -1- AC_DEFINE_TRACE_LITERAL([size_t])
m4trace:configure.ac:126: -1- m4_pattern_allow([^size_t$])
m4trace:configure.ac:126: -1- AH_OUTPUT([size_t], [/* Define to `unsigned int\' if <sys/types.h> does not define. */
@%:@undef size_t])
m4trace:configure.ac:127: -1- AC_DEFINE_TRACE_LITERAL([ssize_t])
m4trace:configure.ac:127: -1- m4_pattern_allow([^ssize_t$])
m4trace:configure.ac:127: -1- AH_OUTPUT([ssize_t], [/* Define to `int\' if <sys/types.h> does not define. */
@%:@undef ssize_t])
m4trace:configure.ac:128: -1- AC_DEFINE_TRACE_LITERAL([_UINT8_T])
m4trace:configure.ac:128: -1- m4_pattern_allow([^_UINT8_T$])
m4trace:configure.ac:128: -1- AH_OUTPUT([_UINT8_T], [/* Define for Solaris 2.5.1 so the uint8_t typedef from <sys/synch.h>,
   <pthread.h>, or <semaphore.h> is not used. If the typedef were allowed, the
   @%:@define below would cause a syntax error. */
@%:@undef _UINT8_T])
m4trace:configure.ac:128: -1- AC_DEFINE_TRACE_LITERAL([uint8_t])
m4trace:configure.ac:128: -1- m4_pattern_allow([^uint8_t$])
m4trace:configure.ac:128: -1- AH_OUTPUT([uint8_t], [/* Define to the type of an unsigned integer type of width exactly 8 bits if
   such a type exists and the standard includes do not define it. */
@%:@undef uint8_t])
m4trace:configure.ac:129: -1- AC_DEFINE_TRACE_LITERAL([uint16_t])
m4trace:configure.ac:129: -1- m4_pattern_allow([^uint16_t$])
m4trace:configure.ac:129: -1- AH_OUTPUT([uint16_t], [/* Define to the type of an unsigned integer type of width exactly 16 bits if
   such a type exists and the standard includes do not define it. */
@%:@undef uint16_t])
m4trace:configure.ac:130: -1- AC_DEFINE_TRACE_LITERAL([_UINT32_T])
m4trace:configure.ac:130: -1- m4_pattern_allow([^_UINT32_T$])
m4trace:configure.ac:130: -1- AH_OUTPUT([_UINT32_T], [/* Define for Solaris 2.5.1 so the uint32_t typedef from <sys/synch.h>,
   <pthread.h>, or <semaphore.h> is not used. If the typedef were allowed, the
   @%:@define below would cause a syntax error. */
@%:@undef _UINT32_T])
m4trace:configure.ac:130: -1- AC_DEFINE_TRACE_LITERAL([uint32_t])
m4trace:configure.ac:130: -1- m4_pattern_allow([^uint32_t$])
m4trace:configure.ac:130: -1- AH_OUTPUT([uint32_t], [/* Define to the type of an unsigned integer type of width exactly 32 bits if
   such a type exists and the standard includes do not define it. */
@%:@undef uint32_t])
m4trace:configure.ac:131: -1- AC_DEFINE_TRACE_LITERAL([_UINT64_T])
m4trace:configure.ac:131: -1- m4_pattern_allow([^_UINT64_T$])
m4trace:configure.ac:131: -1- AH_OUTPUT([_UINT64_T], [/* Define for Solaris 2.5.1 so the uint64_t typedef from <sys/synch.h>,
   <pthread.h>, or <semaphore.h> is not used. If the typedef were allowed, the
   @%:@define below would cause a syntax error. */
@%:@undef _UINT64_T])
m4trace:configure.ac:131: -1- AC_DEFINE_TRACE_LITERAL([uint64_t])
m4trace:configure.ac:131: -1- m4_pattern_allow([^uint64_t$])
m4trace:configure.ac:131: -1- AH_OUTPUT([uint64_t], [/* Define to the type of an unsigned integer type of width exactly 64 bits if
   such a type exists and the standard includes do not define it. */
@%:@undef uint64_t])
m4trace:configure.ac:132: -1- AC_DEFINE_TRACE_LITERAL([int8_t])
m4trace:configure.ac:132: -1- m4_pattern_allow([^int8_t$])
m4trace:configure.ac:132: -1- AH_OUTPUT([int8_t], [/* Define to the type of a signed integer type of width exactly 8 bits if such
   a type exists and the standard includes do not define it. */
@%:@undef int8_t])
m4trace:configure.ac:133: -1- AC_DEFINE_TRACE_LITERAL([int16_t])
m4trace:configure.ac:133: -1- m4_pattern_allow([^int16_t$])
m4trace:configure.ac:133: -1- AH_OUTPUT([int16_t], [/* Define to the type of a signed integer type of width exactly 16 bits if
   such a type exists and the standard includes do not define it. */
@%:@undef int16_t])
m4trace:configure.ac:134: -1- AC_DEFINE_TRACE_LITERAL([int32_t])
m4trace:configure.ac:134: -1- m4_pattern_allow([^int32_t$])
m4trace:configure.ac:134: -1- AH_OUTPUT([int32_t], [/* Define to the type of a signed integer type of width exactly 32 bits if
   such a type exists and the standard includes do not define it. */
@%:@undef int32_t])
m4trace:configure.ac:135: -1- AC_DEFINE_TRACE_LITERAL([int64_t])
m4trace:configure.ac:135: -1- m4_pattern_allow([^int64_t$])
m4trace:configure.ac:135: -1- AH_OUTPUT([int64_t], [/* Define to the type of a signed integer type of width exactly 64 bits if
   such a type exists and the standard includes do not define it. */
@%:@undef int64_t])
m4trace:configure.ac:136: -1- AC_DEFINE_TRACE_LITERAL([off_t])
m4trace:configure.ac:136: -1- m4_pattern_allow([^off_t$])
m4trace:configure.ac:136: -1- AH_OUTPUT([off_t], [/* Define to `long int\' if <sys/types.h> does not define. */
@%:@undef off_t])
m4trace:configure.ac:137: -1- AC_DEFINE_TRACE_LITERAL([pid_t])
m4trace:configure.ac:137: -1- m4_pattern_allow([^pid_t$])
m4trace:configure.ac:137: -1- AH_OUTPUT([pid_t], [/* Define as a signed integer type capable of holding a process identifier. */
@%:@undef pid_t])
m4trace:configure.ac:138: -1- AC_DEFINE_TRACE_LITERAL([uid_t])
m4trace:configure.ac:138: -1- m4_pattern_allow([^uid_t$])
m4trace:configure.ac:138: -1- AH_OUTPUT([uid_t], [/* Define to `int\' if <sys/types.h> doesn\'t define. */
@%:@undef uid_t])
m4trace:configure.ac:138: -1- AC_DEFINE_TRACE_LITERAL([gid_t])
m4trace:configure.ac:138: -1- m4_pattern_allow([^gid_t$])
m4trace:configure.ac:138: -1- AH_OUTPUT([gid_t], [/* Define to `int\' if <sys/types.h> doesn\'t define. */
@%:@undef gid_t])
m4trace:configure.ac:139: -1- AC_DEFINE_TRACE_LITERAL([HAVE_PTRDIFF_T])
m4trace:configure.ac:139: -1- m4_pattern_allow([^HAVE_PTRDIFF_T$])
m4trace:configure.ac:139: -1- AH_OUTPUT([HAVE_PTRDIFF_T], [/* Define to 1 if the system has the type `ptrdiff_t\'. */
@%:@undef HAVE_PTRDIFF_T])
m4trace:configure.ac:140: -1- AH_OUTPUT([WORDS_BIGENDIAN], [/* Define WORDS_BIGENDIAN to 1 if your processor stores words with the most
   significant byte first (like Motorola and SPARC, unlike Intel). */
#if defined AC_APPLE_UNIVERSAL_BUILD
# if defined __BIG_ENDIAN__
#  define WORDS_BIGENDIAN 1
# endif
#else
# ifndef WORDS_BIGENDIAN
#  undef WORDS_BIGENDIAN
# endif
#endif])
m4trace:configure.ac:140: -1- AC_DEFINE_TRACE_LITERAL([WORDS_BIGENDIAN])
m4trace:configure.ac:140: -1- m4_pattern_allow([^WORDS_BIGENDIAN$])
m4trace:configure.ac:140: -1- AC_DEFINE_TRACE_LITERAL([AC_APPLE_UNIVERSAL_BUILD])
m4trace:configure.ac:140: -1- m4_pattern_allow([^AC_APPLE_UNIVERSAL_BUILD$])
m4trace:configure.ac:140: -1- AH_OUTPUT([AC_APPLE_UNIVERSAL_BUILD], [/* Define if building universal (internal helper macro) */
@%:@undef AC_APPLE_UNIVERSAL_BUILD])
m4trace:configure.ac:141: -1- AH_OUTPUT([inline], [/* Define to `__inline__\' or `__inline\' if that\'s what the C compiler
   calls it, or to nothing if \'inline\' is not supported under any name.  */
#ifndef __cplusplus
#undef inline
#endif])
m4trace:configure.ac:142: -1- AC_DEFINE_TRACE_LITERAL([_FILE_OFFSET_BITS])
m4trace:configure.ac:142: -1- m4_pattern_allow([^_FILE_OFFSET_BITS$])
m4trace:configure.ac:142: -1- AH_OUTPUT([_FILE_OFFSET_BITS], [/* Number of bits in a file offset, on hosts where this is settable. */
@%:@undef _FILE_OFFSET_BITS])
m4trace:configure.ac:142: -1- AC_DEFINE_TRACE_LITERAL([_LARGE_FILES])
m4trace:configure.ac:142: -1- m4_pattern_allow([^_LARGE_FILES$])
m4trace:configure.ac:142: -1- AH_OUTPUT([_LARGE_FILES], [/* Define for large files, on AIX-style hosts. */
@%:@undef _LARGE_FILES])
m4trace:configure.ac:145: -1- AH_OUTPUT([HAVE_MEMMOVE], [/* Define to 1 if you have the `memmove\' function. */
@%:@undef HAVE_MEMMOVE])
m4trace:configure.ac:145: -1- AC_DEFINE_TRACE_LITERAL([HAVE_MEMMOVE])
m4trace:configure.ac:145: -1- m4_pattern_allow([^HAVE_MEMMOVE$])
m4trace:configure.ac:145: -1- AH_OUTPUT([HAVE_MEMSET], [/* Define to 1 if you have the `memset\' function. */
@%:@undef HAVE_MEMSET])
m4trace:configure.ac:145: -1- AC_DEFINE_TRACE_LITERAL([HAVE_MEMSET])
m4trace:configure.ac:145: -1- m4_pattern_allow([^HAVE_MEMSET$])
m4trace:configure.ac:151: -1- AC_DEFINE_TRACE_LITERAL([HAVE_DECL_BE64TOH])
m4trace:configure.ac:151: -1- m4_pattern_allow([^HAVE_DECL_BE64TOH$])
m4trace:configure.ac:151: -1- AH_OUTPUT([HAVE_DECL_BE64TOH], [/* Define to 1 if you have the declaration of `be64toh\', and to 0 if you
   don\'t. */
@%:@undef HAVE_DECL_BE64TOH])
m4trace:configure.ac:160: -1- AC_DEFINE_TRACE_LITERAL([HAVE_DECL_BSWAP_64])
m4trace:configure.ac:160: -1- m4_pattern_allow([^HAVE_DECL_BSWAP_64$])
m4trace:configure.ac:160: -1- AH_OUTPUT([HAVE_DECL_BSWAP_64], [/* Define to 1 if you have the declaration of `bswap_64\', and to 0 if you
   don\'t. */
@%:@undef HAVE_DECL_BSWAP_64])
m4trace:configure.ac:247: -1- AC_SUBST([WARNCFLAGS])
m4trace:configure.ac:247: -1- AC_SUBST_TRACE([WARNCFLAGS])
m4trace:configure.ac:247: -1- m4_pattern_allow([^WARNCFLAGS$])
m4trace:configure.ac:248: -1- AC_SUBST([WARNCXXFLAGS])
m4trace:configure.ac:248: -1- AC_SUBST_TRACE([WARNCXXFLAGS])
m4trace:configure.ac:248: -1- m4_pattern_allow([^WARNCXXFLAGS$])
m4trace:configure.ac:260: -1- AC_DEFINE_TRACE_LITERAL([MEMDEBUG])
m4trace:configure.ac:260: -1- m4_pattern_allow([^MEMDEBUG$])
m4trace:configure.ac:260: -1- AH_OUTPUT([MEMDEBUG], [/* Define to 1 to enable memory allocation debug output. */
@%:@undef MEMDEBUG])
m4trace:configure.ac:268: -1- AC_SUBST([EXTRACFLAG])
m4trace:configure.ac:268: -1- AC_SUBST_TRACE([EXTRACFLAG])
m4trace:configure.ac:268: -1- m4_pattern_allow([^EXTRACFLAG$])
m4trace:configure.ac:270: -1- AC_CONFIG_FILES([
  Makefile
  lib/Makefile
  lib/libnghttp3.pc
  lib/includes/Makefile
  lib/includes/nghttp3/version.h
  tests/Makefile
  doc/Makefile
  doc/source/conf.py
  examples/Makefile
])
m4trace:configure.ac:281: -1- AC_SUBST([LIB@&t@OBJS], [$ac_libobjs])
m4trace:configure.ac:281: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.ac:281: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.ac:281: -1- AC_SUBST([LTLIBOBJS], [$ac_ltlibobjs])
m4trace:configure.ac:281: -1- AC_SUBST_TRACE([LTLIBOBJS])
m4trace:configure.ac:281: -1- m4_pattern_allow([^LTLIBOBJS$])
m4trace:configure.ac:281: -1- AM_CONDITIONAL([am__EXEEXT], [test -n "$EXEEXT"])
m4trace:configure.ac:281: -1- AC_SUBST([am__EXEEXT_TRUE])
m4trace:configure.ac:281: -1- AC_SUBST_TRACE([am__EXEEXT_TRUE])
m4trace:configure.ac:281: -1- m4_pattern_allow([^am__EXEEXT_TRUE$])
m4trace:configure.ac:281: -1- AC_SUBST([am__EXEEXT_FALSE])
m4trace:configure.ac:281: -1- AC_SUBST_TRACE([am__EXEEXT_FALSE])
m4trace:configure.ac:281: -1- m4_pattern_allow([^am__EXEEXT_FALSE$])
m4trace:configure.ac:281: -1- _AM_SUBST_NOTMAKE([am__EXEEXT_TRUE])
m4trace:configure.ac:281: -1- _AM_SUBST_NOTMAKE([am__EXEEXT_FALSE])
m4trace:configure.ac:281: -1- AC_SUBST_TRACE([top_builddir])
m4trace:configure.ac:281: -1- AC_SUBST_TRACE([top_build_prefix])
m4trace:configure.ac:281: -1- AC_SUBST_TRACE([srcdir])
m4trace:configure.ac:281: -1- AC_SUBST_TRACE([abs_srcdir])
m4trace:configure.ac:281: -1- AC_SUBST_TRACE([top_srcdir])
m4trace:configure.ac:281: -1- AC_SUBST_TRACE([abs_top_srcdir])
m4trace:configure.ac:281: -1- AC_SUBST_TRACE([builddir])
m4trace:configure.ac:281: -1- AC_SUBST_TRACE([abs_builddir])
m4trace:configure.ac:281: -1- AC_SUBST_TRACE([abs_top_builddir])
m4trace:configure.ac:281: -1- AC_SUBST_TRACE([INSTALL])
m4trace:configure.ac:281: -1- AC_SUBST_TRACE([MKDIR_P])
m4trace:configure.ac:281: -1- AC_REQUIRE_AUX_FILE([ltmain.sh])
