# This file was generated by Autom4te 2.71.
# It contains the lists of macros which have been traced.
# It can be safely removed.

@request = (
             bless( [
                      '0',
                      1,
                      [
                        '/usr/share/autoconf'
                      ],
                      [
                        '/usr/share/autoconf/autoconf/autoconf.m4f',
                        '/usr/share/aclocal-1.16/internal/ac-config-macro-dirs.m4',
                        '/usr/share/aclocal/libtool.m4',
                        '/usr/share/aclocal/ltargz.m4',
                        '/usr/share/aclocal/ltdl.m4',
                        '/usr/share/aclocal/ltoptions.m4',
                        '/usr/share/aclocal/ltsugar.m4',
                        '/usr/share/aclocal/ltversion.m4',
                        '/usr/share/aclocal/lt~obsolete.m4',
                        '/usr/share/aclocal/pkg.m4',
                        '/usr/share/aclocal-1.16/amversion.m4',
                        '/usr/share/aclocal-1.16/auxdir.m4',
                        '/usr/share/aclocal-1.16/cond.m4',
                        '/usr/share/aclocal-1.16/depend.m4',
                        '/usr/share/aclocal-1.16/depout.m4',
                        '/usr/share/aclocal-1.16/init.m4',
                        '/usr/share/aclocal-1.16/install-sh.m4',
                        '/usr/share/aclocal-1.16/lead-dot.m4',
                        '/usr/share/aclocal-1.16/make.m4',
                        '/usr/share/aclocal-1.16/missing.m4',
                        '/usr/share/aclocal-1.16/options.m4',
                        '/usr/share/aclocal-1.16/prog-cc-c-o.m4',
                        '/usr/share/aclocal-1.16/runlog.m4',
                        '/usr/share/aclocal-1.16/sanity.m4',
                        '/usr/share/aclocal-1.16/silent.m4',
                        '/usr/share/aclocal-1.16/strip.m4',
                        '/usr/share/aclocal-1.16/substnot.m4',
                        '/usr/share/aclocal-1.16/tar.m4',
                        'm4/ax_check_compile_flag.m4',
                        'm4/ax_cxx_compile_stdcxx.m4',
                        'configure.ac'
                      ],
                      {
                        'AM_INIT_AUTOMAKE' => 1,
                        'LT_AC_PROG_RC' => 1,
                        'AC_LIBTOOL_LANG_RC_CONFIG' => 1,
                        'LT_AC_PROG_GCJ' => 1,
                        '_LT_AC_LANG_F77' => 1,
                        '_AM_CONFIG_MACRO_DIRS' => 1,
                        'AM_MAKE_INCLUDE' => 1,
                        'LT_PATH_NM' => 1,
                        'AM_PROG_LD' => 1,
                        '_LT_AC_LANG_GCJ' => 1,
                        'include' => 1,
                        'AC_PROG_EGREP' => 1,
                        'AC_LIBTOOL_PROG_CC_C_O' => 1,
                        'LTDL_INIT' => 1,
                        '_AC_PROG_LIBTOOL' => 1,
                        'LT_FUNC_DLSYM_USCORE' => 1,
                        'AM_DISABLE_SHARED' => 1,
                        '_AM_OUTPUT_DEPENDENCY_COMMANDS' => 1,
                        'AM_SET_DEPDIR' => 1,
                        'PKG_CHECK_MODULES_STATIC' => 1,
                        '_LT_REQUIRED_DARWIN_CHECKS' => 1,
                        'PKG_CHECK_EXISTS' => 1,
                        '_LT_AC_PROG_ECHO_BACKSLASH' => 1,
                        'AC_ENABLE_STATIC' => 1,
                        'AC_LIBTOOL_SYS_LIB_STRIP' => 1,
                        'LT_PROG_GO' => 1,
                        'PKG_CHECK_VAR' => 1,
                        'AC_ENABLE_FAST_INSTALL' => 1,
                        'AC_LIBTOOL_LANG_CXX_CONFIG' => 1,
                        '_LT_AC_SYS_LIBPATH_AIX' => 1,
                        'LT_SYS_DLSEARCH_PATH' => 1,
                        'AC_LIBTOOL_FC' => 1,
                        'LT_SYS_MODULE_PATH' => 1,
                        '_LT_COMPILER_OPTION' => 1,
                        'AC_LTDL_DLSYM_USCORE' => 1,
                        '_LT_AC_TAGVAR' => 1,
                        'AC_LIBLTDL_INSTALLABLE' => 1,
                        'AM_PROG_LIBTOOL' => 1,
                        'LTOBSOLETE_VERSION' => 1,
                        'AC_LIBTOOL_SYS_GLOBAL_SYMBOL_PIPE' => 1,
                        'AC_CONFIG_MACRO_DIR_TRACE' => 1,
                        '_LT_PROG_F77' => 1,
                        'AM_ENABLE_STATIC' => 1,
                        'LT_INIT' => 1,
                        'LTVERSION_VERSION' => 1,
                        '_LT_WITH_SYSROOT' => 1,
                        'AM_PROG_INSTALL_SH' => 1,
                        'AM_AUTOMAKE_VERSION' => 1,
                        '_LT_AC_SYS_COMPILER' => 1,
                        'AC_LIBTOOL_GCJ' => 1,
                        'AC_LIBTOOL_PICMODE' => 1,
                        'AC_PROG_LD_RELOAD_FLAG' => 1,
                        'AC_LIBTOOL_RC' => 1,
                        'AC_PROG_LIBTOOL' => 1,
                        'LT_LIB_DLLOAD' => 1,
                        '_LT_PROG_FC' => 1,
                        'AM_RUN_LOG' => 1,
                        'AM_SANITY_CHECK' => 1,
                        '_LT_PROG_ECHO_BACKSLASH' => 1,
                        '_AM_DEPENDENCIES' => 1,
                        '_LT_AC_TAGCONFIG' => 1,
                        'AM_DEP_TRACK' => 1,
                        '_AM_SUBST_NOTMAKE' => 1,
                        'AC_PROG_LD' => 1,
                        'AC_LIB_LTDL' => 1,
                        'AC_PROG_LD_GNU' => 1,
                        'LT_AC_PROG_SED' => 1,
                        'AM_AUX_DIR_EXPAND' => 1,
                        'LT_AC_PROG_EGREP' => 1,
                        '_LT_PROG_LTMAIN' => 1,
                        'AC_LIBTOOL_SETUP' => 1,
                        'AC_LIBTOOL_LANG_F77_CONFIG' => 1,
                        'AM_OUTPUT_DEPENDENCY_COMMANDS' => 1,
                        '_AM_PROG_TAR' => 1,
                        'm4_pattern_forbid' => 1,
                        '_LT_AC_LANG_GCJ_CONFIG' => 1,
                        '_AM_AUTOCONF_VERSION' => 1,
                        'AM_SET_CURRENT_AUTOMAKE_VERSION' => 1,
                        'LT_LIB_M' => 1,
                        '_AC_AM_CONFIG_HEADER_HOOK' => 1,
                        'AC_LTDL_SHLIBPATH' => 1,
                        'LT_SUPPORTED_TAG' => 1,
                        '_LT_AC_LANG_C_CONFIG' => 1,
                        'AC_LIBTOOL_PROG_LD_HARDCODE_LIBPATH' => 1,
                        'AM_SILENT_RULES' => 1,
                        'AU_DEFUN' => 1,
                        '_LTDL_SETUP' => 1,
                        '_LT_AC_LANG_F77_CONFIG' => 1,
                        'AC_DISABLE_SHARED' => 1,
                        '_LT_PROG_CXX' => 1,
                        'PKG_PROG_PKG_CONFIG' => 1,
                        'AC_LTDL_SYSSEARCHPATH' => 1,
                        'AC_DEFUN_ONCE' => 1,
                        'AC_LIBTOOL_POSTDEP_PREDEP' => 1,
                        '_AM_SET_OPTION' => 1,
                        'AC_LIBTOOL_F77' => 1,
                        '_LT_LINKER_BOILERPLATE' => 1,
                        'AC_LIBTOOL_SYS_HARD_LINK_LOCKS' => 1,
                        '_AM_MANGLE_OPTION' => 1,
                        'AC_LIBTOOL_CXX' => 1,
                        '_LT_DLL_DEF_P' => 1,
                        'AC_LIBTOOL_SYS_OLD_ARCHIVE' => 1,
                        'AC_CHECK_LIBM' => 1,
                        'AC_LIBTOOL_OBJDIR' => 1,
                        'LTDL_CONVENIENCE' => 1,
                        'AC_LIBTOOL_LANG_GCJ_CONFIG' => 1,
                        'AC_LIBTOOL_CONFIG' => 1,
                        'AC_LIBTOOL_LANG_C_CONFIG' => 1,
                        '_LT_LINKER_OPTION' => 1,
                        '_AM_SET_OPTIONS' => 1,
                        'AX_CHECK_COMPILE_FLAG' => 1,
                        'LT_OUTPUT' => 1,
                        'AC_CONFIG_MACRO_DIR' => 1,
                        '_LT_AC_CHECK_DLFCN' => 1,
                        'LTDL_INSTALLABLE' => 1,
                        'AC_LTDL_ENABLE_INSTALL' => 1,
                        'AC_LIBTOOL_SYS_DYNAMIC_LINKER' => 1,
                        'LT_PROG_RC' => 1,
                        '_LT_AC_PROG_CXXCPP' => 1,
                        '_LT_AC_LANG_CXX' => 1,
                        'AC_DISABLE_STATIC' => 1,
                        'PKG_CHECK_MODULES' => 1,
                        '_LT_LIBOBJ' => 1,
                        '_LT_AC_LANG_RC_CONFIG' => 1,
                        'LTSUGAR_VERSION' => 1,
                        'AM_ENABLE_SHARED' => 1,
                        'AC_LTDL_PREOPEN' => 1,
                        'AC_DEFUN' => 1,
                        'AC_LTDL_SHLIBEXT' => 1,
                        'AM_MISSING_PROG' => 1,
                        'LT_WITH_LTDL' => 1,
                        '_AM_IF_OPTION' => 1,
                        'AC_LIBTOOL_PROG_LD_SHLIBS' => 1,
                        'LT_SYS_MODULE_EXT' => 1,
                        'LT_CONFIG_LTDL_DIR' => 1,
                        'AM_PROG_NM' => 1,
                        'PKG_INSTALLDIR' => 1,
                        'LT_PATH_LD' => 1,
                        '_LT_PATH_TOOL_PREFIX' => 1,
                        'AM_PROG_CC_C_O' => 1,
                        'AC_LIBTOOL_LINKER_OPTION' => 1,
                        '_AM_PROG_CC_C_O' => 1,
                        'AC_LIBTOOL_SYS_MAX_CMD_LEN' => 1,
                        'AC_DEPLIBS_CHECK_METHOD' => 1,
                        'AC_WITH_LTDL' => 1,
                        '_LT_PREPARE_SED_QUOTE_VARS' => 1,
                        'AC_PATH_MAGIC' => 1,
                        '_LT_AC_FILE_LTDLL_C' => 1,
                        'AC_LIBTOOL_DLOPEN' => 1,
                        'LT_SYS_DLOPEN_SELF' => 1,
                        '_LT_AC_LOCK' => 1,
                        'AM_DISABLE_STATIC' => 1,
                        'AC_PATH_TOOL_PREFIX' => 1,
                        'LT_LANG' => 1,
                        'AC_LIBLTDL_CONVENIENCE' => 1,
                        'PKG_NOARCH_INSTALLDIR' => 1,
                        'AM_SET_LEADING_DOT' => 1,
                        '_LT_COMPILER_BOILERPLATE' => 1,
                        'AC_ENABLE_SHARED' => 1,
                        'AC_PROG_NM' => 1,
                        'AM_MISSING_HAS_RUN' => 1,
                        'LT_PROG_GCJ' => 1,
                        '_LT_CC_BASENAME' => 1,
                        'AM_SUBST_NOTMAKE' => 1,
                        'AC_LTDL_DLLIB' => 1,
                        'AC_DISABLE_FAST_INSTALL' => 1,
                        '_LT_AC_SHELL_INIT' => 1,
                        'm4_include' => 1,
                        'm4_pattern_allow' => 1,
                        'LT_FUNC_ARGZ' => 1,
                        'AC_LIBTOOL_DLOPEN_SELF' => 1,
                        'AX_CXX_COMPILE_STDCXX' => 1,
                        '_LT_AC_LANG_CXX_CONFIG' => 1,
                        'LT_SYS_SYMBOL_USCORE' => 1,
                        'AC_LTDL_SYS_DLOPEN_DEPLIBS' => 1,
                        '_m4_warn' => 1,
                        'AM_CONDITIONAL' => 1,
                        'AC_LIBTOOL_PROG_COMPILER_NO_RTTI' => 1,
                        'AM_PROG_INSTALL_STRIP' => 1,
                        '_LT_AC_TRY_DLOPEN_SELF' => 1,
                        'AC_LTDL_OBJDIR' => 1,
                        'LT_CMD_MAX_LEN' => 1,
                        'LT_SYS_DLOPEN_DEPLIBS' => 1,
                        'AC_LTDL_SYMBOL_USCORE' => 1,
                        'AC_LIBTOOL_PROG_COMPILER_PIC' => 1,
                        'LTOPTIONS_VERSION' => 1,
                        'AC_LIBTOOL_WIN32_DLL' => 1,
                        'AC_LIBTOOL_COMPILER_OPTION' => 1,
                        '_PKG_SHORT_ERRORS_SUPPORTED' => 1
                      }
                    ], 'Autom4te::Request' ),
             bless( [
                      '1',
                      1,
                      [
                        '/usr/share/autoconf'
                      ],
                      [
                        '/usr/share/autoconf/autoconf/autoconf.m4f',
                        'aclocal.m4',
                        'configure.ac'
                      ],
                      {
                        'AC_SUBST' => 1,
                        'GTK_DOC_CHECK' => 1,
                        'IT_PROG_INTLTOOL' => 1,
                        '_m4_warn' => 1,
                        'AM_CONDITIONAL' => 1,
                        'AC_CONFIG_SUBDIRS' => 1,
                        '_AM_COND_ENDIF' => 1,
                        'AM_PROG_MOC' => 1,
                        'AC_REQUIRE_AUX_FILE' => 1,
                        'm4_sinclude' => 1,
                        '_AM_SUBST_NOTMAKE' => 1,
                        'AM_NLS' => 1,
                        'AM_PROG_MKDIR_P' => 1,
                        'AM_PROG_AR' => 1,
                        'AM_ENABLE_MULTILIB' => 1,
                        'AC_DEFINE_TRACE_LITERAL' => 1,
                        'AM_PROG_LIBTOOL' => 1,
                        'AM_SILENT_RULES' => 1,
                        'LT_CONFIG_LTDL_DIR' => 1,
                        'sinclude' => 1,
                        'AC_INIT' => 1,
                        '_AM_MAKEFILE_INCLUDE' => 1,
                        'AM_PROG_FC_C_O' => 1,
                        'AM_PROG_CC_C_O' => 1,
                        'AC_SUBST_TRACE' => 1,
                        'LT_SUPPORTED_TAG' => 1,
                        '_AM_COND_ELSE' => 1,
                        'AM_GNU_GETTEXT' => 1,
                        'AC_CANONICAL_TARGET' => 1,
                        'AC_CANONICAL_BUILD' => 1,
                        'AC_FC_SRCEXT' => 1,
                        'm4_pattern_forbid' => 1,
                        'AC_FC_FREEFORM' => 1,
                        'AM_AUTOMAKE_VERSION' => 1,
                        'AM_POT_TOOLS' => 1,
                        'AC_CANONICAL_HOST' => 1,
                        'AC_LIBSOURCE' => 1,
                        'LT_INIT' => 1,
                        'AM_PROG_CXX_C_O' => 1,
                        'AM_GNU_GETTEXT_INTL_SUBDIR' => 1,
                        'AC_CONFIG_MACRO_DIR_TRACE' => 1,
                        'AM_MAKEFILE_INCLUDE' => 1,
                        'AM_EXTRA_RECURSIVE_TARGETS' => 1,
                        'AM_INIT_AUTOMAKE' => 1,
                        'AC_CONFIG_AUX_DIR' => 1,
                        'AH_OUTPUT' => 1,
                        'AC_CONFIG_LINKS' => 1,
                        'm4_pattern_allow' => 1,
                        'AC_CONFIG_FILES' => 1,
                        'AC_CONFIG_LIBOBJ_DIR' => 1,
                        '_LT_AC_TAGCONFIG' => 1,
                        'm4_include' => 1,
                        'AM_XGETTEXT_OPTION' => 1,
                        '_AM_COND_IF' => 1,
                        'AM_PATH_GUILE' => 1,
                        'AC_PROG_LIBTOOL' => 1,
                        'AC_FC_PP_SRCEXT' => 1,
                        'AC_CONFIG_HEADERS' => 1,
                        'AM_MAINTAINER_MODE' => 1,
                        'AM_PROG_F77_C_O' => 1,
                        'include' => 1,
                        'AC_FC_PP_DEFINE' => 1,
                        'AC_CANONICAL_SYSTEM' => 1
                      }
                    ], 'Autom4te::Request' ),
             bless( [
                      '2',
                      1,
                      [
                        '/usr/share/autoconf'
                      ],
                      [
                        '/usr/share/autoconf/autoconf/autoconf.m4f',
                        '/usr/share/aclocal-1.16/internal/ac-config-macro-dirs.m4',
                        '/usr/share/aclocal/ltargz.m4',
                        '/usr/share/aclocal/ltdl.m4',
                        '/usr/share/aclocal/pkg.m4',
                        '/usr/share/aclocal-1.16/amversion.m4',
                        '/usr/share/aclocal-1.16/auxdir.m4',
                        '/usr/share/aclocal-1.16/cond.m4',
                        '/usr/share/aclocal-1.16/depend.m4',
                        '/usr/share/aclocal-1.16/depout.m4',
                        '/usr/share/aclocal-1.16/init.m4',
                        '/usr/share/aclocal-1.16/install-sh.m4',
                        '/usr/share/aclocal-1.16/lead-dot.m4',
                        '/usr/share/aclocal-1.16/make.m4',
                        '/usr/share/aclocal-1.16/missing.m4',
                        '/usr/share/aclocal-1.16/options.m4',
                        '/usr/share/aclocal-1.16/prog-cc-c-o.m4',
                        '/usr/share/aclocal-1.16/runlog.m4',
                        '/usr/share/aclocal-1.16/sanity.m4',
                        '/usr/share/aclocal-1.16/silent.m4',
                        '/usr/share/aclocal-1.16/strip.m4',
                        '/usr/share/aclocal-1.16/substnot.m4',
                        '/usr/share/aclocal-1.16/tar.m4',
                        'm4/ax_check_compile_flag.m4',
                        'm4/ax_cxx_compile_stdcxx.m4',
                        'm4/libtool.m4',
                        'm4/ltoptions.m4',
                        'm4/ltsugar.m4',
                        'm4/ltversion.m4',
                        'm4/lt~obsolete.m4',
                        'configure.ac'
                      ],
                      {
                        'AC_LIBTOOL_LINKER_OPTION' => 1,
                        '_AM_PROG_CC_C_O' => 1,
                        'AC_LIBTOOL_SYS_MAX_CMD_LEN' => 1,
                        'LT_CONFIG_LTDL_DIR' => 1,
                        'PKG_INSTALLDIR' => 1,
                        'AM_PROG_NM' => 1,
                        'LT_PATH_LD' => 1,
                        '_LT_PATH_TOOL_PREFIX' => 1,
                        'AM_PROG_CC_C_O' => 1,
                        'LT_WITH_LTDL' => 1,
                        'AM_MISSING_PROG' => 1,
                        'AC_LIBTOOL_PROG_LD_SHLIBS' => 1,
                        '_AM_IF_OPTION' => 1,
                        'LT_SYS_MODULE_EXT' => 1,
                        'AC_LTDL_SHLIBEXT' => 1,
                        'AC_LTDL_PREOPEN' => 1,
                        'AC_DEFUN' => 1,
                        '_LT_LIBOBJ' => 1,
                        'LTSUGAR_VERSION' => 1,
                        'AM_ENABLE_SHARED' => 1,
                        '_LT_AC_LANG_RC_CONFIG' => 1,
                        '_LT_AC_LANG_CXX' => 1,
                        'AC_DISABLE_STATIC' => 1,
                        'PKG_CHECK_MODULES' => 1,
                        '_LT_AC_CHECK_DLFCN' => 1,
                        'LTDL_INSTALLABLE' => 1,
                        'AC_LTDL_ENABLE_INSTALL' => 1,
                        'AC_LIBTOOL_SYS_DYNAMIC_LINKER' => 1,
                        'LT_PROG_RC' => 1,
                        '_LT_AC_PROG_CXXCPP' => 1,
                        'LT_OUTPUT' => 1,
                        '_AM_SET_OPTIONS' => 1,
                        'AX_CHECK_COMPILE_FLAG' => 1,
                        'AC_CONFIG_MACRO_DIR' => 1,
                        'AC_LIBTOOL_LANG_C_CONFIG' => 1,
                        'AC_LIBTOOL_CONFIG' => 1,
                        '_LT_LINKER_OPTION' => 1,
                        'AC_CHECK_LIBM' => 1,
                        'AC_LIBTOOL_OBJDIR' => 1,
                        'LTDL_CONVENIENCE' => 1,
                        'AC_LIBTOOL_LANG_GCJ_CONFIG' => 1,
                        'AC_LIBTOOL_CXX' => 1,
                        '_AM_MANGLE_OPTION' => 1,
                        '_LT_DLL_DEF_P' => 1,
                        'AC_LIBTOOL_SYS_OLD_ARCHIVE' => 1,
                        'AC_LIBTOOL_POSTDEP_PREDEP' => 1,
                        '_AM_SET_OPTION' => 1,
                        'AC_LIBTOOL_F77' => 1,
                        '_LT_LINKER_BOILERPLATE' => 1,
                        'AC_LIBTOOL_SYS_HARD_LINK_LOCKS' => 1,
                        'AC_DEFUN_ONCE' => 1,
                        '_LT_PROG_CXX' => 1,
                        'PKG_PROG_PKG_CONFIG' => 1,
                        'AC_LTDL_SYSSEARCHPATH' => 1,
                        '_PKG_SHORT_ERRORS_SUPPORTED' => 1,
                        'AC_LIBTOOL_WIN32_DLL' => 1,
                        'AC_LIBTOOL_COMPILER_OPTION' => 1,
                        'AC_LTDL_SYMBOL_USCORE' => 1,
                        'AC_LIBTOOL_PROG_COMPILER_PIC' => 1,
                        'LTOPTIONS_VERSION' => 1,
                        'LT_CMD_MAX_LEN' => 1,
                        'LT_SYS_DLOPEN_DEPLIBS' => 1,
                        'AC_LTDL_OBJDIR' => 1,
                        '_m4_warn' => 1,
                        'AM_CONDITIONAL' => 1,
                        'AC_LIBTOOL_PROG_COMPILER_NO_RTTI' => 1,
                        'AM_PROG_INSTALL_STRIP' => 1,
                        '_LT_AC_TRY_DLOPEN_SELF' => 1,
                        'LT_SYS_SYMBOL_USCORE' => 1,
                        'AC_LTDL_SYS_DLOPEN_DEPLIBS' => 1,
                        'm4_pattern_allow' => 1,
                        'LT_FUNC_ARGZ' => 1,
                        'AC_LIBTOOL_DLOPEN_SELF' => 1,
                        'AX_CXX_COMPILE_STDCXX' => 1,
                        '_LT_AC_LANG_CXX_CONFIG' => 1,
                        'AC_DISABLE_FAST_INSTALL' => 1,
                        '_LT_AC_SHELL_INIT' => 1,
                        'm4_include' => 1,
                        'LT_PROG_GCJ' => 1,
                        '_LT_CC_BASENAME' => 1,
                        'AM_SUBST_NOTMAKE' => 1,
                        'AC_LTDL_DLLIB' => 1,
                        'AC_PROG_NM' => 1,
                        'AM_MISSING_HAS_RUN' => 1,
                        '_LT_COMPILER_BOILERPLATE' => 1,
                        'AM_SET_LEADING_DOT' => 1,
                        'AC_ENABLE_SHARED' => 1,
                        'AM_DISABLE_STATIC' => 1,
                        'AC_PATH_TOOL_PREFIX' => 1,
                        'LT_LANG' => 1,
                        'AC_LIBLTDL_CONVENIENCE' => 1,
                        'PKG_NOARCH_INSTALLDIR' => 1,
                        '_LT_AC_LOCK' => 1,
                        'AC_LIBTOOL_DLOPEN' => 1,
                        'LT_SYS_DLOPEN_SELF' => 1,
                        '_LT_AC_FILE_LTDLL_C' => 1,
                        'AC_PATH_MAGIC' => 1,
                        'AC_DEPLIBS_CHECK_METHOD' => 1,
                        'AC_WITH_LTDL' => 1,
                        '_LT_PREPARE_SED_QUOTE_VARS' => 1,
                        'AC_LIBLTDL_INSTALLABLE' => 1,
                        'AM_PROG_LIBTOOL' => 1,
                        'AC_LTDL_DLSYM_USCORE' => 1,
                        '_LT_AC_TAGVAR' => 1,
                        'LT_SYS_MODULE_PATH' => 1,
                        '_LT_COMPILER_OPTION' => 1,
                        'LT_SYS_DLSEARCH_PATH' => 1,
                        'AC_LIBTOOL_FC' => 1,
                        'AC_LIBTOOL_LANG_CXX_CONFIG' => 1,
                        '_LT_AC_SYS_LIBPATH_AIX' => 1,
                        'LT_PROG_GO' => 1,
                        'PKG_CHECK_VAR' => 1,
                        'AC_ENABLE_FAST_INSTALL' => 1,
                        'AC_LIBTOOL_SYS_LIB_STRIP' => 1,
                        'PKG_CHECK_EXISTS' => 1,
                        '_LT_AC_PROG_ECHO_BACKSLASH' => 1,
                        'AC_ENABLE_STATIC' => 1,
                        'PKG_CHECK_MODULES_STATIC' => 1,
                        'AM_SET_DEPDIR' => 1,
                        '_LT_REQUIRED_DARWIN_CHECKS' => 1,
                        '_AM_OUTPUT_DEPENDENCY_COMMANDS' => 1,
                        'AC_LIBTOOL_PROG_CC_C_O' => 1,
                        'LTDL_INIT' => 1,
                        '_AC_PROG_LIBTOOL' => 1,
                        'LT_FUNC_DLSYM_USCORE' => 1,
                        'AM_DISABLE_SHARED' => 1,
                        'include' => 1,
                        'AC_PROG_EGREP' => 1,
                        '_LT_AC_LANG_GCJ' => 1,
                        'AM_MAKE_INCLUDE' => 1,
                        'LT_PATH_NM' => 1,
                        'AM_PROG_LD' => 1,
                        'AC_LIBTOOL_LANG_RC_CONFIG' => 1,
                        'LT_AC_PROG_GCJ' => 1,
                        '_LT_AC_LANG_F77' => 1,
                        '_AM_CONFIG_MACRO_DIRS' => 1,
                        'AM_INIT_AUTOMAKE' => 1,
                        'LT_AC_PROG_RC' => 1,
                        'AC_DISABLE_SHARED' => 1,
                        'AC_LIBTOOL_PROG_LD_HARDCODE_LIBPATH' => 1,
                        'AM_SILENT_RULES' => 1,
                        'AU_DEFUN' => 1,
                        '_LTDL_SETUP' => 1,
                        '_LT_AC_LANG_F77_CONFIG' => 1,
                        'AM_SET_CURRENT_AUTOMAKE_VERSION' => 1,
                        '_AM_AUTOCONF_VERSION' => 1,
                        'LT_LIB_M' => 1,
                        '_AC_AM_CONFIG_HEADER_HOOK' => 1,
                        'AC_LTDL_SHLIBPATH' => 1,
                        '_LT_AC_LANG_C_CONFIG' => 1,
                        'LT_SUPPORTED_TAG' => 1,
                        '_LT_PROG_LTMAIN' => 1,
                        'AC_LIBTOOL_SETUP' => 1,
                        'AC_LIBTOOL_LANG_F77_CONFIG' => 1,
                        'AM_OUTPUT_DEPENDENCY_COMMANDS' => 1,
                        '_AM_PROG_TAR' => 1,
                        '_LT_AC_LANG_GCJ_CONFIG' => 1,
                        'm4_pattern_forbid' => 1,
                        'LT_AC_PROG_EGREP' => 1,
                        'AC_PROG_LD_GNU' => 1,
                        'LT_AC_PROG_SED' => 1,
                        'AM_AUX_DIR_EXPAND' => 1,
                        '_AM_SUBST_NOTMAKE' => 1,
                        'AC_PROG_LD' => 1,
                        'AC_LIB_LTDL' => 1,
                        'AM_DEP_TRACK' => 1,
                        'AM_RUN_LOG' => 1,
                        'AM_SANITY_CHECK' => 1,
                        '_LT_PROG_ECHO_BACKSLASH' => 1,
                        '_LT_AC_TAGCONFIG' => 1,
                        '_AM_DEPENDENCIES' => 1,
                        'AC_PROG_LD_RELOAD_FLAG' => 1,
                        'AC_PROG_LIBTOOL' => 1,
                        'AC_LIBTOOL_RC' => 1,
                        'LT_LIB_DLLOAD' => 1,
                        '_LT_PROG_FC' => 1,
                        'AC_LIBTOOL_PICMODE' => 1,
                        '_LT_AC_SYS_COMPILER' => 1,
                        'AC_LIBTOOL_GCJ' => 1,
                        '_LT_WITH_SYSROOT' => 1,
                        'AM_PROG_INSTALL_SH' => 1,
                        'AM_AUTOMAKE_VERSION' => 1,
                        '_LT_PROG_F77' => 1,
                        'AM_ENABLE_STATIC' => 1,
                        'LT_INIT' => 1,
                        'LTVERSION_VERSION' => 1,
                        'AC_CONFIG_MACRO_DIR_TRACE' => 1,
                        'LTOBSOLETE_VERSION' => 1,
                        'AC_LIBTOOL_SYS_GLOBAL_SYMBOL_PIPE' => 1
                      }
                    ], 'Autom4te::Request' ),
             bless( [
                      '3',
                      1,
                      [
                        '/usr/share/autoconf'
                      ],
                      [
                        '/usr/share/autoconf/autoconf/autoconf.m4f',
                        'aclocal.m4',
                        '/usr/share/autoconf/autoconf/trailer.m4',
                        'configure.ac'
                      ],
                      {
                        'LT_CONFIG_LTDL_DIR' => 1,
                        'AM_SILENT_RULES' => 1,
                        'AC_INIT' => 1,
                        'sinclude' => 1,
                        '_AM_MAKEFILE_INCLUDE' => 1,
                        'AM_PROG_FC_C_O' => 1,
                        'AC_SUBST_TRACE' => 1,
                        'AM_PROG_CC_C_O' => 1,
                        'AM_PROG_AR' => 1,
                        'AM_PROG_MKDIR_P' => 1,
                        'AM_NLS' => 1,
                        'AM_ENABLE_MULTILIB' => 1,
                        'AM_PROG_LIBTOOL' => 1,
                        'AC_DEFINE_TRACE_LITERAL' => 1,
                        'AM_GNU_GETTEXT' => 1,
                        'AC_CANONICAL_TARGET' => 1,
                        'AC_CANONICAL_BUILD' => 1,
                        'AC_FC_SRCEXT' => 1,
                        'm4_pattern_forbid' => 1,
                        '_AM_COND_ELSE' => 1,
                        'LT_SUPPORTED_TAG' => 1,
                        'AM_CONDITIONAL' => 1,
                        '_m4_warn' => 1,
                        'GTK_DOC_CHECK' => 1,
                        'IT_PROG_INTLTOOL' => 1,
                        'AC_SUBST' => 1,
                        '_AM_SUBST_NOTMAKE' => 1,
                        'm4_sinclude' => 1,
                        'AC_CONFIG_SUBDIRS' => 1,
                        '_AM_COND_ENDIF' => 1,
                        'AM_PROG_MOC' => 1,
                        'AC_REQUIRE_AUX_FILE' => 1,
                        '_LT_AC_TAGCONFIG' => 1,
                        'm4_include' => 1,
                        'AC_CONFIG_FILES' => 1,
                        'AC_CONFIG_LINKS' => 1,
                        'm4_pattern_allow' => 1,
                        'AC_CONFIG_LIBOBJ_DIR' => 1,
                        'AM_PROG_F77_C_O' => 1,
                        'AM_MAINTAINER_MODE' => 1,
                        'include' => 1,
                        'AC_FC_PP_DEFINE' => 1,
                        'AC_CANONICAL_SYSTEM' => 1,
                        'AM_PATH_GUILE' => 1,
                        '_AM_COND_IF' => 1,
                        'AM_XGETTEXT_OPTION' => 1,
                        'AC_FC_PP_SRCEXT' => 1,
                        'AC_PROG_LIBTOOL' => 1,
                        'AC_CONFIG_HEADERS' => 1,
                        'AC_LIBSOURCE' => 1,
                        'LT_INIT' => 1,
                        'AM_GNU_GETTEXT_INTL_SUBDIR' => 1,
                        'AM_PROG_CXX_C_O' => 1,
                        'AC_FC_FREEFORM' => 1,
                        'AM_AUTOMAKE_VERSION' => 1,
                        'AC_CANONICAL_HOST' => 1,
                        'AM_POT_TOOLS' => 1,
                        'AM_INIT_AUTOMAKE' => 1,
                        'AC_CONFIG_AUX_DIR' => 1,
                        'AH_OUTPUT' => 1,
                        'AC_CONFIG_MACRO_DIR_TRACE' => 1,
                        'AM_MAKEFILE_INCLUDE' => 1,
                        'AM_EXTRA_RECURSIVE_TARGETS' => 1
                      }
                    ], 'Autom4te::Request' )
           );

