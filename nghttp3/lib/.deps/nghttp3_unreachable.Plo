nghttp3_unreachable.lo: nghttp3_unreachable.c /usr/include/stdc-predef.h \
 nghttp3_unreachable.h ../config.h includes/nghttp3/nghttp3.h \
 /usr/include/stdlib.h \
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
 /usr/include/features.h /usr/include/features-time64.h \
 /usr/include/x86_64-linux-gnu/bits/wordsize.h \
 /usr/include/x86_64-linux-gnu/bits/timesize.h \
 /usr/include/x86_64-linux-gnu/sys/cdefs.h \
 /usr/include/x86_64-linux-gnu/bits/long-double.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
 /usr/lib/gcc/x86_64-linux-gnu/11/include/stddef.h \
 /usr/include/x86_64-linux-gnu/bits/waitflags.h \
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h \
 /usr/include/x86_64-linux-gnu/bits/floatn.h \
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
 /usr/include/x86_64-linux-gnu/sys/types.h \
 /usr/include/x86_64-linux-gnu/bits/types.h \
 /usr/include/x86_64-linux-gnu/bits/typesizes.h \
 /usr/include/x86_64-linux-gnu/bits/time64.h \
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h /usr/include/endian.h \
 /usr/include/x86_64-linux-gnu/bits/endian.h \
 /usr/include/x86_64-linux-gnu/bits/endianness.h \
 /usr/include/x86_64-linux-gnu/bits/byteswap.h \
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
 /usr/include/x86_64-linux-gnu/sys/select.h \
 /usr/include/x86_64-linux-gnu/bits/select.h \
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
 /usr/include/x86_64-linux-gnu/bits/select2.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
 /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h \
 /usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
 /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h /usr/include/alloca.h \
 /usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h \
 /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
 /usr/include/x86_64-linux-gnu/bits/stdlib.h /usr/include/inttypes.h \
 /usr/lib/gcc/x86_64-linux-gnu/11/include/stdint.h /usr/include/stdint.h \
 /usr/include/x86_64-linux-gnu/bits/wchar.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h \
 /usr/lib/gcc/x86_64-linux-gnu/11/include/stdarg.h \
 includes/nghttp3/version.h /usr/include/stdio.h \
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h \
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
 /usr/include/x86_64-linux-gnu/bits/stdio.h \
 /usr/include/x86_64-linux-gnu/bits/stdio2.h /usr/include/errno.h \
 /usr/include/x86_64-linux-gnu/bits/errno.h /usr/include/linux/errno.h \
 /usr/include/x86_64-linux-gnu/asm/errno.h \
 /usr/include/asm-generic/errno.h /usr/include/asm-generic/errno-base.h \
 /usr/include/x86_64-linux-gnu/bits/types/error_t.h /usr/include/unistd.h \
 /usr/include/x86_64-linux-gnu/bits/posix_opt.h \
 /usr/include/x86_64-linux-gnu/bits/environments.h \
 /usr/include/x86_64-linux-gnu/bits/confname.h \
 /usr/include/x86_64-linux-gnu/bits/getopt_posix.h \
 /usr/include/x86_64-linux-gnu/bits/getopt_core.h \
 /usr/include/x86_64-linux-gnu/bits/unistd.h \
 /usr/include/x86_64-linux-gnu/bits/unistd_ext.h \
 /usr/include/linux/close_range.h
/usr/include/stdc-predef.h:
nghttp3_unreachable.h:
../config.h:
includes/nghttp3/nghttp3.h:
/usr/include/stdlib.h:
/usr/include/x86_64-linux-gnu/bits/libc-header-start.h:
/usr/include/features.h:
/usr/include/features-time64.h:
/usr/include/x86_64-linux-gnu/bits/wordsize.h:
/usr/include/x86_64-linux-gnu/bits/timesize.h:
/usr/include/x86_64-linux-gnu/sys/cdefs.h:
/usr/include/x86_64-linux-gnu/bits/long-double.h:
/usr/include/x86_64-linux-gnu/gnu/stubs.h:
/usr/include/x86_64-linux-gnu/gnu/stubs-64.h:
/usr/lib/gcc/x86_64-linux-gnu/11/include/stddef.h:
/usr/include/x86_64-linux-gnu/bits/waitflags.h:
/usr/include/x86_64-linux-gnu/bits/waitstatus.h:
/usr/include/x86_64-linux-gnu/bits/floatn.h:
/usr/include/x86_64-linux-gnu/bits/floatn-common.h:
/usr/include/x86_64-linux-gnu/bits/types/locale_t.h:
/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h:
/usr/include/x86_64-linux-gnu/sys/types.h:
/usr/include/x86_64-linux-gnu/bits/types.h:
/usr/include/x86_64-linux-gnu/bits/typesizes.h:
/usr/include/x86_64-linux-gnu/bits/time64.h:
/usr/include/x86_64-linux-gnu/bits/types/clock_t.h:
/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h:
/usr/include/x86_64-linux-gnu/bits/types/time_t.h:
/usr/include/x86_64-linux-gnu/bits/types/timer_t.h:
/usr/include/x86_64-linux-gnu/bits/stdint-intn.h:
/usr/include/endian.h:
/usr/include/x86_64-linux-gnu/bits/endian.h:
/usr/include/x86_64-linux-gnu/bits/endianness.h:
/usr/include/x86_64-linux-gnu/bits/byteswap.h:
/usr/include/x86_64-linux-gnu/bits/uintn-identity.h:
/usr/include/x86_64-linux-gnu/sys/select.h:
/usr/include/x86_64-linux-gnu/bits/select.h:
/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h:
/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h:
/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h:
/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h:
/usr/include/x86_64-linux-gnu/bits/select2.h:
/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h:
/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h:
/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h:
/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h:
/usr/include/x86_64-linux-gnu/bits/struct_mutex.h:
/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h:
/usr/include/alloca.h:
/usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h:
/usr/include/x86_64-linux-gnu/bits/stdlib-float.h:
/usr/include/x86_64-linux-gnu/bits/stdlib.h:
/usr/include/inttypes.h:
/usr/lib/gcc/x86_64-linux-gnu/11/include/stdint.h:
/usr/include/stdint.h:
/usr/include/x86_64-linux-gnu/bits/wchar.h:
/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h:
/usr/lib/gcc/x86_64-linux-gnu/11/include/stdarg.h:
includes/nghttp3/version.h:
/usr/include/stdio.h:
/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h:
/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h:
/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h:
/usr/include/x86_64-linux-gnu/bits/types/__FILE.h:
/usr/include/x86_64-linux-gnu/bits/types/FILE.h:
/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h:
/usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h:
/usr/include/x86_64-linux-gnu/bits/stdio_lim.h:
/usr/include/x86_64-linux-gnu/bits/stdio.h:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:
/usr/include/errno.h:
/usr/include/x86_64-linux-gnu/bits/errno.h:
/usr/include/linux/errno.h:
/usr/include/x86_64-linux-gnu/asm/errno.h:
/usr/include/asm-generic/errno.h:
/usr/include/asm-generic/errno-base.h:
/usr/include/x86_64-linux-gnu/bits/types/error_t.h:
/usr/include/unistd.h:
/usr/include/x86_64-linux-gnu/bits/posix_opt.h:
/usr/include/x86_64-linux-gnu/bits/environments.h:
/usr/include/x86_64-linux-gnu/bits/confname.h:
/usr/include/x86_64-linux-gnu/bits/getopt_posix.h:
/usr/include/x86_64-linux-gnu/bits/getopt_core.h:
/usr/include/x86_64-linux-gnu/bits/unistd.h:
/usr/include/x86_64-linux-gnu/bits/unistd_ext.h:
/usr/include/linux/close_range.h:
