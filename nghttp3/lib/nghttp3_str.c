/*
 * nghttp3
 *
 * Copyright (c) 2019 nghttp3 contributors
 * Copyright (c) 2017 ngtcp2 contributors
 * Copyright (c) 2012 nghttp2 contributors
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
#include "nghttp3_str.h"

#include <string.h>
#include <assert.h>

uint8_t *nghttp3_cpymem(uint8_t *dest, const uint8_t *src, size_t n) {
  memcpy(dest, src, n);
  return dest + n;
}

/* Generated by gendowncasetbl.py */
static const uint8_t DOWNCASE_TBL[] = {
  0 /* NUL  */,   1 /* SOH  */,   2 /* STX  */,   3 /* ETX  */,
  4 /* EOT  */,   5 /* ENQ  */,   6 /* ACK  */,   7 /* BEL  */,
  8 /* BS   */,   9 /* HT   */,   10 /* LF   */,  11 /* VT   */,
  12 /* FF   */,  13 /* CR   */,  14 /* SO   */,  15 /* SI   */,
  16 /* DLE  */,  17 /* DC1  */,  18 /* DC2  */,  19 /* DC3  */,
  20 /* DC4  */,  21 /* NAK  */,  22 /* SYN  */,  23 /* ETB  */,
  24 /* CAN  */,  25 /* EM   */,  26 /* SUB  */,  27 /* ESC  */,
  28 /* FS   */,  29 /* GS   */,  30 /* RS   */,  31 /* US   */,
  32 /* SPC  */,  33 /* !    */,  34 /* "    */,  35 /* #    */,
  36 /* $    */,  37 /* %    */,  38 /* &    */,  39 /* '    */,
  40 /* (    */,  41 /* )    */,  42 /* *    */,  43 /* +    */,
  44 /* ,    */,  45 /* -    */,  46 /* .    */,  47 /* /    */,
  48 /* 0    */,  49 /* 1    */,  50 /* 2    */,  51 /* 3    */,
  52 /* 4    */,  53 /* 5    */,  54 /* 6    */,  55 /* 7    */,
  56 /* 8    */,  57 /* 9    */,  58 /* :    */,  59 /* ;    */,
  60 /* <    */,  61 /* =    */,  62 /* >    */,  63 /* ?    */,
  64 /* @    */,  97 /* A    */,  98 /* B    */,  99 /* C    */,
  100 /* D    */, 101 /* E    */, 102 /* F    */, 103 /* G    */,
  104 /* H    */, 105 /* I    */, 106 /* J    */, 107 /* K    */,
  108 /* L    */, 109 /* M    */, 110 /* N    */, 111 /* O    */,
  112 /* P    */, 113 /* Q    */, 114 /* R    */, 115 /* S    */,
  116 /* T    */, 117 /* U    */, 118 /* V    */, 119 /* W    */,
  120 /* X    */, 121 /* Y    */, 122 /* Z    */, 91 /* [    */,
  92 /* \    */,  93 /* ]    */,  94 /* ^    */,  95 /* _    */,
  96 /* `    */,  97 /* a    */,  98 /* b    */,  99 /* c    */,
  100 /* d    */, 101 /* e    */, 102 /* f    */, 103 /* g    */,
  104 /* h    */, 105 /* i    */, 106 /* j    */, 107 /* k    */,
  108 /* l    */, 109 /* m    */, 110 /* n    */, 111 /* o    */,
  112 /* p    */, 113 /* q    */, 114 /* r    */, 115 /* s    */,
  116 /* t    */, 117 /* u    */, 118 /* v    */, 119 /* w    */,
  120 /* x    */, 121 /* y    */, 122 /* z    */, 123 /* {    */,
  124 /* |    */, 125 /* }    */, 126 /* ~    */, 127 /* DEL  */,
  128 /* 0x80 */, 129 /* 0x81 */, 130 /* 0x82 */, 131 /* 0x83 */,
  132 /* 0x84 */, 133 /* 0x85 */, 134 /* 0x86 */, 135 /* 0x87 */,
  136 /* 0x88 */, 137 /* 0x89 */, 138 /* 0x8a */, 139 /* 0x8b */,
  140 /* 0x8c */, 141 /* 0x8d */, 142 /* 0x8e */, 143 /* 0x8f */,
  144 /* 0x90 */, 145 /* 0x91 */, 146 /* 0x92 */, 147 /* 0x93 */,
  148 /* 0x94 */, 149 /* 0x95 */, 150 /* 0x96 */, 151 /* 0x97 */,
  152 /* 0x98 */, 153 /* 0x99 */, 154 /* 0x9a */, 155 /* 0x9b */,
  156 /* 0x9c */, 157 /* 0x9d */, 158 /* 0x9e */, 159 /* 0x9f */,
  160 /* 0xa0 */, 161 /* 0xa1 */, 162 /* 0xa2 */, 163 /* 0xa3 */,
  164 /* 0xa4 */, 165 /* 0xa5 */, 166 /* 0xa6 */, 167 /* 0xa7 */,
  168 /* 0xa8 */, 169 /* 0xa9 */, 170 /* 0xaa */, 171 /* 0xab */,
  172 /* 0xac */, 173 /* 0xad */, 174 /* 0xae */, 175 /* 0xaf */,
  176 /* 0xb0 */, 177 /* 0xb1 */, 178 /* 0xb2 */, 179 /* 0xb3 */,
  180 /* 0xb4 */, 181 /* 0xb5 */, 182 /* 0xb6 */, 183 /* 0xb7 */,
  184 /* 0xb8 */, 185 /* 0xb9 */, 186 /* 0xba */, 187 /* 0xbb */,
  188 /* 0xbc */, 189 /* 0xbd */, 190 /* 0xbe */, 191 /* 0xbf */,
  192 /* 0xc0 */, 193 /* 0xc1 */, 194 /* 0xc2 */, 195 /* 0xc3 */,
  196 /* 0xc4 */, 197 /* 0xc5 */, 198 /* 0xc6 */, 199 /* 0xc7 */,
  200 /* 0xc8 */, 201 /* 0xc9 */, 202 /* 0xca */, 203 /* 0xcb */,
  204 /* 0xcc */, 205 /* 0xcd */, 206 /* 0xce */, 207 /* 0xcf */,
  208 /* 0xd0 */, 209 /* 0xd1 */, 210 /* 0xd2 */, 211 /* 0xd3 */,
  212 /* 0xd4 */, 213 /* 0xd5 */, 214 /* 0xd6 */, 215 /* 0xd7 */,
  216 /* 0xd8 */, 217 /* 0xd9 */, 218 /* 0xda */, 219 /* 0xdb */,
  220 /* 0xdc */, 221 /* 0xdd */, 222 /* 0xde */, 223 /* 0xdf */,
  224 /* 0xe0 */, 225 /* 0xe1 */, 226 /* 0xe2 */, 227 /* 0xe3 */,
  228 /* 0xe4 */, 229 /* 0xe5 */, 230 /* 0xe6 */, 231 /* 0xe7 */,
  232 /* 0xe8 */, 233 /* 0xe9 */, 234 /* 0xea */, 235 /* 0xeb */,
  236 /* 0xec */, 237 /* 0xed */, 238 /* 0xee */, 239 /* 0xef */,
  240 /* 0xf0 */, 241 /* 0xf1 */, 242 /* 0xf2 */, 243 /* 0xf3 */,
  244 /* 0xf4 */, 245 /* 0xf5 */, 246 /* 0xf6 */, 247 /* 0xf7 */,
  248 /* 0xf8 */, 249 /* 0xf9 */, 250 /* 0xfa */, 251 /* 0xfb */,
  252 /* 0xfc */, 253 /* 0xfd */, 254 /* 0xfe */, 255 /* 0xff */,
};

void nghttp3_downcase(uint8_t *s, size_t len) {
  size_t i;
  for (i = 0; i < len; ++i) {
    s[i] = DOWNCASE_TBL[s[i]];
  }
}
