# libnghttp3.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.6 Debian-2.4.6-15build2
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libnghttp3.so.9'

# Names of this library.
library_names='libnghttp3.so.9.2.9 libnghttp3.so.9 libnghttp3.so'

# The name of the static archive.
old_library='libnghttp3.a'

# Linker flags that cannot go in dependency_libs.
inherited_linker_flags=''

# Libraries that this one depends upon.
dependency_libs=''

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libnghttp3.
current=11
age=2
revision=9

# Is this an already installed library?
installed=no

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/home/<USER>/ngtcp2_qpack/nghttp3/build/lib'
