/*
 * nghttp3
 *
 * Copyright (c) 2019 nghttp3 contributors
 * Copyright (c) 2013 nghttp2 contributors
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
#include "nghttp3_qpack_huffman.h"

/* Generated by mkhufftbl.py */

const nghttp3_qpack_huffman_sym huffman_sym_table[] = {
  {13, 0xffc00000u}, {23, 0xffffb000u}, {28, 0xfffffe20u}, {28, 0xfffffe30u},
  {28, 0xfffffe40u}, {28, 0xfffffe50u}, {28, 0xfffffe60u}, {28, 0xfffffe70u},
  {28, 0xfffffe80u}, {24, 0xffffea00u}, {30, 0xfffffff0u}, {28, 0xfffffe90u},
  {28, 0xfffffea0u}, {30, 0xfffffff4u}, {28, 0xfffffeb0u}, {28, 0xfffffec0u},
  {28, 0xfffffed0u}, {28, 0xfffffee0u}, {28, 0xfffffef0u}, {28, 0xffffff00u},
  {28, 0xffffff10u}, {28, 0xffffff20u}, {30, 0xfffffff8u}, {28, 0xffffff30u},
  {28, 0xffffff40u}, {28, 0xffffff50u}, {28, 0xffffff60u}, {28, 0xffffff70u},
  {28, 0xffffff80u}, {28, 0xffffff90u}, {28, 0xffffffa0u}, {28, 0xffffffb0u},
  {6, 0x50000000u},  {10, 0xfe000000u}, {10, 0xfe400000u}, {12, 0xffa00000u},
  {13, 0xffc80000u}, {6, 0x54000000u},  {8, 0xf8000000u},  {11, 0xff400000u},
  {10, 0xfe800000u}, {10, 0xfec00000u}, {8, 0xf9000000u},  {11, 0xff600000u},
  {8, 0xfa000000u},  {6, 0x58000000u},  {6, 0x5c000000u},  {6, 0x60000000u},
  {5, 0x0u},         {5, 0x8000000u},   {5, 0x10000000u},  {6, 0x64000000u},
  {6, 0x68000000u},  {6, 0x6c000000u},  {6, 0x70000000u},  {6, 0x74000000u},
  {6, 0x78000000u},  {6, 0x7c000000u},  {7, 0xb8000000u},  {8, 0xfb000000u},
  {15, 0xfff80000u}, {6, 0x80000000u},  {12, 0xffb00000u}, {10, 0xff000000u},
  {13, 0xffd00000u}, {6, 0x84000000u},  {7, 0xba000000u},  {7, 0xbc000000u},
  {7, 0xbe000000u},  {7, 0xc0000000u},  {7, 0xc2000000u},  {7, 0xc4000000u},
  {7, 0xc6000000u},  {7, 0xc8000000u},  {7, 0xca000000u},  {7, 0xcc000000u},
  {7, 0xce000000u},  {7, 0xd0000000u},  {7, 0xd2000000u},  {7, 0xd4000000u},
  {7, 0xd6000000u},  {7, 0xd8000000u},  {7, 0xda000000u},  {7, 0xdc000000u},
  {7, 0xde000000u},  {7, 0xe0000000u},  {7, 0xe2000000u},  {7, 0xe4000000u},
  {8, 0xfc000000u},  {7, 0xe6000000u},  {8, 0xfd000000u},  {13, 0xffd80000u},
  {19, 0xfffe0000u}, {13, 0xffe00000u}, {14, 0xfff00000u}, {6, 0x88000000u},
  {15, 0xfffa0000u}, {5, 0x18000000u},  {6, 0x8c000000u},  {5, 0x20000000u},
  {6, 0x90000000u},  {5, 0x28000000u},  {6, 0x94000000u},  {6, 0x98000000u},
  {6, 0x9c000000u},  {5, 0x30000000u},  {7, 0xe8000000u},  {7, 0xea000000u},
  {6, 0xa0000000u},  {6, 0xa4000000u},  {6, 0xa8000000u},  {5, 0x38000000u},
  {6, 0xac000000u},  {7, 0xec000000u},  {6, 0xb0000000u},  {5, 0x40000000u},
  {5, 0x48000000u},  {6, 0xb4000000u},  {7, 0xee000000u},  {7, 0xf0000000u},
  {7, 0xf2000000u},  {7, 0xf4000000u},  {7, 0xf6000000u},  {15, 0xfffc0000u},
  {11, 0xff800000u}, {14, 0xfff40000u}, {13, 0xffe80000u}, {28, 0xffffffc0u},
  {20, 0xfffe6000u}, {22, 0xffff4800u}, {20, 0xfffe7000u}, {20, 0xfffe8000u},
  {22, 0xffff4c00u}, {22, 0xffff5000u}, {22, 0xffff5400u}, {23, 0xffffb200u},
  {22, 0xffff5800u}, {23, 0xffffb400u}, {23, 0xffffb600u}, {23, 0xffffb800u},
  {23, 0xffffba00u}, {23, 0xffffbc00u}, {24, 0xffffeb00u}, {23, 0xffffbe00u},
  {24, 0xffffec00u}, {24, 0xffffed00u}, {22, 0xffff5c00u}, {23, 0xffffc000u},
  {24, 0xffffee00u}, {23, 0xffffc200u}, {23, 0xffffc400u}, {23, 0xffffc600u},
  {23, 0xffffc800u}, {21, 0xfffee000u}, {22, 0xffff6000u}, {23, 0xffffca00u},
  {22, 0xffff6400u}, {23, 0xffffcc00u}, {23, 0xffffce00u}, {24, 0xffffef00u},
  {22, 0xffff6800u}, {21, 0xfffee800u}, {20, 0xfffe9000u}, {22, 0xffff6c00u},
  {22, 0xffff7000u}, {23, 0xffffd000u}, {23, 0xffffd200u}, {21, 0xfffef000u},
  {23, 0xffffd400u}, {22, 0xffff7400u}, {22, 0xffff7800u}, {24, 0xfffff000u},
  {21, 0xfffef800u}, {22, 0xffff7c00u}, {23, 0xffffd600u}, {23, 0xffffd800u},
  {21, 0xffff0000u}, {21, 0xffff0800u}, {22, 0xffff8000u}, {21, 0xffff1000u},
  {23, 0xffffda00u}, {22, 0xffff8400u}, {23, 0xffffdc00u}, {23, 0xffffde00u},
  {20, 0xfffea000u}, {22, 0xffff8800u}, {22, 0xffff8c00u}, {22, 0xffff9000u},
  {23, 0xffffe000u}, {22, 0xffff9400u}, {22, 0xffff9800u}, {23, 0xffffe200u},
  {26, 0xfffff800u}, {26, 0xfffff840u}, {20, 0xfffeb000u}, {19, 0xfffe2000u},
  {22, 0xffff9c00u}, {23, 0xffffe400u}, {22, 0xffffa000u}, {25, 0xfffff600u},
  {26, 0xfffff880u}, {26, 0xfffff8c0u}, {26, 0xfffff900u}, {27, 0xfffffbc0u},
  {27, 0xfffffbe0u}, {26, 0xfffff940u}, {24, 0xfffff100u}, {25, 0xfffff680u},
  {19, 0xfffe4000u}, {21, 0xffff1800u}, {26, 0xfffff980u}, {27, 0xfffffc00u},
  {27, 0xfffffc20u}, {26, 0xfffff9c0u}, {27, 0xfffffc40u}, {24, 0xfffff200u},
  {21, 0xffff2000u}, {21, 0xffff2800u}, {26, 0xfffffa00u}, {26, 0xfffffa40u},
  {28, 0xffffffd0u}, {27, 0xfffffc60u}, {27, 0xfffffc80u}, {27, 0xfffffca0u},
  {20, 0xfffec000u}, {24, 0xfffff300u}, {20, 0xfffed000u}, {21, 0xffff3000u},
  {22, 0xffffa400u}, {21, 0xffff3800u}, {21, 0xffff4000u}, {23, 0xffffe600u},
  {22, 0xffffa800u}, {22, 0xffffac00u}, {25, 0xfffff700u}, {25, 0xfffff780u},
  {24, 0xfffff400u}, {24, 0xfffff500u}, {26, 0xfffffa80u}, {23, 0xffffe800u},
  {26, 0xfffffac0u}, {27, 0xfffffcc0u}, {26, 0xfffffb00u}, {26, 0xfffffb40u},
  {27, 0xfffffce0u}, {27, 0xfffffd00u}, {27, 0xfffffd20u}, {27, 0xfffffd40u},
  {27, 0xfffffd60u}, {28, 0xffffffe0u}, {27, 0xfffffd80u}, {27, 0xfffffda0u},
  {27, 0xfffffdc0u}, {27, 0xfffffde0u}, {27, 0xfffffe00u}, {26, 0xfffffb80u},
  {30, 0xfffffffcu}};

const nghttp3_qpack_huffman_decode_node qpack_huffman_decode_table[][16] = {
  /* 0 */
  {
    {0x04, 0},
    {0x05, 0},
    {0x07, 0},
    {0x08, 0},
    {0x0b, 0},
    {0x0c, 0},
    {0x10, 0},
    {0x13, 0},
    {0x19, 0},
    {0x1c, 0},
    {0x20, 0},
    {0x23, 0},
    {0x2a, 0},
    {0x31, 0},
    {0x39, 0},
    {0x4040, 0},
  },
  /* 1 */
  {
    {0xc000, 48},
    {0xc000, 49},
    {0xc000, 50},
    {0xc000, 97},
    {0xc000, 99},
    {0xc000, 101},
    {0xc000, 105},
    {0xc000, 111},
    {0xc000, 115},
    {0xc000, 116},
    {0x0d, 0},
    {0x0e, 0},
    {0x11, 0},
    {0x12, 0},
    {0x14, 0},
    {0x15, 0},
  },
  /* 2 */
  {
    {0x8001, 48},
    {0xc016, 48},
    {0x8001, 49},
    {0xc016, 49},
    {0x8001, 50},
    {0xc016, 50},
    {0x8001, 97},
    {0xc016, 97},
    {0x8001, 99},
    {0xc016, 99},
    {0x8001, 101},
    {0xc016, 101},
    {0x8001, 105},
    {0xc016, 105},
    {0x8001, 111},
    {0xc016, 111},
  },
  /* 3 */
  {
    {0x8002, 48},
    {0x8009, 48},
    {0x8017, 48},
    {0xc028, 48},
    {0x8002, 49},
    {0x8009, 49},
    {0x8017, 49},
    {0xc028, 49},
    {0x8002, 50},
    {0x8009, 50},
    {0x8017, 50},
    {0xc028, 50},
    {0x8002, 97},
    {0x8009, 97},
    {0x8017, 97},
    {0xc028, 97},
  },
  /* 4 */
  {
    {0x8003, 48},
    {0x8006, 48},
    {0x800a, 48},
    {0x800f, 48},
    {0x8018, 48},
    {0x801f, 48},
    {0x8029, 48},
    {0xc038, 48},
    {0x8003, 49},
    {0x8006, 49},
    {0x800a, 49},
    {0x800f, 49},
    {0x8018, 49},
    {0x801f, 49},
    {0x8029, 49},
    {0xc038, 49},
  },
  /* 5 */
  {
    {0x8003, 50},
    {0x8006, 50},
    {0x800a, 50},
    {0x800f, 50},
    {0x8018, 50},
    {0x801f, 50},
    {0x8029, 50},
    {0xc038, 50},
    {0x8003, 97},
    {0x8006, 97},
    {0x800a, 97},
    {0x800f, 97},
    {0x8018, 97},
    {0x801f, 97},
    {0x8029, 97},
    {0xc038, 97},
  },
  /* 6 */
  {
    {0x8002, 99},
    {0x8009, 99},
    {0x8017, 99},
    {0xc028, 99},
    {0x8002, 101},
    {0x8009, 101},
    {0x8017, 101},
    {0xc028, 101},
    {0x8002, 105},
    {0x8009, 105},
    {0x8017, 105},
    {0xc028, 105},
    {0x8002, 111},
    {0x8009, 111},
    {0x8017, 111},
    {0xc028, 111},
  },
  /* 7 */
  {
    {0x8003, 99},
    {0x8006, 99},
    {0x800a, 99},
    {0x800f, 99},
    {0x8018, 99},
    {0x801f, 99},
    {0x8029, 99},
    {0xc038, 99},
    {0x8003, 101},
    {0x8006, 101},
    {0x800a, 101},
    {0x800f, 101},
    {0x8018, 101},
    {0x801f, 101},
    {0x8029, 101},
    {0xc038, 101},
  },
  /* 8 */
  {
    {0x8003, 105},
    {0x8006, 105},
    {0x800a, 105},
    {0x800f, 105},
    {0x8018, 105},
    {0x801f, 105},
    {0x8029, 105},
    {0xc038, 105},
    {0x8003, 111},
    {0x8006, 111},
    {0x800a, 111},
    {0x800f, 111},
    {0x8018, 111},
    {0x801f, 111},
    {0x8029, 111},
    {0xc038, 111},
  },
  /* 9 */
  {
    {0x8001, 115},
    {0xc016, 115},
    {0x8001, 116},
    {0xc016, 116},
    {0xc000, 32},
    {0xc000, 37},
    {0xc000, 45},
    {0xc000, 46},
    {0xc000, 47},
    {0xc000, 51},
    {0xc000, 52},
    {0xc000, 53},
    {0xc000, 54},
    {0xc000, 55},
    {0xc000, 56},
    {0xc000, 57},
  },
  /* 10 */
  {
    {0x8002, 115},
    {0x8009, 115},
    {0x8017, 115},
    {0xc028, 115},
    {0x8002, 116},
    {0x8009, 116},
    {0x8017, 116},
    {0xc028, 116},
    {0x8001, 32},
    {0xc016, 32},
    {0x8001, 37},
    {0xc016, 37},
    {0x8001, 45},
    {0xc016, 45},
    {0x8001, 46},
    {0xc016, 46},
  },
  /* 11 */
  {
    {0x8003, 115},
    {0x8006, 115},
    {0x800a, 115},
    {0x800f, 115},
    {0x8018, 115},
    {0x801f, 115},
    {0x8029, 115},
    {0xc038, 115},
    {0x8003, 116},
    {0x8006, 116},
    {0x800a, 116},
    {0x800f, 116},
    {0x8018, 116},
    {0x801f, 116},
    {0x8029, 116},
    {0xc038, 116},
  },
  /* 12 */
  {
    {0x8002, 32},
    {0x8009, 32},
    {0x8017, 32},
    {0xc028, 32},
    {0x8002, 37},
    {0x8009, 37},
    {0x8017, 37},
    {0xc028, 37},
    {0x8002, 45},
    {0x8009, 45},
    {0x8017, 45},
    {0xc028, 45},
    {0x8002, 46},
    {0x8009, 46},
    {0x8017, 46},
    {0xc028, 46},
  },
  /* 13 */
  {
    {0x8003, 32},
    {0x8006, 32},
    {0x800a, 32},
    {0x800f, 32},
    {0x8018, 32},
    {0x801f, 32},
    {0x8029, 32},
    {0xc038, 32},
    {0x8003, 37},
    {0x8006, 37},
    {0x800a, 37},
    {0x800f, 37},
    {0x8018, 37},
    {0x801f, 37},
    {0x8029, 37},
    {0xc038, 37},
  },
  /* 14 */
  {
    {0x8003, 45},
    {0x8006, 45},
    {0x800a, 45},
    {0x800f, 45},
    {0x8018, 45},
    {0x801f, 45},
    {0x8029, 45},
    {0xc038, 45},
    {0x8003, 46},
    {0x8006, 46},
    {0x800a, 46},
    {0x800f, 46},
    {0x8018, 46},
    {0x801f, 46},
    {0x8029, 46},
    {0xc038, 46},
  },
  /* 15 */
  {
    {0x8001, 47},
    {0xc016, 47},
    {0x8001, 51},
    {0xc016, 51},
    {0x8001, 52},
    {0xc016, 52},
    {0x8001, 53},
    {0xc016, 53},
    {0x8001, 54},
    {0xc016, 54},
    {0x8001, 55},
    {0xc016, 55},
    {0x8001, 56},
    {0xc016, 56},
    {0x8001, 57},
    {0xc016, 57},
  },
  /* 16 */
  {
    {0x8002, 47},
    {0x8009, 47},
    {0x8017, 47},
    {0xc028, 47},
    {0x8002, 51},
    {0x8009, 51},
    {0x8017, 51},
    {0xc028, 51},
    {0x8002, 52},
    {0x8009, 52},
    {0x8017, 52},
    {0xc028, 52},
    {0x8002, 53},
    {0x8009, 53},
    {0x8017, 53},
    {0xc028, 53},
  },
  /* 17 */
  {
    {0x8003, 47},
    {0x8006, 47},
    {0x800a, 47},
    {0x800f, 47},
    {0x8018, 47},
    {0x801f, 47},
    {0x8029, 47},
    {0xc038, 47},
    {0x8003, 51},
    {0x8006, 51},
    {0x800a, 51},
    {0x800f, 51},
    {0x8018, 51},
    {0x801f, 51},
    {0x8029, 51},
    {0xc038, 51},
  },
  /* 18 */
  {
    {0x8003, 52},
    {0x8006, 52},
    {0x800a, 52},
    {0x800f, 52},
    {0x8018, 52},
    {0x801f, 52},
    {0x8029, 52},
    {0xc038, 52},
    {0x8003, 53},
    {0x8006, 53},
    {0x800a, 53},
    {0x800f, 53},
    {0x8018, 53},
    {0x801f, 53},
    {0x8029, 53},
    {0xc038, 53},
  },
  /* 19 */
  {
    {0x8002, 54},
    {0x8009, 54},
    {0x8017, 54},
    {0xc028, 54},
    {0x8002, 55},
    {0x8009, 55},
    {0x8017, 55},
    {0xc028, 55},
    {0x8002, 56},
    {0x8009, 56},
    {0x8017, 56},
    {0xc028, 56},
    {0x8002, 57},
    {0x8009, 57},
    {0x8017, 57},
    {0xc028, 57},
  },
  /* 20 */
  {
    {0x8003, 54},
    {0x8006, 54},
    {0x800a, 54},
    {0x800f, 54},
    {0x8018, 54},
    {0x801f, 54},
    {0x8029, 54},
    {0xc038, 54},
    {0x8003, 55},
    {0x8006, 55},
    {0x800a, 55},
    {0x800f, 55},
    {0x8018, 55},
    {0x801f, 55},
    {0x8029, 55},
    {0xc038, 55},
  },
  /* 21 */
  {
    {0x8003, 56},
    {0x8006, 56},
    {0x800a, 56},
    {0x800f, 56},
    {0x8018, 56},
    {0x801f, 56},
    {0x8029, 56},
    {0xc038, 56},
    {0x8003, 57},
    {0x8006, 57},
    {0x800a, 57},
    {0x800f, 57},
    {0x8018, 57},
    {0x801f, 57},
    {0x8029, 57},
    {0xc038, 57},
  },
  /* 22 */
  {
    {0x1a, 0},
    {0x1b, 0},
    {0x1d, 0},
    {0x1e, 0},
    {0x21, 0},
    {0x22, 0},
    {0x24, 0},
    {0x25, 0},
    {0x2b, 0},
    {0x2e, 0},
    {0x32, 0},
    {0x35, 0},
    {0x3a, 0},
    {0x3d, 0},
    {0x41, 0},
    {0x4044, 0},
  },
  /* 23 */
  {
    {0xc000, 61},
    {0xc000, 65},
    {0xc000, 95},
    {0xc000, 98},
    {0xc000, 100},
    {0xc000, 102},
    {0xc000, 103},
    {0xc000, 104},
    {0xc000, 108},
    {0xc000, 109},
    {0xc000, 110},
    {0xc000, 112},
    {0xc000, 114},
    {0xc000, 117},
    {0x26, 0},
    {0x27, 0},
  },
  /* 24 */
  {
    {0x8001, 61},
    {0xc016, 61},
    {0x8001, 65},
    {0xc016, 65},
    {0x8001, 95},
    {0xc016, 95},
    {0x8001, 98},
    {0xc016, 98},
    {0x8001, 100},
    {0xc016, 100},
    {0x8001, 102},
    {0xc016, 102},
    {0x8001, 103},
    {0xc016, 103},
    {0x8001, 104},
    {0xc016, 104},
  },
  /* 25 */
  {
    {0x8002, 61},
    {0x8009, 61},
    {0x8017, 61},
    {0xc028, 61},
    {0x8002, 65},
    {0x8009, 65},
    {0x8017, 65},
    {0xc028, 65},
    {0x8002, 95},
    {0x8009, 95},
    {0x8017, 95},
    {0xc028, 95},
    {0x8002, 98},
    {0x8009, 98},
    {0x8017, 98},
    {0xc028, 98},
  },
  /* 26 */
  {
    {0x8003, 61},
    {0x8006, 61},
    {0x800a, 61},
    {0x800f, 61},
    {0x8018, 61},
    {0x801f, 61},
    {0x8029, 61},
    {0xc038, 61},
    {0x8003, 65},
    {0x8006, 65},
    {0x800a, 65},
    {0x800f, 65},
    {0x8018, 65},
    {0x801f, 65},
    {0x8029, 65},
    {0xc038, 65},
  },
  /* 27 */
  {
    {0x8003, 95},
    {0x8006, 95},
    {0x800a, 95},
    {0x800f, 95},
    {0x8018, 95},
    {0x801f, 95},
    {0x8029, 95},
    {0xc038, 95},
    {0x8003, 98},
    {0x8006, 98},
    {0x800a, 98},
    {0x800f, 98},
    {0x8018, 98},
    {0x801f, 98},
    {0x8029, 98},
    {0xc038, 98},
  },
  /* 28 */
  {
    {0x8002, 100},
    {0x8009, 100},
    {0x8017, 100},
    {0xc028, 100},
    {0x8002, 102},
    {0x8009, 102},
    {0x8017, 102},
    {0xc028, 102},
    {0x8002, 103},
    {0x8009, 103},
    {0x8017, 103},
    {0xc028, 103},
    {0x8002, 104},
    {0x8009, 104},
    {0x8017, 104},
    {0xc028, 104},
  },
  /* 29 */
  {
    {0x8003, 100},
    {0x8006, 100},
    {0x800a, 100},
    {0x800f, 100},
    {0x8018, 100},
    {0x801f, 100},
    {0x8029, 100},
    {0xc038, 100},
    {0x8003, 102},
    {0x8006, 102},
    {0x800a, 102},
    {0x800f, 102},
    {0x8018, 102},
    {0x801f, 102},
    {0x8029, 102},
    {0xc038, 102},
  },
  /* 30 */
  {
    {0x8003, 103},
    {0x8006, 103},
    {0x800a, 103},
    {0x800f, 103},
    {0x8018, 103},
    {0x801f, 103},
    {0x8029, 103},
    {0xc038, 103},
    {0x8003, 104},
    {0x8006, 104},
    {0x800a, 104},
    {0x800f, 104},
    {0x8018, 104},
    {0x801f, 104},
    {0x8029, 104},
    {0xc038, 104},
  },
  /* 31 */
  {
    {0x8001, 108},
    {0xc016, 108},
    {0x8001, 109},
    {0xc016, 109},
    {0x8001, 110},
    {0xc016, 110},
    {0x8001, 112},
    {0xc016, 112},
    {0x8001, 114},
    {0xc016, 114},
    {0x8001, 117},
    {0xc016, 117},
    {0xc000, 58},
    {0xc000, 66},
    {0xc000, 67},
    {0xc000, 68},
  },
  /* 32 */
  {
    {0x8002, 108},
    {0x8009, 108},
    {0x8017, 108},
    {0xc028, 108},
    {0x8002, 109},
    {0x8009, 109},
    {0x8017, 109},
    {0xc028, 109},
    {0x8002, 110},
    {0x8009, 110},
    {0x8017, 110},
    {0xc028, 110},
    {0x8002, 112},
    {0x8009, 112},
    {0x8017, 112},
    {0xc028, 112},
  },
  /* 33 */
  {
    {0x8003, 108},
    {0x8006, 108},
    {0x800a, 108},
    {0x800f, 108},
    {0x8018, 108},
    {0x801f, 108},
    {0x8029, 108},
    {0xc038, 108},
    {0x8003, 109},
    {0x8006, 109},
    {0x800a, 109},
    {0x800f, 109},
    {0x8018, 109},
    {0x801f, 109},
    {0x8029, 109},
    {0xc038, 109},
  },
  /* 34 */
  {
    {0x8003, 110},
    {0x8006, 110},
    {0x800a, 110},
    {0x800f, 110},
    {0x8018, 110},
    {0x801f, 110},
    {0x8029, 110},
    {0xc038, 110},
    {0x8003, 112},
    {0x8006, 112},
    {0x800a, 112},
    {0x800f, 112},
    {0x8018, 112},
    {0x801f, 112},
    {0x8029, 112},
    {0xc038, 112},
  },
  /* 35 */
  {
    {0x8002, 114},
    {0x8009, 114},
    {0x8017, 114},
    {0xc028, 114},
    {0x8002, 117},
    {0x8009, 117},
    {0x8017, 117},
    {0xc028, 117},
    {0x8001, 58},
    {0xc016, 58},
    {0x8001, 66},
    {0xc016, 66},
    {0x8001, 67},
    {0xc016, 67},
    {0x8001, 68},
    {0xc016, 68},
  },
  /* 36 */
  {
    {0x8003, 114},
    {0x8006, 114},
    {0x800a, 114},
    {0x800f, 114},
    {0x8018, 114},
    {0x801f, 114},
    {0x8029, 114},
    {0xc038, 114},
    {0x8003, 117},
    {0x8006, 117},
    {0x800a, 117},
    {0x800f, 117},
    {0x8018, 117},
    {0x801f, 117},
    {0x8029, 117},
    {0xc038, 117},
  },
  /* 37 */
  {
    {0x8002, 58},
    {0x8009, 58},
    {0x8017, 58},
    {0xc028, 58},
    {0x8002, 66},
    {0x8009, 66},
    {0x8017, 66},
    {0xc028, 66},
    {0x8002, 67},
    {0x8009, 67},
    {0x8017, 67},
    {0xc028, 67},
    {0x8002, 68},
    {0x8009, 68},
    {0x8017, 68},
    {0xc028, 68},
  },
  /* 38 */
  {
    {0x8003, 58},
    {0x8006, 58},
    {0x800a, 58},
    {0x800f, 58},
    {0x8018, 58},
    {0x801f, 58},
    {0x8029, 58},
    {0xc038, 58},
    {0x8003, 66},
    {0x8006, 66},
    {0x800a, 66},
    {0x800f, 66},
    {0x8018, 66},
    {0x801f, 66},
    {0x8029, 66},
    {0xc038, 66},
  },
  /* 39 */
  {
    {0x8003, 67},
    {0x8006, 67},
    {0x800a, 67},
    {0x800f, 67},
    {0x8018, 67},
    {0x801f, 67},
    {0x8029, 67},
    {0xc038, 67},
    {0x8003, 68},
    {0x8006, 68},
    {0x800a, 68},
    {0x800f, 68},
    {0x8018, 68},
    {0x801f, 68},
    {0x8029, 68},
    {0xc038, 68},
  },
  /* 40 */
  {
    {0x2c, 0},
    {0x2d, 0},
    {0x2f, 0},
    {0x30, 0},
    {0x33, 0},
    {0x34, 0},
    {0x36, 0},
    {0x37, 0},
    {0x3b, 0},
    {0x3c, 0},
    {0x3e, 0},
    {0x3f, 0},
    {0x42, 0},
    {0x43, 0},
    {0x45, 0},
    {0x4048, 0},
  },
  /* 41 */
  {
    {0xc000, 69},
    {0xc000, 70},
    {0xc000, 71},
    {0xc000, 72},
    {0xc000, 73},
    {0xc000, 74},
    {0xc000, 75},
    {0xc000, 76},
    {0xc000, 77},
    {0xc000, 78},
    {0xc000, 79},
    {0xc000, 80},
    {0xc000, 81},
    {0xc000, 82},
    {0xc000, 83},
    {0xc000, 84},
  },
  /* 42 */
  {
    {0x8001, 69},
    {0xc016, 69},
    {0x8001, 70},
    {0xc016, 70},
    {0x8001, 71},
    {0xc016, 71},
    {0x8001, 72},
    {0xc016, 72},
    {0x8001, 73},
    {0xc016, 73},
    {0x8001, 74},
    {0xc016, 74},
    {0x8001, 75},
    {0xc016, 75},
    {0x8001, 76},
    {0xc016, 76},
  },
  /* 43 */
  {
    {0x8002, 69},
    {0x8009, 69},
    {0x8017, 69},
    {0xc028, 69},
    {0x8002, 70},
    {0x8009, 70},
    {0x8017, 70},
    {0xc028, 70},
    {0x8002, 71},
    {0x8009, 71},
    {0x8017, 71},
    {0xc028, 71},
    {0x8002, 72},
    {0x8009, 72},
    {0x8017, 72},
    {0xc028, 72},
  },
  /* 44 */
  {
    {0x8003, 69},
    {0x8006, 69},
    {0x800a, 69},
    {0x800f, 69},
    {0x8018, 69},
    {0x801f, 69},
    {0x8029, 69},
    {0xc038, 69},
    {0x8003, 70},
    {0x8006, 70},
    {0x800a, 70},
    {0x800f, 70},
    {0x8018, 70},
    {0x801f, 70},
    {0x8029, 70},
    {0xc038, 70},
  },
  /* 45 */
  {
    {0x8003, 71},
    {0x8006, 71},
    {0x800a, 71},
    {0x800f, 71},
    {0x8018, 71},
    {0x801f, 71},
    {0x8029, 71},
    {0xc038, 71},
    {0x8003, 72},
    {0x8006, 72},
    {0x800a, 72},
    {0x800f, 72},
    {0x8018, 72},
    {0x801f, 72},
    {0x8029, 72},
    {0xc038, 72},
  },
  /* 46 */
  {
    {0x8002, 73},
    {0x8009, 73},
    {0x8017, 73},
    {0xc028, 73},
    {0x8002, 74},
    {0x8009, 74},
    {0x8017, 74},
    {0xc028, 74},
    {0x8002, 75},
    {0x8009, 75},
    {0x8017, 75},
    {0xc028, 75},
    {0x8002, 76},
    {0x8009, 76},
    {0x8017, 76},
    {0xc028, 76},
  },
  /* 47 */
  {
    {0x8003, 73},
    {0x8006, 73},
    {0x800a, 73},
    {0x800f, 73},
    {0x8018, 73},
    {0x801f, 73},
    {0x8029, 73},
    {0xc038, 73},
    {0x8003, 74},
    {0x8006, 74},
    {0x800a, 74},
    {0x800f, 74},
    {0x8018, 74},
    {0x801f, 74},
    {0x8029, 74},
    {0xc038, 74},
  },
  /* 48 */
  {
    {0x8003, 75},
    {0x8006, 75},
    {0x800a, 75},
    {0x800f, 75},
    {0x8018, 75},
    {0x801f, 75},
    {0x8029, 75},
    {0xc038, 75},
    {0x8003, 76},
    {0x8006, 76},
    {0x800a, 76},
    {0x800f, 76},
    {0x8018, 76},
    {0x801f, 76},
    {0x8029, 76},
    {0xc038, 76},
  },
  /* 49 */
  {
    {0x8001, 77},
    {0xc016, 77},
    {0x8001, 78},
    {0xc016, 78},
    {0x8001, 79},
    {0xc016, 79},
    {0x8001, 80},
    {0xc016, 80},
    {0x8001, 81},
    {0xc016, 81},
    {0x8001, 82},
    {0xc016, 82},
    {0x8001, 83},
    {0xc016, 83},
    {0x8001, 84},
    {0xc016, 84},
  },
  /* 50 */
  {
    {0x8002, 77},
    {0x8009, 77},
    {0x8017, 77},
    {0xc028, 77},
    {0x8002, 78},
    {0x8009, 78},
    {0x8017, 78},
    {0xc028, 78},
    {0x8002, 79},
    {0x8009, 79},
    {0x8017, 79},
    {0xc028, 79},
    {0x8002, 80},
    {0x8009, 80},
    {0x8017, 80},
    {0xc028, 80},
  },
  /* 51 */
  {
    {0x8003, 77},
    {0x8006, 77},
    {0x800a, 77},
    {0x800f, 77},
    {0x8018, 77},
    {0x801f, 77},
    {0x8029, 77},
    {0xc038, 77},
    {0x8003, 78},
    {0x8006, 78},
    {0x800a, 78},
    {0x800f, 78},
    {0x8018, 78},
    {0x801f, 78},
    {0x8029, 78},
    {0xc038, 78},
  },
  /* 52 */
  {
    {0x8003, 79},
    {0x8006, 79},
    {0x800a, 79},
    {0x800f, 79},
    {0x8018, 79},
    {0x801f, 79},
    {0x8029, 79},
    {0xc038, 79},
    {0x8003, 80},
    {0x8006, 80},
    {0x800a, 80},
    {0x800f, 80},
    {0x8018, 80},
    {0x801f, 80},
    {0x8029, 80},
    {0xc038, 80},
  },
  /* 53 */
  {
    {0x8002, 81},
    {0x8009, 81},
    {0x8017, 81},
    {0xc028, 81},
    {0x8002, 82},
    {0x8009, 82},
    {0x8017, 82},
    {0xc028, 82},
    {0x8002, 83},
    {0x8009, 83},
    {0x8017, 83},
    {0xc028, 83},
    {0x8002, 84},
    {0x8009, 84},
    {0x8017, 84},
    {0xc028, 84},
  },
  /* 54 */
  {
    {0x8003, 81},
    {0x8006, 81},
    {0x800a, 81},
    {0x800f, 81},
    {0x8018, 81},
    {0x801f, 81},
    {0x8029, 81},
    {0xc038, 81},
    {0x8003, 82},
    {0x8006, 82},
    {0x800a, 82},
    {0x800f, 82},
    {0x8018, 82},
    {0x801f, 82},
    {0x8029, 82},
    {0xc038, 82},
  },
  /* 55 */
  {
    {0x8003, 83},
    {0x8006, 83},
    {0x800a, 83},
    {0x800f, 83},
    {0x8018, 83},
    {0x801f, 83},
    {0x8029, 83},
    {0xc038, 83},
    {0x8003, 84},
    {0x8006, 84},
    {0x800a, 84},
    {0x800f, 84},
    {0x8018, 84},
    {0x801f, 84},
    {0x8029, 84},
    {0xc038, 84},
  },
  /* 56 */
  {
    {0xc000, 85},
    {0xc000, 86},
    {0xc000, 87},
    {0xc000, 89},
    {0xc000, 106},
    {0xc000, 107},
    {0xc000, 113},
    {0xc000, 118},
    {0xc000, 119},
    {0xc000, 120},
    {0xc000, 121},
    {0xc000, 122},
    {0x46, 0},
    {0x47, 0},
    {0x49, 0},
    {0x404a, 0},
  },
  /* 57 */
  {
    {0x8001, 85},
    {0xc016, 85},
    {0x8001, 86},
    {0xc016, 86},
    {0x8001, 87},
    {0xc016, 87},
    {0x8001, 89},
    {0xc016, 89},
    {0x8001, 106},
    {0xc016, 106},
    {0x8001, 107},
    {0xc016, 107},
    {0x8001, 113},
    {0xc016, 113},
    {0x8001, 118},
    {0xc016, 118},
  },
  /* 58 */
  {
    {0x8002, 85},
    {0x8009, 85},
    {0x8017, 85},
    {0xc028, 85},
    {0x8002, 86},
    {0x8009, 86},
    {0x8017, 86},
    {0xc028, 86},
    {0x8002, 87},
    {0x8009, 87},
    {0x8017, 87},
    {0xc028, 87},
    {0x8002, 89},
    {0x8009, 89},
    {0x8017, 89},
    {0xc028, 89},
  },
  /* 59 */
  {
    {0x8003, 85},
    {0x8006, 85},
    {0x800a, 85},
    {0x800f, 85},
    {0x8018, 85},
    {0x801f, 85},
    {0x8029, 85},
    {0xc038, 85},
    {0x8003, 86},
    {0x8006, 86},
    {0x800a, 86},
    {0x800f, 86},
    {0x8018, 86},
    {0x801f, 86},
    {0x8029, 86},
    {0xc038, 86},
  },
  /* 60 */
  {
    {0x8003, 87},
    {0x8006, 87},
    {0x800a, 87},
    {0x800f, 87},
    {0x8018, 87},
    {0x801f, 87},
    {0x8029, 87},
    {0xc038, 87},
    {0x8003, 89},
    {0x8006, 89},
    {0x800a, 89},
    {0x800f, 89},
    {0x8018, 89},
    {0x801f, 89},
    {0x8029, 89},
    {0xc038, 89},
  },
  /* 61 */
  {
    {0x8002, 106},
    {0x8009, 106},
    {0x8017, 106},
    {0xc028, 106},
    {0x8002, 107},
    {0x8009, 107},
    {0x8017, 107},
    {0xc028, 107},
    {0x8002, 113},
    {0x8009, 113},
    {0x8017, 113},
    {0xc028, 113},
    {0x8002, 118},
    {0x8009, 118},
    {0x8017, 118},
    {0xc028, 118},
  },
  /* 62 */
  {
    {0x8003, 106},
    {0x8006, 106},
    {0x800a, 106},
    {0x800f, 106},
    {0x8018, 106},
    {0x801f, 106},
    {0x8029, 106},
    {0xc038, 106},
    {0x8003, 107},
    {0x8006, 107},
    {0x800a, 107},
    {0x800f, 107},
    {0x8018, 107},
    {0x801f, 107},
    {0x8029, 107},
    {0xc038, 107},
  },
  /* 63 */
  {
    {0x8003, 113},
    {0x8006, 113},
    {0x800a, 113},
    {0x800f, 113},
    {0x8018, 113},
    {0x801f, 113},
    {0x8029, 113},
    {0xc038, 113},
    {0x8003, 118},
    {0x8006, 118},
    {0x800a, 118},
    {0x800f, 118},
    {0x8018, 118},
    {0x801f, 118},
    {0x8029, 118},
    {0xc038, 118},
  },
  /* 64 */
  {
    {0x8001, 119},
    {0xc016, 119},
    {0x8001, 120},
    {0xc016, 120},
    {0x8001, 121},
    {0xc016, 121},
    {0x8001, 122},
    {0xc016, 122},
    {0xc000, 38},
    {0xc000, 42},
    {0xc000, 44},
    {0xc000, 59},
    {0xc000, 88},
    {0xc000, 90},
    {0x4b, 0},
    {0x4e, 0},
  },
  /* 65 */
  {
    {0x8002, 119},
    {0x8009, 119},
    {0x8017, 119},
    {0xc028, 119},
    {0x8002, 120},
    {0x8009, 120},
    {0x8017, 120},
    {0xc028, 120},
    {0x8002, 121},
    {0x8009, 121},
    {0x8017, 121},
    {0xc028, 121},
    {0x8002, 122},
    {0x8009, 122},
    {0x8017, 122},
    {0xc028, 122},
  },
  /* 66 */
  {
    {0x8003, 119},
    {0x8006, 119},
    {0x800a, 119},
    {0x800f, 119},
    {0x8018, 119},
    {0x801f, 119},
    {0x8029, 119},
    {0xc038, 119},
    {0x8003, 120},
    {0x8006, 120},
    {0x800a, 120},
    {0x800f, 120},
    {0x8018, 120},
    {0x801f, 120},
    {0x8029, 120},
    {0xc038, 120},
  },
  /* 67 */
  {
    {0x8003, 121},
    {0x8006, 121},
    {0x800a, 121},
    {0x800f, 121},
    {0x8018, 121},
    {0x801f, 121},
    {0x8029, 121},
    {0xc038, 121},
    {0x8003, 122},
    {0x8006, 122},
    {0x800a, 122},
    {0x800f, 122},
    {0x8018, 122},
    {0x801f, 122},
    {0x8029, 122},
    {0xc038, 122},
  },
  /* 68 */
  {
    {0x8001, 38},
    {0xc016, 38},
    {0x8001, 42},
    {0xc016, 42},
    {0x8001, 44},
    {0xc016, 44},
    {0x8001, 59},
    {0xc016, 59},
    {0x8001, 88},
    {0xc016, 88},
    {0x8001, 90},
    {0xc016, 90},
    {0x4c, 0},
    {0x4d, 0},
    {0x4f, 0},
    {0x51, 0},
  },
  /* 69 */
  {
    {0x8002, 38},
    {0x8009, 38},
    {0x8017, 38},
    {0xc028, 38},
    {0x8002, 42},
    {0x8009, 42},
    {0x8017, 42},
    {0xc028, 42},
    {0x8002, 44},
    {0x8009, 44},
    {0x8017, 44},
    {0xc028, 44},
    {0x8002, 59},
    {0x8009, 59},
    {0x8017, 59},
    {0xc028, 59},
  },
  /* 70 */
  {
    {0x8003, 38},
    {0x8006, 38},
    {0x800a, 38},
    {0x800f, 38},
    {0x8018, 38},
    {0x801f, 38},
    {0x8029, 38},
    {0xc038, 38},
    {0x8003, 42},
    {0x8006, 42},
    {0x800a, 42},
    {0x800f, 42},
    {0x8018, 42},
    {0x801f, 42},
    {0x8029, 42},
    {0xc038, 42},
  },
  /* 71 */
  {
    {0x8003, 44},
    {0x8006, 44},
    {0x800a, 44},
    {0x800f, 44},
    {0x8018, 44},
    {0x801f, 44},
    {0x8029, 44},
    {0xc038, 44},
    {0x8003, 59},
    {0x8006, 59},
    {0x800a, 59},
    {0x800f, 59},
    {0x8018, 59},
    {0x801f, 59},
    {0x8029, 59},
    {0xc038, 59},
  },
  /* 72 */
  {
    {0x8002, 88},
    {0x8009, 88},
    {0x8017, 88},
    {0xc028, 88},
    {0x8002, 90},
    {0x8009, 90},
    {0x8017, 90},
    {0xc028, 90},
    {0xc000, 33},
    {0xc000, 34},
    {0xc000, 40},
    {0xc000, 41},
    {0xc000, 63},
    {0x50, 0},
    {0x52, 0},
    {0x54, 0},
  },
  /* 73 */
  {
    {0x8003, 88},
    {0x8006, 88},
    {0x800a, 88},
    {0x800f, 88},
    {0x8018, 88},
    {0x801f, 88},
    {0x8029, 88},
    {0xc038, 88},
    {0x8003, 90},
    {0x8006, 90},
    {0x800a, 90},
    {0x800f, 90},
    {0x8018, 90},
    {0x801f, 90},
    {0x8029, 90},
    {0xc038, 90},
  },
  /* 74 */
  {
    {0x8001, 33},
    {0xc016, 33},
    {0x8001, 34},
    {0xc016, 34},
    {0x8001, 40},
    {0xc016, 40},
    {0x8001, 41},
    {0xc016, 41},
    {0x8001, 63},
    {0xc016, 63},
    {0xc000, 39},
    {0xc000, 43},
    {0xc000, 124},
    {0x53, 0},
    {0x55, 0},
    {0x58, 0},
  },
  /* 75 */
  {
    {0x8002, 33},
    {0x8009, 33},
    {0x8017, 33},
    {0xc028, 33},
    {0x8002, 34},
    {0x8009, 34},
    {0x8017, 34},
    {0xc028, 34},
    {0x8002, 40},
    {0x8009, 40},
    {0x8017, 40},
    {0xc028, 40},
    {0x8002, 41},
    {0x8009, 41},
    {0x8017, 41},
    {0xc028, 41},
  },
  /* 76 */
  {
    {0x8003, 33},
    {0x8006, 33},
    {0x800a, 33},
    {0x800f, 33},
    {0x8018, 33},
    {0x801f, 33},
    {0x8029, 33},
    {0xc038, 33},
    {0x8003, 34},
    {0x8006, 34},
    {0x800a, 34},
    {0x800f, 34},
    {0x8018, 34},
    {0x801f, 34},
    {0x8029, 34},
    {0xc038, 34},
  },
  /* 77 */
  {
    {0x8003, 40},
    {0x8006, 40},
    {0x800a, 40},
    {0x800f, 40},
    {0x8018, 40},
    {0x801f, 40},
    {0x8029, 40},
    {0xc038, 40},
    {0x8003, 41},
    {0x8006, 41},
    {0x800a, 41},
    {0x800f, 41},
    {0x8018, 41},
    {0x801f, 41},
    {0x8029, 41},
    {0xc038, 41},
  },
  /* 78 */
  {
    {0x8002, 63},
    {0x8009, 63},
    {0x8017, 63},
    {0xc028, 63},
    {0x8001, 39},
    {0xc016, 39},
    {0x8001, 43},
    {0xc016, 43},
    {0x8001, 124},
    {0xc016, 124},
    {0xc000, 35},
    {0xc000, 62},
    {0x56, 0},
    {0x57, 0},
    {0x59, 0},
    {0x5a, 0},
  },
  /* 79 */
  {
    {0x8003, 63},
    {0x8006, 63},
    {0x800a, 63},
    {0x800f, 63},
    {0x8018, 63},
    {0x801f, 63},
    {0x8029, 63},
    {0xc038, 63},
    {0x8002, 39},
    {0x8009, 39},
    {0x8017, 39},
    {0xc028, 39},
    {0x8002, 43},
    {0x8009, 43},
    {0x8017, 43},
    {0xc028, 43},
  },
  /* 80 */
  {
    {0x8003, 39},
    {0x8006, 39},
    {0x800a, 39},
    {0x800f, 39},
    {0x8018, 39},
    {0x801f, 39},
    {0x8029, 39},
    {0xc038, 39},
    {0x8003, 43},
    {0x8006, 43},
    {0x800a, 43},
    {0x800f, 43},
    {0x8018, 43},
    {0x801f, 43},
    {0x8029, 43},
    {0xc038, 43},
  },
  /* 81 */
  {
    {0x8002, 124},
    {0x8009, 124},
    {0x8017, 124},
    {0xc028, 124},
    {0x8001, 35},
    {0xc016, 35},
    {0x8001, 62},
    {0xc016, 62},
    {0xc000, 0},
    {0xc000, 36},
    {0xc000, 64},
    {0xc000, 91},
    {0xc000, 93},
    {0xc000, 126},
    {0x5b, 0},
    {0x5c, 0},
  },
  /* 82 */
  {
    {0x8003, 124},
    {0x8006, 124},
    {0x800a, 124},
    {0x800f, 124},
    {0x8018, 124},
    {0x801f, 124},
    {0x8029, 124},
    {0xc038, 124},
    {0x8002, 35},
    {0x8009, 35},
    {0x8017, 35},
    {0xc028, 35},
    {0x8002, 62},
    {0x8009, 62},
    {0x8017, 62},
    {0xc028, 62},
  },
  /* 83 */
  {
    {0x8003, 35},
    {0x8006, 35},
    {0x800a, 35},
    {0x800f, 35},
    {0x8018, 35},
    {0x801f, 35},
    {0x8029, 35},
    {0xc038, 35},
    {0x8003, 62},
    {0x8006, 62},
    {0x800a, 62},
    {0x800f, 62},
    {0x8018, 62},
    {0x801f, 62},
    {0x8029, 62},
    {0xc038, 62},
  },
  /* 84 */
  {
    {0x8001, 0},
    {0xc016, 0},
    {0x8001, 36},
    {0xc016, 36},
    {0x8001, 64},
    {0xc016, 64},
    {0x8001, 91},
    {0xc016, 91},
    {0x8001, 93},
    {0xc016, 93},
    {0x8001, 126},
    {0xc016, 126},
    {0xc000, 94},
    {0xc000, 125},
    {0x5d, 0},
    {0x5e, 0},
  },
  /* 85 */
  {
    {0x8002, 0},
    {0x8009, 0},
    {0x8017, 0},
    {0xc028, 0},
    {0x8002, 36},
    {0x8009, 36},
    {0x8017, 36},
    {0xc028, 36},
    {0x8002, 64},
    {0x8009, 64},
    {0x8017, 64},
    {0xc028, 64},
    {0x8002, 91},
    {0x8009, 91},
    {0x8017, 91},
    {0xc028, 91},
  },
  /* 86 */
  {
    {0x8003, 0},
    {0x8006, 0},
    {0x800a, 0},
    {0x800f, 0},
    {0x8018, 0},
    {0x801f, 0},
    {0x8029, 0},
    {0xc038, 0},
    {0x8003, 36},
    {0x8006, 36},
    {0x800a, 36},
    {0x800f, 36},
    {0x8018, 36},
    {0x801f, 36},
    {0x8029, 36},
    {0xc038, 36},
  },
  /* 87 */
  {
    {0x8003, 64},
    {0x8006, 64},
    {0x800a, 64},
    {0x800f, 64},
    {0x8018, 64},
    {0x801f, 64},
    {0x8029, 64},
    {0xc038, 64},
    {0x8003, 91},
    {0x8006, 91},
    {0x800a, 91},
    {0x800f, 91},
    {0x8018, 91},
    {0x801f, 91},
    {0x8029, 91},
    {0xc038, 91},
  },
  /* 88 */
  {
    {0x8002, 93},
    {0x8009, 93},
    {0x8017, 93},
    {0xc028, 93},
    {0x8002, 126},
    {0x8009, 126},
    {0x8017, 126},
    {0xc028, 126},
    {0x8001, 94},
    {0xc016, 94},
    {0x8001, 125},
    {0xc016, 125},
    {0xc000, 60},
    {0xc000, 96},
    {0xc000, 123},
    {0x5f, 0},
  },
  /* 89 */
  {
    {0x8003, 93},
    {0x8006, 93},
    {0x800a, 93},
    {0x800f, 93},
    {0x8018, 93},
    {0x801f, 93},
    {0x8029, 93},
    {0xc038, 93},
    {0x8003, 126},
    {0x8006, 126},
    {0x800a, 126},
    {0x800f, 126},
    {0x8018, 126},
    {0x801f, 126},
    {0x8029, 126},
    {0xc038, 126},
  },
  /* 90 */
  {
    {0x8002, 94},
    {0x8009, 94},
    {0x8017, 94},
    {0xc028, 94},
    {0x8002, 125},
    {0x8009, 125},
    {0x8017, 125},
    {0xc028, 125},
    {0x8001, 60},
    {0xc016, 60},
    {0x8001, 96},
    {0xc016, 96},
    {0x8001, 123},
    {0xc016, 123},
    {0x60, 0},
    {0x6e, 0},
  },
  /* 91 */
  {
    {0x8003, 94},
    {0x8006, 94},
    {0x800a, 94},
    {0x800f, 94},
    {0x8018, 94},
    {0x801f, 94},
    {0x8029, 94},
    {0xc038, 94},
    {0x8003, 125},
    {0x8006, 125},
    {0x800a, 125},
    {0x800f, 125},
    {0x8018, 125},
    {0x801f, 125},
    {0x8029, 125},
    {0xc038, 125},
  },
  /* 92 */
  {
    {0x8002, 60},
    {0x8009, 60},
    {0x8017, 60},
    {0xc028, 60},
    {0x8002, 96},
    {0x8009, 96},
    {0x8017, 96},
    {0xc028, 96},
    {0x8002, 123},
    {0x8009, 123},
    {0x8017, 123},
    {0xc028, 123},
    {0x61, 0},
    {0x65, 0},
    {0x6f, 0},
    {0x85, 0},
  },
  /* 93 */
  {
    {0x8003, 60},
    {0x8006, 60},
    {0x800a, 60},
    {0x800f, 60},
    {0x8018, 60},
    {0x801f, 60},
    {0x8029, 60},
    {0xc038, 60},
    {0x8003, 96},
    {0x8006, 96},
    {0x800a, 96},
    {0x800f, 96},
    {0x8018, 96},
    {0x801f, 96},
    {0x8029, 96},
    {0xc038, 96},
  },
  /* 94 */
  {
    {0x8003, 123},
    {0x8006, 123},
    {0x800a, 123},
    {0x800f, 123},
    {0x8018, 123},
    {0x801f, 123},
    {0x8029, 123},
    {0xc038, 123},
    {0x62, 0},
    {0x63, 0},
    {0x66, 0},
    {0x69, 0},
    {0x70, 0},
    {0x77, 0},
    {0x86, 0},
    {0x99, 0},
  },
  /* 95 */
  {
    {0xc000, 92},
    {0xc000, 195},
    {0xc000, 208},
    {0x64, 0},
    {0x67, 0},
    {0x68, 0},
    {0x6a, 0},
    {0x6b, 0},
    {0x71, 0},
    {0x74, 0},
    {0x78, 0},
    {0x7e, 0},
    {0x87, 0},
    {0x8e, 0},
    {0x9a, 0},
    {0xa9, 0},
  },
  /* 96 */
  {
    {0x8001, 92},
    {0xc016, 92},
    {0x8001, 195},
    {0xc016, 195},
    {0x8001, 208},
    {0xc016, 208},
    {0xc000, 128},
    {0xc000, 130},
    {0xc000, 131},
    {0xc000, 162},
    {0xc000, 184},
    {0xc000, 194},
    {0xc000, 224},
    {0xc000, 226},
    {0x6c, 0},
    {0x6d, 0},
  },
  /* 97 */
  {
    {0x8002, 92},
    {0x8009, 92},
    {0x8017, 92},
    {0xc028, 92},
    {0x8002, 195},
    {0x8009, 195},
    {0x8017, 195},
    {0xc028, 195},
    {0x8002, 208},
    {0x8009, 208},
    {0x8017, 208},
    {0xc028, 208},
    {0x8001, 128},
    {0xc016, 128},
    {0x8001, 130},
    {0xc016, 130},
  },
  /* 98 */
  {
    {0x8003, 92},
    {0x8006, 92},
    {0x800a, 92},
    {0x800f, 92},
    {0x8018, 92},
    {0x801f, 92},
    {0x8029, 92},
    {0xc038, 92},
    {0x8003, 195},
    {0x8006, 195},
    {0x800a, 195},
    {0x800f, 195},
    {0x8018, 195},
    {0x801f, 195},
    {0x8029, 195},
    {0xc038, 195},
  },
  /* 99 */
  {
    {0x8003, 208},
    {0x8006, 208},
    {0x800a, 208},
    {0x800f, 208},
    {0x8018, 208},
    {0x801f, 208},
    {0x8029, 208},
    {0xc038, 208},
    {0x8002, 128},
    {0x8009, 128},
    {0x8017, 128},
    {0xc028, 128},
    {0x8002, 130},
    {0x8009, 130},
    {0x8017, 130},
    {0xc028, 130},
  },
  /* 100 */
  {
    {0x8003, 128},
    {0x8006, 128},
    {0x800a, 128},
    {0x800f, 128},
    {0x8018, 128},
    {0x801f, 128},
    {0x8029, 128},
    {0xc038, 128},
    {0x8003, 130},
    {0x8006, 130},
    {0x800a, 130},
    {0x800f, 130},
    {0x8018, 130},
    {0x801f, 130},
    {0x8029, 130},
    {0xc038, 130},
  },
  /* 101 */
  {
    {0x8001, 131},
    {0xc016, 131},
    {0x8001, 162},
    {0xc016, 162},
    {0x8001, 184},
    {0xc016, 184},
    {0x8001, 194},
    {0xc016, 194},
    {0x8001, 224},
    {0xc016, 224},
    {0x8001, 226},
    {0xc016, 226},
    {0xc000, 153},
    {0xc000, 161},
    {0xc000, 167},
    {0xc000, 172},
  },
  /* 102 */
  {
    {0x8002, 131},
    {0x8009, 131},
    {0x8017, 131},
    {0xc028, 131},
    {0x8002, 162},
    {0x8009, 162},
    {0x8017, 162},
    {0xc028, 162},
    {0x8002, 184},
    {0x8009, 184},
    {0x8017, 184},
    {0xc028, 184},
    {0x8002, 194},
    {0x8009, 194},
    {0x8017, 194},
    {0xc028, 194},
  },
  /* 103 */
  {
    {0x8003, 131},
    {0x8006, 131},
    {0x800a, 131},
    {0x800f, 131},
    {0x8018, 131},
    {0x801f, 131},
    {0x8029, 131},
    {0xc038, 131},
    {0x8003, 162},
    {0x8006, 162},
    {0x800a, 162},
    {0x800f, 162},
    {0x8018, 162},
    {0x801f, 162},
    {0x8029, 162},
    {0xc038, 162},
  },
  /* 104 */
  {
    {0x8003, 184},
    {0x8006, 184},
    {0x800a, 184},
    {0x800f, 184},
    {0x8018, 184},
    {0x801f, 184},
    {0x8029, 184},
    {0xc038, 184},
    {0x8003, 194},
    {0x8006, 194},
    {0x800a, 194},
    {0x800f, 194},
    {0x8018, 194},
    {0x801f, 194},
    {0x8029, 194},
    {0xc038, 194},
  },
  /* 105 */
  {
    {0x8002, 224},
    {0x8009, 224},
    {0x8017, 224},
    {0xc028, 224},
    {0x8002, 226},
    {0x8009, 226},
    {0x8017, 226},
    {0xc028, 226},
    {0x8001, 153},
    {0xc016, 153},
    {0x8001, 161},
    {0xc016, 161},
    {0x8001, 167},
    {0xc016, 167},
    {0x8001, 172},
    {0xc016, 172},
  },
  /* 106 */
  {
    {0x8003, 224},
    {0x8006, 224},
    {0x800a, 224},
    {0x800f, 224},
    {0x8018, 224},
    {0x801f, 224},
    {0x8029, 224},
    {0xc038, 224},
    {0x8003, 226},
    {0x8006, 226},
    {0x800a, 226},
    {0x800f, 226},
    {0x8018, 226},
    {0x801f, 226},
    {0x8029, 226},
    {0xc038, 226},
  },
  /* 107 */
  {
    {0x8002, 153},
    {0x8009, 153},
    {0x8017, 153},
    {0xc028, 153},
    {0x8002, 161},
    {0x8009, 161},
    {0x8017, 161},
    {0xc028, 161},
    {0x8002, 167},
    {0x8009, 167},
    {0x8017, 167},
    {0xc028, 167},
    {0x8002, 172},
    {0x8009, 172},
    {0x8017, 172},
    {0xc028, 172},
  },
  /* 108 */
  {
    {0x8003, 153},
    {0x8006, 153},
    {0x800a, 153},
    {0x800f, 153},
    {0x8018, 153},
    {0x801f, 153},
    {0x8029, 153},
    {0xc038, 153},
    {0x8003, 161},
    {0x8006, 161},
    {0x800a, 161},
    {0x800f, 161},
    {0x8018, 161},
    {0x801f, 161},
    {0x8029, 161},
    {0xc038, 161},
  },
  /* 109 */
  {
    {0x8003, 167},
    {0x8006, 167},
    {0x800a, 167},
    {0x800f, 167},
    {0x8018, 167},
    {0x801f, 167},
    {0x8029, 167},
    {0xc038, 167},
    {0x8003, 172},
    {0x8006, 172},
    {0x800a, 172},
    {0x800f, 172},
    {0x8018, 172},
    {0x801f, 172},
    {0x8029, 172},
    {0xc038, 172},
  },
  /* 110 */
  {
    {0x72, 0},
    {0x73, 0},
    {0x75, 0},
    {0x76, 0},
    {0x79, 0},
    {0x7b, 0},
    {0x7f, 0},
    {0x82, 0},
    {0x88, 0},
    {0x8b, 0},
    {0x8f, 0},
    {0x92, 0},
    {0x9b, 0},
    {0xa2, 0},
    {0xaa, 0},
    {0xb4, 0},
  },
  /* 111 */
  {
    {0xc000, 176},
    {0xc000, 177},
    {0xc000, 179},
    {0xc000, 209},
    {0xc000, 216},
    {0xc000, 217},
    {0xc000, 227},
    {0xc000, 229},
    {0xc000, 230},
    {0x7a, 0},
    {0x7c, 0},
    {0x7d, 0},
    {0x80, 0},
    {0x81, 0},
    {0x83, 0},
    {0x84, 0},
  },
  /* 112 */
  {
    {0x8001, 176},
    {0xc016, 176},
    {0x8001, 177},
    {0xc016, 177},
    {0x8001, 179},
    {0xc016, 179},
    {0x8001, 209},
    {0xc016, 209},
    {0x8001, 216},
    {0xc016, 216},
    {0x8001, 217},
    {0xc016, 217},
    {0x8001, 227},
    {0xc016, 227},
    {0x8001, 229},
    {0xc016, 229},
  },
  /* 113 */
  {
    {0x8002, 176},
    {0x8009, 176},
    {0x8017, 176},
    {0xc028, 176},
    {0x8002, 177},
    {0x8009, 177},
    {0x8017, 177},
    {0xc028, 177},
    {0x8002, 179},
    {0x8009, 179},
    {0x8017, 179},
    {0xc028, 179},
    {0x8002, 209},
    {0x8009, 209},
    {0x8017, 209},
    {0xc028, 209},
  },
  /* 114 */
  {
    {0x8003, 176},
    {0x8006, 176},
    {0x800a, 176},
    {0x800f, 176},
    {0x8018, 176},
    {0x801f, 176},
    {0x8029, 176},
    {0xc038, 176},
    {0x8003, 177},
    {0x8006, 177},
    {0x800a, 177},
    {0x800f, 177},
    {0x8018, 177},
    {0x801f, 177},
    {0x8029, 177},
    {0xc038, 177},
  },
  /* 115 */
  {
    {0x8003, 179},
    {0x8006, 179},
    {0x800a, 179},
    {0x800f, 179},
    {0x8018, 179},
    {0x801f, 179},
    {0x8029, 179},
    {0xc038, 179},
    {0x8003, 209},
    {0x8006, 209},
    {0x800a, 209},
    {0x800f, 209},
    {0x8018, 209},
    {0x801f, 209},
    {0x8029, 209},
    {0xc038, 209},
  },
  /* 116 */
  {
    {0x8002, 216},
    {0x8009, 216},
    {0x8017, 216},
    {0xc028, 216},
    {0x8002, 217},
    {0x8009, 217},
    {0x8017, 217},
    {0xc028, 217},
    {0x8002, 227},
    {0x8009, 227},
    {0x8017, 227},
    {0xc028, 227},
    {0x8002, 229},
    {0x8009, 229},
    {0x8017, 229},
    {0xc028, 229},
  },
  /* 117 */
  {
    {0x8003, 216},
    {0x8006, 216},
    {0x800a, 216},
    {0x800f, 216},
    {0x8018, 216},
    {0x801f, 216},
    {0x8029, 216},
    {0xc038, 216},
    {0x8003, 217},
    {0x8006, 217},
    {0x800a, 217},
    {0x800f, 217},
    {0x8018, 217},
    {0x801f, 217},
    {0x8029, 217},
    {0xc038, 217},
  },
  /* 118 */
  {
    {0x8003, 227},
    {0x8006, 227},
    {0x800a, 227},
    {0x800f, 227},
    {0x8018, 227},
    {0x801f, 227},
    {0x8029, 227},
    {0xc038, 227},
    {0x8003, 229},
    {0x8006, 229},
    {0x800a, 229},
    {0x800f, 229},
    {0x8018, 229},
    {0x801f, 229},
    {0x8029, 229},
    {0xc038, 229},
  },
  /* 119 */
  {
    {0x8001, 230},
    {0xc016, 230},
    {0xc000, 129},
    {0xc000, 132},
    {0xc000, 133},
    {0xc000, 134},
    {0xc000, 136},
    {0xc000, 146},
    {0xc000, 154},
    {0xc000, 156},
    {0xc000, 160},
    {0xc000, 163},
    {0xc000, 164},
    {0xc000, 169},
    {0xc000, 170},
    {0xc000, 173},
  },
  /* 120 */
  {
    {0x8002, 230},
    {0x8009, 230},
    {0x8017, 230},
    {0xc028, 230},
    {0x8001, 129},
    {0xc016, 129},
    {0x8001, 132},
    {0xc016, 132},
    {0x8001, 133},
    {0xc016, 133},
    {0x8001, 134},
    {0xc016, 134},
    {0x8001, 136},
    {0xc016, 136},
    {0x8001, 146},
    {0xc016, 146},
  },
  /* 121 */
  {
    {0x8003, 230},
    {0x8006, 230},
    {0x800a, 230},
    {0x800f, 230},
    {0x8018, 230},
    {0x801f, 230},
    {0x8029, 230},
    {0xc038, 230},
    {0x8002, 129},
    {0x8009, 129},
    {0x8017, 129},
    {0xc028, 129},
    {0x8002, 132},
    {0x8009, 132},
    {0x8017, 132},
    {0xc028, 132},
  },
  /* 122 */
  {
    {0x8003, 129},
    {0x8006, 129},
    {0x800a, 129},
    {0x800f, 129},
    {0x8018, 129},
    {0x801f, 129},
    {0x8029, 129},
    {0xc038, 129},
    {0x8003, 132},
    {0x8006, 132},
    {0x800a, 132},
    {0x800f, 132},
    {0x8018, 132},
    {0x801f, 132},
    {0x8029, 132},
    {0xc038, 132},
  },
  /* 123 */
  {
    {0x8002, 133},
    {0x8009, 133},
    {0x8017, 133},
    {0xc028, 133},
    {0x8002, 134},
    {0x8009, 134},
    {0x8017, 134},
    {0xc028, 134},
    {0x8002, 136},
    {0x8009, 136},
    {0x8017, 136},
    {0xc028, 136},
    {0x8002, 146},
    {0x8009, 146},
    {0x8017, 146},
    {0xc028, 146},
  },
  /* 124 */
  {
    {0x8003, 133},
    {0x8006, 133},
    {0x800a, 133},
    {0x800f, 133},
    {0x8018, 133},
    {0x801f, 133},
    {0x8029, 133},
    {0xc038, 133},
    {0x8003, 134},
    {0x8006, 134},
    {0x800a, 134},
    {0x800f, 134},
    {0x8018, 134},
    {0x801f, 134},
    {0x8029, 134},
    {0xc038, 134},
  },
  /* 125 */
  {
    {0x8003, 136},
    {0x8006, 136},
    {0x800a, 136},
    {0x800f, 136},
    {0x8018, 136},
    {0x801f, 136},
    {0x8029, 136},
    {0xc038, 136},
    {0x8003, 146},
    {0x8006, 146},
    {0x800a, 146},
    {0x800f, 146},
    {0x8018, 146},
    {0x801f, 146},
    {0x8029, 146},
    {0xc038, 146},
  },
  /* 126 */
  {
    {0x8001, 154},
    {0xc016, 154},
    {0x8001, 156},
    {0xc016, 156},
    {0x8001, 160},
    {0xc016, 160},
    {0x8001, 163},
    {0xc016, 163},
    {0x8001, 164},
    {0xc016, 164},
    {0x8001, 169},
    {0xc016, 169},
    {0x8001, 170},
    {0xc016, 170},
    {0x8001, 173},
    {0xc016, 173},
  },
  /* 127 */
  {
    {0x8002, 154},
    {0x8009, 154},
    {0x8017, 154},
    {0xc028, 154},
    {0x8002, 156},
    {0x8009, 156},
    {0x8017, 156},
    {0xc028, 156},
    {0x8002, 160},
    {0x8009, 160},
    {0x8017, 160},
    {0xc028, 160},
    {0x8002, 163},
    {0x8009, 163},
    {0x8017, 163},
    {0xc028, 163},
  },
  /* 128 */
  {
    {0x8003, 154},
    {0x8006, 154},
    {0x800a, 154},
    {0x800f, 154},
    {0x8018, 154},
    {0x801f, 154},
    {0x8029, 154},
    {0xc038, 154},
    {0x8003, 156},
    {0x8006, 156},
    {0x800a, 156},
    {0x800f, 156},
    {0x8018, 156},
    {0x801f, 156},
    {0x8029, 156},
    {0xc038, 156},
  },
  /* 129 */
  {
    {0x8003, 160},
    {0x8006, 160},
    {0x800a, 160},
    {0x800f, 160},
    {0x8018, 160},
    {0x801f, 160},
    {0x8029, 160},
    {0xc038, 160},
    {0x8003, 163},
    {0x8006, 163},
    {0x800a, 163},
    {0x800f, 163},
    {0x8018, 163},
    {0x801f, 163},
    {0x8029, 163},
    {0xc038, 163},
  },
  /* 130 */
  {
    {0x8002, 164},
    {0x8009, 164},
    {0x8017, 164},
    {0xc028, 164},
    {0x8002, 169},
    {0x8009, 169},
    {0x8017, 169},
    {0xc028, 169},
    {0x8002, 170},
    {0x8009, 170},
    {0x8017, 170},
    {0xc028, 170},
    {0x8002, 173},
    {0x8009, 173},
    {0x8017, 173},
    {0xc028, 173},
  },
  /* 131 */
  {
    {0x8003, 164},
    {0x8006, 164},
    {0x800a, 164},
    {0x800f, 164},
    {0x8018, 164},
    {0x801f, 164},
    {0x8029, 164},
    {0xc038, 164},
    {0x8003, 169},
    {0x8006, 169},
    {0x800a, 169},
    {0x800f, 169},
    {0x8018, 169},
    {0x801f, 169},
    {0x8029, 169},
    {0xc038, 169},
  },
  /* 132 */
  {
    {0x8003, 170},
    {0x8006, 170},
    {0x800a, 170},
    {0x800f, 170},
    {0x8018, 170},
    {0x801f, 170},
    {0x8029, 170},
    {0xc038, 170},
    {0x8003, 173},
    {0x8006, 173},
    {0x800a, 173},
    {0x800f, 173},
    {0x8018, 173},
    {0x801f, 173},
    {0x8029, 173},
    {0xc038, 173},
  },
  /* 133 */
  {
    {0x89, 0},
    {0x8a, 0},
    {0x8c, 0},
    {0x8d, 0},
    {0x90, 0},
    {0x91, 0},
    {0x93, 0},
    {0x96, 0},
    {0x9c, 0},
    {0x9f, 0},
    {0xa3, 0},
    {0xa6, 0},
    {0xab, 0},
    {0xae, 0},
    {0xb5, 0},
    {0xbe, 0},
  },
  /* 134 */
  {
    {0xc000, 178},
    {0xc000, 181},
    {0xc000, 185},
    {0xc000, 186},
    {0xc000, 187},
    {0xc000, 189},
    {0xc000, 190},
    {0xc000, 196},
    {0xc000, 198},
    {0xc000, 228},
    {0xc000, 232},
    {0xc000, 233},
    {0x94, 0},
    {0x95, 0},
    {0x97, 0},
    {0x98, 0},
  },
  /* 135 */
  {
    {0x8001, 178},
    {0xc016, 178},
    {0x8001, 181},
    {0xc016, 181},
    {0x8001, 185},
    {0xc016, 185},
    {0x8001, 186},
    {0xc016, 186},
    {0x8001, 187},
    {0xc016, 187},
    {0x8001, 189},
    {0xc016, 189},
    {0x8001, 190},
    {0xc016, 190},
    {0x8001, 196},
    {0xc016, 196},
  },
  /* 136 */
  {
    {0x8002, 178},
    {0x8009, 178},
    {0x8017, 178},
    {0xc028, 178},
    {0x8002, 181},
    {0x8009, 181},
    {0x8017, 181},
    {0xc028, 181},
    {0x8002, 185},
    {0x8009, 185},
    {0x8017, 185},
    {0xc028, 185},
    {0x8002, 186},
    {0x8009, 186},
    {0x8017, 186},
    {0xc028, 186},
  },
  /* 137 */
  {
    {0x8003, 178},
    {0x8006, 178},
    {0x800a, 178},
    {0x800f, 178},
    {0x8018, 178},
    {0x801f, 178},
    {0x8029, 178},
    {0xc038, 178},
    {0x8003, 181},
    {0x8006, 181},
    {0x800a, 181},
    {0x800f, 181},
    {0x8018, 181},
    {0x801f, 181},
    {0x8029, 181},
    {0xc038, 181},
  },
  /* 138 */
  {
    {0x8003, 185},
    {0x8006, 185},
    {0x800a, 185},
    {0x800f, 185},
    {0x8018, 185},
    {0x801f, 185},
    {0x8029, 185},
    {0xc038, 185},
    {0x8003, 186},
    {0x8006, 186},
    {0x800a, 186},
    {0x800f, 186},
    {0x8018, 186},
    {0x801f, 186},
    {0x8029, 186},
    {0xc038, 186},
  },
  /* 139 */
  {
    {0x8002, 187},
    {0x8009, 187},
    {0x8017, 187},
    {0xc028, 187},
    {0x8002, 189},
    {0x8009, 189},
    {0x8017, 189},
    {0xc028, 189},
    {0x8002, 190},
    {0x8009, 190},
    {0x8017, 190},
    {0xc028, 190},
    {0x8002, 196},
    {0x8009, 196},
    {0x8017, 196},
    {0xc028, 196},
  },
  /* 140 */
  {
    {0x8003, 187},
    {0x8006, 187},
    {0x800a, 187},
    {0x800f, 187},
    {0x8018, 187},
    {0x801f, 187},
    {0x8029, 187},
    {0xc038, 187},
    {0x8003, 189},
    {0x8006, 189},
    {0x800a, 189},
    {0x800f, 189},
    {0x8018, 189},
    {0x801f, 189},
    {0x8029, 189},
    {0xc038, 189},
  },
  /* 141 */
  {
    {0x8003, 190},
    {0x8006, 190},
    {0x800a, 190},
    {0x800f, 190},
    {0x8018, 190},
    {0x801f, 190},
    {0x8029, 190},
    {0xc038, 190},
    {0x8003, 196},
    {0x8006, 196},
    {0x800a, 196},
    {0x800f, 196},
    {0x8018, 196},
    {0x801f, 196},
    {0x8029, 196},
    {0xc038, 196},
  },
  /* 142 */
  {
    {0x8001, 198},
    {0xc016, 198},
    {0x8001, 228},
    {0xc016, 228},
    {0x8001, 232},
    {0xc016, 232},
    {0x8001, 233},
    {0xc016, 233},
    {0xc000, 1},
    {0xc000, 135},
    {0xc000, 137},
    {0xc000, 138},
    {0xc000, 139},
    {0xc000, 140},
    {0xc000, 141},
    {0xc000, 143},
  },
  /* 143 */
  {
    {0x8002, 198},
    {0x8009, 198},
    {0x8017, 198},
    {0xc028, 198},
    {0x8002, 228},
    {0x8009, 228},
    {0x8017, 228},
    {0xc028, 228},
    {0x8002, 232},
    {0x8009, 232},
    {0x8017, 232},
    {0xc028, 232},
    {0x8002, 233},
    {0x8009, 233},
    {0x8017, 233},
    {0xc028, 233},
  },
  /* 144 */
  {
    {0x8003, 198},
    {0x8006, 198},
    {0x800a, 198},
    {0x800f, 198},
    {0x8018, 198},
    {0x801f, 198},
    {0x8029, 198},
    {0xc038, 198},
    {0x8003, 228},
    {0x8006, 228},
    {0x800a, 228},
    {0x800f, 228},
    {0x8018, 228},
    {0x801f, 228},
    {0x8029, 228},
    {0xc038, 228},
  },
  /* 145 */
  {
    {0x8003, 232},
    {0x8006, 232},
    {0x800a, 232},
    {0x800f, 232},
    {0x8018, 232},
    {0x801f, 232},
    {0x8029, 232},
    {0xc038, 232},
    {0x8003, 233},
    {0x8006, 233},
    {0x800a, 233},
    {0x800f, 233},
    {0x8018, 233},
    {0x801f, 233},
    {0x8029, 233},
    {0xc038, 233},
  },
  /* 146 */
  {
    {0x8001, 1},
    {0xc016, 1},
    {0x8001, 135},
    {0xc016, 135},
    {0x8001, 137},
    {0xc016, 137},
    {0x8001, 138},
    {0xc016, 138},
    {0x8001, 139},
    {0xc016, 139},
    {0x8001, 140},
    {0xc016, 140},
    {0x8001, 141},
    {0xc016, 141},
    {0x8001, 143},
    {0xc016, 143},
  },
  /* 147 */
  {
    {0x8002, 1},
    {0x8009, 1},
    {0x8017, 1},
    {0xc028, 1},
    {0x8002, 135},
    {0x8009, 135},
    {0x8017, 135},
    {0xc028, 135},
    {0x8002, 137},
    {0x8009, 137},
    {0x8017, 137},
    {0xc028, 137},
    {0x8002, 138},
    {0x8009, 138},
    {0x8017, 138},
    {0xc028, 138},
  },
  /* 148 */
  {
    {0x8003, 1},
    {0x8006, 1},
    {0x800a, 1},
    {0x800f, 1},
    {0x8018, 1},
    {0x801f, 1},
    {0x8029, 1},
    {0xc038, 1},
    {0x8003, 135},
    {0x8006, 135},
    {0x800a, 135},
    {0x800f, 135},
    {0x8018, 135},
    {0x801f, 135},
    {0x8029, 135},
    {0xc038, 135},
  },
  /* 149 */
  {
    {0x8003, 137},
    {0x8006, 137},
    {0x800a, 137},
    {0x800f, 137},
    {0x8018, 137},
    {0x801f, 137},
    {0x8029, 137},
    {0xc038, 137},
    {0x8003, 138},
    {0x8006, 138},
    {0x800a, 138},
    {0x800f, 138},
    {0x8018, 138},
    {0x801f, 138},
    {0x8029, 138},
    {0xc038, 138},
  },
  /* 150 */
  {
    {0x8002, 139},
    {0x8009, 139},
    {0x8017, 139},
    {0xc028, 139},
    {0x8002, 140},
    {0x8009, 140},
    {0x8017, 140},
    {0xc028, 140},
    {0x8002, 141},
    {0x8009, 141},
    {0x8017, 141},
    {0xc028, 141},
    {0x8002, 143},
    {0x8009, 143},
    {0x8017, 143},
    {0xc028, 143},
  },
  /* 151 */
  {
    {0x8003, 139},
    {0x8006, 139},
    {0x800a, 139},
    {0x800f, 139},
    {0x8018, 139},
    {0x801f, 139},
    {0x8029, 139},
    {0xc038, 139},
    {0x8003, 140},
    {0x8006, 140},
    {0x800a, 140},
    {0x800f, 140},
    {0x8018, 140},
    {0x801f, 140},
    {0x8029, 140},
    {0xc038, 140},
  },
  /* 152 */
  {
    {0x8003, 141},
    {0x8006, 141},
    {0x800a, 141},
    {0x800f, 141},
    {0x8018, 141},
    {0x801f, 141},
    {0x8029, 141},
    {0xc038, 141},
    {0x8003, 143},
    {0x8006, 143},
    {0x800a, 143},
    {0x800f, 143},
    {0x8018, 143},
    {0x801f, 143},
    {0x8029, 143},
    {0xc038, 143},
  },
  /* 153 */
  {
    {0x9d, 0},
    {0x9e, 0},
    {0xa0, 0},
    {0xa1, 0},
    {0xa4, 0},
    {0xa5, 0},
    {0xa7, 0},
    {0xa8, 0},
    {0xac, 0},
    {0xad, 0},
    {0xaf, 0},
    {0xb1, 0},
    {0xb6, 0},
    {0xb9, 0},
    {0xbf, 0},
    {0xcf, 0},
  },
  /* 154 */
  {
    {0xc000, 147},
    {0xc000, 149},
    {0xc000, 150},
    {0xc000, 151},
    {0xc000, 152},
    {0xc000, 155},
    {0xc000, 157},
    {0xc000, 158},
    {0xc000, 165},
    {0xc000, 166},
    {0xc000, 168},
    {0xc000, 174},
    {0xc000, 175},
    {0xc000, 180},
    {0xc000, 182},
    {0xc000, 183},
  },
  /* 155 */
  {
    {0x8001, 147},
    {0xc016, 147},
    {0x8001, 149},
    {0xc016, 149},
    {0x8001, 150},
    {0xc016, 150},
    {0x8001, 151},
    {0xc016, 151},
    {0x8001, 152},
    {0xc016, 152},
    {0x8001, 155},
    {0xc016, 155},
    {0x8001, 157},
    {0xc016, 157},
    {0x8001, 158},
    {0xc016, 158},
  },
  /* 156 */
  {
    {0x8002, 147},
    {0x8009, 147},
    {0x8017, 147},
    {0xc028, 147},
    {0x8002, 149},
    {0x8009, 149},
    {0x8017, 149},
    {0xc028, 149},
    {0x8002, 150},
    {0x8009, 150},
    {0x8017, 150},
    {0xc028, 150},
    {0x8002, 151},
    {0x8009, 151},
    {0x8017, 151},
    {0xc028, 151},
  },
  /* 157 */
  {
    {0x8003, 147},
    {0x8006, 147},
    {0x800a, 147},
    {0x800f, 147},
    {0x8018, 147},
    {0x801f, 147},
    {0x8029, 147},
    {0xc038, 147},
    {0x8003, 149},
    {0x8006, 149},
    {0x800a, 149},
    {0x800f, 149},
    {0x8018, 149},
    {0x801f, 149},
    {0x8029, 149},
    {0xc038, 149},
  },
  /* 158 */
  {
    {0x8003, 150},
    {0x8006, 150},
    {0x800a, 150},
    {0x800f, 150},
    {0x8018, 150},
    {0x801f, 150},
    {0x8029, 150},
    {0xc038, 150},
    {0x8003, 151},
    {0x8006, 151},
    {0x800a, 151},
    {0x800f, 151},
    {0x8018, 151},
    {0x801f, 151},
    {0x8029, 151},
    {0xc038, 151},
  },
  /* 159 */
  {
    {0x8002, 152},
    {0x8009, 152},
    {0x8017, 152},
    {0xc028, 152},
    {0x8002, 155},
    {0x8009, 155},
    {0x8017, 155},
    {0xc028, 155},
    {0x8002, 157},
    {0x8009, 157},
    {0x8017, 157},
    {0xc028, 157},
    {0x8002, 158},
    {0x8009, 158},
    {0x8017, 158},
    {0xc028, 158},
  },
  /* 160 */
  {
    {0x8003, 152},
    {0x8006, 152},
    {0x800a, 152},
    {0x800f, 152},
    {0x8018, 152},
    {0x801f, 152},
    {0x8029, 152},
    {0xc038, 152},
    {0x8003, 155},
    {0x8006, 155},
    {0x800a, 155},
    {0x800f, 155},
    {0x8018, 155},
    {0x801f, 155},
    {0x8029, 155},
    {0xc038, 155},
  },
  /* 161 */
  {
    {0x8003, 157},
    {0x8006, 157},
    {0x800a, 157},
    {0x800f, 157},
    {0x8018, 157},
    {0x801f, 157},
    {0x8029, 157},
    {0xc038, 157},
    {0x8003, 158},
    {0x8006, 158},
    {0x800a, 158},
    {0x800f, 158},
    {0x8018, 158},
    {0x801f, 158},
    {0x8029, 158},
    {0xc038, 158},
  },
  /* 162 */
  {
    {0x8001, 165},
    {0xc016, 165},
    {0x8001, 166},
    {0xc016, 166},
    {0x8001, 168},
    {0xc016, 168},
    {0x8001, 174},
    {0xc016, 174},
    {0x8001, 175},
    {0xc016, 175},
    {0x8001, 180},
    {0xc016, 180},
    {0x8001, 182},
    {0xc016, 182},
    {0x8001, 183},
    {0xc016, 183},
  },
  /* 163 */
  {
    {0x8002, 165},
    {0x8009, 165},
    {0x8017, 165},
    {0xc028, 165},
    {0x8002, 166},
    {0x8009, 166},
    {0x8017, 166},
    {0xc028, 166},
    {0x8002, 168},
    {0x8009, 168},
    {0x8017, 168},
    {0xc028, 168},
    {0x8002, 174},
    {0x8009, 174},
    {0x8017, 174},
    {0xc028, 174},
  },
  /* 164 */
  {
    {0x8003, 165},
    {0x8006, 165},
    {0x800a, 165},
    {0x800f, 165},
    {0x8018, 165},
    {0x801f, 165},
    {0x8029, 165},
    {0xc038, 165},
    {0x8003, 166},
    {0x8006, 166},
    {0x800a, 166},
    {0x800f, 166},
    {0x8018, 166},
    {0x801f, 166},
    {0x8029, 166},
    {0xc038, 166},
  },
  /* 165 */
  {
    {0x8003, 168},
    {0x8006, 168},
    {0x800a, 168},
    {0x800f, 168},
    {0x8018, 168},
    {0x801f, 168},
    {0x8029, 168},
    {0xc038, 168},
    {0x8003, 174},
    {0x8006, 174},
    {0x800a, 174},
    {0x800f, 174},
    {0x8018, 174},
    {0x801f, 174},
    {0x8029, 174},
    {0xc038, 174},
  },
  /* 166 */
  {
    {0x8002, 175},
    {0x8009, 175},
    {0x8017, 175},
    {0xc028, 175},
    {0x8002, 180},
    {0x8009, 180},
    {0x8017, 180},
    {0xc028, 180},
    {0x8002, 182},
    {0x8009, 182},
    {0x8017, 182},
    {0xc028, 182},
    {0x8002, 183},
    {0x8009, 183},
    {0x8017, 183},
    {0xc028, 183},
  },
  /* 167 */
  {
    {0x8003, 175},
    {0x8006, 175},
    {0x800a, 175},
    {0x800f, 175},
    {0x8018, 175},
    {0x801f, 175},
    {0x8029, 175},
    {0xc038, 175},
    {0x8003, 180},
    {0x8006, 180},
    {0x800a, 180},
    {0x800f, 180},
    {0x8018, 180},
    {0x801f, 180},
    {0x8029, 180},
    {0xc038, 180},
  },
  /* 168 */
  {
    {0x8003, 182},
    {0x8006, 182},
    {0x800a, 182},
    {0x800f, 182},
    {0x8018, 182},
    {0x801f, 182},
    {0x8029, 182},
    {0xc038, 182},
    {0x8003, 183},
    {0x8006, 183},
    {0x800a, 183},
    {0x800f, 183},
    {0x8018, 183},
    {0x801f, 183},
    {0x8029, 183},
    {0xc038, 183},
  },
  /* 169 */
  {
    {0xc000, 188},
    {0xc000, 191},
    {0xc000, 197},
    {0xc000, 231},
    {0xc000, 239},
    {0xb0, 0},
    {0xb2, 0},
    {0xb3, 0},
    {0xb7, 0},
    {0xb8, 0},
    {0xba, 0},
    {0xbb, 0},
    {0xc0, 0},
    {0xc7, 0},
    {0xd0, 0},
    {0xdf, 0},
  },
  /* 170 */
  {
    {0x8001, 188},
    {0xc016, 188},
    {0x8001, 191},
    {0xc016, 191},
    {0x8001, 197},
    {0xc016, 197},
    {0x8001, 231},
    {0xc016, 231},
    {0x8001, 239},
    {0xc016, 239},
    {0xc000, 9},
    {0xc000, 142},
    {0xc000, 144},
    {0xc000, 145},
    {0xc000, 148},
    {0xc000, 159},
  },
  /* 171 */
  {
    {0x8002, 188},
    {0x8009, 188},
    {0x8017, 188},
    {0xc028, 188},
    {0x8002, 191},
    {0x8009, 191},
    {0x8017, 191},
    {0xc028, 191},
    {0x8002, 197},
    {0x8009, 197},
    {0x8017, 197},
    {0xc028, 197},
    {0x8002, 231},
    {0x8009, 231},
    {0x8017, 231},
    {0xc028, 231},
  },
  /* 172 */
  {
    {0x8003, 188},
    {0x8006, 188},
    {0x800a, 188},
    {0x800f, 188},
    {0x8018, 188},
    {0x801f, 188},
    {0x8029, 188},
    {0xc038, 188},
    {0x8003, 191},
    {0x8006, 191},
    {0x800a, 191},
    {0x800f, 191},
    {0x8018, 191},
    {0x801f, 191},
    {0x8029, 191},
    {0xc038, 191},
  },
  /* 173 */
  {
    {0x8003, 197},
    {0x8006, 197},
    {0x800a, 197},
    {0x800f, 197},
    {0x8018, 197},
    {0x801f, 197},
    {0x8029, 197},
    {0xc038, 197},
    {0x8003, 231},
    {0x8006, 231},
    {0x800a, 231},
    {0x800f, 231},
    {0x8018, 231},
    {0x801f, 231},
    {0x8029, 231},
    {0xc038, 231},
  },
  /* 174 */
  {
    {0x8002, 239},
    {0x8009, 239},
    {0x8017, 239},
    {0xc028, 239},
    {0x8001, 9},
    {0xc016, 9},
    {0x8001, 142},
    {0xc016, 142},
    {0x8001, 144},
    {0xc016, 144},
    {0x8001, 145},
    {0xc016, 145},
    {0x8001, 148},
    {0xc016, 148},
    {0x8001, 159},
    {0xc016, 159},
  },
  /* 175 */
  {
    {0x8003, 239},
    {0x8006, 239},
    {0x800a, 239},
    {0x800f, 239},
    {0x8018, 239},
    {0x801f, 239},
    {0x8029, 239},
    {0xc038, 239},
    {0x8002, 9},
    {0x8009, 9},
    {0x8017, 9},
    {0xc028, 9},
    {0x8002, 142},
    {0x8009, 142},
    {0x8017, 142},
    {0xc028, 142},
  },
  /* 176 */
  {
    {0x8003, 9},
    {0x8006, 9},
    {0x800a, 9},
    {0x800f, 9},
    {0x8018, 9},
    {0x801f, 9},
    {0x8029, 9},
    {0xc038, 9},
    {0x8003, 142},
    {0x8006, 142},
    {0x800a, 142},
    {0x800f, 142},
    {0x8018, 142},
    {0x801f, 142},
    {0x8029, 142},
    {0xc038, 142},
  },
  /* 177 */
  {
    {0x8002, 144},
    {0x8009, 144},
    {0x8017, 144},
    {0xc028, 144},
    {0x8002, 145},
    {0x8009, 145},
    {0x8017, 145},
    {0xc028, 145},
    {0x8002, 148},
    {0x8009, 148},
    {0x8017, 148},
    {0xc028, 148},
    {0x8002, 159},
    {0x8009, 159},
    {0x8017, 159},
    {0xc028, 159},
  },
  /* 178 */
  {
    {0x8003, 144},
    {0x8006, 144},
    {0x800a, 144},
    {0x800f, 144},
    {0x8018, 144},
    {0x801f, 144},
    {0x8029, 144},
    {0xc038, 144},
    {0x8003, 145},
    {0x8006, 145},
    {0x800a, 145},
    {0x800f, 145},
    {0x8018, 145},
    {0x801f, 145},
    {0x8029, 145},
    {0xc038, 145},
  },
  /* 179 */
  {
    {0x8003, 148},
    {0x8006, 148},
    {0x800a, 148},
    {0x800f, 148},
    {0x8018, 148},
    {0x801f, 148},
    {0x8029, 148},
    {0xc038, 148},
    {0x8003, 159},
    {0x8006, 159},
    {0x800a, 159},
    {0x800f, 159},
    {0x8018, 159},
    {0x801f, 159},
    {0x8029, 159},
    {0xc038, 159},
  },
  /* 180 */
  {
    {0xc000, 171},
    {0xc000, 206},
    {0xc000, 215},
    {0xc000, 225},
    {0xc000, 236},
    {0xc000, 237},
    {0xbc, 0},
    {0xbd, 0},
    {0xc1, 0},
    {0xc4, 0},
    {0xc8, 0},
    {0xcb, 0},
    {0xd1, 0},
    {0xd8, 0},
    {0xe0, 0},
    {0xee, 0},
  },
  /* 181 */
  {
    {0x8001, 171},
    {0xc016, 171},
    {0x8001, 206},
    {0xc016, 206},
    {0x8001, 215},
    {0xc016, 215},
    {0x8001, 225},
    {0xc016, 225},
    {0x8001, 236},
    {0xc016, 236},
    {0x8001, 237},
    {0xc016, 237},
    {0xc000, 199},
    {0xc000, 207},
    {0xc000, 234},
    {0xc000, 235},
  },
  /* 182 */
  {
    {0x8002, 171},
    {0x8009, 171},
    {0x8017, 171},
    {0xc028, 171},
    {0x8002, 206},
    {0x8009, 206},
    {0x8017, 206},
    {0xc028, 206},
    {0x8002, 215},
    {0x8009, 215},
    {0x8017, 215},
    {0xc028, 215},
    {0x8002, 225},
    {0x8009, 225},
    {0x8017, 225},
    {0xc028, 225},
  },
  /* 183 */
  {
    {0x8003, 171},
    {0x8006, 171},
    {0x800a, 171},
    {0x800f, 171},
    {0x8018, 171},
    {0x801f, 171},
    {0x8029, 171},
    {0xc038, 171},
    {0x8003, 206},
    {0x8006, 206},
    {0x800a, 206},
    {0x800f, 206},
    {0x8018, 206},
    {0x801f, 206},
    {0x8029, 206},
    {0xc038, 206},
  },
  /* 184 */
  {
    {0x8003, 215},
    {0x8006, 215},
    {0x800a, 215},
    {0x800f, 215},
    {0x8018, 215},
    {0x801f, 215},
    {0x8029, 215},
    {0xc038, 215},
    {0x8003, 225},
    {0x8006, 225},
    {0x800a, 225},
    {0x800f, 225},
    {0x8018, 225},
    {0x801f, 225},
    {0x8029, 225},
    {0xc038, 225},
  },
  /* 185 */
  {
    {0x8002, 236},
    {0x8009, 236},
    {0x8017, 236},
    {0xc028, 236},
    {0x8002, 237},
    {0x8009, 237},
    {0x8017, 237},
    {0xc028, 237},
    {0x8001, 199},
    {0xc016, 199},
    {0x8001, 207},
    {0xc016, 207},
    {0x8001, 234},
    {0xc016, 234},
    {0x8001, 235},
    {0xc016, 235},
  },
  /* 186 */
  {
    {0x8003, 236},
    {0x8006, 236},
    {0x800a, 236},
    {0x800f, 236},
    {0x8018, 236},
    {0x801f, 236},
    {0x8029, 236},
    {0xc038, 236},
    {0x8003, 237},
    {0x8006, 237},
    {0x800a, 237},
    {0x800f, 237},
    {0x8018, 237},
    {0x801f, 237},
    {0x8029, 237},
    {0xc038, 237},
  },
  /* 187 */
  {
    {0x8002, 199},
    {0x8009, 199},
    {0x8017, 199},
    {0xc028, 199},
    {0x8002, 207},
    {0x8009, 207},
    {0x8017, 207},
    {0xc028, 207},
    {0x8002, 234},
    {0x8009, 234},
    {0x8017, 234},
    {0xc028, 234},
    {0x8002, 235},
    {0x8009, 235},
    {0x8017, 235},
    {0xc028, 235},
  },
  /* 188 */
  {
    {0x8003, 199},
    {0x8006, 199},
    {0x800a, 199},
    {0x800f, 199},
    {0x8018, 199},
    {0x801f, 199},
    {0x8029, 199},
    {0xc038, 199},
    {0x8003, 207},
    {0x8006, 207},
    {0x800a, 207},
    {0x800f, 207},
    {0x8018, 207},
    {0x801f, 207},
    {0x8029, 207},
    {0xc038, 207},
  },
  /* 189 */
  {
    {0x8003, 234},
    {0x8006, 234},
    {0x800a, 234},
    {0x800f, 234},
    {0x8018, 234},
    {0x801f, 234},
    {0x8029, 234},
    {0xc038, 234},
    {0x8003, 235},
    {0x8006, 235},
    {0x800a, 235},
    {0x800f, 235},
    {0x8018, 235},
    {0x801f, 235},
    {0x8029, 235},
    {0xc038, 235},
  },
  /* 190 */
  {
    {0xc2, 0},
    {0xc3, 0},
    {0xc5, 0},
    {0xc6, 0},
    {0xc9, 0},
    {0xca, 0},
    {0xcc, 0},
    {0xcd, 0},
    {0xd2, 0},
    {0xd5, 0},
    {0xd9, 0},
    {0xdc, 0},
    {0xe1, 0},
    {0xe7, 0},
    {0xef, 0},
    {0xf6, 0},
  },
  /* 191 */
  {
    {0xc000, 192},
    {0xc000, 193},
    {0xc000, 200},
    {0xc000, 201},
    {0xc000, 202},
    {0xc000, 205},
    {0xc000, 210},
    {0xc000, 213},
    {0xc000, 218},
    {0xc000, 219},
    {0xc000, 238},
    {0xc000, 240},
    {0xc000, 242},
    {0xc000, 243},
    {0xc000, 255},
    {0xce, 0},
  },
  /* 192 */
  {
    {0x8001, 192},
    {0xc016, 192},
    {0x8001, 193},
    {0xc016, 193},
    {0x8001, 200},
    {0xc016, 200},
    {0x8001, 201},
    {0xc016, 201},
    {0x8001, 202},
    {0xc016, 202},
    {0x8001, 205},
    {0xc016, 205},
    {0x8001, 210},
    {0xc016, 210},
    {0x8001, 213},
    {0xc016, 213},
  },
  /* 193 */
  {
    {0x8002, 192},
    {0x8009, 192},
    {0x8017, 192},
    {0xc028, 192},
    {0x8002, 193},
    {0x8009, 193},
    {0x8017, 193},
    {0xc028, 193},
    {0x8002, 200},
    {0x8009, 200},
    {0x8017, 200},
    {0xc028, 200},
    {0x8002, 201},
    {0x8009, 201},
    {0x8017, 201},
    {0xc028, 201},
  },
  /* 194 */
  {
    {0x8003, 192},
    {0x8006, 192},
    {0x800a, 192},
    {0x800f, 192},
    {0x8018, 192},
    {0x801f, 192},
    {0x8029, 192},
    {0xc038, 192},
    {0x8003, 193},
    {0x8006, 193},
    {0x800a, 193},
    {0x800f, 193},
    {0x8018, 193},
    {0x801f, 193},
    {0x8029, 193},
    {0xc038, 193},
  },
  /* 195 */
  {
    {0x8003, 200},
    {0x8006, 200},
    {0x800a, 200},
    {0x800f, 200},
    {0x8018, 200},
    {0x801f, 200},
    {0x8029, 200},
    {0xc038, 200},
    {0x8003, 201},
    {0x8006, 201},
    {0x800a, 201},
    {0x800f, 201},
    {0x8018, 201},
    {0x801f, 201},
    {0x8029, 201},
    {0xc038, 201},
  },
  /* 196 */
  {
    {0x8002, 202},
    {0x8009, 202},
    {0x8017, 202},
    {0xc028, 202},
    {0x8002, 205},
    {0x8009, 205},
    {0x8017, 205},
    {0xc028, 205},
    {0x8002, 210},
    {0x8009, 210},
    {0x8017, 210},
    {0xc028, 210},
    {0x8002, 213},
    {0x8009, 213},
    {0x8017, 213},
    {0xc028, 213},
  },
  /* 197 */
  {
    {0x8003, 202},
    {0x8006, 202},
    {0x800a, 202},
    {0x800f, 202},
    {0x8018, 202},
    {0x801f, 202},
    {0x8029, 202},
    {0xc038, 202},
    {0x8003, 205},
    {0x8006, 205},
    {0x800a, 205},
    {0x800f, 205},
    {0x8018, 205},
    {0x801f, 205},
    {0x8029, 205},
    {0xc038, 205},
  },
  /* 198 */
  {
    {0x8003, 210},
    {0x8006, 210},
    {0x800a, 210},
    {0x800f, 210},
    {0x8018, 210},
    {0x801f, 210},
    {0x8029, 210},
    {0xc038, 210},
    {0x8003, 213},
    {0x8006, 213},
    {0x800a, 213},
    {0x800f, 213},
    {0x8018, 213},
    {0x801f, 213},
    {0x8029, 213},
    {0xc038, 213},
  },
  /* 199 */
  {
    {0x8001, 218},
    {0xc016, 218},
    {0x8001, 219},
    {0xc016, 219},
    {0x8001, 238},
    {0xc016, 238},
    {0x8001, 240},
    {0xc016, 240},
    {0x8001, 242},
    {0xc016, 242},
    {0x8001, 243},
    {0xc016, 243},
    {0x8001, 255},
    {0xc016, 255},
    {0xc000, 203},
    {0xc000, 204},
  },
  /* 200 */
  {
    {0x8002, 218},
    {0x8009, 218},
    {0x8017, 218},
    {0xc028, 218},
    {0x8002, 219},
    {0x8009, 219},
    {0x8017, 219},
    {0xc028, 219},
    {0x8002, 238},
    {0x8009, 238},
    {0x8017, 238},
    {0xc028, 238},
    {0x8002, 240},
    {0x8009, 240},
    {0x8017, 240},
    {0xc028, 240},
  },
  /* 201 */
  {
    {0x8003, 218},
    {0x8006, 218},
    {0x800a, 218},
    {0x800f, 218},
    {0x8018, 218},
    {0x801f, 218},
    {0x8029, 218},
    {0xc038, 218},
    {0x8003, 219},
    {0x8006, 219},
    {0x800a, 219},
    {0x800f, 219},
    {0x8018, 219},
    {0x801f, 219},
    {0x8029, 219},
    {0xc038, 219},
  },
  /* 202 */
  {
    {0x8003, 238},
    {0x8006, 238},
    {0x800a, 238},
    {0x800f, 238},
    {0x8018, 238},
    {0x801f, 238},
    {0x8029, 238},
    {0xc038, 238},
    {0x8003, 240},
    {0x8006, 240},
    {0x800a, 240},
    {0x800f, 240},
    {0x8018, 240},
    {0x801f, 240},
    {0x8029, 240},
    {0xc038, 240},
  },
  /* 203 */
  {
    {0x8002, 242},
    {0x8009, 242},
    {0x8017, 242},
    {0xc028, 242},
    {0x8002, 243},
    {0x8009, 243},
    {0x8017, 243},
    {0xc028, 243},
    {0x8002, 255},
    {0x8009, 255},
    {0x8017, 255},
    {0xc028, 255},
    {0x8001, 203},
    {0xc016, 203},
    {0x8001, 204},
    {0xc016, 204},
  },
  /* 204 */
  {
    {0x8003, 242},
    {0x8006, 242},
    {0x800a, 242},
    {0x800f, 242},
    {0x8018, 242},
    {0x801f, 242},
    {0x8029, 242},
    {0xc038, 242},
    {0x8003, 243},
    {0x8006, 243},
    {0x800a, 243},
    {0x800f, 243},
    {0x8018, 243},
    {0x801f, 243},
    {0x8029, 243},
    {0xc038, 243},
  },
  /* 205 */
  {
    {0x8003, 255},
    {0x8006, 255},
    {0x800a, 255},
    {0x800f, 255},
    {0x8018, 255},
    {0x801f, 255},
    {0x8029, 255},
    {0xc038, 255},
    {0x8002, 203},
    {0x8009, 203},
    {0x8017, 203},
    {0xc028, 203},
    {0x8002, 204},
    {0x8009, 204},
    {0x8017, 204},
    {0xc028, 204},
  },
  /* 206 */
  {
    {0x8003, 203},
    {0x8006, 203},
    {0x800a, 203},
    {0x800f, 203},
    {0x8018, 203},
    {0x801f, 203},
    {0x8029, 203},
    {0xc038, 203},
    {0x8003, 204},
    {0x8006, 204},
    {0x800a, 204},
    {0x800f, 204},
    {0x8018, 204},
    {0x801f, 204},
    {0x8029, 204},
    {0xc038, 204},
  },
  /* 207 */
  {
    {0xd3, 0},
    {0xd4, 0},
    {0xd6, 0},
    {0xd7, 0},
    {0xda, 0},
    {0xdb, 0},
    {0xdd, 0},
    {0xde, 0},
    {0xe2, 0},
    {0xe4, 0},
    {0xe8, 0},
    {0xeb, 0},
    {0xf0, 0},
    {0xf3, 0},
    {0xf7, 0},
    {0xfa, 0},
  },
  /* 208 */
  {
    {0xc000, 211},
    {0xc000, 212},
    {0xc000, 214},
    {0xc000, 221},
    {0xc000, 222},
    {0xc000, 223},
    {0xc000, 241},
    {0xc000, 244},
    {0xc000, 245},
    {0xc000, 246},
    {0xc000, 247},
    {0xc000, 248},
    {0xc000, 250},
    {0xc000, 251},
    {0xc000, 252},
    {0xc000, 253},
  },
  /* 209 */
  {
    {0x8001, 211},
    {0xc016, 211},
    {0x8001, 212},
    {0xc016, 212},
    {0x8001, 214},
    {0xc016, 214},
    {0x8001, 221},
    {0xc016, 221},
    {0x8001, 222},
    {0xc016, 222},
    {0x8001, 223},
    {0xc016, 223},
    {0x8001, 241},
    {0xc016, 241},
    {0x8001, 244},
    {0xc016, 244},
  },
  /* 210 */
  {
    {0x8002, 211},
    {0x8009, 211},
    {0x8017, 211},
    {0xc028, 211},
    {0x8002, 212},
    {0x8009, 212},
    {0x8017, 212},
    {0xc028, 212},
    {0x8002, 214},
    {0x8009, 214},
    {0x8017, 214},
    {0xc028, 214},
    {0x8002, 221},
    {0x8009, 221},
    {0x8017, 221},
    {0xc028, 221},
  },
  /* 211 */
  {
    {0x8003, 211},
    {0x8006, 211},
    {0x800a, 211},
    {0x800f, 211},
    {0x8018, 211},
    {0x801f, 211},
    {0x8029, 211},
    {0xc038, 211},
    {0x8003, 212},
    {0x8006, 212},
    {0x800a, 212},
    {0x800f, 212},
    {0x8018, 212},
    {0x801f, 212},
    {0x8029, 212},
    {0xc038, 212},
  },
  /* 212 */
  {
    {0x8003, 214},
    {0x8006, 214},
    {0x800a, 214},
    {0x800f, 214},
    {0x8018, 214},
    {0x801f, 214},
    {0x8029, 214},
    {0xc038, 214},
    {0x8003, 221},
    {0x8006, 221},
    {0x800a, 221},
    {0x800f, 221},
    {0x8018, 221},
    {0x801f, 221},
    {0x8029, 221},
    {0xc038, 221},
  },
  /* 213 */
  {
    {0x8002, 222},
    {0x8009, 222},
    {0x8017, 222},
    {0xc028, 222},
    {0x8002, 223},
    {0x8009, 223},
    {0x8017, 223},
    {0xc028, 223},
    {0x8002, 241},
    {0x8009, 241},
    {0x8017, 241},
    {0xc028, 241},
    {0x8002, 244},
    {0x8009, 244},
    {0x8017, 244},
    {0xc028, 244},
  },
  /* 214 */
  {
    {0x8003, 222},
    {0x8006, 222},
    {0x800a, 222},
    {0x800f, 222},
    {0x8018, 222},
    {0x801f, 222},
    {0x8029, 222},
    {0xc038, 222},
    {0x8003, 223},
    {0x8006, 223},
    {0x800a, 223},
    {0x800f, 223},
    {0x8018, 223},
    {0x801f, 223},
    {0x8029, 223},
    {0xc038, 223},
  },
  /* 215 */
  {
    {0x8003, 241},
    {0x8006, 241},
    {0x800a, 241},
    {0x800f, 241},
    {0x8018, 241},
    {0x801f, 241},
    {0x8029, 241},
    {0xc038, 241},
    {0x8003, 244},
    {0x8006, 244},
    {0x800a, 244},
    {0x800f, 244},
    {0x8018, 244},
    {0x801f, 244},
    {0x8029, 244},
    {0xc038, 244},
  },
  /* 216 */
  {
    {0x8001, 245},
    {0xc016, 245},
    {0x8001, 246},
    {0xc016, 246},
    {0x8001, 247},
    {0xc016, 247},
    {0x8001, 248},
    {0xc016, 248},
    {0x8001, 250},
    {0xc016, 250},
    {0x8001, 251},
    {0xc016, 251},
    {0x8001, 252},
    {0xc016, 252},
    {0x8001, 253},
    {0xc016, 253},
  },
  /* 217 */
  {
    {0x8002, 245},
    {0x8009, 245},
    {0x8017, 245},
    {0xc028, 245},
    {0x8002, 246},
    {0x8009, 246},
    {0x8017, 246},
    {0xc028, 246},
    {0x8002, 247},
    {0x8009, 247},
    {0x8017, 247},
    {0xc028, 247},
    {0x8002, 248},
    {0x8009, 248},
    {0x8017, 248},
    {0xc028, 248},
  },
  /* 218 */
  {
    {0x8003, 245},
    {0x8006, 245},
    {0x800a, 245},
    {0x800f, 245},
    {0x8018, 245},
    {0x801f, 245},
    {0x8029, 245},
    {0xc038, 245},
    {0x8003, 246},
    {0x8006, 246},
    {0x800a, 246},
    {0x800f, 246},
    {0x8018, 246},
    {0x801f, 246},
    {0x8029, 246},
    {0xc038, 246},
  },
  /* 219 */
  {
    {0x8003, 247},
    {0x8006, 247},
    {0x800a, 247},
    {0x800f, 247},
    {0x8018, 247},
    {0x801f, 247},
    {0x8029, 247},
    {0xc038, 247},
    {0x8003, 248},
    {0x8006, 248},
    {0x800a, 248},
    {0x800f, 248},
    {0x8018, 248},
    {0x801f, 248},
    {0x8029, 248},
    {0xc038, 248},
  },
  /* 220 */
  {
    {0x8002, 250},
    {0x8009, 250},
    {0x8017, 250},
    {0xc028, 250},
    {0x8002, 251},
    {0x8009, 251},
    {0x8017, 251},
    {0xc028, 251},
    {0x8002, 252},
    {0x8009, 252},
    {0x8017, 252},
    {0xc028, 252},
    {0x8002, 253},
    {0x8009, 253},
    {0x8017, 253},
    {0xc028, 253},
  },
  /* 221 */
  {
    {0x8003, 250},
    {0x8006, 250},
    {0x800a, 250},
    {0x800f, 250},
    {0x8018, 250},
    {0x801f, 250},
    {0x8029, 250},
    {0xc038, 250},
    {0x8003, 251},
    {0x8006, 251},
    {0x800a, 251},
    {0x800f, 251},
    {0x8018, 251},
    {0x801f, 251},
    {0x8029, 251},
    {0xc038, 251},
  },
  /* 222 */
  {
    {0x8003, 252},
    {0x8006, 252},
    {0x800a, 252},
    {0x800f, 252},
    {0x8018, 252},
    {0x801f, 252},
    {0x8029, 252},
    {0xc038, 252},
    {0x8003, 253},
    {0x8006, 253},
    {0x800a, 253},
    {0x800f, 253},
    {0x8018, 253},
    {0x801f, 253},
    {0x8029, 253},
    {0xc038, 253},
  },
  /* 223 */
  {
    {0xc000, 254},
    {0xe3, 0},
    {0xe5, 0},
    {0xe6, 0},
    {0xe9, 0},
    {0xea, 0},
    {0xec, 0},
    {0xed, 0},
    {0xf1, 0},
    {0xf2, 0},
    {0xf4, 0},
    {0xf5, 0},
    {0xf8, 0},
    {0xf9, 0},
    {0xfb, 0},
    {0xfc, 0},
  },
  /* 224 */
  {
    {0x8001, 254},
    {0xc016, 254},
    {0xc000, 2},
    {0xc000, 3},
    {0xc000, 4},
    {0xc000, 5},
    {0xc000, 6},
    {0xc000, 7},
    {0xc000, 8},
    {0xc000, 11},
    {0xc000, 12},
    {0xc000, 14},
    {0xc000, 15},
    {0xc000, 16},
    {0xc000, 17},
    {0xc000, 18},
  },
  /* 225 */
  {
    {0x8002, 254},
    {0x8009, 254},
    {0x8017, 254},
    {0xc028, 254},
    {0x8001, 2},
    {0xc016, 2},
    {0x8001, 3},
    {0xc016, 3},
    {0x8001, 4},
    {0xc016, 4},
    {0x8001, 5},
    {0xc016, 5},
    {0x8001, 6},
    {0xc016, 6},
    {0x8001, 7},
    {0xc016, 7},
  },
  /* 226 */
  {
    {0x8003, 254},
    {0x8006, 254},
    {0x800a, 254},
    {0x800f, 254},
    {0x8018, 254},
    {0x801f, 254},
    {0x8029, 254},
    {0xc038, 254},
    {0x8002, 2},
    {0x8009, 2},
    {0x8017, 2},
    {0xc028, 2},
    {0x8002, 3},
    {0x8009, 3},
    {0x8017, 3},
    {0xc028, 3},
  },
  /* 227 */
  {
    {0x8003, 2},
    {0x8006, 2},
    {0x800a, 2},
    {0x800f, 2},
    {0x8018, 2},
    {0x801f, 2},
    {0x8029, 2},
    {0xc038, 2},
    {0x8003, 3},
    {0x8006, 3},
    {0x800a, 3},
    {0x800f, 3},
    {0x8018, 3},
    {0x801f, 3},
    {0x8029, 3},
    {0xc038, 3},
  },
  /* 228 */
  {
    {0x8002, 4},
    {0x8009, 4},
    {0x8017, 4},
    {0xc028, 4},
    {0x8002, 5},
    {0x8009, 5},
    {0x8017, 5},
    {0xc028, 5},
    {0x8002, 6},
    {0x8009, 6},
    {0x8017, 6},
    {0xc028, 6},
    {0x8002, 7},
    {0x8009, 7},
    {0x8017, 7},
    {0xc028, 7},
  },
  /* 229 */
  {
    {0x8003, 4},
    {0x8006, 4},
    {0x800a, 4},
    {0x800f, 4},
    {0x8018, 4},
    {0x801f, 4},
    {0x8029, 4},
    {0xc038, 4},
    {0x8003, 5},
    {0x8006, 5},
    {0x800a, 5},
    {0x800f, 5},
    {0x8018, 5},
    {0x801f, 5},
    {0x8029, 5},
    {0xc038, 5},
  },
  /* 230 */
  {
    {0x8003, 6},
    {0x8006, 6},
    {0x800a, 6},
    {0x800f, 6},
    {0x8018, 6},
    {0x801f, 6},
    {0x8029, 6},
    {0xc038, 6},
    {0x8003, 7},
    {0x8006, 7},
    {0x800a, 7},
    {0x800f, 7},
    {0x8018, 7},
    {0x801f, 7},
    {0x8029, 7},
    {0xc038, 7},
  },
  /* 231 */
  {
    {0x8001, 8},
    {0xc016, 8},
    {0x8001, 11},
    {0xc016, 11},
    {0x8001, 12},
    {0xc016, 12},
    {0x8001, 14},
    {0xc016, 14},
    {0x8001, 15},
    {0xc016, 15},
    {0x8001, 16},
    {0xc016, 16},
    {0x8001, 17},
    {0xc016, 17},
    {0x8001, 18},
    {0xc016, 18},
  },
  /* 232 */
  {
    {0x8002, 8},
    {0x8009, 8},
    {0x8017, 8},
    {0xc028, 8},
    {0x8002, 11},
    {0x8009, 11},
    {0x8017, 11},
    {0xc028, 11},
    {0x8002, 12},
    {0x8009, 12},
    {0x8017, 12},
    {0xc028, 12},
    {0x8002, 14},
    {0x8009, 14},
    {0x8017, 14},
    {0xc028, 14},
  },
  /* 233 */
  {
    {0x8003, 8},
    {0x8006, 8},
    {0x800a, 8},
    {0x800f, 8},
    {0x8018, 8},
    {0x801f, 8},
    {0x8029, 8},
    {0xc038, 8},
    {0x8003, 11},
    {0x8006, 11},
    {0x800a, 11},
    {0x800f, 11},
    {0x8018, 11},
    {0x801f, 11},
    {0x8029, 11},
    {0xc038, 11},
  },
  /* 234 */
  {
    {0x8003, 12},
    {0x8006, 12},
    {0x800a, 12},
    {0x800f, 12},
    {0x8018, 12},
    {0x801f, 12},
    {0x8029, 12},
    {0xc038, 12},
    {0x8003, 14},
    {0x8006, 14},
    {0x800a, 14},
    {0x800f, 14},
    {0x8018, 14},
    {0x801f, 14},
    {0x8029, 14},
    {0xc038, 14},
  },
  /* 235 */
  {
    {0x8002, 15},
    {0x8009, 15},
    {0x8017, 15},
    {0xc028, 15},
    {0x8002, 16},
    {0x8009, 16},
    {0x8017, 16},
    {0xc028, 16},
    {0x8002, 17},
    {0x8009, 17},
    {0x8017, 17},
    {0xc028, 17},
    {0x8002, 18},
    {0x8009, 18},
    {0x8017, 18},
    {0xc028, 18},
  },
  /* 236 */
  {
    {0x8003, 15},
    {0x8006, 15},
    {0x800a, 15},
    {0x800f, 15},
    {0x8018, 15},
    {0x801f, 15},
    {0x8029, 15},
    {0xc038, 15},
    {0x8003, 16},
    {0x8006, 16},
    {0x800a, 16},
    {0x800f, 16},
    {0x8018, 16},
    {0x801f, 16},
    {0x8029, 16},
    {0xc038, 16},
  },
  /* 237 */
  {
    {0x8003, 17},
    {0x8006, 17},
    {0x800a, 17},
    {0x800f, 17},
    {0x8018, 17},
    {0x801f, 17},
    {0x8029, 17},
    {0xc038, 17},
    {0x8003, 18},
    {0x8006, 18},
    {0x800a, 18},
    {0x800f, 18},
    {0x8018, 18},
    {0x801f, 18},
    {0x8029, 18},
    {0xc038, 18},
  },
  /* 238 */
  {
    {0xc000, 19},
    {0xc000, 20},
    {0xc000, 21},
    {0xc000, 23},
    {0xc000, 24},
    {0xc000, 25},
    {0xc000, 26},
    {0xc000, 27},
    {0xc000, 28},
    {0xc000, 29},
    {0xc000, 30},
    {0xc000, 31},
    {0xc000, 127},
    {0xc000, 220},
    {0xc000, 249},
    {0xfd, 0},
  },
  /* 239 */
  {
    {0x8001, 19},
    {0xc016, 19},
    {0x8001, 20},
    {0xc016, 20},
    {0x8001, 21},
    {0xc016, 21},
    {0x8001, 23},
    {0xc016, 23},
    {0x8001, 24},
    {0xc016, 24},
    {0x8001, 25},
    {0xc016, 25},
    {0x8001, 26},
    {0xc016, 26},
    {0x8001, 27},
    {0xc016, 27},
  },
  /* 240 */
  {
    {0x8002, 19},
    {0x8009, 19},
    {0x8017, 19},
    {0xc028, 19},
    {0x8002, 20},
    {0x8009, 20},
    {0x8017, 20},
    {0xc028, 20},
    {0x8002, 21},
    {0x8009, 21},
    {0x8017, 21},
    {0xc028, 21},
    {0x8002, 23},
    {0x8009, 23},
    {0x8017, 23},
    {0xc028, 23},
  },
  /* 241 */
  {
    {0x8003, 19},
    {0x8006, 19},
    {0x800a, 19},
    {0x800f, 19},
    {0x8018, 19},
    {0x801f, 19},
    {0x8029, 19},
    {0xc038, 19},
    {0x8003, 20},
    {0x8006, 20},
    {0x800a, 20},
    {0x800f, 20},
    {0x8018, 20},
    {0x801f, 20},
    {0x8029, 20},
    {0xc038, 20},
  },
  /* 242 */
  {
    {0x8003, 21},
    {0x8006, 21},
    {0x800a, 21},
    {0x800f, 21},
    {0x8018, 21},
    {0x801f, 21},
    {0x8029, 21},
    {0xc038, 21},
    {0x8003, 23},
    {0x8006, 23},
    {0x800a, 23},
    {0x800f, 23},
    {0x8018, 23},
    {0x801f, 23},
    {0x8029, 23},
    {0xc038, 23},
  },
  /* 243 */
  {
    {0x8002, 24},
    {0x8009, 24},
    {0x8017, 24},
    {0xc028, 24},
    {0x8002, 25},
    {0x8009, 25},
    {0x8017, 25},
    {0xc028, 25},
    {0x8002, 26},
    {0x8009, 26},
    {0x8017, 26},
    {0xc028, 26},
    {0x8002, 27},
    {0x8009, 27},
    {0x8017, 27},
    {0xc028, 27},
  },
  /* 244 */
  {
    {0x8003, 24},
    {0x8006, 24},
    {0x800a, 24},
    {0x800f, 24},
    {0x8018, 24},
    {0x801f, 24},
    {0x8029, 24},
    {0xc038, 24},
    {0x8003, 25},
    {0x8006, 25},
    {0x800a, 25},
    {0x800f, 25},
    {0x8018, 25},
    {0x801f, 25},
    {0x8029, 25},
    {0xc038, 25},
  },
  /* 245 */
  {
    {0x8003, 26},
    {0x8006, 26},
    {0x800a, 26},
    {0x800f, 26},
    {0x8018, 26},
    {0x801f, 26},
    {0x8029, 26},
    {0xc038, 26},
    {0x8003, 27},
    {0x8006, 27},
    {0x800a, 27},
    {0x800f, 27},
    {0x8018, 27},
    {0x801f, 27},
    {0x8029, 27},
    {0xc038, 27},
  },
  /* 246 */
  {
    {0x8001, 28},
    {0xc016, 28},
    {0x8001, 29},
    {0xc016, 29},
    {0x8001, 30},
    {0xc016, 30},
    {0x8001, 31},
    {0xc016, 31},
    {0x8001, 127},
    {0xc016, 127},
    {0x8001, 220},
    {0xc016, 220},
    {0x8001, 249},
    {0xc016, 249},
    {0xfe, 0},
    {0xff, 0},
  },
  /* 247 */
  {
    {0x8002, 28},
    {0x8009, 28},
    {0x8017, 28},
    {0xc028, 28},
    {0x8002, 29},
    {0x8009, 29},
    {0x8017, 29},
    {0xc028, 29},
    {0x8002, 30},
    {0x8009, 30},
    {0x8017, 30},
    {0xc028, 30},
    {0x8002, 31},
    {0x8009, 31},
    {0x8017, 31},
    {0xc028, 31},
  },
  /* 248 */
  {
    {0x8003, 28},
    {0x8006, 28},
    {0x800a, 28},
    {0x800f, 28},
    {0x8018, 28},
    {0x801f, 28},
    {0x8029, 28},
    {0xc038, 28},
    {0x8003, 29},
    {0x8006, 29},
    {0x800a, 29},
    {0x800f, 29},
    {0x8018, 29},
    {0x801f, 29},
    {0x8029, 29},
    {0xc038, 29},
  },
  /* 249 */
  {
    {0x8003, 30},
    {0x8006, 30},
    {0x800a, 30},
    {0x800f, 30},
    {0x8018, 30},
    {0x801f, 30},
    {0x8029, 30},
    {0xc038, 30},
    {0x8003, 31},
    {0x8006, 31},
    {0x800a, 31},
    {0x800f, 31},
    {0x8018, 31},
    {0x801f, 31},
    {0x8029, 31},
    {0xc038, 31},
  },
  /* 250 */
  {
    {0x8002, 127},
    {0x8009, 127},
    {0x8017, 127},
    {0xc028, 127},
    {0x8002, 220},
    {0x8009, 220},
    {0x8017, 220},
    {0xc028, 220},
    {0x8002, 249},
    {0x8009, 249},
    {0x8017, 249},
    {0xc028, 249},
    {0xc000, 10},
    {0xc000, 13},
    {0xc000, 22},
    {0x100, 0},
  },
  /* 251 */
  {
    {0x8003, 127},
    {0x8006, 127},
    {0x800a, 127},
    {0x800f, 127},
    {0x8018, 127},
    {0x801f, 127},
    {0x8029, 127},
    {0xc038, 127},
    {0x8003, 220},
    {0x8006, 220},
    {0x800a, 220},
    {0x800f, 220},
    {0x8018, 220},
    {0x801f, 220},
    {0x8029, 220},
    {0xc038, 220},
  },
  /* 252 */
  {
    {0x8003, 249},
    {0x8006, 249},
    {0x800a, 249},
    {0x800f, 249},
    {0x8018, 249},
    {0x801f, 249},
    {0x8029, 249},
    {0xc038, 249},
    {0x8001, 10},
    {0xc016, 10},
    {0x8001, 13},
    {0xc016, 13},
    {0x8001, 22},
    {0xc016, 22},
    {0x100, 0},
    {0x100, 0},
  },
  /* 253 */
  {
    {0x8002, 10},
    {0x8009, 10},
    {0x8017, 10},
    {0xc028, 10},
    {0x8002, 13},
    {0x8009, 13},
    {0x8017, 13},
    {0xc028, 13},
    {0x8002, 22},
    {0x8009, 22},
    {0x8017, 22},
    {0xc028, 22},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
  },
  /* 254 */
  {
    {0x8003, 10},
    {0x8006, 10},
    {0x800a, 10},
    {0x800f, 10},
    {0x8018, 10},
    {0x801f, 10},
    {0x8029, 10},
    {0xc038, 10},
    {0x8003, 13},
    {0x8006, 13},
    {0x800a, 13},
    {0x800f, 13},
    {0x8018, 13},
    {0x801f, 13},
    {0x8029, 13},
    {0xc038, 13},
  },
  /* 255 */
  {
    {0x8003, 22},
    {0x8006, 22},
    {0x800a, 22},
    {0x800f, 22},
    {0x8018, 22},
    {0x801f, 22},
    {0x8029, 22},
    {0xc038, 22},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
  },
  /* 256 */
  {
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
    {0x100, 0},
  },
};
