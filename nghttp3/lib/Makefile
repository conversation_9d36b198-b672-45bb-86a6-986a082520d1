# Makefile.in generated by automake 1.16.5 from Makefile.am.
# lib/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.





am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/nghttp3
pkgincludedir = $(includedir)/nghttp3
pkglibdir = $(libdir)/nghttp3
pkglibexecdir = $(libexecdir)/nghttp3
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-pc-linux-gnu
host_triplet = x86_64-pc-linux-gnu
target_triplet = x86_64-pc-linux-gnu
subdir = lib
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_check_compile_flag.m4 \
	$(top_srcdir)/m4/ax_cxx_compile_stdcxx.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES = libnghttp3.pc
CONFIG_CLEAN_VPATH_FILES =
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(libdir)" "$(DESTDIR)$(pkgconfigdir)"
LTLIBRARIES = $(lib_LTLIBRARIES)
libnghttp3_la_LIBADD =
am__objects_1 =
am__dirstamp = $(am__leading_dot)dirstamp
am__objects_2 = nghttp3_rcbuf.lo nghttp3_mem.lo nghttp3_str.lo \
	nghttp3_conv.lo nghttp3_buf.lo nghttp3_ringbuf.lo \
	nghttp3_pq.lo nghttp3_map.lo nghttp3_ksl.lo nghttp3_qpack.lo \
	nghttp3_qpack_huffman.lo nghttp3_qpack_huffman_data.lo \
	nghttp3_err.lo nghttp3_debug.lo nghttp3_conn.lo \
	nghttp3_stream.lo nghttp3_frame.lo nghttp3_tnode.lo \
	nghttp3_vec.lo nghttp3_gaptr.lo nghttp3_idtr.lo \
	nghttp3_range.lo nghttp3_http.lo nghttp3_version.lo \
	nghttp3_balloc.lo nghttp3_opl.lo nghttp3_objalloc.lo \
	nghttp3_unreachable.lo nghttp3_settings.lo \
	nghttp3_callbacks.lo sfparse/sfparse.lo
am_libnghttp3_la_OBJECTS = $(am__objects_1) $(am__objects_2)
libnghttp3_la_OBJECTS = $(am_libnghttp3_la_OBJECTS)
AM_V_lt = $(am__v_lt_$(V))
am__v_lt_ = $(am__v_lt_$(AM_DEFAULT_VERBOSITY))
am__v_lt_0 = --silent
am__v_lt_1 = 
libnghttp3_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(libnghttp3_la_LDFLAGS) $(LDFLAGS) -o $@
AM_V_P = $(am__v_P_$(V))
am__v_P_ = $(am__v_P_$(AM_DEFAULT_VERBOSITY))
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I. -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/nghttp3_balloc.Plo \
	./$(DEPDIR)/nghttp3_buf.Plo ./$(DEPDIR)/nghttp3_callbacks.Plo \
	./$(DEPDIR)/nghttp3_conn.Plo ./$(DEPDIR)/nghttp3_conv.Plo \
	./$(DEPDIR)/nghttp3_debug.Plo ./$(DEPDIR)/nghttp3_err.Plo \
	./$(DEPDIR)/nghttp3_frame.Plo ./$(DEPDIR)/nghttp3_gaptr.Plo \
	./$(DEPDIR)/nghttp3_http.Plo ./$(DEPDIR)/nghttp3_idtr.Plo \
	./$(DEPDIR)/nghttp3_ksl.Plo ./$(DEPDIR)/nghttp3_map.Plo \
	./$(DEPDIR)/nghttp3_mem.Plo ./$(DEPDIR)/nghttp3_objalloc.Plo \
	./$(DEPDIR)/nghttp3_opl.Plo ./$(DEPDIR)/nghttp3_pq.Plo \
	./$(DEPDIR)/nghttp3_qpack.Plo \
	./$(DEPDIR)/nghttp3_qpack_huffman.Plo \
	./$(DEPDIR)/nghttp3_qpack_huffman_data.Plo \
	./$(DEPDIR)/nghttp3_range.Plo ./$(DEPDIR)/nghttp3_rcbuf.Plo \
	./$(DEPDIR)/nghttp3_ringbuf.Plo \
	./$(DEPDIR)/nghttp3_settings.Plo ./$(DEPDIR)/nghttp3_str.Plo \
	./$(DEPDIR)/nghttp3_stream.Plo ./$(DEPDIR)/nghttp3_tnode.Plo \
	./$(DEPDIR)/nghttp3_unreachable.Plo \
	./$(DEPDIR)/nghttp3_vec.Plo ./$(DEPDIR)/nghttp3_version.Plo \
	sfparse/$(DEPDIR)/sfparse.Plo
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_$(V))
am__v_CC_ = $(am__v_CC_$(AM_DEFAULT_VERBOSITY))
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_$(V))
am__v_CCLD_ = $(am__v_CCLD_$(AM_DEFAULT_VERBOSITY))
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(libnghttp3_la_SOURCES)
DIST_SOURCES = $(libnghttp3_la_SOURCES)
RECURSIVE_TARGETS = all-recursive check-recursive cscopelist-recursive \
	ctags-recursive dvi-recursive html-recursive info-recursive \
	install-data-recursive install-dvi-recursive \
	install-exec-recursive install-html-recursive \
	install-info-recursive install-pdf-recursive \
	install-ps-recursive install-recursive installcheck-recursive \
	installdirs-recursive pdf-recursive ps-recursive \
	tags-recursive uninstall-recursive
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
DATA = $(pkgconfig_DATA)
RECURSIVE_CLEAN_TARGETS = mostlyclean-recursive clean-recursive	\
  distclean-recursive maintainer-clean-recursive
am__recursive_targets = \
  $(RECURSIVE_TARGETS) \
  $(RECURSIVE_CLEAN_TARGETS) \
  $(am__extra_recursive_targets)
AM_RECURSIVE_TARGETS = $(am__recursive_targets:-recursive=) TAGS CTAGS \
	distdir distdir-am
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
DIST_SUBDIRS = $(SUBDIRS)
am__DIST_COMMON = $(srcdir)/Makefile.in $(srcdir)/libnghttp3.pc.in \
	$(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
am__relativize = \
  dir0=`pwd`; \
  sed_first='s,^\([^/]*\)/.*$$,\1,'; \
  sed_rest='s,^[^/]*/*,,'; \
  sed_last='s,^.*/\([^/]*\)$$,\1,'; \
  sed_butlast='s,/*[^/]*$$,,'; \
  while test -n "$$dir1"; do \
    first=`echo "$$dir1" | sed -e "$$sed_first"`; \
    if test "$$first" != "."; then \
      if test "$$first" = ".."; then \
        dir2=`echo "$$dir0" | sed -e "$$sed_last"`/"$$dir2"; \
        dir0=`echo "$$dir0" | sed -e "$$sed_butlast"`; \
      else \
        first2=`echo "$$dir2" | sed -e "$$sed_first"`; \
        if test "$$first2" = "$$first"; then \
          dir2=`echo "$$dir2" | sed -e "$$sed_rest"`; \
        else \
          dir2="../$$dir2"; \
        fi; \
        dir0="$$dir0"/"$$first"; \
      fi; \
    fi; \
    dir1=`echo "$$dir1" | sed -e "$$sed_rest"`; \
  done; \
  reldir="$$dir2"
ACLOCAL = ${SHELL} '/home/<USER>/ngtcp2_qpack/nghttp3/missing' aclocal-1.16
AMTAR = $${TAR-tar}
AM_DEFAULT_VERBOSITY = 0
AR = ar
AUTOCONF = ${SHELL} '/home/<USER>/ngtcp2_qpack/nghttp3/missing' autoconf
AUTOHEADER = ${SHELL} '/home/<USER>/ngtcp2_qpack/nghttp3/missing' autoheader
AUTOMAKE = ${SHELL} '/home/<USER>/ngtcp2_qpack/nghttp3/missing' automake-1.16
AWK = gawk
CC = gcc
CCDEPMODE = depmode=gcc3
CFLAGS = -g -O2
CPP = gcc -E
CPPFLAGS = 
CSCOPE = cscope
CTAGS = ctags
CXX = g++
CXXCPP = g++ -E
CXXDEPMODE = depmode=gcc3
CXXFLAGS = -g -O2
CYGPATH_W = echo
DEBUGCFLAGS = 
DEFS = -DHAVE_CONFIG_H
DEPDIR = .deps
DLLTOOL = false
DSYMUTIL = 
DUMPBIN = 
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
ETAGS = etags
EXEEXT = 
EXTRACFLAG = -fvisibility=hidden
FGREP = /usr/bin/grep -F
GREP = /usr/bin/grep
HAVE_CXX17 = 1
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
LD = /usr/bin/ld -m elf_x86_64
LDFLAGS = 
LIBOBJS = 
LIBS = 
LIBTOOL = $(SHELL) $(top_builddir)/libtool
LIPO = 
LN_S = ln -s
LTLIBOBJS = 
LT_AGE = 2
LT_CURRENT = 11
LT_REVISION = 9
LT_SYS_LIBRARY_PATH = 
MAKEINFO = ${SHELL} '/home/<USER>/ngtcp2_qpack/nghttp3/missing' makeinfo
MANIFEST_TOOL = :
MKDIR_P = /usr/bin/mkdir -p
NM = /usr/bin/nm -B
NMEDIT = 
OBJDUMP = objdump
OBJEXT = o
OTOOL = 
OTOOL64 = 
PACKAGE = nghttp3
PACKAGE_BUGREPORT = <EMAIL>
PACKAGE_NAME = nghttp3
PACKAGE_STRING = nghttp3 1.11.0-DEV
PACKAGE_TARNAME = nghttp3
PACKAGE_URL = 
PACKAGE_VERSION = 1.11.0-DEV
PACKAGE_VERSION_NUM = 0x010b00
PATH_SEPARATOR = :
PKG_CONFIG = /usr/bin/pkg-config
PKG_CONFIG_LIBDIR = 
PKG_CONFIG_PATH = 
RANLIB = ranlib
SED = /usr/bin/sed
SET_MAKE = 
SHELL = /bin/bash
STRIP = strip
VERSION = 1.11.0-DEV
WARNCFLAGS = 
WARNCXXFLAGS = 
abs_builddir = /home/<USER>/ngtcp2_qpack/nghttp3/lib
abs_srcdir = /home/<USER>/ngtcp2_qpack/nghttp3/lib
abs_top_builddir = /home/<USER>/ngtcp2_qpack/nghttp3
abs_top_srcdir = /home/<USER>/ngtcp2_qpack/nghttp3
ac_ct_AR = ar
ac_ct_CC = gcc
ac_ct_CXX = g++
ac_ct_DUMPBIN = 
am__include = include
am__leading_dot = .
am__quote = 
am__tar = tar --format=posix -chf - "$$tardir"
am__untar = tar -xf -
bindir = ${exec_prefix}/bin
build = x86_64-pc-linux-gnu
build_alias = 
build_cpu = x86_64
build_os = linux-gnu
build_vendor = pc
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = ${prefix}
host = x86_64-pc-linux-gnu
host_alias = 
host_cpu = x86_64
host_os = linux-gnu
host_vendor = pc
htmldir = ${docdir}
includedir = ${prefix}/include
infodir = ${datarootdir}/info
install_sh = ${SHELL} /home/<USER>/ngtcp2_qpack/nghttp3/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
localedir = ${datarootdir}/locale
localstatedir = ${prefix}/var
mandir = ${datarootdir}/man
mkdir_p = $(MKDIR_P)
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /home/<USER>/ngtcp2_qpack/nghttp3/build
program_transform_name = s,x,x,
psdir = ${docdir}
runstatedir = ${localstatedir}/run
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = .
sysconfdir = ${prefix}/etc
target = x86_64-pc-linux-gnu
target_alias = 
target_cpu = x86_64
target_os = linux-gnu
target_vendor = pc
top_build_prefix = ../
top_builddir = ..
top_srcdir = ..

# nghttp3
#
# Copyright (c) 2019 nghttp3
# Copyright (c) 2016 ngtcp2
#
# Permission is hereby granted, free of charge, to any person obtaining
# a copy of this software and associated documentation files (the
# "Software"), to deal in the Software without restriction, including
# without limitation the rights to use, copy, modify, merge, publish,
# distribute, sublicense, and/or sell copies of the Software, and to
# permit persons to whom the Software is furnished to do so, subject to
# the following conditions:
#
# The above copyright notice and this permission notice shall be
# included in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
# EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
# MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
# NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
# LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
# OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
# WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
SUBDIRS = includes
EXTRA_DIST = CMakeLists.txt sfparse/COPYING config.cmake.in
AM_CFLAGS = $(WARNCFLAGS) $(DEBUGCFLAGS) $(EXTRACFLAG)
AM_CPPFLAGS = -I$(srcdir)/includes -I$(builddir)/includes -DBUILDING_NGHTTP3
pkgconfigdir = $(libdir)/pkgconfig
pkgconfig_DATA = libnghttp3.pc
DISTCLEANFILES = $(pkgconfig_DATA)
lib_LTLIBRARIES = libnghttp3.la
OBJECTS = \
	nghttp3_rcbuf.c \
	nghttp3_mem.c \
	nghttp3_str.c \
	nghttp3_conv.c \
	nghttp3_buf.c \
	nghttp3_ringbuf.c \
	nghttp3_pq.c \
	nghttp3_map.c \
	nghttp3_ksl.c \
	nghttp3_qpack.c \
	nghttp3_qpack_huffman.c \
	nghttp3_qpack_huffman_data.c \
	nghttp3_err.c \
	nghttp3_debug.c \
	nghttp3_conn.c \
	nghttp3_stream.c \
	nghttp3_frame.c \
	nghttp3_tnode.c \
	nghttp3_vec.c \
	nghttp3_gaptr.c \
	nghttp3_idtr.c \
	nghttp3_range.c \
	nghttp3_http.c \
	nghttp3_version.c \
	nghttp3_balloc.c \
	nghttp3_opl.c \
	nghttp3_objalloc.c \
	nghttp3_unreachable.c \
	nghttp3_settings.c \
	nghttp3_callbacks.c \
	sfparse/sfparse.c

HFILES = \
	nghttp3_rcbuf.h \
	nghttp3_mem.h \
	nghttp3_str.h \
	nghttp3_conv.h \
	nghttp3_buf.h \
	nghttp3_ringbuf.h \
	nghttp3_pq.h \
	nghttp3_map.h \
	nghttp3_ksl.h \
	nghttp3_qpack.h \
	nghttp3_qpack_huffman.h \
	nghttp3_err.h \
	nghttp3_debug.h \
	nghttp3_conn.h \
	nghttp3_stream.h \
	nghttp3_frame.h \
	nghttp3_tnode.h \
	nghttp3_vec.h \
	nghttp3_gaptr.h \
	nghttp3_idtr.h \
	nghttp3_range.h \
	nghttp3_http.h \
	nghttp3_balloc.h \
	nghttp3_opl.h \
	nghttp3_objalloc.h \
	nghttp3_unreachable.h \
	nghttp3_settings.h \
	nghttp3_callbacks.h \
	sfparse/sfparse.h \
	nghttp3_macro.h

libnghttp3_la_SOURCES = $(HFILES) $(OBJECTS)
libnghttp3_la_LDFLAGS = -no-undefined \
	-version-info $(LT_CURRENT):$(LT_REVISION):$(LT_AGE)

all: all-recursive

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu lib/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu lib/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
libnghttp3.pc: $(top_builddir)/config.status $(srcdir)/libnghttp3.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@

install-libLTLIBRARIES: $(lib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libdir)"; \
	}

uninstall-libLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(libdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(libdir)/$$f"; \
	done

clean-libLTLIBRARIES:
	-test -z "$(lib_LTLIBRARIES)" || rm -f $(lib_LTLIBRARIES)
	@list='$(lib_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}
sfparse/$(am__dirstamp):
	@$(MKDIR_P) sfparse
	@: > sfparse/$(am__dirstamp)
sfparse/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) sfparse/$(DEPDIR)
	@: > sfparse/$(DEPDIR)/$(am__dirstamp)
sfparse/sfparse.lo: sfparse/$(am__dirstamp) \
	sfparse/$(DEPDIR)/$(am__dirstamp)

libnghttp3.la: $(libnghttp3_la_OBJECTS) $(libnghttp3_la_DEPENDENCIES) $(EXTRA_libnghttp3_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libnghttp3_la_LINK) -rpath $(libdir) $(libnghttp3_la_OBJECTS) $(libnghttp3_la_LIBADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f sfparse/*.$(OBJEXT)
	-rm -f sfparse/*.lo

distclean-compile:
	-rm -f *.tab.c

include ./$(DEPDIR)/nghttp3_balloc.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_buf.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_callbacks.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_conn.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_conv.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_debug.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_err.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_frame.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_gaptr.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_http.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_idtr.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_ksl.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_map.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_mem.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_objalloc.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_opl.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_pq.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_qpack.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_qpack_huffman.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_qpack_huffman_data.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_range.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_rcbuf.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_ringbuf.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_settings.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_str.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_stream.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_tnode.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_unreachable.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_vec.Plo # am--include-marker
include ./$(DEPDIR)/nghttp3_version.Plo # am--include-marker
include sfparse/$(DEPDIR)/sfparse.Plo # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(COMPILE) -c -o $@ $<

.c.obj:
	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
	$(am__mv) $$depbase.Tpo $$depbase.Plo
#	$(AM_V_CC)source='$<' object='$@' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LTCOMPILE) -c -o $@ $<

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
	-rm -rf sfparse/.libs sfparse/_libs
install-pkgconfigDATA: $(pkgconfig_DATA)
	@$(NORMAL_INSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgconfigdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(pkgconfigdir)" || exit $$?; \
	done

uninstall-pkgconfigDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgconfigdir)'; $(am__uninstall_files_from_dir)

# This directory's subdirectories are mostly independent; you can cd
# into them and run 'make' without going through this Makefile.
# To change the values of 'make' variables: instead of editing Makefiles,
# (1) if the variable is set in 'config.status', edit 'config.status'
#     (which will cause the Makefiles to be regenerated when you run 'make');
# (2) otherwise, pass the desired values on the 'make' command line.
$(am__recursive_targets):
	@fail=; \
	if $(am__make_keepgoing); then \
	  failcom='fail=yes'; \
	else \
	  failcom='exit 1'; \
	fi; \
	dot_seen=no; \
	target=`echo $@ | sed s/-recursive//`; \
	case "$@" in \
	  distclean-* | maintainer-clean-*) list='$(DIST_SUBDIRS)' ;; \
	  *) list='$(SUBDIRS)' ;; \
	esac; \
	for subdir in $$list; do \
	  echo "Making $$target in $$subdir"; \
	  if test "$$subdir" = "."; then \
	    dot_seen=yes; \
	    local_target="$$target-am"; \
	  else \
	    local_target="$$target"; \
	  fi; \
	  ($(am__cd) $$subdir && $(MAKE) $(AM_MAKEFLAGS) $$local_target) \
	  || eval $$failcom; \
	done; \
	if test "$$dot_seen" = "no"; then \
	  $(MAKE) $(AM_MAKEFLAGS) "$$target-am" || exit 1; \
	fi; test -z "$$fail"

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-recursive
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	if ($(ETAGS) --etags-include --version) >/dev/null 2>&1; then \
	  include_option=--etags-include; \
	  empty_fix=.; \
	else \
	  include_option=--include; \
	  empty_fix=; \
	fi; \
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    test ! -f $$subdir/TAGS || \
	      set "$$@" "$$include_option=$$here/$$subdir/TAGS"; \
	  fi; \
	done; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-recursive

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-recursive

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	@list='$(DIST_SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    $(am__make_dryrun) \
	      || test -d "$(distdir)/$$subdir" \
	      || $(MKDIR_P) "$(distdir)/$$subdir" \
	      || exit 1; \
	    dir1=$$subdir; dir2="$(distdir)/$$subdir"; \
	    $(am__relativize); \
	    new_distdir=$$reldir; \
	    dir1=$$subdir; dir2="$(top_distdir)"; \
	    $(am__relativize); \
	    new_top_distdir=$$reldir; \
	    echo " (cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) top_distdir="$$new_top_distdir" distdir="$$new_distdir" \\"; \
	    echo "     am__remove_distdir=: am__skip_length_check=: am__skip_mode_fix=: distdir)"; \
	    ($(am__cd) $$subdir && \
	      $(MAKE) $(AM_MAKEFLAGS) \
	        top_distdir="$$new_top_distdir" \
	        distdir="$$new_distdir" \
		am__remove_distdir=: \
		am__skip_length_check=: \
		am__skip_mode_fix=: \
	        distdir) \
	      || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-recursive
all-am: Makefile $(LTLIBRARIES) $(DATA)
installdirs: installdirs-recursive
installdirs-am:
	for dir in "$(DESTDIR)$(libdir)" "$(DESTDIR)$(pkgconfigdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-recursive
install-exec: install-exec-recursive
install-data: install-data-recursive
uninstall: uninstall-recursive

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-recursive
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f sfparse/$(DEPDIR)/$(am__dirstamp)
	-rm -f sfparse/$(am__dirstamp)
	-test -z "$(DISTCLEANFILES)" || rm -f $(DISTCLEANFILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-recursive

clean-am: clean-generic clean-libLTLIBRARIES clean-libtool \
	mostlyclean-am

distclean: distclean-recursive
		-rm -f ./$(DEPDIR)/nghttp3_balloc.Plo
	-rm -f ./$(DEPDIR)/nghttp3_buf.Plo
	-rm -f ./$(DEPDIR)/nghttp3_callbacks.Plo
	-rm -f ./$(DEPDIR)/nghttp3_conn.Plo
	-rm -f ./$(DEPDIR)/nghttp3_conv.Plo
	-rm -f ./$(DEPDIR)/nghttp3_debug.Plo
	-rm -f ./$(DEPDIR)/nghttp3_err.Plo
	-rm -f ./$(DEPDIR)/nghttp3_frame.Plo
	-rm -f ./$(DEPDIR)/nghttp3_gaptr.Plo
	-rm -f ./$(DEPDIR)/nghttp3_http.Plo
	-rm -f ./$(DEPDIR)/nghttp3_idtr.Plo
	-rm -f ./$(DEPDIR)/nghttp3_ksl.Plo
	-rm -f ./$(DEPDIR)/nghttp3_map.Plo
	-rm -f ./$(DEPDIR)/nghttp3_mem.Plo
	-rm -f ./$(DEPDIR)/nghttp3_objalloc.Plo
	-rm -f ./$(DEPDIR)/nghttp3_opl.Plo
	-rm -f ./$(DEPDIR)/nghttp3_pq.Plo
	-rm -f ./$(DEPDIR)/nghttp3_qpack.Plo
	-rm -f ./$(DEPDIR)/nghttp3_qpack_huffman.Plo
	-rm -f ./$(DEPDIR)/nghttp3_qpack_huffman_data.Plo
	-rm -f ./$(DEPDIR)/nghttp3_range.Plo
	-rm -f ./$(DEPDIR)/nghttp3_rcbuf.Plo
	-rm -f ./$(DEPDIR)/nghttp3_ringbuf.Plo
	-rm -f ./$(DEPDIR)/nghttp3_settings.Plo
	-rm -f ./$(DEPDIR)/nghttp3_str.Plo
	-rm -f ./$(DEPDIR)/nghttp3_stream.Plo
	-rm -f ./$(DEPDIR)/nghttp3_tnode.Plo
	-rm -f ./$(DEPDIR)/nghttp3_unreachable.Plo
	-rm -f ./$(DEPDIR)/nghttp3_vec.Plo
	-rm -f ./$(DEPDIR)/nghttp3_version.Plo
	-rm -f sfparse/$(DEPDIR)/sfparse.Plo
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-recursive

dvi-am:

html: html-recursive

html-am:

info: info-recursive

info-am:

install-data-am: install-pkgconfigDATA

install-dvi: install-dvi-recursive

install-dvi-am:

install-exec-am: install-libLTLIBRARIES

install-html: install-html-recursive

install-html-am:

install-info: install-info-recursive

install-info-am:

install-man:

install-pdf: install-pdf-recursive

install-pdf-am:

install-ps: install-ps-recursive

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-recursive
		-rm -f ./$(DEPDIR)/nghttp3_balloc.Plo
	-rm -f ./$(DEPDIR)/nghttp3_buf.Plo
	-rm -f ./$(DEPDIR)/nghttp3_callbacks.Plo
	-rm -f ./$(DEPDIR)/nghttp3_conn.Plo
	-rm -f ./$(DEPDIR)/nghttp3_conv.Plo
	-rm -f ./$(DEPDIR)/nghttp3_debug.Plo
	-rm -f ./$(DEPDIR)/nghttp3_err.Plo
	-rm -f ./$(DEPDIR)/nghttp3_frame.Plo
	-rm -f ./$(DEPDIR)/nghttp3_gaptr.Plo
	-rm -f ./$(DEPDIR)/nghttp3_http.Plo
	-rm -f ./$(DEPDIR)/nghttp3_idtr.Plo
	-rm -f ./$(DEPDIR)/nghttp3_ksl.Plo
	-rm -f ./$(DEPDIR)/nghttp3_map.Plo
	-rm -f ./$(DEPDIR)/nghttp3_mem.Plo
	-rm -f ./$(DEPDIR)/nghttp3_objalloc.Plo
	-rm -f ./$(DEPDIR)/nghttp3_opl.Plo
	-rm -f ./$(DEPDIR)/nghttp3_pq.Plo
	-rm -f ./$(DEPDIR)/nghttp3_qpack.Plo
	-rm -f ./$(DEPDIR)/nghttp3_qpack_huffman.Plo
	-rm -f ./$(DEPDIR)/nghttp3_qpack_huffman_data.Plo
	-rm -f ./$(DEPDIR)/nghttp3_range.Plo
	-rm -f ./$(DEPDIR)/nghttp3_rcbuf.Plo
	-rm -f ./$(DEPDIR)/nghttp3_ringbuf.Plo
	-rm -f ./$(DEPDIR)/nghttp3_settings.Plo
	-rm -f ./$(DEPDIR)/nghttp3_str.Plo
	-rm -f ./$(DEPDIR)/nghttp3_stream.Plo
	-rm -f ./$(DEPDIR)/nghttp3_tnode.Plo
	-rm -f ./$(DEPDIR)/nghttp3_unreachable.Plo
	-rm -f ./$(DEPDIR)/nghttp3_vec.Plo
	-rm -f ./$(DEPDIR)/nghttp3_version.Plo
	-rm -f sfparse/$(DEPDIR)/sfparse.Plo
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-recursive

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-recursive

pdf-am:

ps: ps-recursive

ps-am:

uninstall-am: uninstall-libLTLIBRARIES uninstall-pkgconfigDATA

.MAKE: $(am__recursive_targets) install-am install-strip

.PHONY: $(am__recursive_targets) CTAGS GTAGS TAGS all all-am \
	am--depfiles check check-am clean clean-generic \
	clean-libLTLIBRARIES clean-libtool cscopelist-am ctags \
	ctags-am distclean distclean-compile distclean-generic \
	distclean-libtool distclean-tags distdir dvi dvi-am html \
	html-am info info-am install install-am install-data \
	install-data-am install-dvi install-dvi-am install-exec \
	install-exec-am install-html install-html-am install-info \
	install-info-am install-libLTLIBRARIES install-man install-pdf \
	install-pdf-am install-pkgconfigDATA install-ps install-ps-am \
	install-strip installcheck installcheck-am installdirs \
	installdirs-am maintainer-clean maintainer-clean-generic \
	mostlyclean mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool pdf pdf-am ps ps-am tags tags-am uninstall \
	uninstall-am uninstall-libLTLIBRARIES uninstall-pkgconfigDATA

.PRECIOUS: Makefile


# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
