This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.

It was created by nghttp3 configure 1.11.0-DEV, which was
generated by GNU Autoconf 2.71.  Invocation command line was

  $ ./configure --prefix=/home/<USER>/ngtcp2_qpack/nghttp3/build --enable-lib-only

## --------- ##
## Platform. ##
## --------- ##

hostname = VM-0-3-ubuntu
uname -m = x86_64
uname -r = 5.15.0-118-generic
uname -s = Linux
uname -v = #128-Ubuntu SMP Fri Jul 5 09:28:59 UTC 2024

/usr/bin/uname -p = x86_64
/bin/uname -X     = unknown

/bin/arch              = x86_64
/usr/bin/arch -k       = unknown
/usr/convex/getsysinfo = unknown
/usr/bin/hostinfo      = unknown
/bin/machine           = unknown
/usr/bin/oslevel       = unknown
/bin/universe          = unknown

PATH: /home/<USER>/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/remote-cli/
PATH: /home/<USER>/anaconda3/bin/
PATH: /home/<USER>/anaconda3/condabin/
PATH: /usr/local/sbin/
PATH: /usr/local/bin/
PATH: /usr/sbin/
PATH: /usr/bin/
PATH: /sbin/
PATH: /bin/
PATH: /usr/games/
PATH: /usr/local/games/
PATH: /snap/bin/


## ----------- ##
## Core tests. ##
## ----------- ##

configure:3078: looking for aux files: missing install-sh config.guess config.sub ltmain.sh compile
configure:3091:  trying ././
configure:3120:   ././missing found
configure:3102:   ././install-sh found
configure:3120:   ././config.guess found
configure:3120:   ././config.sub found
configure:3120:   ././ltmain.sh found
configure:3120:   ././compile found
configure:3302: checking for gcc
configure:3323: found /usr/bin/gcc
configure:3334: result: gcc
configure:3687: checking for C compiler version
configure:3696: gcc --version >&5
gcc (Ubuntu 11.4.0-1ubuntu1~22.04) 11.4.0
Copyright (C) 2021 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:3707: $? = 0
configure:3696: gcc -v >&5
Using built-in specs.
COLLECT_GCC=gcc
COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-11-XeT9lY/gcc-11-11.4.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/gcc-11-XeT9lY/gcc-11-11.4.0/debian/tmp-gcn/usr --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
Thread model: posix
Supported LTO compression algorithms: zlib zstd
gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04) 
... rest of stderr output deleted ...
configure:3707: $? = 0
configure:3696: gcc -V >&5
gcc: error: unrecognized command-line option '-V'
gcc: fatal error: no input files
compilation terminated.
configure:3707: $? = 1
configure:3696: gcc -qversion >&5
gcc: error: unrecognized command-line option '-qversion'; did you mean '--version'?
gcc: fatal error: no input files
compilation terminated.
configure:3707: $? = 1
configure:3696: gcc -version >&5
gcc: error: unrecognized command-line option '-version'
gcc: fatal error: no input files
compilation terminated.
configure:3707: $? = 1
configure:3727: checking whether the C compiler works
configure:3749: gcc    conftest.c  >&5
configure:3753: $? = 0
configure:3803: result: yes
configure:3806: checking for C compiler default output file name
configure:3808: result: a.out
configure:3814: checking for suffix of executables
configure:3821: gcc -o conftest    conftest.c  >&5
configure:3825: $? = 0
configure:3848: result: 
configure:3870: checking whether we are cross compiling
configure:3878: gcc -o conftest    conftest.c  >&5
configure:3882: $? = 0
configure:3889: ./conftest
configure:3893: $? = 0
configure:3908: result: no
configure:3913: checking for suffix of object files
configure:3936: gcc -c   conftest.c >&5
configure:3940: $? = 0
configure:3962: result: o
configure:3966: checking whether the compiler supports GNU C
configure:3986: gcc -c   conftest.c >&5
configure:3986: $? = 0
configure:3996: result: yes
configure:4007: checking whether gcc accepts -g
configure:4028: gcc -c -g  conftest.c >&5
configure:4028: $? = 0
configure:4072: result: yes
configure:4092: checking for gcc option to enable C11 features
configure:4107: gcc  -c -g -O2  conftest.c >&5
configure:4107: $? = 0
configure:4125: result: none needed
configure:4241: checking whether gcc understands -c and -o together
configure:4264: gcc -c conftest.c -o conftest2.o
configure:4267: $? = 0
configure:4264: gcc -c conftest.c -o conftest2.o
configure:4267: $? = 0
configure:4279: result: yes
configure:4301: checking for stdio.h
configure:4301: gcc -c -g -O2  conftest.c >&5
configure:4301: $? = 0
configure:4301: result: yes
configure:4301: checking for stdlib.h
configure:4301: gcc -c -g -O2  conftest.c >&5
configure:4301: $? = 0
configure:4301: result: yes
configure:4301: checking for string.h
configure:4301: gcc -c -g -O2  conftest.c >&5
configure:4301: $? = 0
configure:4301: result: yes
configure:4301: checking for inttypes.h
configure:4301: gcc -c -g -O2  conftest.c >&5
configure:4301: $? = 0
configure:4301: result: yes
configure:4301: checking for stdint.h
configure:4301: gcc -c -g -O2  conftest.c >&5
configure:4301: $? = 0
configure:4301: result: yes
configure:4301: checking for strings.h
configure:4301: gcc -c -g -O2  conftest.c >&5
configure:4301: $? = 0
configure:4301: result: yes
configure:4301: checking for sys/stat.h
configure:4301: gcc -c -g -O2  conftest.c >&5
configure:4301: $? = 0
configure:4301: result: yes
configure:4301: checking for sys/types.h
configure:4301: gcc -c -g -O2  conftest.c >&5
configure:4301: $? = 0
configure:4301: result: yes
configure:4301: checking for unistd.h
configure:4301: gcc -c -g -O2  conftest.c >&5
configure:4301: $? = 0
configure:4301: result: yes
configure:4301: checking for wchar.h
configure:4301: gcc -c -g -O2  conftest.c >&5
configure:4301: $? = 0
configure:4301: result: yes
configure:4301: checking for minix/config.h
configure:4301: gcc -c -g -O2  conftest.c >&5
conftest.c:47:10: fatal error: minix/config.h: No such file or directory
   47 | #include <minix/config.h>
      |          ^~~~~~~~~~~~~~~~
compilation terminated.
configure:4301: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| #include <minix/config.h>
configure:4301: result: no
configure:4332: checking whether it is safe to define __EXTENSIONS__
configure:4351: gcc -c -g -O2  conftest.c >&5
configure:4351: $? = 0
configure:4359: result: yes
configure:4362: checking whether _XOPEN_SOURCE should be defined
configure:4384: gcc -c -g -O2  conftest.c >&5
configure:4384: $? = 0
configure:4411: result: no
configure:4501: checking build system type
configure:4516: result: x86_64-pc-linux-gnu
configure:4536: checking host system type
configure:4550: result: x86_64-pc-linux-gnu
configure:4591: checking how to print strings
configure:4618: result: printf
configure:4639: checking for a sed that does not truncate output
configure:4709: result: /usr/bin/sed
configure:4727: checking for grep that handles long lines and -e
configure:4791: result: /usr/bin/grep
configure:4796: checking for egrep
configure:4864: result: /usr/bin/grep -E
configure:4869: checking for fgrep
configure:4937: result: /usr/bin/grep -F
configure:4973: checking for ld used by gcc
configure:5041: result: /usr/bin/ld
configure:5048: checking if the linker (/usr/bin/ld) is GNU ld
configure:5064: result: yes
configure:5076: checking for BSD- or MS-compatible name lister (nm)
configure:5131: result: /usr/bin/nm -B
configure:5271: checking the name lister (/usr/bin/nm -B) interface
configure:5279: gcc -c -g -O2  conftest.c >&5
configure:5282: /usr/bin/nm -B "conftest.o"
configure:5285: output
0000000000000000 B some_variable
configure:5292: result: BSD nm
configure:5295: checking whether ln -s works
configure:5299: result: yes
configure:5307: checking the maximum length of command line arguments
configure:5439: result: 1572864
configure:5487: checking how to convert x86_64-pc-linux-gnu file names to x86_64-pc-linux-gnu format
configure:5528: result: func_convert_file_noop
configure:5535: checking how to convert x86_64-pc-linux-gnu file names to toolchain format
configure:5556: result: func_convert_file_noop
configure:5563: checking for /usr/bin/ld option to reload object files
configure:5571: result: -r
configure:5650: checking for objdump
configure:5671: found /usr/bin/objdump
configure:5682: result: objdump
configure:5714: checking how to recognize dependent libraries
configure:5915: result: pass_all
configure:6005: checking for dlltool
configure:6040: result: no
configure:6070: checking how to associate runtime and link libraries
configure:6098: result: printf %s\n
configure:6163: checking for ar
configure:6184: found /usr/bin/ar
configure:6195: result: ar
configure:6232: checking for archiver @FILE support
configure:6250: gcc -c -g -O2  conftest.c >&5
configure:6250: $? = 0
configure:6254: ar cr libconftest.a @conftest.lst >&5
configure:6257: $? = 0
configure:6262: ar cr libconftest.a @conftest.lst >&5
ar: conftest.o: No such file or directory
configure:6265: $? = 1
configure:6277: result: @
configure:6340: checking for strip
configure:6361: found /usr/bin/strip
configure:6372: result: strip
configure:6449: checking for ranlib
configure:6470: found /usr/bin/ranlib
configure:6481: result: ranlib
configure:6558: checking for gawk
configure:6579: found /usr/bin/gawk
configure:6590: result: gawk
configure:6630: checking command to parse /usr/bin/nm -B output from gcc object
configure:6784: gcc -c -g -O2  conftest.c >&5
configure:6787: $? = 0
configure:6791: /usr/bin/nm -B conftest.o | sed -n -e 's/^.*[	 ]\([ABCDGIRSTW][ABCDGIRSTW]*\)[	 ][	 ]*\([_A-Za-z][_A-Za-z0-9]*\)$/\1 \2 \2/p' | sed '/ __gnu_lto/d' > conftest.nm
configure:6857: gcc -o conftest -g -O2   conftest.c conftstm.o >&5
configure:6860: $? = 0
configure:6898: result: ok
configure:6945: checking for sysroot
configure:6976: result: no
configure:6983: checking for a working dd
configure:7027: result: /usr/bin/dd
configure:7031: checking how to truncate binary pipes
configure:7047: result: /usr/bin/dd bs=4096 count=1
configure:7184: gcc -c -g -O2  conftest.c >&5
configure:7187: $? = 0
configure:7384: checking for mt
configure:7405: found /usr/bin/mt
configure:7416: result: mt
configure:7439: checking if mt is a manifest tool
configure:7446: mt '-?'
configure:7454: result: no
configure:8180: checking for dlfcn.h
configure:8180: gcc -c -g -O2  conftest.c >&5
configure:8180: $? = 0
configure:8180: result: yes
configure:8439: checking for objdir
configure:8455: result: .libs
configure:8719: checking if gcc supports -fno-rtti -fno-exceptions
configure:8738: gcc -c -g -O2  -fno-rtti -fno-exceptions conftest.c >&5
cc1: warning: command-line option '-fno-rtti' is valid for C++/D/ObjC++ but not for C
configure:8742: $? = 0
configure:8755: result: no
configure:9119: checking for gcc option to produce PIC
configure:9127: result: -fPIC -DPIC
configure:9135: checking if gcc PIC flag -fPIC -DPIC works
configure:9154: gcc -c -g -O2  -fPIC -DPIC -DPIC conftest.c >&5
configure:9158: $? = 0
configure:9171: result: yes
configure:9200: checking if gcc static flag -static works
configure:9229: result: yes
configure:9244: checking if gcc supports -c -o file.o
configure:9266: gcc -c -g -O2  -o out/conftest2.o conftest.c >&5
configure:9270: $? = 0
configure:9292: result: yes
configure:9300: checking if gcc supports -c -o file.o
configure:9348: result: yes
configure:9381: checking whether the gcc linker (/usr/bin/ld -m elf_x86_64) supports shared libraries
configure:10651: result: yes
configure:10688: checking whether -lc should be explicitly linked in
configure:10697: gcc -c -g -O2  conftest.c >&5
configure:10700: $? = 0
configure:10715: gcc -shared  -fPIC -DPIC conftest.o  -v -Wl,-soname -Wl,conftest -o conftest 2\>\&1 \| /usr/bin/grep  -lc  \>/dev/null 2\>\&1
configure:10718: $? = 0
configure:10732: result: no
configure:10892: checking dynamic linker characteristics
configure:11474: gcc -o conftest -g -O2   -Wl,-rpath -Wl,/foo conftest.c  >&5
configure:11474: $? = 0
configure:11725: result: GNU/Linux ld.so
configure:11847: checking how to hardcode library paths into programs
configure:11872: result: immediate
configure:12424: checking whether stripping libraries is possible
configure:12429: result: yes
configure:12464: checking if libtool supports shared libraries
configure:12466: result: yes
configure:12469: checking whether to build shared libraries
configure:12494: result: yes
configure:12497: checking whether to build static libraries
configure:12501: result: yes
configure:12541: checking target system type
configure:12555: result: x86_64-pc-linux-gnu
configure:12599: checking for a BSD-compatible install
configure:12672: result: /usr/bin/install -c
configure:12683: checking whether build environment is sane
configure:12738: result: yes
configure:12893: checking for a race-free mkdir -p
configure:12937: result: /usr/bin/mkdir -p
configure:12940: checking whether make sets $(MAKE)
configure:12963: result: yes
configure:12985: checking whether make supports the include directive
configure:13000: make -f confmf.GNU && cat confinc.out
this is the am__doit target
configure:13003: $? = 0
configure:13022: result: yes (GNU style)
configure:13057: checking whether make supports nested variables
configure:13075: result: yes
configure:13150: checking how to create a pax tar archive
configure:13161: tar --version
tar (GNU tar) 1.34
Copyright (C) 2021 Free Software Foundation, Inc.
License GPLv3+: GNU GPL version 3 or later <https://gnu.org/licenses/gpl.html>.
This is free software: you are free to change and redistribute it.
There is NO WARRANTY, to the extent permitted by law.

Written by John Gilmore and Jay Fenlason.
configure:13164: $? = 0
configure:13204: tardir=conftest.dir && eval tar --format=posix -chf - "$tardir" >conftest.tar
configure:13207: $? = 0
configure:13211: tar -xf - <conftest.tar
configure:13214: $? = 0
configure:13216: cat conftest.dir/file
GrepMe
configure:13219: $? = 0
configure:13233: result: gnutar
configure:13242: checking dependency style of gcc
configure:13354: result: gcc3
configure:13439: checking whether make supports nested variables
configure:13457: result: yes
configure:13595: checking for gcc
configure:13627: result: gcc
configure:13980: checking for C compiler version
configure:13989: gcc --version >&5
gcc (Ubuntu 11.4.0-1ubuntu1~22.04) 11.4.0
Copyright (C) 2021 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:14000: $? = 0
configure:13989: gcc -v >&5
Using built-in specs.
COLLECT_GCC=gcc
COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-11-XeT9lY/gcc-11-11.4.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/gcc-11-XeT9lY/gcc-11-11.4.0/debian/tmp-gcn/usr --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
Thread model: posix
Supported LTO compression algorithms: zlib zstd
gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04) 
... rest of stderr output deleted ...
configure:14000: $? = 0
configure:13989: gcc -V >&5
gcc: error: unrecognized command-line option '-V'
gcc: fatal error: no input files
compilation terminated.
configure:14000: $? = 1
configure:13989: gcc -qversion >&5
gcc: error: unrecognized command-line option '-qversion'; did you mean '--version'?
gcc: fatal error: no input files
compilation terminated.
configure:14000: $? = 1
configure:13989: gcc -version >&5
gcc: error: unrecognized command-line option '-version'
gcc: fatal error: no input files
compilation terminated.
configure:14000: $? = 1
configure:14004: checking whether the compiler supports GNU C
configure:14034: result: yes
configure:14045: checking whether gcc accepts -g
configure:14110: result: yes
configure:14130: checking for gcc option to enable C11 features
configure:14163: result: none needed
configure:14279: checking whether gcc understands -c and -o together
configure:14317: result: yes
configure:14403: checking for g++
configure:14424: found /usr/bin/g++
configure:14435: result: g++
configure:14462: checking for C++ compiler version
configure:14471: g++ --version >&5
g++ (Ubuntu 11.4.0-1ubuntu1~22.04) 11.4.0
Copyright (C) 2021 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:14482: $? = 0
configure:14471: g++ -v >&5
Using built-in specs.
COLLECT_GCC=g++
COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/11/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 11.4.0-1ubuntu1~22.04' --with-bugurl=file:///usr/share/doc/gcc-11/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-11 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-11-XeT9lY/gcc-11-11.4.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/gcc-11-XeT9lY/gcc-11-11.4.0/debian/tmp-gcn/usr --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
Thread model: posix
Supported LTO compression algorithms: zlib zstd
gcc version 11.4.0 (Ubuntu 11.4.0-1ubuntu1~22.04) 
... rest of stderr output deleted ...
configure:14482: $? = 0
configure:14471: g++ -V >&5
g++: error: unrecognized command-line option '-V'
g++: fatal error: no input files
compilation terminated.
configure:14482: $? = 1
configure:14471: g++ -qversion >&5
g++: error: unrecognized command-line option '-qversion'; did you mean '--version'?
g++: fatal error: no input files
compilation terminated.
configure:14482: $? = 1
configure:14486: checking whether the compiler supports GNU C++
configure:14506: g++ -c   conftest.cpp >&5
configure:14506: $? = 0
configure:14516: result: yes
configure:14527: checking whether g++ accepts -g
configure:14548: g++ -c -g  conftest.cpp >&5
configure:14548: $? = 0
configure:14592: result: yes
configure:14612: checking for g++ option to enable C++11 features
configure:14627: g++  -c -g -O2  conftest.cpp >&5
conftest.cpp: In function 'int main(int, char**)':
conftest.cpp:206:25: warning: empty parentheses were disambiguated as a function declaration [-Wvexing-parse]
  206 |   cxx11test::delegate d2();
      |                         ^~
conftest.cpp:206:25: note: remove parentheses to default-initialize a variable
  206 |   cxx11test::delegate d2();
      |                         ^~
      |                         --
conftest.cpp:206:25: note: or replace parentheses with braces to value-initialize a variable
configure:14627: $? = 0
configure:14645: result: none needed
configure:14727: checking how to run the C++ preprocessor
configure:14749: g++ -E  conftest.cpp
configure:14749: $? = 0
configure:14764: g++ -E  conftest.cpp
conftest.cpp:40:10: fatal error: ac_nonexistent.h: No such file or directory
   40 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:14764: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:14791: result: g++ -E
configure:14805: g++ -E  conftest.cpp
configure:14805: $? = 0
configure:14820: g++ -E  conftest.cpp
conftest.cpp:40:10: fatal error: ac_nonexistent.h: No such file or directory
   40 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:14820: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:14985: checking for ld used by g++
configure:15053: result: /usr/bin/ld -m elf_x86_64
configure:15060: checking if the linker (/usr/bin/ld -m elf_x86_64) is GNU ld
configure:15076: result: yes
configure:15131: checking whether the g++ linker (/usr/bin/ld -m elf_x86_64) supports shared libraries
configure:16208: result: yes
configure:16244: g++ -c -g -O2  conftest.cpp >&5
configure:16247: $? = 0
configure:16728: checking for g++ option to produce PIC
configure:16736: result: -fPIC -DPIC
configure:16744: checking if g++ PIC flag -fPIC -DPIC works
configure:16763: g++ -c -g -O2  -fPIC -DPIC -DPIC conftest.cpp >&5
configure:16767: $? = 0
configure:16780: result: yes
configure:16803: checking if g++ static flag -static works
configure:16832: result: yes
configure:16844: checking if g++ supports -c -o file.o
configure:16866: g++ -c -g -O2  -o out/conftest2.o conftest.cpp >&5
configure:16870: $? = 0
configure:16892: result: yes
configure:16897: checking if g++ supports -c -o file.o
configure:16945: result: yes
configure:16975: checking whether the g++ linker (/usr/bin/ld -m elf_x86_64) supports shared libraries
configure:17018: result: yes
configure:17160: checking dynamic linker characteristics
configure:17920: result: GNU/Linux ld.so
configure:17985: checking how to hardcode library paths into programs
configure:18010: result: immediate
configure:18052: checking dependency style of g++
configure:18164: result: gcc3
configure:18184: checking how to run the C preprocessor
configure:18210: gcc -E  conftest.c
configure:18210: $? = 0
configure:18225: gcc -E  conftest.c
conftest.c:40:10: fatal error: ac_nonexistent.h: No such file or directory
   40 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:18225: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:18252: result: gcc -E
configure:18266: gcc -E  conftest.c
configure:18266: $? = 0
configure:18281: gcc -E  conftest.c
conftest.c:40:10: fatal error: ac_nonexistent.h: No such file or directory
   40 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:18281: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:18312: checking whether ln -s works
configure:18316: result: yes
configure:18323: checking whether make sets $(MAKE)
configure:18346: result: yes
configure:18416: checking for pkg-config
configure:18439: found /usr/bin/pkg-config
configure:18451: result: /usr/bin/pkg-config
configure:18476: checking pkg-config is at least version 0.20
configure:18479: result: yes
configure:18496: checking whether g++ supports C++17 features by default
configure:19295: g++ -c -g -O2  conftest.cpp >&5
configure:19295: $? = 0
configure:19303: result: yes
configure:21014: checking for arpa/inet.h
configure:21014: gcc -c -g -O2  conftest.c >&5
configure:21014: $? = 0
configure:21014: result: yes
configure:21020: checking for stddef.h
configure:21020: gcc -c -g -O2  conftest.c >&5
configure:21020: $? = 0
configure:21020: result: yes
configure:21026: checking for stdint.h
configure:21026: result: yes
configure:21032: checking for stdlib.h
configure:21032: result: yes
configure:21038: checking for string.h
configure:21038: result: yes
configure:21044: checking for unistd.h
configure:21044: result: yes
configure:21050: checking for sys/endian.h
configure:21050: gcc -c -g -O2  conftest.c >&5
conftest.c:75:10: fatal error: sys/endian.h: No such file or directory
   75 | #include <sys/endian.h>
      |          ^~~~~~~~~~~~~~
compilation terminated.
configure:21050: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| #define HAVE_CXX17 1
| #define HAVE_ARPA_INET_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| #include <sys/endian.h>
configure:21050: result: no
configure:21056: checking for endian.h
configure:21056: gcc -c -g -O2  conftest.c >&5
configure:21056: $? = 0
configure:21056: result: yes
configure:21062: checking for byteswap.h
configure:21062: gcc -c -g -O2  conftest.c >&5
configure:21062: $? = 0
configure:21062: result: yes
configure:21071: checking for size_t
configure:21071: gcc -c -g -O2  conftest.c >&5
configure:21071: $? = 0
configure:21071: gcc -c -g -O2  conftest.c >&5
conftest.c: In function 'main':
conftest.c:80:21: error: expected expression before ')' token
   80 | if (sizeof ((size_t)))
      |                     ^
configure:21071: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| #define HAVE_CXX17 1
| #define HAVE_ARPA_INET_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_ENDIAN_H 1
| #define HAVE_BYTESWAP_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| int
| main (void)
| {
| if (sizeof ((size_t)))
| 	    return 0;
|   ;
|   return 0;
| }
configure:21071: result: yes
configure:21081: checking for ssize_t
configure:21081: gcc -c -g -O2  conftest.c >&5
configure:21081: $? = 0
configure:21081: gcc -c -g -O2  conftest.c >&5
conftest.c: In function 'main':
conftest.c:80:22: error: expected expression before ')' token
   80 | if (sizeof ((ssize_t)))
      |                      ^
configure:21081: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| #define HAVE_CXX17 1
| #define HAVE_ARPA_INET_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_ENDIAN_H 1
| #define HAVE_BYTESWAP_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| int
| main (void)
| {
| if (sizeof ((ssize_t)))
| 	    return 0;
|   ;
|   return 0;
| }
configure:21081: result: yes
configure:21091: checking for uint8_t
configure:21091: gcc -c -g -O2  conftest.c >&5
configure:21091: $? = 0
configure:21091: result: yes
configure:21103: checking for uint16_t
configure:21103: gcc -c -g -O2  conftest.c >&5
configure:21103: $? = 0
configure:21103: result: yes
configure:21113: checking for uint32_t
configure:21113: gcc -c -g -O2  conftest.c >&5
configure:21113: $? = 0
configure:21113: result: yes
configure:21125: checking for uint64_t
configure:21125: gcc -c -g -O2  conftest.c >&5
configure:21125: $? = 0
configure:21125: result: yes
configure:21137: checking for int8_t
configure:21137: gcc -c -g -O2  conftest.c >&5
configure:21137: $? = 0
configure:21137: gcc -c -g -O2  conftest.c >&5
conftest.c: In function 'main':
conftest.c:81:12: error: size of array 'test_array' is negative
   81 | static int test_array [1 - 2 * !((int8_t) (((((int8_t) 1 << N) << N) - 1) * 2 + 1)
      |            ^~~~~~~~~~
configure:21137: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| #define HAVE_CXX17 1
| #define HAVE_ARPA_INET_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_ENDIAN_H 1
| #define HAVE_BYTESWAP_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| 	        enum { N = 8 / 2 - 1 };
| int
| main (void)
| {
| static int test_array [1 - 2 * !((int8_t) (((((int8_t) 1 << N) << N) - 1) * 2 + 1)
| 		 < (int8_t) (((((int8_t) 1 << N) << N) - 1) * 2 + 2))];
| test_array [0] = 0;
| return test_array [0];
| 
|   ;
|   return 0;
| }
configure:21137: result: yes
configure:21146: checking for int16_t
configure:21146: gcc -c -g -O2  conftest.c >&5
configure:21146: $? = 0
configure:21146: gcc -c -g -O2  conftest.c >&5
conftest.c: In function 'main':
conftest.c:81:12: error: size of array 'test_array' is negative
   81 | static int test_array [1 - 2 * !((int16_t) (((((int16_t) 1 << N) << N) - 1) * 2 + 1)
      |            ^~~~~~~~~~
configure:21146: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| #define HAVE_CXX17 1
| #define HAVE_ARPA_INET_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_ENDIAN_H 1
| #define HAVE_BYTESWAP_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| 	        enum { N = 16 / 2 - 1 };
| int
| main (void)
| {
| static int test_array [1 - 2 * !((int16_t) (((((int16_t) 1 << N) << N) - 1) * 2 + 1)
| 		 < (int16_t) (((((int16_t) 1 << N) << N) - 1) * 2 + 2))];
| test_array [0] = 0;
| return test_array [0];
| 
|   ;
|   return 0;
| }
configure:21146: result: yes
configure:21155: checking for int32_t
configure:21155: gcc -c -g -O2  conftest.c >&5
configure:21155: $? = 0
configure:21155: gcc -c -g -O2  conftest.c >&5
conftest.c: In function 'main':
conftest.c:82:67: warning: integer overflow in expression of type 'int' results in '-2147483648' [-Woverflow]
   82 |                  < (int32_t) (((((int32_t) 1 << N) << N) - 1) * 2 + 2))];
      |                                                                   ^
conftest.c:81:12: error: storage size of 'test_array' isn't constant
   81 | static int test_array [1 - 2 * !((int32_t) (((((int32_t) 1 << N) << N) - 1) * 2 + 1)
      |            ^~~~~~~~~~
configure:21155: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| #define HAVE_CXX17 1
| #define HAVE_ARPA_INET_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_ENDIAN_H 1
| #define HAVE_BYTESWAP_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| 	        enum { N = 32 / 2 - 1 };
| int
| main (void)
| {
| static int test_array [1 - 2 * !((int32_t) (((((int32_t) 1 << N) << N) - 1) * 2 + 1)
| 		 < (int32_t) (((((int32_t) 1 << N) << N) - 1) * 2 + 2))];
| test_array [0] = 0;
| return test_array [0];
| 
|   ;
|   return 0;
| }
configure:21155: result: yes
configure:21164: checking for int64_t
configure:21164: gcc -c -g -O2  conftest.c >&5
configure:21164: $? = 0
configure:21164: gcc -c -g -O2  conftest.c >&5
conftest.c: In function 'main':
conftest.c:82:67: warning: integer overflow in expression of type 'long int' results in '-9223372036854775808' [-Woverflow]
   82 |                  < (int64_t) (((((int64_t) 1 << N) << N) - 1) * 2 + 2))];
      |                                                                   ^
conftest.c:81:12: error: storage size of 'test_array' isn't constant
   81 | static int test_array [1 - 2 * !((int64_t) (((((int64_t) 1 << N) << N) - 1) * 2 + 1)
      |            ^~~~~~~~~~
configure:21164: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| #define HAVE_CXX17 1
| #define HAVE_ARPA_INET_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_ENDIAN_H 1
| #define HAVE_BYTESWAP_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| 	        enum { N = 64 / 2 - 1 };
| int
| main (void)
| {
| static int test_array [1 - 2 * !((int64_t) (((((int64_t) 1 << N) << N) - 1) * 2 + 1)
| 		 < (int64_t) (((((int64_t) 1 << N) << N) - 1) * 2 + 2))];
| test_array [0] = 0;
| return test_array [0];
| 
|   ;
|   return 0;
| }
configure:21164: result: yes
configure:21173: checking for off_t
configure:21173: gcc -c -g -O2  conftest.c >&5
configure:21173: $? = 0
configure:21173: gcc -c -g -O2  conftest.c >&5
conftest.c: In function 'main':
conftest.c:80:20: error: expected expression before ')' token
   80 | if (sizeof ((off_t)))
      |                    ^
configure:21173: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| #define HAVE_CXX17 1
| #define HAVE_ARPA_INET_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_ENDIAN_H 1
| #define HAVE_BYTESWAP_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| int
| main (void)
| {
| if (sizeof ((off_t)))
| 	    return 0;
|   ;
|   return 0;
| }
configure:21173: result: yes
configure:21184: checking for pid_t
configure:21184: gcc -c -g -O2  conftest.c >&5
configure:21184: $? = 0
configure:21184: gcc -c -g -O2  conftest.c >&5
conftest.c: In function 'main':
conftest.c:81:20: error: expected expression before ')' token
   81 | if (sizeof ((pid_t)))
      |                    ^
configure:21184: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| #define HAVE_CXX17 1
| #define HAVE_ARPA_INET_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_ENDIAN_H 1
| #define HAVE_BYTESWAP_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| 
| int
| main (void)
| {
| if (sizeof ((pid_t)))
| 	    return 0;
|   ;
|   return 0;
| }
configure:21184: result: yes
configure:21221: checking for uid_t in sys/types.h
configure:21242: result: yes
configure:21253: checking for ptrdiff_t
configure:21253: gcc -c -g -O2  conftest.c >&5
configure:21253: $? = 0
configure:21253: gcc -c -g -O2  conftest.c >&5
conftest.c: In function 'main':
conftest.c:80:24: error: expected expression before ')' token
   80 | if (sizeof ((ptrdiff_t)))
      |                        ^
configure:21253: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| #define HAVE_CXX17 1
| #define HAVE_ARPA_INET_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_ENDIAN_H 1
| #define HAVE_BYTESWAP_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| int
| main (void)
| {
| if (sizeof ((ptrdiff_t)))
| 	    return 0;
|   ;
|   return 0;
| }
configure:21253: result: yes
configure:21262: checking whether byte ordering is bigendian
configure:21278: gcc -c -g -O2  conftest.c >&5
conftest.c:51:16: error: unknown type name 'not'
   51 |                not a universal capable compiler
      |                ^~~
conftest.c:51:22: error: expected '=', ',', ';', 'asm' or '__attribute__' before 'universal'
   51 |                not a universal capable compiler
      |                      ^~~~~~~~~
conftest.c:51:22: error: unknown type name 'universal'
configure:21278: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| #define HAVE_CXX17 1
| #define HAVE_ARPA_INET_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_ENDIAN_H 1
| #define HAVE_BYTESWAP_H 1
| #define HAVE_PTRDIFF_T 1
| /* end confdefs.h.  */
| #ifndef __APPLE_CC__
| 	       not a universal capable compiler
| 	     #endif
| 	     typedef int dummy;
| 
configure:21324: gcc -c -g -O2  conftest.c >&5
configure:21324: $? = 0
configure:21343: gcc -c -g -O2  conftest.c >&5
conftest.c: In function 'main':
conftest.c:57:18: error: unknown type name 'not'; did you mean 'ino_t'?
   57 |                  not big endian
      |                  ^~~
      |                  ino_t
conftest.c:57:26: error: expected '=', ',', ';', 'asm' or '__attribute__' before 'endian'
   57 |                  not big endian
      |                          ^~~~~~
configure:21343: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| #define HAVE_CXX17 1
| #define HAVE_ARPA_INET_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_ENDIAN_H 1
| #define HAVE_BYTESWAP_H 1
| #define HAVE_PTRDIFF_T 1
| /* end confdefs.h.  */
| #include <sys/types.h>
| 		#include <sys/param.h>
| 
| int
| main (void)
| {
| #if BYTE_ORDER != BIG_ENDIAN
| 		 not big endian
| 		#endif
| 
|   ;
|   return 0;
| }
configure:21477: result: no
configure:21495: checking for inline
configure:21512: gcc -c -g -O2  conftest.c >&5
configure:21512: $? = 0
configure:21521: result: inline
configure:21547: checking for special C compiler options needed for large files
configure:21595: result: no
configure:21601: checking for _FILE_OFFSET_BITS value needed for large files
configure:21627: gcc -c -g -O2  conftest.c >&5
configure:21627: $? = 0
configure:21661: result: no
configure:21745: checking for memmove
configure:21745: gcc -o conftest -g -O2   conftest.c  >&5
conftest.c:66:6: warning: conflicting types for built-in function 'memmove'; expected 'void *(void *, const void *, long unsigned int)' [-Wbuiltin-declaration-mismatch]
   66 | char memmove ();
      |      ^~~~~~~
conftest.c:58:1: note: 'memmove' is declared in header '<string.h>'
   57 | #include <limits.h>
   58 | #undef memmove
configure:21745: $? = 0
configure:21745: result: yes
configure:21751: checking for memset
configure:21751: gcc -o conftest -g -O2   conftest.c  >&5
conftest.c:67:6: warning: conflicting types for built-in function 'memset'; expected 'void *(void *, int,  long unsigned int)' [-Wbuiltin-declaration-mismatch]
   67 | char memset ();
      |      ^~~~~~
conftest.c:59:1: note: 'memset' is declared in header '<string.h>'
   58 | #include <limits.h>
   59 | #undef memset
configure:21751: $? = 0
configure:21751: result: yes
configure:21760: checking for gcc options needed to detect all undeclared functions
configure:21782: gcc -c -g -O2   conftest.c >&5
conftest.c: In function 'main':
conftest.c:56:8: error: 'strchr' undeclared (first use in this function)
   56 | (void) strchr;
      |        ^~~~~~
conftest.c:1:1: note: 'strchr' is defined in header '<string.h>'; did you forget to '#include <string.h>'?
    1 | /* confdefs.h */
conftest.c:56:8: note: each undeclared identifier is reported only once for each function it appears in
   56 | (void) strchr;
      |        ^~~~~~
configure:21782: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "nghttp3"
| #define PACKAGE_TARNAME "nghttp3"
| #define PACKAGE_VERSION "1.11.0-DEV"
| #define PACKAGE_STRING "nghttp3 1.11.0-DEV"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| #define PACKAGE "nghttp3"
| #define VERSION "1.11.0-DEV"
| #define HAVE_CXX17 1
| #define HAVE_ARPA_INET_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_ENDIAN_H 1
| #define HAVE_BYTESWAP_H 1
| #define HAVE_PTRDIFF_T 1
| #define HAVE_MEMMOVE 1
| #define HAVE_MEMSET 1
| /* end confdefs.h.  */
| 
| int
| main (void)
| {
| (void) strchr;
|   ;
|   return 0;
| }
configure:21809: gcc -c -g -O2   conftest.c >&5
configure:21809: $? = 0
configure:21826: result: none needed
configure:21840: checking whether be64toh is declared
configure:21840: gcc -c -g -O2   conftest.c >&5
configure:21840: $? = 0
configure:21840: result: yes
configure:21858: checking whether bswap_64 is declared
configure:21858: gcc -c -g -O2   conftest.c >&5
configure:21858: $? = 0
configure:21858: result: yes
configure:23795: checking whether C compiler accepts -fvisibility=hidden
configure:23815: gcc -c -g -O2  -fvisibility=hidden  conftest.c >&5
configure:23815: $? = 0
configure:23824: result: yes
configure:23939: checking that generated files are newer than configure
configure:23945: result: done
configure:23977: creating ./config.status

## ---------------------- ##
## Running config.status. ##
## ---------------------- ##

This file was extended by nghttp3 config.status 1.11.0-DEV, which was
generated by GNU Autoconf 2.71.  Invocation command line was

  CONFIG_FILES    = 
  CONFIG_HEADERS  = 
  CONFIG_LINKS    = 
  CONFIG_COMMANDS = 
  $ ./config.status 

on VM-0-3-ubuntu

config.status:1200: creating Makefile
config.status:1200: creating lib/Makefile
config.status:1200: creating lib/libnghttp3.pc
config.status:1200: creating lib/includes/Makefile
config.status:1200: creating lib/includes/nghttp3/version.h
config.status:1200: creating tests/Makefile
config.status:1200: creating doc/Makefile
config.status:1200: creating doc/source/conf.py
config.status:1200: creating examples/Makefile
config.status:1200: creating config.h
config.status:1429: executing libtool commands
config.status:1429: executing depfiles commands
config.status:2216: cd lib       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
config.status:2221: $? = 0
config.status:2216: cd tests       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
config.status:2221: $? = 0
config.status:2216: cd examples       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
config.status:2221: $? = 0
configure:26392: summary of build options:

    Package version: 1.11.0-DEV
    Library version: 11:9:2
    Install prefix:  /home/<USER>/ngtcp2_qpack/nghttp3/build
    System types:
      Build:         x86_64-pc-linux-gnu
      Host:          x86_64-pc-linux-gnu
      Target:        x86_64-pc-linux-gnu
    Compiler:
      C preprocessor: gcc -E
      CPPFLAGS:       
      C compiler:     gcc
      CFLAGS:         -g -O2
      C++ compiler:   g++
      CXXFLAGS:       -g -O2
      LDFLAGS:        
      WARNCFLAGS:     
      WARNCXXFLAGS:   
      EXTRACFLAG:     -fvisibility=hidden
      LIBS:           
    Library:
      Shared:         yes
      Static:         yes
    Debug:
      Debug:          no (CFLAGS='')
    Library only:     yes
    Examples:         no


## ---------------- ##
## Cache variables. ##
## ---------------- ##

ac_cv_build=x86_64-pc-linux-gnu
ac_cv_c_bigendian=no
ac_cv_c_compiler_gnu=yes
ac_cv_c_inline=inline
ac_cv_c_int16_t=yes
ac_cv_c_int32_t=yes
ac_cv_c_int64_t=yes
ac_cv_c_int8_t=yes
ac_cv_c_uint16_t=yes
ac_cv_c_uint32_t=yes
ac_cv_c_uint64_t=yes
ac_cv_c_uint8_t=yes
ac_cv_c_undeclared_builtin_options='none needed'
ac_cv_cxx_compiler_gnu=yes
ac_cv_env_CCC_set=
ac_cv_env_CCC_value=
ac_cv_env_CC_set=
ac_cv_env_CC_value=
ac_cv_env_CFLAGS_set=
ac_cv_env_CFLAGS_value=
ac_cv_env_CPPFLAGS_set=
ac_cv_env_CPPFLAGS_value=
ac_cv_env_CPP_set=
ac_cv_env_CPP_value=
ac_cv_env_CXXCPP_set=
ac_cv_env_CXXCPP_value=
ac_cv_env_CXXFLAGS_set=
ac_cv_env_CXXFLAGS_value=
ac_cv_env_CXX_set=
ac_cv_env_CXX_value=
ac_cv_env_LDFLAGS_set=
ac_cv_env_LDFLAGS_value=
ac_cv_env_LIBS_set=
ac_cv_env_LIBS_value=
ac_cv_env_LT_SYS_LIBRARY_PATH_set=
ac_cv_env_LT_SYS_LIBRARY_PATH_value=
ac_cv_env_PKG_CONFIG_LIBDIR_set=
ac_cv_env_PKG_CONFIG_LIBDIR_value=
ac_cv_env_PKG_CONFIG_PATH_set=
ac_cv_env_PKG_CONFIG_PATH_value=
ac_cv_env_PKG_CONFIG_set=
ac_cv_env_PKG_CONFIG_value=
ac_cv_env_build_alias_set=
ac_cv_env_build_alias_value=
ac_cv_env_host_alias_set=
ac_cv_env_host_alias_value=
ac_cv_env_target_alias_set=
ac_cv_env_target_alias_value=
ac_cv_func_memmove=yes
ac_cv_func_memset=yes
ac_cv_have_decl_be64toh=yes
ac_cv_have_decl_bswap_64=yes
ac_cv_header_arpa_inet_h=yes
ac_cv_header_byteswap_h=yes
ac_cv_header_dlfcn_h=yes
ac_cv_header_endian_h=yes
ac_cv_header_inttypes_h=yes
ac_cv_header_minix_config_h=no
ac_cv_header_stddef_h=yes
ac_cv_header_stdint_h=yes
ac_cv_header_stdio_h=yes
ac_cv_header_stdlib_h=yes
ac_cv_header_string_h=yes
ac_cv_header_strings_h=yes
ac_cv_header_sys_endian_h=no
ac_cv_header_sys_stat_h=yes
ac_cv_header_sys_types_h=yes
ac_cv_header_unistd_h=yes
ac_cv_header_wchar_h=yes
ac_cv_host=x86_64-pc-linux-gnu
ac_cv_objext=o
ac_cv_path_EGREP='/usr/bin/grep -E'
ac_cv_path_FGREP='/usr/bin/grep -F'
ac_cv_path_GREP=/usr/bin/grep
ac_cv_path_SED=/usr/bin/sed
ac_cv_path_ac_pt_PKG_CONFIG=/usr/bin/pkg-config
ac_cv_path_install='/usr/bin/install -c'
ac_cv_path_lt_DD=/usr/bin/dd
ac_cv_path_mkdir=/usr/bin/mkdir
ac_cv_prog_AWK=gawk
ac_cv_prog_CPP='gcc -E'
ac_cv_prog_CXXCPP='g++ -E'
ac_cv_prog_ac_ct_AR=ar
ac_cv_prog_ac_ct_CC=gcc
ac_cv_prog_ac_ct_CXX=g++
ac_cv_prog_ac_ct_MANIFEST_TOOL=mt
ac_cv_prog_ac_ct_OBJDUMP=objdump
ac_cv_prog_ac_ct_RANLIB=ranlib
ac_cv_prog_ac_ct_STRIP=strip
ac_cv_prog_cc_c11=
ac_cv_prog_cc_g=yes
ac_cv_prog_cc_stdc=
ac_cv_prog_cxx_11=no
ac_cv_prog_cxx_cxx11=
ac_cv_prog_cxx_g=yes
ac_cv_prog_cxx_stdcxx=
ac_cv_prog_make_make_set=yes
ac_cv_safe_to_define___extensions__=yes
ac_cv_should_define__xopen_source=no
ac_cv_sys_file_offset_bits=no
ac_cv_sys_largefile_CC=no
ac_cv_target=x86_64-pc-linux-gnu
ac_cv_type_off_t=yes
ac_cv_type_pid_t=yes
ac_cv_type_ptrdiff_t=yes
ac_cv_type_size_t=yes
ac_cv_type_ssize_t=yes
ac_cv_type_uid_t=yes
am_cv_CC_dependencies_compiler_type=gcc3
am_cv_CXX_dependencies_compiler_type=gcc3
am_cv_make_support_nested_variables=yes
am_cv_prog_cc_c_o=yes
am_cv_prog_tar_pax=gnutar
ax_cv_check_cflags___fvisibility_hidden=yes
ax_cv_cxx_compile_cxx17=yes
lt_cv_ar_at_file=@
lt_cv_archive_cmds_need_lc=no
lt_cv_deplibs_check_method=pass_all
lt_cv_file_magic_cmd='$MAGIC_CMD'
lt_cv_file_magic_test_file=
lt_cv_ld_reload_flag=-r
lt_cv_nm_interface='BSD nm'
lt_cv_objdir=.libs
lt_cv_path_LD=/usr/bin/ld
lt_cv_path_LDCXX='/usr/bin/ld -m elf_x86_64'
lt_cv_path_NM='/usr/bin/nm -B'
lt_cv_path_mainfest_tool=no
lt_cv_prog_compiler_c_o=yes
lt_cv_prog_compiler_c_o_CXX=yes
lt_cv_prog_compiler_pic='-fPIC -DPIC'
lt_cv_prog_compiler_pic_CXX='-fPIC -DPIC'
lt_cv_prog_compiler_pic_works=yes
lt_cv_prog_compiler_pic_works_CXX=yes
lt_cv_prog_compiler_rtti_exceptions=no
lt_cv_prog_compiler_static_works=yes
lt_cv_prog_compiler_static_works_CXX=yes
lt_cv_prog_gnu_ld=yes
lt_cv_prog_gnu_ldcxx=yes
lt_cv_sharedlib_from_linklib_cmd='printf %s\n'
lt_cv_shlibpath_overrides_runpath=yes
lt_cv_sys_global_symbol_pipe='sed -n -e '\''s/^.*[	 ]\([ABCDGIRSTW][ABCDGIRSTW]*\)[	 ][	 ]*\([_A-Za-z][_A-Za-z0-9]*\)$/\1 \2 \2/p'\'' | sed '\''/ __gnu_lto/d'\'''
lt_cv_sys_global_symbol_to_c_name_address='sed -n -e '\''s/^: \(.*\) .*$/  {"\1", (void *) 0},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/  {"\1", (void *) \&\1},/p'\'''
lt_cv_sys_global_symbol_to_c_name_address_lib_prefix='sed -n -e '\''s/^: \(.*\) .*$/  {"\1", (void *) 0},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(lib.*\)$/  {"\1", (void *) \&\1},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/  {"lib\1", (void *) \&\1},/p'\'''
lt_cv_sys_global_symbol_to_cdecl='sed -n -e '\''s/^T .* \(.*\)$/extern int \1();/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/extern char \1;/p'\'''
lt_cv_sys_global_symbol_to_import=
lt_cv_sys_max_cmd_len=1572864
lt_cv_to_host_file_cmd=func_convert_file_noop
lt_cv_to_tool_file_cmd=func_convert_file_noop
lt_cv_truncate_bin='/usr/bin/dd bs=4096 count=1'

## ----------------- ##
## Output variables. ##
## ----------------- ##

ACLOCAL='${SHELL} '\''/home/<USER>/ngtcp2_qpack/nghttp3/missing'\'' aclocal-1.16'
AMDEPBACKSLASH='\'
AMDEP_FALSE='#'
AMDEP_TRUE=''
AMTAR='$${TAR-tar}'
AM_BACKSLASH='\'
AM_DEFAULT_V='$(AM_DEFAULT_VERBOSITY)'
AM_DEFAULT_VERBOSITY='0'
AM_V='$(V)'
AR='ar'
AUTOCONF='${SHELL} '\''/home/<USER>/ngtcp2_qpack/nghttp3/missing'\'' autoconf'
AUTOHEADER='${SHELL} '\''/home/<USER>/ngtcp2_qpack/nghttp3/missing'\'' autoheader'
AUTOMAKE='${SHELL} '\''/home/<USER>/ngtcp2_qpack/nghttp3/missing'\'' automake-1.16'
AWK='gawk'
CC='gcc'
CCDEPMODE='depmode=gcc3'
CFLAGS='-g -O2'
CPP='gcc -E'
CPPFLAGS=''
CSCOPE='cscope'
CTAGS='ctags'
CXX='g++'
CXXCPP='g++ -E'
CXXDEPMODE='depmode=gcc3'
CXXFLAGS='-g -O2'
CYGPATH_W='echo'
DEBUGCFLAGS=''
DEFS='-DHAVE_CONFIG_H'
DEPDIR='.deps'
DLLTOOL='false'
DSYMUTIL=''
DUMPBIN=''
ECHO_C=''
ECHO_N='-n'
ECHO_T=''
EGREP='/usr/bin/grep -E'
ENABLE_EXAMPLES_FALSE=''
ENABLE_EXAMPLES_TRUE='#'
ETAGS='etags'
EXEEXT=''
EXTRACFLAG='-fvisibility=hidden'
FGREP='/usr/bin/grep -F'
GREP='/usr/bin/grep'
HAVE_CXX17='1'
INSTALL_DATA='${INSTALL} -m 644'
INSTALL_PROGRAM='${INSTALL}'
INSTALL_SCRIPT='${INSTALL}'
INSTALL_STRIP_PROGRAM='$(install_sh) -c -s'
LD='/usr/bin/ld -m elf_x86_64'
LDFLAGS=''
LIBOBJS=''
LIBS=''
LIBTOOL='$(SHELL) $(top_builddir)/libtool'
LIPO=''
LN_S='ln -s'
LTLIBOBJS=''
LT_AGE='2'
LT_CURRENT='11'
LT_REVISION='9'
LT_SYS_LIBRARY_PATH=''
MAKEINFO='${SHELL} '\''/home/<USER>/ngtcp2_qpack/nghttp3/missing'\'' makeinfo'
MANIFEST_TOOL=':'
MKDIR_P='/usr/bin/mkdir -p'
NM='/usr/bin/nm -B'
NMEDIT=''
OBJDUMP='objdump'
OBJEXT='o'
OTOOL64=''
OTOOL=''
PACKAGE='nghttp3'
PACKAGE_BUGREPORT='<EMAIL>'
PACKAGE_NAME='nghttp3'
PACKAGE_STRING='nghttp3 1.11.0-DEV'
PACKAGE_TARNAME='nghttp3'
PACKAGE_URL=''
PACKAGE_VERSION='1.11.0-DEV'
PACKAGE_VERSION_NUM='0x010b00'
PATH_SEPARATOR=':'
PKG_CONFIG='/usr/bin/pkg-config'
PKG_CONFIG_LIBDIR=''
PKG_CONFIG_PATH=''
RANLIB='ranlib'
SED='/usr/bin/sed'
SET_MAKE=''
SHELL='/bin/bash'
STRIP='strip'
VERSION='1.11.0-DEV'
WARNCFLAGS=''
WARNCXXFLAGS=''
ac_ct_AR='ar'
ac_ct_CC='gcc'
ac_ct_CXX='g++'
ac_ct_DUMPBIN=''
am__EXEEXT_FALSE=''
am__EXEEXT_TRUE='#'
am__fastdepCC_FALSE='#'
am__fastdepCC_TRUE=''
am__fastdepCXX_FALSE='#'
am__fastdepCXX_TRUE=''
am__include='include'
am__isrc=''
am__leading_dot='.'
am__nodep='_no'
am__quote=''
am__tar='tar --format=posix -chf - "$$tardir"'
am__untar='tar -xf -'
bindir='${exec_prefix}/bin'
build='x86_64-pc-linux-gnu'
build_alias=''
build_cpu='x86_64'
build_os='linux-gnu'
build_vendor='pc'
datadir='${datarootdir}'
datarootdir='${prefix}/share'
docdir='${datarootdir}/doc/${PACKAGE_TARNAME}'
dvidir='${docdir}'
exec_prefix='${prefix}'
host='x86_64-pc-linux-gnu'
host_alias=''
host_cpu='x86_64'
host_os='linux-gnu'
host_vendor='pc'
htmldir='${docdir}'
includedir='${prefix}/include'
infodir='${datarootdir}/info'
install_sh='${SHELL} /home/<USER>/ngtcp2_qpack/nghttp3/install-sh'
libdir='${exec_prefix}/lib'
libexecdir='${exec_prefix}/libexec'
localedir='${datarootdir}/locale'
localstatedir='${prefix}/var'
mandir='${datarootdir}/man'
mkdir_p='$(MKDIR_P)'
oldincludedir='/usr/include'
pdfdir='${docdir}'
prefix='/home/<USER>/ngtcp2_qpack/nghttp3/build'
program_transform_name='s,x,x,'
psdir='${docdir}'
runstatedir='${localstatedir}/run'
sbindir='${exec_prefix}/sbin'
sharedstatedir='${prefix}/com'
sysconfdir='${prefix}/etc'
target='x86_64-pc-linux-gnu'
target_alias=''
target_cpu='x86_64'
target_os='linux-gnu'
target_vendor='pc'

## ----------- ##
## confdefs.h. ##
## ----------- ##

/* confdefs.h */
#define PACKAGE_NAME "nghttp3"
#define PACKAGE_TARNAME "nghttp3"
#define PACKAGE_VERSION "1.11.0-DEV"
#define PACKAGE_STRING "nghttp3 1.11.0-DEV"
#define PACKAGE_BUGREPORT "<EMAIL>"
#define PACKAGE_URL ""
#define HAVE_STDIO_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_STDINT_H 1
#define HAVE_STRINGS_H 1
#define HAVE_SYS_STAT_H 1
#define HAVE_SYS_TYPES_H 1
#define HAVE_UNISTD_H 1
#define HAVE_WCHAR_H 1
#define STDC_HEADERS 1
#define _ALL_SOURCE 1
#define _DARWIN_C_SOURCE 1
#define _GNU_SOURCE 1
#define _HPUX_ALT_XOPEN_SOCKET_API 1
#define _NETBSD_SOURCE 1
#define _OPENBSD_SOURCE 1
#define _POSIX_PTHREAD_SEMANTICS 1
#define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
#define __STDC_WANT_IEC_60559_BFP_EXT__ 1
#define __STDC_WANT_IEC_60559_DFP_EXT__ 1
#define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
#define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
#define __STDC_WANT_LIB_EXT2__ 1
#define __STDC_WANT_MATH_SPEC_FUNCS__ 1
#define _TANDEM_SOURCE 1
#define __EXTENSIONS__ 1
#define HAVE_DLFCN_H 1
#define LT_OBJDIR ".libs/"
#define PACKAGE "nghttp3"
#define VERSION "1.11.0-DEV"
#define HAVE_CXX17 1
#define HAVE_ARPA_INET_H 1
#define HAVE_STDDEF_H 1
#define HAVE_STDINT_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_UNISTD_H 1
#define HAVE_ENDIAN_H 1
#define HAVE_BYTESWAP_H 1
#define HAVE_PTRDIFF_T 1
#define HAVE_MEMMOVE 1
#define HAVE_MEMSET 1
#define HAVE_DECL_BE64TOH 1
#define HAVE_DECL_BSWAP_64 1

configure: exit 0
